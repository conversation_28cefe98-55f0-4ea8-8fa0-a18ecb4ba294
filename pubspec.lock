# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _fe_analyzer_shared:
    dependency: transitive
    description:
      name: _fe_analyzer_shared
      sha256: "16e298750b6d0af7ce8a3ba7c18c69c3785d11b15ec83f6dcd0ad2a0009b3cab"
      url: "https://pub.dev"
    source: hosted
    version: "76.0.0"
  _flutterfire_internals:
    dependency: transitive
    description:
      name: _flutterfire_internals
      sha256: "37a42d06068e2fe3deddb2da079a8c4d105f241225ba27b7122b37e9865fd8f7"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.35"
  _macros:
    dependency: transitive
    description: dart
    source: sdk
    version: "0.3.3"
  analyzer:
    dependency: transitive
    description:
      name: analyzer
      sha256: "1f14db053a8c23e260789e9b0980fa27f2680dd640932cae5e1137cce0e46e1e"
      url: "https://pub.dev"
    source: hosted
    version: "6.11.0"
  animations:
    dependency: "direct main"
    description:
      name: animations
      sha256: b70b084d3a3895aab7764bd505b89e72c199e5edcd1e4e3af0c76b52c2c7c5b6
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  another_flushbar:
    dependency: "direct main"
    description:
      name: another_flushbar
      sha256: "19bf9520230ec40b300aaf9dd2a8fefcb277b25ecd1c4838f530566965befc2a"
      url: "https://pub.dev"
    source: hosted
    version: "1.12.30"
  ansi_styles:
    dependency: transitive
    description:
      name: ansi_styles
      sha256: "9c656cc12b3c27b17dd982b2cc5c0cfdfbdabd7bc8f3ae5e8542d9867b47ce8a"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.2+1"
  app_settings:
    dependency: "direct main"
    description:
      name: app_settings
      sha256: "09bc7fe0313a507087bec1a3baf555f0576e816a760cbb31813a88890a09d9e5"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  archive:
    dependency: "direct overridden"
    description:
      name: archive
      sha256: cb6a278ef2dbb298455e1a713bda08524a175630ec643a242c399c932a0a1f7d
      url: "https://pub.dev"
    source: hosted
    version: "3.6.1"
  args:
    dependency: transitive
    description:
      name: args
      sha256: bf9f5caeea8d8fe6721a9c358dd8a5c1947b27f1cfaa18b39c301273594919e6
      url: "https://pub.dev"
    source: hosted
    version: "2.6.0"
  asn1lib:
    dependency: transitive
    description:
      name: asn1lib
      sha256: c273725e171cea7e69c8953181202a2850297bcc7617916d83b396cd791a2dcd
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  async:
    dependency: transitive
    description:
      name: async
      sha256: "947bfcf187f74dbc5e146c9eb9c0f10c9f8b30743e341481c1e2ed3ecc18c20c"
      url: "https://pub.dev"
    source: hosted
    version: "2.11.0"
  auto_size_text:
    dependency: transitive
    description:
      name: auto_size_text
      sha256: "3f5261cd3fb5f2a9ab4e2fc3fba84fd9fcaac8821f20a1d4e71f557521b22599"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  badges:
    dependency: "direct main"
    description:
      name: badges
      sha256: a7b6bbd60dce418df0db3058b53f9d083c22cdb5132a052145dc267494df0b84
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  base32:
    dependency: transitive
    description:
      name: base32
      sha256: ddad4ebfedf93d4500818ed8e61443b734ffe7cf8a45c668c9b34ef6adde02e2
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  bloc:
    dependency: transitive
    description:
      name: bloc
      sha256: "3820f15f502372d979121de1f6b97bfcf1630ebff8fe1d52fb2b0bfa49be5b49"
      url: "https://pub.dev"
    source: hosted
    version: "8.1.2"
  bloc_notification:
    dependency: transitive
    description:
      name: bloc_notification
      sha256: d50411169a571bb5868b2130222a48173c4286d899c0c408c6c0da46442310c1
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2+1"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "6cfb5af12253eaf2b368f07bacc5a80d1301a071c73360d746b7f2e32d762c66"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  build:
    dependency: transitive
    description:
      name: build
      sha256: "80184af8b6cb3e5c1c4ec6d8544d27711700bc3e6d2efad04238c7b5290889f0"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  build_config:
    dependency: transitive
    description:
      name: build_config
      sha256: "5b7355c14258f5e7df24bad1566f7b991de3e54aeacfb94e1a65e5233d9739c1"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  build_daemon:
    dependency: transitive
    description:
      name: build_daemon
      sha256: "5f02d73eb2ba16483e693f80bee4f088563a820e47d1027d4cdfe62b5bb43e65"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  build_resolvers:
    dependency: transitive
    description:
      name: build_resolvers
      sha256: b9e4fda21d846e192628e7a4f6deda6888c36b5b69ba02ff291a01fd529140f0
      url: "https://pub.dev"
    source: hosted
    version: "2.4.4"
  build_runner:
    dependency: "direct dev"
    description:
      name: build_runner
      sha256: "74691599a5bc750dc96a6b4bfd48f7d9d66453eab04c7f4063134800d6a5c573"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.14"
  build_runner_core:
    dependency: transitive
    description:
      name: build_runner_core
      sha256: "22e3aa1c80e0ada3722fe5b63fd43d9c8990759d0a2cf489c8c5d7b2bdebc021"
      url: "https://pub.dev"
    source: hosted
    version: "8.0.0"
  built_collection:
    dependency: transitive
    description:
      name: built_collection
      sha256: "376e3dd27b51ea877c28d525560790aee2e6fbb5f20e2f85d5081027d94e2100"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  built_value:
    dependency: transitive
    description:
      name: built_value
      sha256: "8f4772ec1e72822da7213627a0db16029085a8d709a9032e77b9a049209b6cb2"
      url: "https://pub.dev"
    source: hosted
    version: "8.4.0"
  cached_network_image:
    dependency: "direct main"
    description:
      name: cached_network_image
      sha256: f98972704692ba679db144261172a8e20feb145636c617af0eb4022132a6797f
      url: "https://pub.dev"
    source: hosted
    version: "3.3.0"
  cached_network_image_platform_interface:
    dependency: transitive
    description:
      name: cached_network_image_platform_interface
      sha256: "56aa42a7a01e3c9db8456d9f3f999931f1e05535b5a424271e9a38cabf066613"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  cached_network_image_web:
    dependency: transitive
    description:
      name: cached_network_image_web
      sha256: "759b9a9f8f6ccbb66c185df805fac107f05730b1dab9c64626d1008cca532257"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  camera:
    dependency: "direct main"
    description:
      name: camera
      sha256: dfa8fc5a1adaeb95e7a54d86a5bd56f4bb0e035515354c8ac6d262e35cec2ec8
      url: "https://pub.dev"
    source: hosted
    version: "0.10.6"
  camera_android:
    dependency: transitive
    description:
      name: camera_android
      sha256: "19b7226387218864cb2388e1ad5db7db50d065222f5511254b03fc397dd21a5e"
      url: "https://pub.dev"
    source: hosted
    version: "0.10.9+17"
  camera_avfoundation:
    dependency: transitive
    description:
      name: camera_avfoundation
      sha256: "2e4c568f70e406ccb87376bc06b53d2f5bebaab71e2fbcc1a950e31449381bcf"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.17+5"
  camera_platform_interface:
    dependency: transitive
    description:
      name: camera_platform_interface
      sha256: b3ede1f171532e0d83111fe0980b46d17f1aa9788a07a2fbed07366bbdbb9061
      url: "https://pub.dev"
    source: hosted
    version: "2.8.0"
  camera_web:
    dependency: transitive
    description:
      name: camera_web
      sha256: "595f28c89d1fb62d77c73c633193755b781c6d2e0ebcd8dc25b763b514e6ba8f"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.5"
  carousel_slider:
    dependency: "direct main"
    description:
      name: carousel_slider
      sha256: "7b006ec356205054af5beaef62e2221160ea36b90fb70a35e4deacd49d0349ae"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: "04a925763edad70e8443c99234dc3328f442e811f1d8fd1a72f1c8ad0f69a605"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  charcode:
    dependency: transitive
    description:
      name: charcode
      sha256: fb98c0f6d12c920a02ee2d998da788bca066ca5f148492b7085ee23372b12306
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  checked_yaml:
    dependency: transitive
    description:
      name: checked_yaml
      sha256: dd007e4fb8270916820a0d66e24f619266b60773cddd082c6439341645af2659
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  cli_launcher:
    dependency: transitive
    description:
      name: cli_launcher
      sha256: "5e7e0282b79e8642edd6510ee468ae2976d847a0a29b3916e85f5fa1bfe24005"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.1"
  cli_util:
    dependency: transitive
    description:
      name: cli_util
      sha256: ff6785f7e9e3c38ac98b2fb035701789de90154024a75b6cb926445e83197d1c
      url: "https://pub.dev"
    source: hosted
    version: "0.4.2"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: cb6d7f03e1de671e34607e909a7213e31d7752be4fb66a86d29fe1eb14bfb5cf
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  code_builder:
    dependency: transitive
    description:
      name: code_builder
      sha256: "0d43dd1288fd145de1ecc9a3948ad4a6d5a82f0a14c4fdd0892260787d975cbe"
      url: "https://pub.dev"
    source: hosted
    version: "4.4.0"
  collection:
    dependency: "direct main"
    description:
      name: collection
      sha256: a1ace0a119f20aabc852d165077c036cd864315bd99b7eaa10a60100341941bf
      url: "https://pub.dev"
    source: hosted
    version: "1.19.0"
  common:
    dependency: "direct main"
    description:
      path: "modules/common"
      relative: true
    source: path
    version: "1.0.0+1"
  connectivity:
    dependency: transitive
    description:
      name: connectivity
      sha256: a8e91263cf3e25fb5cc95e19dfde4999e32a648ac3b9e8a558a28165731678f8
      url: "https://pub.dev"
    source: hosted
    version: "3.0.6"
  connectivity_for_web:
    dependency: transitive
    description:
      name: connectivity_for_web
      sha256: "01a390c1d5adc2ed1fa1f52d120c07fe9fd01166a93f965a832fd6cfc0ea6482"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0+1"
  connectivity_macos:
    dependency: transitive
    description:
      name: connectivity_macos
      sha256: "51ae08d5162eca9669b9d8951ed83ce19c5355a81149f94e4dee2740beb93628"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+2"
  connectivity_platform_interface:
    dependency: transitive
    description:
      name: connectivity_platform_interface
      sha256: "2d82e942df9d49f29a24bb07fb5ce085d4a53e47818c62364d2b6deb9e0d7a8e"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  contacts_service:
    dependency: "direct main"
    description:
      name: contacts_service
      sha256: f6d5ea33b31dfcdcd2e65d8abdc836502e04ddb0f66a96aa726fa9891ea9671e
      url: "https://pub.dev"
    source: hosted
    version: "0.6.3"
  conventional_commit:
    dependency: transitive
    description:
      name: conventional_commit
      sha256: dec15ad1118f029c618651a4359eb9135d8b88f761aa24e4016d061cd45948f2
      url: "https://pub.dev"
    source: hosted
    version: "0.6.0+1"
  convert:
    dependency: transitive
    description:
      name: convert
      sha256: b30acd5944035672bc15c6b7a8b47d773e41e2f17de064350988c5d02adb1c68
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      sha256: "7caf6a750a0c04effbb52a676dce9a4a592e10ad35c34d6d2d0e4811160d5670"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.4+2"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: "1e445881f28f22d6140f181e07737b22f1e099a5e1ff94b0af2f9e4a463f4855"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.6"
  crypto_keys:
    dependency: transitive
    description:
      name: crypto_keys
      sha256: aece28bf800f045577db1ec0c206088dcbcb620837ba35acbdc03e5c6ba8d799
      url: "https://pub.dev"
    source: hosted
    version: "0.3.0"
  csslib:
    dependency: transitive
    description:
      name: csslib
      sha256: "831883fb353c8bdc1d71979e5b342c7d88acfbc643113c14ae51e2442ea0f20f"
      url: "https://pub.dev"
    source: hosted
    version: "0.17.3"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      sha256: e35129dc44c9118cee2a5603506d823bab99c68393879edb440e0090d07586be
      url: "https://pub.dev"
    source: hosted
    version: "1.0.5"
  dart_style:
    dependency: transitive
    description:
      name: dart_style
      sha256: "7306ab8a2359a48d22310ad823521d723acfed60ee1f7e37388e8986853b6820"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.8"
  dbus:
    dependency: transitive
    description:
      name: dbus
      sha256: "365c771ac3b0e58845f39ec6deebc76e3276aa9922b0cc60840712094d9047ac"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.10"
  device_info:
    dependency: "direct main"
    description:
      name: device_info
      sha256: f4a8156cb7b7480d969cb734907d18b333c8f0bc0b1ad0b342cdcecf30d62c48
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  device_info_platform_interface:
    dependency: transitive
    description:
      name: device_info_platform_interface
      sha256: b148e0bf9640145d09a4f8dea96614076f889e7f7f8b5ecab1c7e5c2dbc73c1b
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  device_info_plus:
    dependency: "direct main"
    description:
      name: device_info_plus
      sha256: a7fd703482b391a87d60b6061d04dfdeab07826b96f9abd8f5ed98068acc0074
      url: "https://pub.dev"
    source: hosted
    version: "10.1.2"
  device_info_plus_platform_interface:
    dependency: transitive
    description:
      name: device_info_plus_platform_interface
      sha256: "282d3cf731045a2feb66abfe61bbc40870ae50a3ed10a4d3d217556c35c8c2ba"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.1"
  dio:
    dependency: "direct main"
    description:
      name: dio
      sha256: "5598aa796bbf4699afd5c67c0f5f6e2ed542afc956884b9cd58c306966efc260"
      url: "https://pub.dev"
    source: hosted
    version: "5.7.0"
  dio_web_adapter:
    dependency: transitive
    description:
      name: dio_web_adapter
      sha256: "33259a9276d6cea88774a0000cfae0d861003497755969c92faa223108620dc8"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  dotted_border:
    dependency: "direct main"
    description:
      name: dotted_border
      sha256: "108837e11848ca776c53b30bc870086f84b62ed6e01c503ed976e8f8c7df9c04"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  dotted_line:
    dependency: "direct main"
    description:
      name: dotted_line
      sha256: "453e6d0ec4fe923ae00469dbc2973b9e9b11ef1c71abcded003d05af0a219d23"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  ekyc:
    dependency: "direct main"
    description:
      path: "modules/ekyc"
      relative: true
    source: path
    version: "0.0.1"
  ekyc_bloc:
    dependency: transitive
    description:
      path: "modules/ekyc/bloc"
      relative: true
    source: path
    version: "0.0.1"
  equatable:
    dependency: transitive
    description:
      name: equatable
      sha256: c2b87cb7756efdf69892005af546c56c0b5037f54d2a88269b4f347a505e3ca2
      url: "https://pub.dev"
    source: hosted
    version: "2.0.5"
  expandable_page_view:
    dependency: "direct main"
    description:
      name: expandable_page_view
      sha256: "2174b6c6d7fa94f0b1b8762ea95d6da2328f33a9b44e543eb81cce1d7e65ffcf"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.13"
  fading_edge_scrollview:
    dependency: transitive
    description:
      name: fading_edge_scrollview
      sha256: fcc6184eb6ea6761f310a3619bccd1f59520380328cb1a9fa161730bb2078910
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "511392330127add0b769b75a987850d136345d9227c6b94c96a04cf4a391bf78"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: "16ed7b077ef01ad6170a3d0c57caa4a112a38d7a2ed5602e0aca9ca6f3d98da6"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  file:
    dependency: "direct main"
    description:
      name: file
      sha256: a3b4f84adafef897088c160faf7dfffb7696046cb13ae90b508c2cbc95d3b8d4
      url: "https://pub.dev"
    source: hosted
    version: "7.0.1"
  file_picker:
    dependency: "direct main"
    description:
      name: file_picker
      sha256: "825aec673606875c33cd8d3c4083f1a3c3999015a84178b317b7ef396b7384f3"
      url: "https://pub.dev"
    source: hosted
    version: "8.0.7"
  file_selector_linux:
    dependency: transitive
    description:
      name: file_selector_linux
      sha256: b2b91daf8a68ecfa4a01b778a6f52edef9b14ecd506e771488ea0f2e0784198b
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3+1"
  file_selector_macos:
    dependency: transitive
    description:
      name: file_selector_macos
      sha256: "271ab9986df0c135d45c3cdb6bd0faa5db6f4976d3e4b437cf7d0f258d941bfc"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.4+2"
  file_selector_platform_interface:
    dependency: transitive
    description:
      name: file_selector_platform_interface
      sha256: a3994c26f10378a039faa11de174d7b78eb8f79e4dd0af2a451410c1a5c3f66b
      url: "https://pub.dev"
    source: hosted
    version: "2.6.2"
  file_selector_windows:
    dependency: transitive
    description:
      name: file_selector_windows
      sha256: "8f5d2f6590d51ecd9179ba39c64f722edc15226cc93dcc8698466ad36a4a85a4"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3+3"
  firebase_analytics:
    dependency: transitive
    description:
      name: firebase_analytics
      sha256: dbf1e7ab22cfb1f4a4adb103b46a26276b4edc593d4a78ef6fb942bafc92e035
      url: "https://pub.dev"
    source: hosted
    version: "10.10.7"
  firebase_analytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_analytics_platform_interface
      sha256: "3729b74f8cf1d974a27ba70332ecb55ff5ff560edc8164a6469f4a055b429c37"
      url: "https://pub.dev"
    source: hosted
    version: "3.10.8"
  firebase_analytics_web:
    dependency: transitive
    description:
      name: firebase_analytics_web
      sha256: "019cd7eee74254d33fbd2e29229367ce33063516bf6b3258a341d89e3b0f1655"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.7+7"
  firebase_core:
    dependency: transitive
    description:
      name: firebase_core
      sha256: "26de145bb9688a90962faec6f838247377b0b0d32cc0abecd9a4e43525fc856c"
      url: "https://pub.dev"
    source: hosted
    version: "2.32.0"
  firebase_core_platform_interface:
    dependency: transitive
    description:
      name: firebase_core_platform_interface
      sha256: e30da58198a6d4b49d5bce4e852f985c32cb10db329ebef9473db2b9f09ce810
      url: "https://pub.dev"
    source: hosted
    version: "5.3.0"
  firebase_core_web:
    dependency: transitive
    description:
      name: firebase_core_web
      sha256: "362e52457ed2b7b180964769c1e04d1e0ea0259fdf7025fdfedd019d4ae2bd88"
      url: "https://pub.dev"
    source: hosted
    version: "2.17.5"
  firebase_crashlytics:
    dependency: "direct main"
    description:
      name: firebase_crashlytics
      sha256: "9897c01efaa950d2f6da8317d12452749a74dc45f33b46390a14cfe28067f271"
      url: "https://pub.dev"
    source: hosted
    version: "3.5.7"
  firebase_crashlytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_crashlytics_platform_interface
      sha256: "16a71e08fbf6e00382816e1b13397898c29a54fa0ad969c2c2a3b82a704877f0"
      url: "https://pub.dev"
    source: hosted
    version: "3.6.35"
  firebase_messaging:
    dependency: "direct main"
    description:
      name: firebase_messaging
      sha256: a1662cc95d9750a324ad9df349b873360af6f11414902021f130c68ec02267c4
      url: "https://pub.dev"
    source: hosted
    version: "14.9.4"
  firebase_messaging_platform_interface:
    dependency: transitive
    description:
      name: firebase_messaging_platform_interface
      sha256: "87c4a922cb6f811cfb7a889bdbb3622702443c52a0271636cbc90d813ceac147"
      url: "https://pub.dev"
    source: hosted
    version: "4.5.37"
  firebase_messaging_web:
    dependency: transitive
    description:
      name: firebase_messaging_web
      sha256: "0d34dca01a7b103ed7f20138bffbb28eb0e61a677bf9e78a028a932e2c7322d5"
      url: "https://pub.dev"
    source: hosted
    version: "3.8.7"
  firebase_remote_config:
    dependency: transitive
    description:
      name: firebase_remote_config
      sha256: "653bd94b68e2c4e89eca10db90576101f1024151f39f2d4e7c64ae6a90a5f9c5"
      url: "https://pub.dev"
    source: hosted
    version: "4.4.7"
  firebase_remote_config_platform_interface:
    dependency: transitive
    description:
      name: firebase_remote_config_platform_interface
      sha256: "24a2c445b15de3af7e4582ebceb2aa9a1e3731d0202cb3e7a1e03012440fa07d"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.35"
  firebase_remote_config_web:
    dependency: transitive
    description:
      name: firebase_remote_config_web
      sha256: "525aa3000fd27cd023841c802010a06515e564aab2f147aa964b35f54abbf449"
      url: "https://pub.dev"
    source: hosted
    version: "1.6.7"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: b6dc7065e46c974bc7c5f143080a6764ec7a4be6da1285ececdc37be96de53be
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  fl_chart:
    dependency: transitive
    description:
      name: fl_chart
      sha256: d0f0d49112f2f4b192481c16d05b6418bd7820e021e265a3c22db98acf7ed7fb
      url: "https://pub.dev"
    source: hosted
    version: "0.68.0"
  flare_flutter:
    dependency: "direct main"
    description:
      path: flare_flutter
      ref: remove_hashValues
      resolved-ref: d915c89bf01f1a9d5c97d9f5b2f964eb21ddf1e9
      url: "https://github.com/mbfakourii/Flare-Flutter.git"
    source: git
    version: "3.0.2"
  flat:
    dependency: "direct dev"
    description:
      name: flat
      sha256: c12a224ac14666985222e9b1ffc4e72c3cfee980004b99b8de101be3099b003a
      url: "https://pub.dev"
    source: hosted
    version: "0.5.0"
  flick_video_player:
    dependency: transitive
    description:
      name: flick_video_player
      sha256: f011cc28c6e4932485ac3a9d09ccacd25d9d430c99090480ccad2f5ebca9366e
      url: "https://pub.dev"
    source: hosted
    version: "0.9.0"
  flinq:
    dependency: transitive
    description:
      name: flinq
      sha256: "828d6cc2f2525f2c1fbf1803f1a526f4c6592068b70f46609131d0cc0562f514"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_bloc:
    dependency: transitive
    description:
      name: flutter_bloc
      sha256: e74efb89ee6945bcbce74a5b3a5a3376b088e5f21f55c263fc38cbdc6237faae
      url: "https://pub.dev"
    source: hosted
    version: "8.1.3"
  flutter_cache_manager:
    dependency: transitive
    description:
      name: flutter_cache_manager
      sha256: "8207f27539deb83732fdda03e259349046a39a4c767269285f449ade355d54ba"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.1"
  flutter_device_type:
    dependency: transitive
    description:
      name: flutter_device_type
      sha256: "8f41f2271733f615957924dd1dcd10ef90848bb86b603ae20578f91591b900f3"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0"
  flutter_dotenv:
    dependency: transitive
    description:
      name: flutter_dotenv
      sha256: d9283d92059a22e9834bc0a31336658ffba77089fb6f3cc36751f1fc7c6661a3
      url: "https://pub.dev"
    source: hosted
    version: "5.0.2"
  flutter_downloader:
    dependency: "direct main"
    description:
      name: flutter_downloader
      sha256: b6da5495b6258aa7c243d0f0a5281e3430b385bccac11cc508f981e653b25aa6
      url: "https://pub.dev"
    source: hosted
    version: "1.11.8"
  flutter_echarts:
    dependency: "direct main"
    description:
      name: flutter_echarts
      sha256: "1102f0f82d3ca6025cf700d345ecc8c15c8bab38f5315dd2dcb67ecdb3fe3d91"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.0"
  flutter_html:
    dependency: "direct main"
    description:
      name: flutter_html
      sha256: "02ad69e813ecfc0728a455e4bf892b9379983e050722b1dce00192ee2e41d1ee"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0-beta.2"
  flutter_jailbreak_detection:
    dependency: "direct main"
    description:
      path: flutter_jailbreak_detection
      relative: true
    source: path
    version: "1.8.0"
  flutter_keyboard_visibility:
    dependency: "direct main"
    description:
      name: flutter_keyboard_visibility
      sha256: "40d25e00e511fc7e0735d79002db28c2d4736773e5933c45bf239ad1fb80661c"
      url: "https://pub.dev"
    source: hosted
    version: "5.3.0"
  flutter_keyboard_visibility_platform_interface:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_platform_interface
      sha256: e43a89845873f7be10cb3884345ceb9aebf00a659f479d1c8f4293fcb37022a4
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  flutter_keyboard_visibility_web:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_web
      sha256: d3771a2e752880c79203f8d80658401d0c998e4183edca05a149f5098ce6e3d1
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  flutter_lints:
    dependency: "direct dev"
    description:
      name: flutter_lints
      sha256: aeb0b80a8b3709709c9cc496cdc027c5b3216796bc0af0ce1007eaf24464fd4c
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  flutter_local_notifications:
    dependency: "direct main"
    description:
      name: flutter_local_notifications
      sha256: "49eeef364fddb71515bc78d5a8c51435a68bccd6e4d68e25a942c5e47761ae71"
      url: "https://pub.dev"
    source: hosted
    version: "17.2.3"
  flutter_local_notifications_linux:
    dependency: transitive
    description:
      name: flutter_local_notifications_linux
      sha256: c49bd06165cad9beeb79090b18cd1eb0296f4bf4b23b84426e37dd7c027fc3af
      url: "https://pub.dev"
    source: hosted
    version: "4.0.1"
  flutter_local_notifications_platform_interface:
    dependency: transitive
    description:
      name: flutter_local_notifications_platform_interface
      sha256: "85f8d07fe708c1bdcf45037f2c0109753b26ae077e9d9e899d55971711a4ea66"
      url: "https://pub.dev"
    source: hosted
    version: "7.2.0"
  flutter_localizations:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_markdown:
    dependency: "direct main"
    description:
      name: flutter_markdown
      sha256: f0e599ba89c9946c8e051780f0ec99aba4ba15895e0380a7ab68f420046fc44e
      url: "https://pub.dev"
    source: hosted
    version: "0.7.4+1"
  flutter_pdfview:
    dependency: "direct main"
    description:
      name: flutter_pdfview
      sha256: "715085f9f2c1ad5129dfe0d31a5f0e5481e2e296ce4e6ce72662de28df5456a6"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.4"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      sha256: b068ffc46f82a55844acfa4fdbb61fad72fa2aef0905548419d97f0f95c456da
      url: "https://pub.dev"
    source: hosted
    version: "2.0.17"
  flutter_rating_bar:
    dependency: "direct main"
    description:
      name: flutter_rating_bar
      sha256: d2af03469eac832c591a1eba47c91ecc871fe5708e69967073c043b2d775ed93
      url: "https://pub.dev"
    source: hosted
    version: "4.0.1"
  flutter_screenutil:
    dependency: "direct main"
    description:
      name: flutter_screenutil
      sha256: "8cf100b8e4973dc570b6415a2090b0bfaa8756ad333db46939efc3e774ee100d"
      url: "https://pub.dev"
    source: hosted
    version: "5.9.0"
  flutter_secure_storage:
    dependency: "direct main"
    description:
      name: flutter_secure_storage
      sha256: de957362e046bc68da8dcf6c1d922cb8bdad8dd4979ec69480cf1a3c481abe8e
      url: "https://pub.dev"
    source: hosted
    version: "6.1.0"
  flutter_secure_storage_linux:
    dependency: transitive
    description:
      name: flutter_secure_storage_linux
      sha256: "0912ae29a572230ad52d8a4697e5518d7f0f429052fd51df7e5a7952c7efe2a3"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.3"
  flutter_secure_storage_macos:
    dependency: transitive
    description:
      name: flutter_secure_storage_macos
      sha256: "388f76fd0f093e7415a39ec4c169ae7cceeee6d9f9ba529d788a13f2be4de7bd"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  flutter_secure_storage_platform_interface:
    dependency: transitive
    description:
      name: flutter_secure_storage_platform_interface
      sha256: b3773190e385a3c8a382007893d678ae95462b3c2279e987b55d140d3b0cb81b
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  flutter_secure_storage_web:
    dependency: transitive
    description:
      name: flutter_secure_storage_web
      sha256: "42938e70d4b872e856e678c423cc0e9065d7d294f45bc41fc1981a4eb4beaffe"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  flutter_secure_storage_windows:
    dependency: transitive
    description:
      name: flutter_secure_storage_windows
      sha256: ca89c8059cf439985aa83c59619b3674c7ef6cc2e86943d169a7369d6a69cab5
      url: "https://pub.dev"
    source: hosted
    version: "1.1.3"
  flutter_simple_dependency_injection:
    dependency: "direct main"
    description:
      name: flutter_simple_dependency_injection
      sha256: cca273f637cbb46df1f1147cc9f688ea51ec3f611bae37e632f5aaa414f6e27b
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  flutter_slidable:
    dependency: "direct main"
    description:
      name: flutter_slidable
      sha256: ab7dbb16f783307c9d7762ede2593ce32c220ba2ba0fd540a3db8e9a3acba71a
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  flutter_speed_dial:
    dependency: "direct main"
    description:
      name: flutter_speed_dial
      sha256: "5aa34c99bb480a38edda643199dea538583c095d25105b84f8660945be322ee3"
      url: "https://pub.dev"
    source: hosted
    version: "6.0.0"
  flutter_staggered_grid_view:
    dependency: "direct main"
    description:
      name: flutter_staggered_grid_view
      sha256: "19e7abb550c96fbfeb546b23f3ff356ee7c59a019a651f8f102a4ba9b7349395"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.0"
  flutter_svg:
    dependency: "direct main"
    description:
      name: flutter_svg
      sha256: "8c5d68a82add3ca76d792f058b186a0599414f279f00ece4830b9b231b570338"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.7"
  flutter_svg_provider:
    dependency: "direct main"
    description:
      name: flutter_svg_provider
      sha256: aad5ab28feb23280962820a4b5db4404777c597f62349b3467b4813974a1cb99
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  flutter_switch:
    dependency: "direct main"
    description:
      name: flutter_switch
      sha256: b91477f926bba135d2d203d7b24367492662d8d9c3aa6adb960b14c1087d3c41
      url: "https://pub.dev"
    source: hosted
    version: "0.3.2"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  frontend_server_client:
    dependency: "direct dev"
    description:
      name: frontend_server_client
      sha256: f64a0333a82f30b0cca061bc3d143813a486dc086b574bfb233b7c1372427694
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  gato:
    dependency: "direct dev"
    description:
      name: gato
      sha256: "03fa3a33d50f71a912e30827ddf47c89e06e9acfd2b6b178aff0e1aa2f1e8b90"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.5+1"
  geocoding:
    dependency: "direct main"
    description:
      name: geocoding
      sha256: "0d7e4a15f1f9b9bc2b0a9898aab0e014ef0534a24e0d8ad73fedfaedd384ca3b"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.4"
  geocoding_platform_interface:
    dependency: transitive
    description:
      name: geocoding_platform_interface
      sha256: "8848605d307d844d89937cdb4b8ad7dfa880552078f310fa24d8a460f6dddab4"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  geolocator:
    dependency: "direct main"
    description:
      name: geolocator
      sha256: "5c23f3613f50586c0bbb2b8f970240ae66b3bd992088cf60dd5ee2e6f7dde3a8"
      url: "https://pub.dev"
    source: hosted
    version: "9.0.2"
  geolocator_android:
    dependency: transitive
    description:
      name: geolocator_android
      sha256: "2ba24690aee0a3e1b6b7bd47c2711a50c874e95e4c758346589d35194adf6d6a"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.7"
  geolocator_apple:
    dependency: transitive
    description:
      name: geolocator_apple
      sha256: "1b17544d250bbfebd2f36157f9ce094b0b07967441c1010243d6ea04f6f5bfe8"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  geolocator_platform_interface:
    dependency: transitive
    description:
      name: geolocator_platform_interface
      sha256: "8c10ba5c825abdcc337ba918fbc1d3a5a2b006affe6ba610e3143cd32f54388d"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.6"
  geolocator_web:
    dependency: transitive
    description:
      name: geolocator_web
      sha256: f68a122da48fcfff68bbc9846bb0b74ef651afe84a1b1f6ec20939de4d6860e1
      url: "https://pub.dev"
    source: hosted
    version: "2.1.6"
  geolocator_windows:
    dependency: transitive
    description:
      name: geolocator_windows
      sha256: f5911c88e23f48b598dd506c7c19eff0e001645bdc03bb6fecb9f4549208354d
      url: "https://pub.dev"
    source: hosted
    version: "0.1.1"
  get_it:
    dependency: transitive
    description:
      name: get_it
      sha256: "290fde3a86072e4b37dbb03c07bec6126f0ecc28dad403c12ffe2e5a2d751ab7"
      url: "https://pub.dev"
    source: hosted
    version: "7.2.0"
  glob:
    dependency: transitive
    description:
      name: glob
      sha256: "0e7014b3b7d4dac1ca4d6114f82bf1782ee86745b9b42a92c9289c23d8a0ab63"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  google_directions_api:
    dependency: transitive
    description:
      name: google_directions_api
      sha256: "1463d07b82a46bcf48c9d37155ecfdd81bf6319a13176f04f5cea0daf3d2e2bf"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.1"
  google_maps_flutter:
    dependency: "direct main"
    description:
      name: google_maps_flutter
      sha256: "460b8c3318c5be1346e7182b848e3c2b83fe65f2f5d404c871ac7e134ee59801"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  google_maps_flutter_android:
    dependency: transitive
    description:
      name: google_maps_flutter_android
      sha256: "9fd11f2ee960f8a10be95b64f261b1c3521e1d4c9f313f5547123b72ac1db507"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.10"
  google_maps_flutter_ios:
    dependency: transitive
    description:
      name: google_maps_flutter_ios
      sha256: "43a71553d9973af6886b6984e246dac18b8a62dc59581904d8d1292388b05ea5"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.12"
  google_maps_flutter_platform_interface:
    dependency: transitive
    description:
      name: google_maps_flutter_platform_interface
      sha256: b26afd071ced828d304892163a88902167e11a732e7ef9c33bab0e1b91c4295e
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  google_mlkit_barcode_scanning:
    dependency: transitive
    description:
      name: google_mlkit_barcode_scanning
      sha256: "1df232be24ae9cb93443d2786681efab71b5964c8c476f6072d8b4d61aa03ecc"
      url: "https://pub.dev"
    source: hosted
    version: "0.12.1"
  google_mlkit_commons:
    dependency: transitive
    description:
      name: google_mlkit_commons
      sha256: "9990a65f407a3ef6bae646bf10143faa93fec126683771465bc6c0b43fb0e6e9"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.1"
  google_mlkit_face_detection:
    dependency: "direct main"
    description:
      name: google_mlkit_face_detection
      sha256: "0aeab4f39204f7a235ed4cccedfe7e61401b43f4ef139a868c01fa29fdc225ab"
      url: "https://pub.dev"
    source: hosted
    version: "0.11.1"
  google_mlkit_text_recognition:
    dependency: transitive
    description:
      name: google_mlkit_text_recognition
      sha256: "179349417066fa2c275d7a6ed6cbceeb7fa265d73aacdb2d732f1a2991face0a"
      url: "https://pub.dev"
    source: hosted
    version: "0.13.1"
  google_polyline_algorithm:
    dependency: transitive
    description:
      name: google_polyline_algorithm
      sha256: "357874f00d3f93c3ba1bf4b4d9a154aa9ee87147c068238c1e8392012b686a03"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  graphs:
    dependency: transitive
    description:
      name: graphs
      sha256: "741bbf84165310a68ff28fe9e727332eef1407342fca52759cb21ad8177bb8d0"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  grouped_list:
    dependency: "direct main"
    description:
      name: grouped_list
      sha256: c52551bc17699e304634d4653b824a1aa7c6b1d3a2c1a0da1a80839f867353fb
      url: "https://pub.dev"
    source: hosted
    version: "6.0.0"
  html:
    dependency: transitive
    description:
      name: html
      sha256: "3a7812d5bcd2894edf53dfaf8cd640876cf6cef50a8f238745c8b8120ea74d3a"
      url: "https://pub.dev"
    source: hosted
    version: "0.15.4"
  http:
    dependency: "direct overridden"
    description:
      name: http
      sha256: b9c29a161230ee03d3ccf545097fccd9b87a5264228c5d348202e0f0c28f9010
      url: "https://pub.dev"
    source: hosted
    version: "1.2.2"
  http_certificate_pinning:
    dependency: "direct main"
    description:
      name: http_certificate_pinning
      sha256: "12b4848113d50c570af93e94bed64004caba14dde87719310d1e4556686c0afa"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  http_multi_server:
    dependency: transitive
    description:
      name: http_multi_server
      sha256: "97486f20f9c2f7be8f514851703d0119c3596d14ea63227af6f7a481ef2b2f8b"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: db3060f22889f3d9d55f6a217565486737037eec3609f7f3eca4d0c67ee0d8a0
      url: "https://pub.dev"
    source: hosted
    version: "4.0.1"
  image:
    dependency: "direct main"
    description:
      name: image
      sha256: "8e9d133755c3e84c73288363e6343157c383a0c6c56fc51afcc5d4d7180306d6"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.0"
  image_cropper:
    dependency: "direct main"
    description:
      name: image_cropper
      sha256: "542c3453109d16bcc388e43ae2276044d2cd6a6d20c68bdcff2c94ab9363ea15"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.1"
  image_cropper_for_web:
    dependency: transitive
    description:
      name: image_cropper_for_web
      sha256: "89c936aa772a35b69ca67b78049ae9fa163a4fb8da2f6dee3893db8883fb49d2"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  image_cropper_platform_interface:
    dependency: transitive
    description:
      name: image_cropper_platform_interface
      sha256: b232175c132b2f7ede3e1f101652bcd635cb4079a77c6dded8e6d32e6578d685
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  image_gallery_saver:
    dependency: "direct main"
    description:
      name: image_gallery_saver
      sha256: "0aba74216a4d9b0561510cb968015d56b701ba1bd94aace26aacdd8ae5761816"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  image_picker:
    dependency: "direct main"
    description:
      name: image_picker
      sha256: "021834d9c0c3de46bf0fe40341fa07168407f694d9b2bb18d532dc1261867f7a"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  image_picker_android:
    dependency: transitive
    description:
      name: image_picker_android
      sha256: "8c5abf0dcc24fe6e8e0b4a5c0b51a5cf30cefdf6407a3213dae61edc75a70f56"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.12+12"
  image_picker_for_web:
    dependency: transitive
    description:
      name: image_picker_for_web
      sha256: "717eb042ab08c40767684327be06a5d8dbb341fe791d514e4b92c7bbe1b7bb83"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.6"
  image_picker_ios:
    dependency: transitive
    description:
      name: image_picker_ios
      sha256: "4f0568120c6fcc0aaa04511cb9f9f4d29fc3d0139884b1d06be88dcec7641d6b"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.12+1"
  image_picker_linux:
    dependency: transitive
    description:
      name: image_picker_linux
      sha256: "4ed1d9bb36f7cd60aa6e6cd479779cc56a4cb4e4de8f49d487b1aaad831300fa"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  image_picker_macos:
    dependency: transitive
    description:
      name: image_picker_macos
      sha256: "3f5ad1e8112a9a6111c46d0b57a7be2286a9a07fc6e1976fdf5be2bd31d4ff62"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  image_picker_platform_interface:
    dependency: transitive
    description:
      name: image_picker_platform_interface
      sha256: "9ec26d410ff46f483c5519c29c02ef0e02e13a543f882b152d4bfd2f06802f80"
      url: "https://pub.dev"
    source: hosted
    version: "2.10.0"
  image_picker_windows:
    dependency: transitive
    description:
      name: image_picker_windows
      sha256: "6ad07afc4eb1bc25f3a01084d28520496c4a3bb0cb13685435838167c9dcedeb"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  infinite_scroll_pagination:
    dependency: "direct main"
    description:
      name: infinite_scroll_pagination
      sha256: "4047eb8191e8b33573690922a9e995af64c3949dc87efc844f936b039ea279df"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0"
  intl:
    dependency: transitive
    description:
      name: intl
      sha256: d6f56758b7d3014a48af9701c085700aac781a92a87a62b1333b46d8879661cf
      url: "https://pub.dev"
    source: hosted
    version: "0.19.0"
  intl_utils:
    dependency: "direct dev"
    description:
      name: intl_utils
      sha256: c2b1f5c72c25512cbeef5ab015c008fc50fe7e04813ba5541c25272300484bf4
      url: "https://pub.dev"
    source: hosted
    version: "2.8.7"
  io:
    dependency: transitive
    description:
      name: io
      sha256: "0d4c73c3653ab85bf696d51a9657604c900a370549196a91f33e4c39af760852"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  jiffy:
    dependency: "direct main"
    description:
      name: jiffy
      sha256: "3497caaa36d36a29033e66803c9739ce6bccbc7e241ca46070f76ee9e6f6eb0c"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.1"
  jitsi_meet:
    dependency: "direct main"
    description:
      path: "modules/jitsi_meet"
      relative: true
    source: path
    version: "4.0.0"
  jitsi_meet_platform_interface:
    dependency: transitive
    description:
      path: "modules/jitsi_meet/jitsi_meet_platform_interface"
      relative: true
    source: path
    version: "2.0.0"
  jitsi_meet_web_plugin:
    dependency: transitive
    description:
      path: "modules/jitsi_meet/jitsi_meet_web_plugin"
      relative: true
    source: path
    version: "2.0.0"
  jose:
    dependency: transitive
    description:
      name: jose
      sha256: "907f3484bead9033ef1ae5451b0cada8f5b41ad17bb4d61022548ca3ab599e0d"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.2"
  js:
    dependency: transitive
    description:
      name: js
      sha256: "5528c2f391ededb7775ec1daa69e65a2d61276f7552de2b5f7b8d34ee9fd4ab7"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.5"
  json_annotation:
    dependency: transitive
    description:
      name: json_annotation
      sha256: "1ce844379ca14835a50d2f019a3099f419082cfdd231cd86a142af94dd5c6bb1"
      url: "https://pub.dev"
    source: hosted
    version: "4.9.0"
  jwt_decoder:
    dependency: transitive
    description:
      name: jwt_decoder
      sha256: "54774aebf83f2923b99e6416b4ea915d47af3bde56884eb622de85feabbc559f"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  keyboard_attachable:
    dependency: "direct main"
    description:
      name: keyboard_attachable
      sha256: "4d363742c10aafba414712d518dc6ec733ad796aafa104d2eb6d023a3b91587f"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  ks_chat:
    dependency: "direct main"
    description:
      path: "modules/ks_chat"
      relative: true
    source: path
    version: "1.0.0+1"
  ksb_bloc:
    dependency: "direct main"
    description:
      path: bloc
      relative: true
    source: path
    version: "0.0.1"
  ksb_common:
    dependency: "direct main"
    description:
      path: "modules/ksb_common"
      relative: true
    source: path
    version: "0.0.1"
  ksbank_api_loyalty:
    dependency: transitive
    description:
      path: "open_api/ksbank/loyalty"
      relative: true
    source: path
    version: "1.0.0"
  ksbank_api_maintenance:
    dependency: transitive
    description:
      path: "open_api/ksbank/maintenance"
      relative: true
    source: path
    version: "1.0.0"
  ksbank_api_media:
    dependency: transitive
    description:
      path: "open_api/ksbank/media"
      relative: true
    source: path
    version: "1.0.0"
  ksbank_api_nickname:
    dependency: transitive
    description:
      path: "open_api/ksbank/nickname"
      relative: true
    source: path
    version: "1.0.0"
  ksbank_api_notification:
    dependency: transitive
    description:
      path: "open_api/ksbank/notification"
      relative: true
    source: path
    version: "1.0.0"
  ksbank_api_profile:
    dependency: transitive
    description:
      path: "open_api/ksbank/profile"
      relative: true
    source: path
    version: "1.0.0"
  ksbank_api_smartbank:
    dependency: transitive
    description:
      path: "open_api/ksbank/smartbank"
      relative: true
    source: path
    version: "1.0.0"
  ksbank_api_stm:
    dependency: transitive
    description:
      path: "open_api/ksbank/stm"
      relative: true
    source: path
    version: "1.0.0"
  ksbank_api_stocks:
    dependency: transitive
    description:
      path: "open_api/ksbank/stocks"
      relative: true
    source: path
    version: "1.0.0"
  leak_tracker:
    dependency: transitive
    description:
      name: leak_tracker
      sha256: "7bb2830ebd849694d1ec25bf1f44582d6ac531a57a365a803a6034ff751d2d06"
      url: "https://pub.dev"
    source: hosted
    version: "10.0.7"
  leak_tracker_flutter_testing:
    dependency: transitive
    description:
      name: leak_tracker_flutter_testing
      sha256: "9491a714cca3667b60b5c420da8217e6de0d1ba7a5ec322fab01758f6998f379"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.8"
  leak_tracker_testing:
    dependency: transitive
    description:
      name: leak_tracker_testing
      sha256: "6ba465d5d76e67ddf503e1161d1f4a6bc42306f9d66ca1e8f079a47290fb06d3"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  lints:
    dependency: transitive
    description:
      name: lints
      sha256: "5e4a9cd06d447758280a8ac2405101e0e2094d2a1dbdd3756aec3fe7775ba593"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  list_counter:
    dependency: transitive
    description:
      name: list_counter
      sha256: c447ae3dfcd1c55f0152867090e67e219d42fe6d4f2807db4bbe8b8d69912237
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  loading_indicator:
    dependency: "direct main"
    description:
      name: loading_indicator
      sha256: "3af0c7cab5ea9d6acac588a46ce98445637ce718bcc62698342d17df9c797dd4"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.3"
  local_auth:
    dependency: "direct main"
    description:
      name: local_auth
      sha256: "434d854cf478f17f12ab29a76a02b3067f86a63a6d6c4eb8fbfdcfe4879c1b7b"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  local_auth_android:
    dependency: transitive
    description:
      name: local_auth_android
      sha256: e99c44ca0bce08f26f25e2a2e07d3b443d69986e1c3acf67c1449f7d847e3625
      url: "https://pub.dev"
    source: hosted
    version: "1.0.43"
  local_auth_darwin:
    dependency: transitive
    description:
      name: local_auth_darwin
      sha256: "6d2950da311d26d492a89aeb247c72b4653ddc93601ea36a84924a396806d49c"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.1"
  local_auth_platform_interface:
    dependency: transitive
    description:
      name: local_auth_platform_interface
      sha256: fc5bd537970a324260fda506cfb61b33ad7426f37a8ea5c461cf612161ebba54
      url: "https://pub.dev"
    source: hosted
    version: "1.0.8"
  local_auth_windows:
    dependency: transitive
    description:
      name: local_auth_windows
      sha256: "505ba3367ca781efb1c50d3132e44a2446bccc4163427bc203b9b4d8994d97ea"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.10"
  logger:
    dependency: "direct main"
    description:
      name: logger
      sha256: be4b23575aac7ebf01f225a241eb7f6b5641eeaf43c6a8613510fc2f8cf187d1
      url: "https://pub.dev"
    source: hosted
    version: "2.5.0"
  logging:
    dependency: transitive
    description:
      name: logging
      sha256: c8245ada5f1717ed44271ed1c26b8ce85ca3228fd2ffdb75468ab01979309d61
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  loggy:
    dependency: transitive
    description:
      name: loggy
      sha256: "981e03162bbd3a5a843026f75f73d26e4a0d8aa035ae060456ca7b30dfd1e339"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  lottie:
    dependency: "direct main"
    description:
      name: lottie
      sha256: "23522951540d20a57a60202ed7022e6376bed206a4eee1c347a91f58bd57eb9f"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  lucky_money_api:
    dependency: transitive
    description:
      path: "open_api/ksbank/lucky_money"
      relative: true
    source: path
    version: "1.0.0"
  macros:
    dependency: transitive
    description:
      name: macros
      sha256: "1d9e801cd66f7ea3663c45fc708450db1fa57f988142c64289142c9b7ee80656"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3-main.0"
  map_launcher:
    dependency: "direct main"
    description:
      name: map_launcher
      sha256: "7436d6ef9ae57ff15beafcedafe0a8f0604006cbecd2d26024c4cfb0158c2b9a"
      url: "https://pub.dev"
    source: hosted
    version: "3.5.0"
  markdown:
    dependency: transitive
    description:
      name: markdown
      sha256: ef2a1298144e3f985cc736b22e0ccdaf188b5b3970648f2d9dc13efd1d9df051
      url: "https://pub.dev"
    source: hosted
    version: "7.2.2"
  marquee:
    dependency: "direct main"
    description:
      name: marquee
      sha256: ac5fd2176dd6262d9eec0d0cc6daa99edcbd88097433cfe86a889112cd254145
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: d2323aa2060500f906aa31a895b4030b6da3ebdcc5619d14ce1aada65cd161cb
      url: "https://pub.dev"
    source: hosted
    version: "0.12.16+1"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: f7142bb1154231d7ea5f96bc7bde4bda2a0945d2806bb11670e30b850d56bdec
      url: "https://pub.dev"
    source: hosted
    version: "0.11.1"
  melos:
    dependency: "direct main"
    description:
      name: melos
      sha256: a62abfa8c7826cec927f8585572bb9adf591be152150494d879ca2c75118809d
      url: "https://pub.dev"
    source: hosted
    version: "6.2.0"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: bdb68674043280c3428e9ec998512fb681678676b3c54e773629ffe74419f8c7
      url: "https://pub.dev"
    source: hosted
    version: "1.15.0"
  mime:
    dependency: "direct main"
    description:
      name: mime
      sha256: "801fd0b26f14a4a58ccb09d5892c3fbdeff209594300a542492cf13fba9d247a"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.6"
  mime_type:
    dependency: transitive
    description:
      name: mime_type
      sha256: "2ad6e67d3d2de9ac0f8ef5352d998fd103cb21351ae8c02fb0c78b079b37d275"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  mobile_scanner:
    dependency: "direct main"
    description:
      name: mobile_scanner
      sha256: d234581c090526676fd8fab4ada92f35c6746e3fb4f05a399665d75a399fb760
      url: "https://pub.dev"
    source: hosted
    version: "5.2.3"
  modal_bottom_sheet:
    dependency: "direct main"
    description:
      name: modal_bottom_sheet
      sha256: "3bba63c62d35c931bce7f8ae23a47f9a05836d8cb3c11122ada64e0b2f3d718f"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0-pre"
  month_picker_dialog:
    dependency: "direct main"
    description:
      name: month_picker_dialog
      sha256: "009628b6b8536f5f8e9625019659281698e64509f290536f65088e304c33d2c7"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.1"
  multiple_localization:
    dependency: "direct main"
    description:
      name: multiple_localization
      sha256: "8b071f538bdf087a7bf13cd8b78a1a041994960c8e9f0a1aaaaf0cfb39845019"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.0"
  mustache_template:
    dependency: transitive
    description:
      name: mustache_template
      sha256: a46e26f91445bfb0b60519be280555b06792460b27b19e2b19ad5b9740df5d1c
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  myshop_api:
    dependency: transitive
    description:
      path: "open_api/ksbank/myshop"
      relative: true
    source: path
    version: "1.0.0"
  nested:
    dependency: transitive
    description:
      name: nested
      sha256: "03bac4c528c64c95c722ec99280375a6f2fc708eec17c7b3f07253b626cd2a20"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  octo_image:
    dependency: transitive
    description:
      name: octo_image
      sha256: "45b40f99622f11901238e18d48f5f12ea36426d8eced9f4cbf58479c7aa2430d"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  one_of:
    dependency: transitive
    description:
      name: one_of
      sha256: "25fe0fcf181e761c6fcd604caf9d5fdf952321be17584ba81c72c06bdaa511f0"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.0"
  one_of_serializer:
    dependency: transitive
    description:
      name: one_of_serializer
      sha256: "3f3dfb5c1578ba3afef1cb47fcc49e585e797af3f2b6c2cc7ed90aad0c5e7b83"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.0"
  onesignal_flutter:
    dependency: "direct main"
    description:
      name: onesignal_flutter
      sha256: cbb201463fd82ddca823516e48d3c3dfbccd82297353e95a417fb990771c38ec
      url: "https://pub.dev"
    source: hosted
    version: "5.0.3"
  open_api_umee:
    dependency: "direct main"
    description:
      path: "modules/open-api-umee"
      relative: true
    source: path
    version: "1.0.0+1"
  open_file:
    dependency: transitive
    description:
      name: open_file
      sha256: a5ef7162fb9b72955d42abab9f3159b2fd3ee81a27187535c8038dcd5d1fa7f2
      url: "https://pub.dev"
    source: hosted
    version: "3.5.4"
  open_file_android:
    dependency: transitive
    description:
      name: open_file_android
      sha256: "58141fcaece2f453a9684509a7275f231ac0e3d6ceb9a5e6de310a7dff9084aa"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.6"
  open_file_ios:
    dependency: transitive
    description:
      name: open_file_ios
      sha256: "02996f01e5f6863832068e97f8f3a5ef9b613516db6897f373b43b79849e4d07"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  open_file_linux:
    dependency: transitive
    description:
      name: open_file_linux
      sha256: d189f799eecbb139c97f8bc7d303f9e720954fa4e0fa1b0b7294767e5f2d7550
      url: "https://pub.dev"
    source: hosted
    version: "0.0.5"
  open_file_mac:
    dependency: transitive
    description:
      name: open_file_mac
      sha256: "1440b1e37ceb0642208cfeb2c659c6cda27b25187a90635c9d1acb7d0584d324"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  open_file_macos:
    dependency: transitive
    description:
      name: open_file_macos
      sha256: cde004426515712d72d003b38e0b407636094e53266653c0d77ed7c60be88ef2
      url: "https://pub.dev"
    source: hosted
    version: "0.1.0"
  open_file_platform_interface:
    dependency: transitive
    description:
      name: open_file_platform_interface
      sha256: "101b424ca359632699a7e1213e83d025722ab668b9fd1412338221bf9b0e5757"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  open_file_web:
    dependency: transitive
    description:
      name: open_file_web
      sha256: ba35c6f38c21c2bb4268a80927bb828353dda0edfce92e274e0b9639e4f31360
      url: "https://pub.dev"
    source: hosted
    version: "0.0.2"
  open_file_windows:
    dependency: transitive
    description:
      name: open_file_windows
      sha256: d26c31ddf935a94a1a3aa43a23f4fff8a5ff4eea395fe7a8cb819cf55431c875
      url: "https://pub.dev"
    source: hosted
    version: "0.0.3"
  openapi_generator:
    dependency: "direct dev"
    description:
      name: openapi_generator
      sha256: fb39046f1753d92d2e25d1b4b132dd4f5b462b3b1b8af4a2b63078b8ac8e3a56
      url: "https://pub.dev"
    source: hosted
    version: "6.1.0"
  openapi_generator_annotations:
    dependency: "direct main"
    description:
      name: openapi_generator_annotations
      sha256: "5b4d548c18b417cfc6abd03098a28971d6211d6e10ec57e4f8fb9b2eb46c0641"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.0"
  openapi_generator_cli:
    dependency: transitive
    description:
      name: openapi_generator_cli
      sha256: a7b764d8b121ecb7cdd466c688da1e1a592f7fff73673dcdb35ee5009f49739b
      url: "https://pub.dev"
    source: hosted
    version: "6.1.0"
  otp:
    dependency: "direct main"
    description:
      name: otp
      sha256: ab667304ebf9a7f85c624cdedd6293d676c7045c284a3af2d18733ceac88a1f8
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  package_config:
    dependency: transitive
    description:
      name: package_config
      sha256: "1c5b77ccc91e4823a5af61ee74e6b972db1ef98c2ff5a18d3161c982a55448bd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  package_info:
    dependency: transitive
    description:
      name: package_info
      sha256: "6c07d9d82c69e16afeeeeb6866fe43985a20b3b50df243091bfc4a4ad2b03b75"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  package_info_plus:
    dependency: "direct main"
    description:
      name: package_info_plus
      sha256: a75164ade98cb7d24cfd0a13c6408927c6b217fa60dee5a7ff5c116a58f28918
      url: "https://pub.dev"
    source: hosted
    version: "8.0.2"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      sha256: ac1f4a4847f1ade8e6a87d1f39f5d7c67490738642e2542f559ec38c37489a66
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  passport_reader_plugin:
    dependency: "direct main"
    description:
      path: "modules/passport-reader-plugin"
      relative: true
    source: path
    version: "0.0.1"
  path:
    dependency: "direct dev"
    description:
      name: path
      sha256: "087ce49c3f0dc39180befefc60fdb4acd8f8620e5682fe2476afd0b3688bb4af"
      url: "https://pub.dev"
    source: hosted
    version: "1.9.0"
  path_drawing:
    dependency: transitive
    description:
      name: path_drawing
      sha256: bbb1934c0cbb03091af082a6389ca2080345291ef07a5fa6d6e078ba8682f977
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      sha256: e3e67b1629e6f7e8100b367d3db6ba6af4b1f0bb80f64db18ef1fbabd2fa9ccf
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  path_provider:
    dependency: "direct main"
    description:
      name: path_provider
      sha256: "50c5dd5b6e1aaf6fb3a78b33f6aa3afca52bf903a8a5298f53101fdaee55bbcd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.5"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: "6f01f8e37ec30b07bc424b4deabac37cacb1bc7e2e515ad74486039918a37eb7"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.10"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: f234384a3fdd67f989b4d54a5d73ca2a6c422fa55ae694381ae0f4375cd1ea16
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "88f5779f72ba699763fa3a3b06aa4bf6de76c8e5de842cf6f29e2e06476c2334"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: bd6f00dbd873bfb70d0761682da2b3a2c2fccc2b9e84c495821639601d81afe7
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  percent_indicator:
    dependency: "direct main"
    description:
      name: percent_indicator
      sha256: c37099ad833a883c9d71782321cb65c3a848c21b6939b6185f0ff6640d05814c
      url: "https://pub.dev"
    source: hosted
    version: "4.2.3"
  permission_handler:
    dependency: "direct main"
    description:
      name: permission_handler
      sha256: "18bf33f7fefbd812f37e72091a15575e72d5318854877e0e4035a24ac1113ecb"
      url: "https://pub.dev"
    source: hosted
    version: "11.3.1"
  permission_handler_android:
    dependency: transitive
    description:
      name: permission_handler_android
      sha256: "71bbecfee799e65aff7c744761a57e817e73b738fedf62ab7afd5593da21f9f1"
      url: "https://pub.dev"
    source: hosted
    version: "12.0.13"
  permission_handler_apple:
    dependency: "direct overridden"
    description:
      name: permission_handler_apple
      sha256: e6f6d73b12438ef13e648c4ae56bd106ec60d17e90a59c4545db6781229082a0
      url: "https://pub.dev"
    source: hosted
    version: "9.4.5"
  permission_handler_html:
    dependency: transitive
    description:
      name: permission_handler_html
      sha256: af26edbbb1f2674af65a8f4b56e1a6f526156bc273d0e65dd8075fab51c78851
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3+2"
  permission_handler_platform_interface:
    dependency: transitive
    description:
      name: permission_handler_platform_interface
      sha256: e9c8eadee926c4532d0305dff94b85bf961f16759c3af791486613152af4b4f9
      url: "https://pub.dev"
    source: hosted
    version: "4.2.3"
  permission_handler_windows:
    dependency: transitive
    description:
      name: permission_handler_windows
      sha256: "1a790728016f79a41216d88672dbc5df30e686e811ad4e698bfc51f76ad91f1e"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: cb3798bef7fc021ac45b308f4b51208a152792445cce0448c9a4ba5879dd8750
      url: "https://pub.dev"
    source: hosted
    version: "5.4.0"
  photo_view:
    dependency: "direct main"
    description:
      name: photo_view
      sha256: "1fc3d970a91295fbd1364296575f854c9863f225505c28c46e0a03e48960c75e"
      url: "https://pub.dev"
    source: hosted
    version: "0.15.0"
  pin_code_fields:
    dependency: "direct main"
    description:
      name: pin_code_fields
      sha256: "4c0db7fbc889e622e7c71ea54b9ee624bb70c7365b532abea0271b17ea75b729"
      url: "https://pub.dev"
    source: hosted
    version: "8.0.1"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "5d6b1b0036a5f331ebc77c850ebc8506cbc1e9416c27e59b439f917a902a4984"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.6"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: "4820fbfdb9478b1ebae27888254d445073732dae3d6ea81f0b7e06d5dedc3f02"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.8"
  pointycastle:
    dependency: transitive
    description:
      name: pointycastle
      sha256: "041b249f5c70e8983ebbaff4fdea252c2af8d4912ba64b649edb17615349afef"
      url: "https://pub.dev"
    source: hosted
    version: "3.6.1"
  pool:
    dependency: transitive
    description:
      name: pool
      sha256: "20fe868b6314b322ea036ba325e6fc0711a22948856475e2c2b6306e8ab39c2a"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.1"
  process:
    dependency: transitive
    description:
      name: process
      sha256: "107d8be718f120bbba9dcd1e95e3bd325b1b4a4f07db64154635ba03f2567a0d"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.3"
  prompts:
    dependency: transitive
    description:
      name: prompts
      sha256: "3773b845e85a849f01e793c4fc18a45d52d7783b4cb6c0569fad19f9d0a774a1"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  provider:
    dependency: transitive
    description:
      name: provider
      sha256: c8a055ee5ce3fd98d6fc872478b03823ffdb448699c6ebdbbc71d59b596fd48c
      url: "https://pub.dev"
    source: hosted
    version: "6.1.2"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      sha256: "40d3ab1bbd474c4c2328c91e3a7df8c6dd629b79ece4c4bd04bee496a224fb0c"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  pub_updater:
    dependency: transitive
    description:
      name: pub_updater
      sha256: "54e8dc865349059ebe7f163d6acce7c89eb958b8047e6d6e80ce93b13d7c9e60"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0"
  pubspec:
    dependency: transitive
    description:
      name: pubspec
      sha256: f534a50a2b4d48dc3bc0ec147c8bd7c304280fff23b153f3f11803c4d49d927e
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  pubspec_parse:
    dependency: transitive
    description:
      name: pubspec_parse
      sha256: "3686efe4a4613a4449b1a4ae08670aadbd3376f2e78d93e3f8f0919db02a7256"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  pull_to_refresh:
    dependency: "direct main"
    description:
      name: pull_to_refresh
      sha256: bbadd5a931837b57739cf08736bea63167e284e71fb23b218c8c9a6e042aad12
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  qr:
    dependency: transitive
    description:
      name: qr
      sha256: "64957a3930367bf97cc211a5af99551d630f2f4625e38af10edd6b19131b64b3"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  qr_code_scanner:
    dependency: transitive
    description:
      name: qr_code_scanner
      sha256: f23b68d893505a424f0bd2e324ebea71ed88465d572d26bb8d2e78a4749591fd
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  qr_flutter:
    dependency: "direct main"
    description:
      name: qr_flutter
      sha256: "5095f0fc6e3f71d08adef8feccc8cea4f12eec18a2e31c2e8d82cb6019f4b097"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0"
  quiver:
    dependency: transitive
    description:
      name: quiver
      sha256: b1c1ac5ce6688d77f65f3375a9abb9319b3cb32486bdc7a1e0fdf004d7ba4e47
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  reorderables:
    dependency: "direct main"
    description:
      name: reorderables
      sha256: "004a886e4878df1ee27321831c838bc1c976311f4ca6a74ce7d561e506540a77"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.0"
  rocket_chat_flutter_connector:
    dependency: transitive
    description:
      name: rocket_chat_flutter_connector
      sha256: c2faa576c404b7d03d39b91bfc121994a05b2a3993695ad4720e2d9dfc36c160
      url: "https://pub.dev"
    source: hosted
    version: "0.1.4"
  rxdart:
    dependency: "direct main"
    description:
      name: rxdart
      sha256: "0c7c0cedd93788d996e33041ffecda924cc54389199cde4e6a34b440f50044cb"
      url: "https://pub.dev"
    source: hosted
    version: "0.27.7"
  saving_vnp_api:
    dependency: transitive
    description:
      path: "open_api/ksbank/saving_vnp"
      relative: true
    source: path
    version: "1.0.0"
  screenshot:
    dependency: "direct main"
    description:
      name: screenshot
      sha256: "63817697a7835e6ce82add4228e15d233b74d42975c143ad8cfe07009fab866b"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  sensors_plus:
    dependency: "direct overridden"
    description:
      name: sensors_plus
      sha256: "905282c917c6bb731c242f928665c2ea15445aa491249dea9d98d7c79dc8fd39"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.1"
  sensors_plus_platform_interface:
    dependency: transitive
    description:
      name: sensors_plus_platform_interface
      sha256: "58815d2f5e46c0c41c40fb39375d3f127306f7742efe3b891c0b1c87e2b5cd5d"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  shake:
    dependency: "direct main"
    description:
      name: shake
      sha256: "107546951c6b8f5e4c2dca66dfb3aa27dd1a853b4e9a26c9aea224b167045023"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  share:
    dependency: "direct main"
    description:
      name: share
      sha256: "97e6403f564ed1051a01534c2fc919cb6e40ea55e60a18ec23cee6e0ce19f4be"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.4"
  shared_preferences:
    dependency: "direct main"
    description:
      name: shared_preferences
      sha256: "76917b7d4b9526b2ba416808a7eb9fb2863c1a09cf63ec85f1453da240fa818a"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.15"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      sha256: "853801ce6ba7429ec4e923e37317f32a57c903de50b8c33ffcfbdb7e6f0dd39c"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.12"
  shared_preferences_ios:
    dependency: transitive
    description:
      name: shared_preferences_ios
      sha256: "585a14cefec7da8c9c2fb8cd283a3bb726b4155c0952afe6a0caaa7b2272de34"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      sha256: "580abfd40f415611503cae30adf626e6656dfb2f0cee8f465ece7b6defb40f2f"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  shared_preferences_macos:
    dependency: transitive
    description:
      name: shared_preferences_macos
      sha256: fbb94bf296576f49be37a1496d5951796211a8db0aa22cc0d68c46440dad808c
      url: "https://pub.dev"
    source: hosted
    version: "2.0.4"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      sha256: "57cbf196c486bc2cf1f02b85784932c6094376284b3ad5779d1b1c6c6a816b80"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      sha256: a4b5bc37fe1b368bbc81f953197d55e12f49d0296e7e412dfe2d2d77d6929958
      url: "https://pub.dev"
    source: hosted
    version: "2.0.4"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      sha256: "94ef0f72b2d71bc3e700e025db3710911bd51a71cefb65cc609dd0d9a982e3c1"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  shelf:
    dependency: transitive
    description:
      name: shelf
      sha256: "8ec607599dd0a78931a5114cdac7d609b6dbbf479a38acc9a6dba024b2a30ea0"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.2"
  shelf_web_socket:
    dependency: transitive
    description:
      name: shelf_web_socket
      sha256: "6db16374bc3497d21aa0eebe674d3db9fdf82082aac0f04dc7b44e4af5b08afc"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  shimmer:
    dependency: "direct main"
    description:
      name: shimmer
      sha256: "5f88c883a22e9f9f299e5ba0e4f7e6054857224976a5d9f839d4ebdc94a14ac9"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  showcaseview:
    dependency: "direct main"
    description:
      name: showcaseview
      sha256: f236c1f44b286e1ba888f8701adca067af92c33e29ea937d0fe9b4a29d4cd41e
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  simple_gesture_detector:
    dependency: transitive
    description:
      name: simple_gesture_detector
      sha256: "86d08f85f1f58583b7b4b941d989f48ea6ce08c1724a1d10954a277c2ec36592"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  sliver_tools:
    dependency: transitive
    description:
      name: sliver_tools
      sha256: eae28220badfb9d0559207badcbbc9ad5331aac829a88cb0964d330d2a4636a6
      url: "https://pub.dev"
    source: hosted
    version: "0.2.12"
  smooth_page_indicator:
    dependency: transitive
    description:
      name: smooth_page_indicator
      sha256: "49e9b6a265790454c39bd4a447a02f398c02b44b2602e7c5e3a381dc2e3b4102"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0+2"
  smooth_star_rating_null_safety:
    dependency: "direct main"
    description:
      name: smooth_star_rating_null_safety
      sha256: "8b3e56609da43577e5e72732d1dcad62fcabbd8131b228542f9ce2e9dc245dc1"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4+2"
  source_gen:
    dependency: transitive
    description:
      name: source_gen
      sha256: fc0da689e5302edb6177fdd964efcb7f58912f43c28c2047a808f5bfff643d16
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: "53e943d4206a5e30df338fd4c6e7a077e02254531b138a15aec3bd143c1a8b3c"
      url: "https://pub.dev"
    source: hosted
    version: "1.10.0"
  sprintf:
    dependency: transitive
    description:
      name: sprintf
      sha256: "1fc9ffe69d4df602376b52949af107d8f5703b77cda567c4d7d86a0693120f23"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  sqflite:
    dependency: transitive
    description:
      name: sqflite
      sha256: "51c09d414ca74b1cd4a5880d63763ebd2033a4fc6d921708c7c1e98c603735d4"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2+1"
  sqflite_common:
    dependency: transitive
    description:
      name: sqflite_common
      sha256: b504fc5b4576a05586a0bb99d9bcc0d37a78d9d5ed68b96c361d5d3a8e538275
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1+1"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: "9f47fd3630d76be3ab26f0ee06d213679aa425996925ff3feffdec504931c377"
      url: "https://pub.dev"
    source: hosted
    version: "1.12.0"
  step_progress_indicator:
    dependency: "direct main"
    description:
      name: step_progress_indicator
      sha256: b51bb1fcfc78454359f0658c5a2c21548c3825ebf76e826308e9ca10f383bbb8
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  steps_indicator:
    dependency: "direct main"
    description:
      name: steps_indicator
      sha256: a6e55fae97f29db3df2b8bed77d55ead127f16ffa024e0fe0234b5d71eddffa1
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: ba2aa5d8cc609d96bbb2899c28934f9e1af5cddbd60a827822ea467161eb54e7
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      sha256: ad47125e588cfd37a9a7f86c7d6356dde8dfe89d071d293f80ca9e9273a33871
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "688af5ed3402a4bde5b3a6c15fd768dbf2621a614950b17f04626c431ab3c4c3"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  substring_highlight:
    dependency: "direct main"
    description:
      name: substring_highlight
      sha256: "96c61e8316098831f6bee87d2386617e4be6aaf87fbc89402dc049d371b67efb"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.33"
  synchronized:
    dependency: transitive
    description:
      name: synchronized
      sha256: a7f0790927c0806ae0d5eb061c713748fa6070ef0037e391a2d53c3844c09dc2
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0+2"
  table_calendar:
    dependency: "direct main"
    description:
      name: table_calendar
      sha256: "4ca32b2fc919452c9974abd4c6ea611a63e33b9e4f0b8c38dba3ac1f4a6549d1"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: a29248a84fbb7c79282b40b8c72a1209db169a2e0542bce341da992fe1bc7e84
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: "664d3a9a64782fcdeb83ce9c6b39e78fd2971d4e37827b9b06c3aa1edc5e760c"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.3"
  tiengviet:
    dependency: transitive
    description:
      name: tiengviet
      sha256: "4b9c8371b4c2255ad7363820ec9873eab4cb4202516532cab4f76bd5826d94a8"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.0"
  timelines_plus:
    dependency: "direct main"
    description:
      name: timelines_plus
      sha256: be31f493402dc24df7fe410dc5f82a605807bb4ca13183de6d4362886449b593
      url: "https://pub.dev"
    source: hosted
    version: "1.0.6"
  timezone:
    dependency: transitive
    description:
      name: timezone
      sha256: "1cfd8ddc2d1cfd836bc93e67b9be88c3adaeca6f40a00ca999104c30693cdca0"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.2"
  timing:
    dependency: transitive
    description:
      name: timing
      sha256: c386d07d7f5efc613479a7c4d9d64b03710b03cfaa7e8ad5f2bfb295a1f0dfad
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  translations_cleaner:
    dependency: "direct main"
    description:
      name: translations_cleaner
      sha256: "060f4a8cd782e271509719741dd3540fe81ddaad49bd79e1d8fc4598299a6b84"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.5"
  tuple:
    dependency: "direct main"
    description:
      name: tuple
      sha256: fe3ae4f0dca3f9aac0888e2e0d117b642ce283a82d7017b54136290c0a3b0dd3
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: facc8d6582f16042dd49f2463ff1bd6e2c9ef9f3d5da3d9b087e244a7b564b3c
      url: "https://pub.dev"
    source: hosted
    version: "1.3.2"
  umee_api_collab:
    dependency: transitive
    description:
      path: "modules/open-api-umee/open_api/umee_api/collab"
      relative: true
    source: path
    version: "1.0.0"
  umee_api_credit:
    dependency: transitive
    description:
      path: "modules/open-api-umee/open_api/umee_api/credit"
      relative: true
    source: path
    version: "1.0.0"
  umee_api_game:
    dependency: transitive
    description:
      path: "modules/open-api-umee/open_api/umee_api/game"
      relative: true
    source: path
    version: "1.0.0"
  umee_api_pay:
    dependency: transitive
    description:
      path: "modules/open-api-umee/open_api/umee_api/pay"
      relative: true
    source: path
    version: "1.0.0"
  umee_shop:
    dependency: "direct main"
    description:
      path: "modules/myshop"
      relative: true
    source: path
    version: "1.0.0+1"
  uni_links:
    dependency: transitive
    description:
      name: uni_links
      sha256: "051098acfc9e26a9fde03b487bef5d3d228ca8f67693480c6f33fd4fbb8e2b6e"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.1"
  uni_links_platform_interface:
    dependency: transitive
    description:
      name: uni_links_platform_interface
      sha256: "929cf1a71b59e3b7c2d8a2605a9cf7e0b125b13bc858e55083d88c62722d4507"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  uni_links_web:
    dependency: transitive
    description:
      name: uni_links_web
      sha256: "7539db908e25f67de2438e33cc1020b30ab94e66720b5677ba6763b25f6394df"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.0"
  universal_html:
    dependency: transitive
    description:
      name: universal_html
      sha256: "56536254004e24d9d8cfdb7dbbf09b74cf8df96729f38a2f5c238163e3d58971"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.4"
  universal_io:
    dependency: transitive
    description:
      name: universal_io
      sha256: "1722b2dcc462b4b2f3ee7d188dad008b6eb4c40bbd03a3de451d82c78bba9aad"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.2"
  uri:
    dependency: transitive
    description:
      name: uri
      sha256: "889eea21e953187c6099802b7b4cf5219ba8f3518f604a1033064d45b1b8268a"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  url_launcher:
    dependency: "direct main"
    description:
      name: url_launcher
      sha256: "21b704ce5fa560ea9f3b525b43601c678728ba46725bab9b01187b4831377ed3"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.0"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      sha256: "17cd5e205ea615e2c6ea7a77323a11712dffa0720a8a90540db57a01347f9ad9"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.2"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      sha256: e43b677296fadce447e987a2f519dcf5f6d1e527dc35d01ffab4fff5b8a7063e
      url: "https://pub.dev"
    source: hosted
    version: "6.3.1"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: e2b9622b4007f97f504cd64c0128309dfb978ae66adbe944125ed9e1750f06af
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: "769549c999acdb42b8bcfa7c43d72bf79a382ca7441ab18a808e101149daf672"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: "552f8a1e663569be95a8190206a38187b531910283c3e982193e4f2733f01029"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: "772638d3b34c779ede05ba3d38af34657a05ac55b06279ea6edd409e323dca8e"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.3"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: "49c10f879746271804767cb45551ec5592cdab00ee105c06dddde1a98f73b185"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  uuid:
    dependency: "direct main"
    description:
      name: uuid
      sha256: a5be9ef6618a7ac1e964353ef476418026db906c4facdedaa299b7a2e71690ff
      url: "https://pub.dev"
    source: hosted
    version: "4.5.1"
  vector_graphics:
    dependency: transitive
    description:
      name: vector_graphics
      sha256: "670f6e07aca990b4a2bcdc08a784193c4ccdd1932620244c3a86bb72a0eac67f"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.7"
  vector_graphics_codec:
    dependency: transitive
    description:
      name: vector_graphics_codec
      sha256: "7451721781d967db9933b63f5733b1c4533022c0ba373a01bdd79d1a5457f69f"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.7"
  vector_graphics_compiler:
    dependency: transitive
    description:
      name: vector_graphics_compiler
      sha256: "80a13c613c8bde758b1464a1755a7b3a8f2b6cec61fbf0f5a53c94c30f03ba2e"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.7"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  version:
    dependency: "direct main"
    description:
      name: version
      sha256: "3d4140128e6ea10d83da32fef2fa4003fccbf6852217bb854845802f04191f94"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  vibration:
    dependency: "direct main"
    description:
      name: vibration
      sha256: "1dc9d1a0d62b12f1276eb7146b21585db5fe01771b8ca0234fefdf74ba51f963"
      url: "https://pub.dev"
    source: hosted
    version: "1.7.6"
  video_compress:
    dependency: transitive
    description:
      name: video_compress
      sha256: "5b42d89f3970c956bad7a86c29682b0892c11a4ddf95ae6e29897ee28788e377"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.3"
  video_ekyc_api:
    dependency: transitive
    description:
      path: "open_api/video_ekyc"
      relative: true
    source: path
    version: "1.0.0"
  video_player:
    dependency: "direct main"
    description:
      name: video_player
      sha256: "4a8c3492d734f7c39c2588a3206707a05ee80cef52e8c7f3b2078d430c84bc17"
      url: "https://pub.dev"
    source: hosted
    version: "2.9.2"
  video_player_android:
    dependency: transitive
    description:
      name: video_player_android
      sha256: e343701aa890b74a863fa460f5c0e628127ed06a975d7d9af6b697133fb25bdf
      url: "https://pub.dev"
    source: hosted
    version: "2.7.1"
  video_player_avfoundation:
    dependency: transitive
    description:
      name: video_player_avfoundation
      sha256: cd5ab8a8bc0eab65ab0cea40304097edc46da574c8c1ecdee96f28cd8ef3792f
      url: "https://pub.dev"
    source: hosted
    version: "2.6.2"
  video_player_platform_interface:
    dependency: transitive
    description:
      name: video_player_platform_interface
      sha256: "229d7642ccd9f3dc4aba169609dd6b5f3f443bb4cc15b82f7785fcada5af9bbb"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.3"
  video_player_web:
    dependency: transitive
    description:
      name: video_player_web
      sha256: "881b375a934d8ebf868c7fb1423b2bfaa393a0a265fa3f733079a86536064a10"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.3"
  visibility_detector:
    dependency: transitive
    description:
      name: visibility_detector
      sha256: dd5cc11e13494f432d15939c3aa8ae76844c42b723398643ce9addb88a5ed420
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0+2"
  vm_service:
    dependency: transitive
    description:
      name: vm_service
      sha256: f6be3ed8bd01289b34d679c2b62226f63c0e69f9fd2e50a6b3c1c729a961041b
      url: "https://pub.dev"
    source: hosted
    version: "14.3.0"
  wakelock_plus:
    dependency: transitive
    description:
      name: wakelock_plus
      sha256: bf4ee6f17a2fa373ed3753ad0e602b7603f8c75af006d5b9bdade263928c0484
      url: "https://pub.dev"
    source: hosted
    version: "1.2.8"
  wakelock_plus_platform_interface:
    dependency: transitive
    description:
      name: wakelock_plus_platform_interface
      sha256: "422d1cdbb448079a8a62a5a770b69baa489f8f7ca21aef47800c726d404f9d16"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  watcher:
    dependency: transitive
    description:
      name: watcher
      sha256: "3d2ad6751b3c16cf07c7fca317a1413b3f26530319181b37e3b9039b84fc01d8"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  web:
    dependency: transitive
    description:
      name: web
      sha256: "97da13628db363c635202ad97068d47c5b8aa555808e7a9411963c533b449b27"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.1"
  web_socket_channel:
    dependency: transitive
    description:
      name: web_socket_channel
      sha256: "3a969ddcc204a3e34e863d204b29c0752716f78b6f9cc8235083208d268a4ccd"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  webview_flutter:
    dependency: "direct main"
    description:
      name: webview_flutter
      sha256: "889a0a678e7c793c308c68739996227c9661590605e70b1f6cf6b9a6634f7aec"
      url: "https://pub.dev"
    source: hosted
    version: "4.10.0"
  webview_flutter_android:
    dependency: transitive
    description:
      name: webview_flutter_android
      sha256: "3d535126f7244871542b2f0b0fcf94629c9a14883250461f9abe1a6644c1c379"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.0"
  webview_flutter_platform_interface:
    dependency: transitive
    description:
      name: webview_flutter_platform_interface
      sha256: d937581d6e558908d7ae3dc1989c4f87b786891ab47bb9df7de548a151779d8d
      url: "https://pub.dev"
    source: hosted
    version: "2.10.0"
  webview_flutter_wkwebview:
    dependency: "direct main"
    description:
      name: webview_flutter_wkwebview
      sha256: c49a98510080378b1525132f407a92c3dcd3b7145bef04fb8137724aadcf1cf0
      url: "https://pub.dev"
    source: hosted
    version: "3.18.4"
  win32:
    dependency: "direct overridden"
    description:
      name: win32
      sha256: "84ba388638ed7a8cb3445a320c8273136ab2631cd5f2c57888335504ddab1bc2"
      url: "https://pub.dev"
    source: hosted
    version: "5.8.0"
  win32_registry:
    dependency: transitive
    description:
      name: win32_registry
      sha256: "723b7f851e5724c55409bb3d5a32b203b3afe8587eaf5dafb93a5fed8ecda0d6"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.4"
  x509:
    dependency: transitive
    description:
      name: x509
      sha256: d4dab1f888e9abe92d718d172c7500c165dc9104ad0a0da2b878b99f55a664b1
      url: "https://pub.dev"
    source: hosted
    version: "0.2.2"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: "7a3f37b05d989967cdddcbb571f1ea834867ae2faa29725fd085180e0883aa15"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: "5bc72e1e45e941d825fd7468b9b4cc3b9327942649aeb6fc5cdbf135f0a86e84"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.0"
  yaml:
    dependency: transitive
    description:
      name: yaml
      sha256: "75769501ea3489fca56601ff33454fe45507ea3bfb014161abc3b43ae25989d5"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  yaml_edit:
    dependency: transitive
    description:
      name: yaml_edit
      sha256: fb38626579fb345ad00e674e2af3a5c9b0cc4b9bfb8fd7f7ff322c7c9e62aef5
      url: "https://pub.dev"
    source: hosted
    version: "2.2.2"
sdks:
  dart: ">=3.6.0 <4.0.0"
  flutter: ">=3.27.0"
