plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
    id "com.google.firebase.crashlytics"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

def firebaseCoreProject = findProject(':firebase_core')
if (firebaseCoreProject == null) {
    throw new GradleException('Could not find the firebase_core FlutterFire plugin, have you added it as a dependency in your pubspec?')
} else if (!firebaseCoreProject.properties['FirebaseSDKVersion']) {
    throw new GradleException('A newer version of the firebase_core FlutterFire plugin is required, please update your firebase_core pubspec dependency.')
}

def getRootProjectExtOrCoreProperty(name, firebaseCoreProject) {
    if (!rootProject.ext.has('FlutterFire')) return firebaseCoreProject.properties[name]
    if (!rootProject.ext.get('FlutterFire')[name]) return firebaseCoreProject.properties[name]
    return rootProject.ext.get('FlutterFire').get(name)
}

android {
    namespace "com.sunshine.mobile_banking"

    compileSdkVersion 35
    ndkVersion '27.0.********'

    packagingOptions {
        pickFirst '**/*.so'
    }

    externalNativeBuild {
        cmake {
            path "../CMakeLists.txt"
        }
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    lintOptions {
        disable 'InvalidPackage'
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.kienlongbank.mobile_banking"
        minSdkVersion 23
        targetSdkVersion 35
        multiDexEnabled true
        versionCode project.hasProperty('versionCode') ? project.property('versionCode') as int : flutterVersionCode.toInteger()
        versionName flutterVersionName
    }

    signingConfigs {
        release {
            keyAlias 'super-app'
            keyPassword 'Sunshine@123'
            storeFile file('../sunshine_keystore.jks')
            storePassword 'Sunshine@2017'
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            // Add below 3 lines for proguard
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug {
            signingConfig signingConfigs.debug

            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation 'androidx.multidex:multidex:2.0.1'
    implementation 'com.google.mlkit:face-detection:16.1.5'
    implementation platform('com.google.firebase:firebase-bom:28.3.0')
    implementation "com.google.firebase:firebase-messaging-ktx"
    implementation "com.google.firebase:firebase-crashlytics"
}
