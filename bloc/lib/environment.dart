import 'dart:convert';

import 'package:ksb_bloc/utils/log.dart';
import 'package:flutter/foundation.dart';
import 'package:rxdart/rxdart.dart';

class Environment {
  static const _development = 'develop';
  static const _staging = 'staging';
  static const _cloudStaging = 'cloud_staging';
  static const _prod = 'production';
  static const _newcore = 'new_core';
  static const _newcore2 = 'new_core_2';
  static final _liveNess = 'live_ness';

  String? _name;
  String? _endPoint;
  String? _proxy = "";
  bool? _isDevelopment;
  String? _liveNessToken = '';
  String? _apiAI = '';

  String get name => _name ?? '';

  bool get isDevelopment => _isDevelopment == true || kDebugMode;

  String get endPoint => _endPoint ?? '';

  String get proxy => _proxy ?? '';

  set proxyValue(String value) => _proxy = value;

  String? get liveNessToken => _liveNessToken;

  String? get apiAi => _apiAI;

  static List<String> servers = [
    // serverUrlStaging,
    serverUrlDev,
    // serverCloudStaging,
    serverCloud,
    serverNewCore,
    serverNewCore2,
    serverLiveNess,
  ];

  static List<String> serverNames = [
    _development,
    _prod,
    _newcore,
    _newcore2,
    _liveNess,
  ];

  static Map<String, Environment> get serverNameToEnv => {
        _staging: Environment.staging(),
        _development: Environment.dev(),
        _cloudStaging: Environment.cloudStaging(),
        _prod: Environment.prod(),
        _liveNess: Environment.liveNess(),
        _newcore: Environment.newcore(),
        _newcore2: Environment.newcore2(),
      };

  static Map<String, String> get mapServerName => {
        _staging: 'Staging Server',
        _development: 'Dev Server',
        _cloudStaging: 'Cloud Staging Server',
        _prod: 'Cloud Server',
        _liveNess: 'LiveNess',
        _newcore: 'New Core',
        _newcore2: 'New Core 2',
      };

  static List<String> serversQr = [
    ...servers,
    'ksbank://ssf.vn',
    'ksbank://ksbank.co',
    'https://qr.ksbank.co/',
    'https://lixi.kienlongbank.com',
    'ksbank://lixi.kienlongbank.com'
  ];
  static String SALF_KEY = "1FE6B8CF494A4BDCADEF88964C0F2DB9";

  static const String serverUrlDev = 'https://dev-ksapi.ssf.vn';

  static const String serverUrlStaging = 'https://staging-ksapi.ssf.vn';

  static const String serverCloudStaging =
      'https://api-staging.kienlongbank.co';

  static const String serverCloud = 'https://api.kienlongbank.co';
  static const String serverNewCore2 = 'https://api-flex.kienlongbank.co';
  static const String serverNewCore =
      'https://api-flex-staging.kienlongbank.co';
  static const String serverLiveNess =
      'https://api-flex-staging.kienlongbank.co';

  //APP DEV
  //"d5c786a5-3836-48ea-9e47-4ee9da006387"
  //APP UAT
  //'1431a6a1-6095-4cbf-bd4c-0fe7d543e237'
  //APP LIVE
  //'61b92559-bf34-4eb9-8616-5249ec707603'
  String get appId {
    return "d5c786a5-3836-48ea-9e47-4ee9da006387";
  }

  String get chatSocketServer {
    if (name == _development) {
      return 'wss://dev-messaging.ssf.vn/websocket';
    } else if (name == _staging) {
      return 'wss://staging-messaging.ssf.vn/websocket';
    } else if (name == _newcore) {
      return 'wss://chat-staging.kienlongbank.co/websocket';
    } else if (name == _cloudStaging) {
      return 'wss://chat-staging.kienlongbank.co/websocket';
    } else {
      return 'wss://chat.kienlongbank.co/websocket';
    }
  }

  String get shareWebLuckyMoney {
    if (name == _development) {
      return 'https://lixi-staging.kienlongbank.co';
    } else if (name == _staging) {
      return 'https://lixi-staging.kienlongbank.co';
    } else if (name == _newcore) {
      return 'https://lixi-staging.kienlongbank.co';
    } else if (name == _cloudStaging) {
      return 'https://lixi-staging.kienlongbank.co';
    } else {
      return 'https://lixi.kienlongbank.com';
    }
  }

  Uri get chatServerUrl {
    if (name == _development) {
      return Uri.parse("https://dev-messaging.ssf.vn");
    } else if (name == _staging) {
      return Uri.parse("https://staging-messaging.ssf.vn");
    } else if (name == _newcore) {
      return Uri.parse("https://chat-staging.kienlongbank.co");
    } else if (name == _cloudStaging) {
      return Uri.parse("https://chat-staging.kienlongbank.co");
    } else {
      return Uri.parse("https://chat.kienlongbank.co");
    }
  }

  String get strApiUrl {
    if (name == _development) {
      return "https://crm.dev-ksapi.ssf.vn";
    } else if (name == _staging) {
      return "https://dev-smartbank-fcc-strapi.ssf.vn";
    } else if (name == _cloudStaging) {
      return "https://cms-staging.kienlongbank.co";
    } else if (name == _newcore) {
      return "https://cms-flex-staging.kienlongbank.co";
    } else if (name == _newcore2) {
      return "https://cms-flex.kienlongbank.co";
    } else {
      return "https://cms.kienlongbank.co";
    }
  }

  String get myShopPayboxConfigUrl {
    if (name == _development) {
      return 'https://paybox-staging.unicloud.com.vn/login?phone=';
    } else if (name == _staging) {
      return 'https://paybox-staging.unicloud.com.vn/login?phone=';
    } else if (name == _newcore) {
      return 'https://paybox-staging.unicloud.com.vn/login?phone=';
    } else if (name == _cloudStaging) {
      return 'https://paybox-staging.unicloud.com.vn/login?phone=';
    } else {
      return 'https://paybox.unicloud.com.vn/login?phone='; // config production later ..
    }
  }

  updateEnv(Environment env) {
    if (env.endPoint.isNotEmpty == true) {
      _name = env.name;
      _endPoint = env.endPoint;
      _proxy = env.proxy;
      _isDevelopment = env.isDevelopment;
      setShowLog(isDevelopment);
    }
  }

  Environment.dev() {
    _name = _development;
    _endPoint = serverUrlDev;
    _proxy = "";
    _isDevelopment = true;
  }

  Environment.staging() {
    _name = _staging;
    _endPoint = serverUrlStaging;
    _proxy = "";
    _isDevelopment = true;
  }

  Environment.newcore() {
    _name = _newcore;
    _endPoint = serverNewCore;
    _proxy = "";
    _isDevelopment = true;
  }

  Environment.newcore2() {
    _name = _newcore2;
    _endPoint = serverNewCore2;
    _proxy = "";
    _isDevelopment = false;
  }

  Environment.cloudStaging() {
    _name = _cloudStaging;
    _endPoint = serverCloudStaging;
    _proxy = "";
    _isDevelopment = false;
  }

  Environment.prod() {
    _name = _prod;
    _endPoint = serverCloud;
    _proxy = "";
    _isDevelopment = false;
  }

  Environment.liveNess() {
    _name = _liveNess;
    _endPoint = serverLiveNess;
    _proxy = "";
    _liveNessToken = 'c3N0LWRldjphbGxvdw==';
    _apiAI = 'https://api.unicloud.ai/ekyc';
    _isDevelopment = true;
  }

  Environment.fromJson(String json) {
    final map = jsonDecode(json);
    _name = map['name'];
    _endPoint = map['endpoint'];
    _proxy = map['proxy'];
    _isDevelopment = map['is_development'];
    _liveNessToken = map['live_ness_token'];
    _apiAI = map['api_ai'];
  }

  Environment.fromMap(Map<String, dynamic> map) : _name = map["name"];

  Map<String, dynamic> toMap() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = _name;
    data['endpoint'] = _endPoint;
    data['proxy'] = _proxy;
    data['is_development'] = _isDevelopment;
    data['live_ness_token'] = _liveNessToken;
    data['api_ai'] = _apiAI;
    return data;
  }

  String toJson() {
    return jsonEncode(toMap());
  }

  final _changeLanguage = BehaviorSubject<String>();
  final _changeThemeLight = BehaviorSubject<bool>();
  final _appEvent = PublishSubject<String>();
  final _changeThemes = BehaviorSubject<int>();
  final _changePartner = BehaviorSubject<bool>();
  final _useSystemFontSize = BehaviorSubject<bool>();
  final _appEventData = BehaviorSubject<AppEventData>();

  Sink<String> get changeLanguageSink => _changeLanguage.sink;

  Stream<String> get changeLanguage => _changeLanguage.stream;

  String get language => _changeLanguage.valueOrNull ?? "vi";

  Sink<bool> get changeThemeLightSink => _changeThemeLight.sink;

  Stream<bool> get changeThemeLight => _changeThemeLight.stream;

  Stream<String> get appEventStream => _appEvent.stream;

  Sink<String> get appEventSink => _appEvent.sink;

  Stream<int> get changeThemes => _changeThemes.stream;

  Sink<int> get changeThemesSink => _changeThemes.sink;

  Stream<bool> get changePartnerStream => _changePartner.stream;

  Sink<bool> get changePartnerSink => _changePartner.sink;

  bool get isThemeLight => _changeThemeLight.valueOrNull ?? true;

  int get themes => _changeThemes.valueOrNull ?? 0;

  bool get partner => _changePartner.valueOrNull ?? true;

  Stream<bool> get useSystemFontSizeStream => _useSystemFontSize.stream;

  Sink<bool> get useSystemFontSizeSink => _useSystemFontSize.sink;

  bool get useSystemFontSize => _useSystemFontSize.valueOrNull ?? false;

  Sink<AppEventData> get appEventDataSink => _appEventData.sink;

  Stream<AppEventData> get appEventDataStream => _appEventData.stream;

  dispose() {
    _changeLanguage.close();
    _changeThemeLight.close();
    _appEvent.close();
    _changeThemes.close();
    _changePartner.close();
    _useSystemFontSize.close();
    _appEventData.close();
  }
}

class AppEvent {
  AppEvent._();

  static const String SCHEDULE_TEMPLATE_CHANGE = 'SCHEDULE_TEMPLATE_CHANGE';
  static const String MONEY_CHANGE = 'MONEY_CHANGE';
  static const String BILL_PAID = 'BILL_PAID';
  static const String SAVING_ACCOUNT_CHANGE = 'SAVING_ACCOUNT_CHANGE';
  static const String SAVING_VNP_ACCOUNT_CHANGE = 'SAVING_VNP_ACCOUNT_CHANGE';
  static const String BILL_SCHEDULE_CHANGE = 'BILL_SCHEDULE_CHANGE';
  static const String TRANSACTION_CHANGE = 'TRANSACTION_CHANGE';
  static const String BIG_TRANSACTION = 'BIG_TRANSACTION';
  static const String REFRESH_NICKNAME = 'REFRESH_NICKNAME';
  static const String NEW_NOTIFICATION = 'NEW_NOTIFICATION';
  static const String UPDATE_CHIP_ID = 'UPDATE_CHIP_ID';
  static const String ADD_NEW_VR_CARD = 'ADD_NEW_VR_CARD';
  static const String MOBILE_SERVICE = 'MOBILE_SERVICE';
  static const String REVIEW_TRANSACTION = 'REVIEW_TRANSACTION';
}

class AppEventData<T> {
  String? appEvent;
  T? data;

  AppEventData({this.appEvent, this.data});
}