import 'dart:async';

import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:ksb_bloc/utils/log.dart';
import 'package:rxdart/rxdart.dart';
import 'package:uni_links/uni_links.dart';

class DeepLink {
  BehaviorSubject<Uri?> _currentLink = BehaviorSubject<Uri?>();
  StreamSubscription? _subscription;

  Stream<Uri?> get stream => _currentLink.stream;
  static DeepLink? _instance;

  static DeepLink get instance {
    if (_instance == null) {
      _instance = DeepLink._();
    }
    return _instance!;
  }

  DeepLink._();

  Future<void> init() async {
    try {
      Uri? initialUri = await getInitialUri();
      _currentLink.add(initialUri);

      _subscription?.cancel();
      _subscription = uriLinkStream.listen((Uri? uri) {
        logger.t(uri);
        _currentLink.add(uri);
      });
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(
        e,
        StackTrace.current,
      );
    }
  }

  void dispose() {
    _subscription?.cancel();
    _currentLink.close();
  }

  Uri? takeValue() {
    final currentValue = _currentLink.valueOrNull;
    _currentLink.add(null);
    return currentValue;
  }

  clear() {
    _currentLink.add(null);
  }
}
