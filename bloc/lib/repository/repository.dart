import 'package:common/repository/strapi/index.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:open_api_umee/open_api_collab.dart';
import 'package:open_api_umee/open_api_credit.dart';
import 'package:open_api_umee/open_api_pay.dart';
import 'package:video_ekyc_api/video_ekyc_api.dart';

abstract class Repository {
  KsbankApiProfile? get profileApi;
  KsbankApiMedia? get mediaApi;
  KsbankApiSmartbank? get bankApi;
  KsbankApiNotification? get notificationApi;
  KsbankApiStocks? get stocksApi;
  KsbankApiStm? get stmApi;
  StrApi? get strApi;
  CRMApi? get crmApi;
  KsbankApiLoyalty? get loyaltyApi;
  KsbankApiMaintenance? get maintenanceApi;
  NetworkApi? get networkApi;
  UmeeApiCollab? get umeeApiCollab;
  UmeeApiCredit? get umeeApiCredit;
  KsbankApiNickname? get nicknameApi;
  VideoEkycApi? get videoEKycApi;
  SavingVnpApi? get savingVnpApi;
  // Klb Pay - use open api umee
  UmeeApiPay? klbPayApi;
  LuckyMoneyApi? luckyMoneyApi;
}
