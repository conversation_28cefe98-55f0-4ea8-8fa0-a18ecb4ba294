import 'package:flutter/widgets.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:rxdart/rxdart.dart';

class SmsBankingBloc extends BaseBloc {
  SmsBankingBloc(
    Repository repository,
    Preferences preferences,
    Session session,
  ) : super(repository, preferences, session);

  final _detail = BehaviorSubject<GetSmsBankingDetailResponse?>();
  Stream<GetSmsBankingDetailResponse?> get detailStream => _detail.stream;

  final _fees = BehaviorSubject<GetSmsBankingFeeResponse?>();
  ValueStream<GetSmsBankingFeeResponse?> get feesStream => _fees.stream;

  final phoneNumberController1 = TextEditingController();
  final phoneNumberController2 = TextEditingController();

  final useCustom = BehaviorSubject<bool>.seeded(false);
  final _validRegister = BehaviorSubject<bool>.seeded(false);
  final _validAdd = BehaviorSubject<bool>.seeded(false);
  Stream<bool> get validRegisterStream => _validRegister.stream;
  Stream<bool> get validAddStream => _validAdd.stream;
  final acceptTerm = BehaviorSubject<bool>.seeded(false);

  @override
  void init() {
    super.init();
    preferences.getProfileInfo().then((value) {
      if (value?.phoneNumber?.isNotEmpty == true) {
        phoneNumberController1.text = value!.phoneNumber!;
      }
    });
    streamSubs.addAll([
      useCustom.listen((value) => validateRegsiter()),
      acceptTerm.listen((value) => validateRegsiter()),
    ]);
    phoneNumberController2.addListener(validateRegsiter);
    phoneNumberController2.addListener(validateAdd);
  }

  @override
  void dispose() {
    _detail.close();
    _fees.close();
    _validRegister.close();
    _validAdd.close();
    useCustom.close();
    acceptTerm.close();
    phoneNumberController1.dispose();
    phoneNumberController2.dispose();
    super.dispose();
  }

  Preferences get pref => preferences;

  void getSmsBankingDetail() {
    _detail.add(null);
    showLoading();
    repository.bankApi
        ?.getSMSBankingManagementApi()
        .getSmsBankingDetail()
        .then((res) {
      _detail.add(res.data?.data);
      if (_detail.valueOrNull?.smsPhone?.isNotEmpty == true) {
        phoneNumberController1.text =
            _detail.valueOrNull?.smsPhone?.asMap()[0]?.phone ?? '';
        phoneNumberController2.text =
            _detail.valueOrNull?.smsPhone?.asMap()[1]?.phone ?? '';
      }
    }).catchError((error) {
      handlerApiError(error);
    }).whenComplete(() {
      completeLoading();
    });
  }

  Future<void> getFees() async {
    showLoading();
    return repository.bankApi
        ?.getSMSBankingManagementApi()
        .getFees()
        .then((res) {
      _fees.add(res.data?.data);
    }).catchError((error) {
      handlerApiError(error);
    }).whenComplete(() {
      completeLoading();
    });
  }

  void validateRegsiter() {
    _validRegister.add(
      acceptTerm.valueOrNull == true &&
          (useCustom.valueOrNull == false ||
              (useCustom.valueOrNull == true &&
                  validatePhone(phoneNumberController2.text))),
    );
  }

  void validateAdd() {
    _validAdd.add(
      phoneNumberController2.text != phoneNumberController1.text &&
          validatePhone(phoneNumberController2.text),
    );
  }

  bool validatePhone(String phone) {
    RegExp regExp = new RegExp(r'(^(?:[+0]9)?[0-9]{10,12}$)');
    return (phone.length == 10 && regExp.hasMatch(phone));
  }

  Future<PreviewUpdateSmsBankingResponse?> previewUpdate({
    required PreviewUpdateSmsBankingRequestActionEnum action,
    String? phoneNumber,
    String? indexPhoneRemove,
    List<String>? accounts,
  }) async {
    final request = PreviewUpdateSmsBankingRequest((builder) {
      builder
        ..action = action
        ..phoneNumber = phoneNumber
        ..indexPhoneRemove = indexPhoneRemove
        ..regCasaAccounts = removeCustomAccount(accounts)?.join(',')
        ..regLoanAccounts =
            accounts?.contains(ListAccountsCustomType.loan) == true ? 1 : 0
        ..regTdAccounts =
            accounts?.contains(ListAccountsCustomType.saving) == true ? 1 : 0;
    });
    showLoading();
    return await repository.bankApi
        ?.getSMSBankingManagementApi()
        .previewUpdateSmsBanking(previewUpdateSmsBankingRequest: request)
        .then((res) {
      return res.data?.data;
    }).catchError((error) {
      handlerApiError(error);
      return null;
    }).whenComplete(() {
      completeLoading();
    });
  }

  Future<bool> update(UpdateSmsBankingRequest request) async {
    showLoading();
    final result = await repository.bankApi
        ?.getSMSBankingManagementApi()
        .updateSmsBanking(updateSmsBankingRequest: request)
        .then((res) {
      return res.data?.success == true;
    }).catchError((error) {
      handlerApiError(error);
      return false;
    }).whenComplete(() {
      completeLoading();
    });
    return result ?? false;
  }

  List<String>? removeCustomAccount(List<String>? accounts) {
    final result = accounts?.toList();
    final removes = [
      ListAccountsCustomType.loan,
      ListAccountsCustomType.saving
    ];
    result?.removeWhere((e) => removes.contains(e));
    return result;
  }

  List<String>? addCustomAccount(GetSmsBankingDetailResponse? detail) {
    final detailPhone = detail?.smsPhone?.asMap()[0];
    final result = detailPhone?.accounts?.toList();
    if (detailPhone?.regLoanAccounts == 1) {
      result?.add(ListAccountsCustomType.loan);
    }
    if (detailPhone?.regTdAccounts == 1) {
      result?.add(ListAccountsCustomType.saving);
    }
    return result;
  }
}
