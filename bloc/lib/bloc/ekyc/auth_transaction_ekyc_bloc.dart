import 'dart:typed_data';

import 'package:common/model/view_data_model.dart';
import 'package:dio/dio.dart';
import 'package:ekyc_bloc/model/liveness_model.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:ekyc_bloc/bloc/live_ness_bloc_v2.dart';
import 'package:ksb_bloc/bloc/model/ekyc/profile_ekyc_model.dart';
import 'package:ksb_common/model/constant_type.dart';
import 'package:rxdart/rxdart.dart';
import 'package:ekyc_bloc/model/profile_liveness_model.dart';

class AuthTransactionEKycBloc extends BaseBloc with LiveNessBlocIntV2 {
  AuthTransactionEKycBloc(
      Repository repository, Preferences preferences, Session session)
      : super(repository, preferences, session);

  final _liveNess = BehaviorSubject<ViewDataModel<ProfileLivenessModel>>();

  BehaviorSubject<ViewDataModel<ProfileLivenessModel>> get liveNess =>
      _liveNess;

  @override
  Stream<ViewDataModel<ProfileLivenessModel>> get liveNessStream =>
      _liveNess.stream;

  @override
  ProfileLivenessModel? get liveNessValue => _liveNess.valueOrNull?.data;

  String? _transactionNo;

  void setTransactionNo(String? transactionNo) {
    _transactionNo = transactionNo;
  }

  @override
  void dispose() {
    _liveNess.close();
    super.dispose();
  }

  void refresh() {
    addError('');
  }

  @override
  bool isToBase64Frame() {
    return false;
  }

  @override
  Stream<String?> get liveNessErrorStream => super.errorStream;

  @override
  start({
    int? width,
    int? height,
    String? personId,
    String? referralCode,
    String? exercise,
    bool? static,
  }) async {
    safeAddData(_liveNess, ViewDataModel<ProfileLivenessModel>.loading());
    final token = await session.getToken();
    return repository.profileApi!
        .getUserCommandResourceApi()
        .startProfileLiveNessTransaction2345(
          startProfileLivenessTransaction2345Request:
              StartProfileLivenessTransaction2345Request((b) {
            b.userIdentity = personId;
            b.imageHeight = height.toString();
            b.imageWidth = width.toString();
          }),
          headers: getHttpHeader(token: token.accessTokenRequest),
        )
        .then((data) {
      logger.t(data.data?.data);
      if (data.data?.data != null) {
        final model = ProfileEKycModel.fromResponse2345(data.data!.data!);
        safeAddData(
          _liveNess,
          ViewDataModel<ProfileLivenessModel>.success(model),
        );
      }
    }).catchError((error) {
      handlerApiError(error);
      safeAddData(
        _liveNess,
        ViewDataModel<ProfileLivenessModel>.error(errorValue ?? ''),
      );
    }).whenComplete(completeLoading);
  }

  @override
  Future<bool> frames({
    String? content,
    List<int>? imageBytes,
    String? personId,
    String? task,
    int? timeStamp,
  }) {
    return repository.mediaApi!
        .getMinioControllerApi()
        .upload(
          fileType: FileType.ID_CARD,
          bucketName: personId,
          file: imageBytes != null
              ? MultipartFile.fromBytes(imageBytes,
                  filename: '${DateTime.now().millisecondsSinceEpoch}.jpg')
              : null,
        )
        .then((response) {
      final String faceUrl = toFileUrl(
        session.environment.endPoint,
        response.data?.previewUrl,
      );
      liveNessValue?.faceUrl = faceUrl;
      return true;
    }).catchError((error) {
      handlerApiError(error);
      FirebaseCrashlytics.instance.recordError(error, StackTrace.current);
      return false;
    }).whenComplete(completeLoading);
  }

  @override
  Future<LivenessVerifyResponse?> verify({String? personId}) async {
    final tokenValue = await session.getToken();
    return repository.profileApi!
        .getUserCommandResourceApi()
        .compareCustomerFace2345(
          compareCustomerFace2345Request:
              CompareCustomerFace2345Request((builder) {
            builder.newFaceImageUrl = liveNessValue?.faceUrl;
            builder.transactionNo = _transactionNo;
          }),
          headers: getHttpHeader(token: tokenValue.accessTokenRequest),
        )
        .then((value) {
      return Future<LivenessVerifyResponse?>.value(LivenessVerifyResponse(
        success: toBool(value.data?.success),
        message: '',
      ));
    }).catchError((error) {
      handlerApiError(error);
      return null;
    }).whenComplete(completeLoading);
  }

  @override
  Future<bool> sendVideoEKyc({
    required List<int> videoBytes,
    required String personId,
  }) async {
    return true;
  }
}
