import 'dart:async';
import 'dart:math';
import 'dart:typed_data';

import 'package:common/model/loading_event.dart';
import 'package:common/model/strapi/strapi_models.dart';
import 'package:common/model/view_data_model.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_bloc/bloc/model/referral/referral_model.dart';
import 'package:ksb_bloc/bloc/model/saving/saving_vnp/partner.dart';
import 'package:ksb_bloc/bloc/model/saving/saving_vnp/review_open.dart';
import 'package:ksb_bloc/bloc/model/saving/saving_vnp/saving_detail.dart';
import 'package:ksb_bloc/bloc/model/saving/saving_vnp/saving_open.dart';
import 'package:ksb_bloc/bloc/model/saving/saving_vnp/saving_update_delete.dart';
import 'package:ksb_bloc/bloc/model/saving/saving_vnp/saving_vnp.dart';
import 'package:rxdart/rxdart.dart';

import '../base_authenticate_bloc.dart';

class SavingBloc extends BaseBloc with BaseAuthBloc {
  SavingBloc(Repository repository, Preferences preferences, Session session)
      : super(repository, preferences, session);

  final _accountController = BehaviorSubject<AccountModel>();

  Stream<AccountModel> get accountStream => _accountController.stream;

  final _savingTypeController = BehaviorSubject<LoadingWidgetModel>();

  Stream<LoadingWidgetModel> get savingTypeStream =>
      _savingTypeController.stream;

  final _savingPeriodController = BehaviorSubject<List<SavingPeriod>>();

  Stream<List<SavingPeriod>> get savingPeriodStream =>
      _savingPeriodController.stream;

  final _savingInterestReceiveController =
      BehaviorSubject<LoadingWidgetModel>();

  Stream<LoadingWidgetModel> get savingInterestReceiveStream =>
      _savingInterestReceiveController.stream;

  final _savingDetailController = BehaviorSubject<LoadingWidgetModel>();

  Stream<LoadingWidgetModel> get savingDetailStream =>
      _savingDetailController.stream;

  final _savingVNPController = BehaviorSubject<LoadingWidgetModel>();

  Stream<LoadingWidgetModel> get savingVNPStream => _savingVNPController.stream;

  final _deleteSavingVNPController = BehaviorSubject<LoadingWidgetModel>();

  Stream<LoadingWidgetModel> get deleteSavingVNPController =>
      _deleteSavingVNPController.stream;

  final _partnerController = BehaviorSubject<List<DataPartner>>();

  Stream<List<DataPartner>> get partnerController => _partnerController.stream;

  final _updateSavingVNPController = BehaviorSubject<LoadingWidgetModel>();

  Stream<LoadingWidgetModel> get updateSavingVNPController =>
      _updateSavingVNPController.stream;

  final _openSavingVNPController = BehaviorSubject<LoadingWidgetModel>();

  Stream<LoadingWidgetModel> get openSavingVNPController =>
      _openSavingVNPController.stream;

  final _detailSavingVNPController = BehaviorSubject<LoadingWidgetModel>();

  Stream<LoadingWidgetModel> get detailSavingVNPController =>
      _detailSavingVNPController.stream;

  final _savingInterestMoneyController =
      BehaviorSubject<SavingInterestMoney?>();

  Stream<SavingInterestMoney?> get savingInterestMoneyStream =>
      _savingInterestMoneyController.stream;

  final _savingAccountsController = BehaviorSubject<LoadingWidgetModel>();

  Stream<LoadingWidgetModel> get savingAccountStream =>
      _savingAccountsController.stream;

  final _savingCertificateController = BehaviorSubject<LoadingWidgetModel>();

  Stream<LoadingWidgetModel> get savingAccountCertificateStream =>
      _savingCertificateController.stream;

  final _accountNoController = BehaviorSubject<String>();

  Stream<String> get accountNoStream => _accountNoController.stream;

  final _terms = BehaviorSubject<StrapiTerms>();

  Stream<StrapiTerms> get termsStream => _terms.stream;

  String? cifNumber;
  final _pdfPath = BehaviorSubject<String?>();

  Stream<String?> get pdfPathStream => _pdfPath.stream;

  Sink<String?> get pdfPathSink => _pdfPath.sink;

  final _accountInfo = BehaviorSubject<AccountModel>();

  Stream<AccountModel> get accountInfoStream => _accountInfo.stream;

  @override
  void dispose() {
    _savingInterestReceiveController.close();
    _savingInterestMoneyController.close();
    _savingCertificateController.close();
    _deleteSavingVNPController.close();
    _updateSavingVNPController.close();
    _detailSavingVNPController.close();
    _savingAccountsController.close();
    _openSavingVNPController.close();
    _savingPeriodController.close();
    _savingDetailController.close();
    _savingTypeController.close();
    _savingVNPController.close();
    _accountNoController.close();
    _accountController.close();
    _partnerController.close();
    _accountInfo.close();
    _pdfPath.close();
    _terms.close();
    super.dispose();
  }

  refreshView() {
    _pdfPath.safeAdd(_pdfPath.valueOrNull);
  }

  updateAccountNo(String data) {
    safeAddData(_accountNoController, data);
  }

  updateAccount(AccountModel data) {
    safeAddData(_accountController, data);
  }

  resetInterestAmount() {
    safeAddData(
        _savingInterestMoneyController,
        SavingInterestMoney(
          interestMoney: 0,
          dueDate: null,
        ));
  }

  getSavingVNP() async {
    cifNumber = await preferences.cifNumber;
    try {
      final result =
          await repository.savingVnpApi?.getSavingAPPKApi().getSavingCodes(
        vnpGetSavingCodesRequest: VnpGetSavingCodesRequest(
          (b) {
            b.bankcif = cifNumber;
          },
        ),
      );
      successResponse(result);
      final data = result?.data?.data;
      if (data != null) {
        final dataFromJson = SavingVNPModel.fomResponse(data);
        LoadingResult.success(
          behavior: _savingVNPController,
          data: dataFromJson,
        );
      }
    } catch (e) {
      logger.e(e);
      handlerApiError(e, behavior: _savingVNPController);
    } finally {
      completeLoading();
    }
  }

  Future<SavingVNPOpen?> openAccountSavingVNP(
      NormalSavingAccountInfo info) async {
    showLoading();
    try {
      cifNumber = await preferences.cifNumber;
      ProfileInfo? profileInfo = await preferences.getProfileInfo();
      final result =
          await repository.savingVnpApi?.getSavingAPPKApi().createSavingCode1(
        createSavingCodeAppRequest: CreateSavingCodeAppRequest(
          (builder) {
            builder
              ..bankCIF = cifNumber
              ..customerName = profileInfo?.fullName
              ..idCard = profileInfo?.idCardNo
              ..issueDate = profileInfo?.idCardIssueDate.formatDMY
              ..cardPlace = profileInfo?.idCardIssuePlace
              ..periodId = info.termId
              ..period = info.termName
              ..accountNo1 = info.accountNumber
              ..partnercd = info.partnerCD
              ..amount = info.amount?.toInt()
              ..interest = info.rate?.toDouble()
              ..interestReceivesId = info.finalTypeId.toString()
              ..interestReceivesName = info.finalTypeName
              ..accountNo2 = info.finalAccountNumber
              ..phoneNumber = profileInfo?.phoneNumber;
          },
        ),
      );
      successResponse(result);
      final data = result?.data?.data;
      if (data != null) {
        final savingAccounts = SavingVNPOpen.fromResponse(data);
        LoadingResult.success(
          behavior: _openSavingVNPController,
          data: savingAccounts,
        );
        return savingAccounts;
      }
    } catch (e) {
      logger.e(e);
      handlerApiError(e, behavior: _openSavingVNPController);
    } finally {
      completeLoading();
    }
    return null;
  }

  Future<SavingDeleteUpdate?> updateSavingVNP(
      NormalSavingAccountInfo info) async {
    showLoading();
    try {
      cifNumber = await preferences.cifNumber;
      final result =
          await repository.savingVnpApi?.getSavingAPPKApi().updateSavingCode(
        vnpUpdateSavingCodeRequest: VnpUpdateSavingCodeRequest(
          (builder) {
            builder
              ..bankcif = cifNumber
              ..savingcode = info.savingCode
              ..periodId = info.termId
              ..period = info.termName
              ..accountNo1 = info.accountNumber
              ..amount = info.amount?.toInt()
              ..interest = info.rate?.toDouble()
              ..interestReceivesId = info.finalTypeId.toString()
              ..interestReceivesName = info.finalTypeName
              ..accountNo2 = info.finalAccountNumber;
          },
        ),
      );
      successResponse(result);
      final data = result?.data?.data;
      if (data != null) {
        final savingAccounts = SavingDeleteUpdate.fromResponseUpdate(data);
        LoadingResult.success(
          behavior: _updateSavingVNPController,
          data: savingAccounts,
        );
        return savingAccounts;
      }
      completeLoading();
    } catch (e) {
      logger.e(e);
      handlerApiError(e, behavior: _updateSavingVNPController);
    } finally {
      completeLoading();
    }
    return null;
  }

  getDetailSavingVNP(String savingCode) async {
    try {
      cifNumber = await preferences.cifNumber;
      final result =
          await repository.savingVnpApi?.getSavingAPPKApi().getDetailSavingCode(
        vnpSavingCodeRequest: VnpSavingCodeRequest(
          (b) {
            b.savingcode = savingCode;
            b.bankcif = cifNumber;
          },
        ),
      );
      successResponse(result);
      final data = result?.data?.data;
      if (data != null) {
        final detailSaving = SavingsVNPDetail.fromResponseUpdate(data);
        LoadingResult.success(
          behavior: _detailSavingVNPController,
          data: detailSaving,
        );
      }
      completeLoading();
    } catch (e) {
      logger.e(e);
      handlerApiError(e, behavior: _detailSavingVNPController);
    } finally {
      completeLoading();
    }
  }

  deleteSavingVNP(String savingCode) async {
    showLoading();
    try {
      cifNumber = await preferences.cifNumber;
      final result =
          await repository.savingVnpApi?.getSavingAPPKApi().deleteSavingCode(
        vnpDeleteSavingCodeRequest: VnpDeleteSavingCodeRequest(
          (b) {
            b.bankcif = cifNumber;
            b.savingcode = savingCode;
          },
        ),
      );
      successResponse(result);
      final data = result?.data?.data;
      if (data != null) {
        final deleteAccounts = SavingDeleteUpdate.fromResponseDelete(data);
        LoadingResult.success(
          behavior: _deleteSavingVNPController,
          data: deleteAccounts,
        );
      }
    } catch (e) {
      logger.e(e);
      handlerApiError(e, behavior: _deleteSavingVNPController);
    } finally {
      completeLoading();
    }
  }

  getPartner() async {
    try {
      final result =
          await repository.savingVnpApi?.getSavingAPPKApi().getPartners();
      successResponse(result);
      final data = result?.data?.data;
      if (data != null) {
        final dataResult = Partner.fromResponse(data);
        safeAddData(_partnerController, dataResult.data);
      }
      ;
    } catch (e) {
      logger.e(e);
      handlerApiError(e, behavior: _partnerController);
    } finally {
      completeLoading();
    }
  }

  Future<ReviewOpen?> reviewOpen(NormalSavingAccountInfo? info) async {
    try {
      showLoading();
      cifNumber = await preferences.cifNumber;
      final result =
          await repository.savingVnpApi?.getSavingAPPKApi().reviewOpen(
        reviewOpenAppRequest: ReviewOpenAppRequest(
          (b) {
            b.bankCif = cifNumber;
            b.transactionNo = DateTime.now().formatYMDHMS;
            b.accountNumber = info?.accountNumber;
            b.amount = info?.amount?.toInt();
            b.currency = "VND";
            b.termId = info?.termId;
            b.rate = info?.rate;
            b.finalTypeId = info?.finalTypeId;
            b.finalAccountNumber = info?.finalAccountNumber;
            b.accountType = info?.accountType;
          },
        ),
      );
      successResponse(result);
      final data = result?.data?.data;
      if (data != null) {
        final dataResult = ReviewOpen.fromResponse(data);
        return dataResult;
      }
    } catch (e) {
      logger.e(e);
      handlerApiError(e);
    } finally {
      completeLoading();
    }
    return null;
  }

  getSavingAccounts() async {
    cifNumber = await preferences.cifNumber;
    return repository.bankApi!
        .getSavingControllerApi()
        .getSavingAccounts(bankCif: cifNumber)
        .then((res) {
      successResponse(res);
      final data = res.data?.data;
      if (data != null) {
        final savingAccounts = SavingAccounts.fromResponse(data);
        LoadingResult.success(
            behavior: _savingAccountsController, data: savingAccounts);
      }
    }).catchError((err) {
      handlerApiError(err, behavior: _savingAccountsController);
    }).whenComplete(() => completeLoading());
  }

  getSavingTypes() {
    showAreaLoading();
    repository.bankApi!.getSavingControllerApi().getSavingTypes().then((res) {
      successResponse(res);
      final savingTypes = res.data?.data?.savingTypes
          ?.map((type) => SavingType.fromResponse(type))
          .toList();
      LoadingResult.success(behavior: _savingTypeController, data: savingTypes);
    }).catchError((err) {
      handlerApiError(err, behavior: _savingTypeController);
    }).whenComplete(() => completeLoading());
  }

  getSavingPeriods() async {
    showScreenLoading();
    cifNumber = await preferences.cifNumber;
    repository.bankApi!
        .getSavingControllerApi()
        .getSavingPeriods(bankCif: cifNumber ?? '')
        .then((res) {
      successResponse(res);
      final savingPeriods = res.data?.data?.savingPeriods
          ?.map((period) => SavingPeriod.fromResponse(period))
          .toList();
      safeAddData(_savingPeriodController, savingPeriods);
    }).catchError((e) {
      handlerApiError(e);
    }).whenComplete(() => completeLoading());
  }

  getInterestMoney(String? termId, double? money) async {
    showScreenLoading(isSubmit: true);
    if (termId == null || termId.isEmpty || money == null || money <= 0) {
      _savingInterestMoneyController.safeAdd(null);
      return;
    }
    cifNumber = await preferences.cifNumber;
    return repository.bankApi!
        .getSavingControllerApi()
        .calculateInterestOnlineSaving(calculateInterestOnlineSavingRequest:
            CalculateInterestOnlineSavingRequest((builder) {
      builder
        ..bankCif = cifNumber
        ..amount = money
        ..termId = termId;
    })).then((res) {
      successResponse(res);
      final data = res.data?.data;
      if (data != null) {
        final interestMoney = SavingInterestMoney.fromResponse(data);
        safeAddData(_savingInterestMoneyController, interestMoney);
        return interestMoney;
      }
    }).catchError((error) {
      handlerApiError(error);
      return null;
    }).whenComplete(completeLoading);
  }

  getInterestReceives() async {
    cifNumber = await preferences.cifNumber;
    showAreaLoading(behavior: _savingInterestReceiveController);
    repository.bankApi!
        .getSavingControllerApi()
        .getInterestReceives(bankCif: cifNumber ?? '')
        .then((res) {
      successResponse(res);
      final interestReceives = res.data?.data?.interestReceives
          ?.map((receive) => SavingInterestReceive.fromResponse(receive))
          .toList();
      LoadingResult.success(
          behavior: _savingInterestReceiveController, data: interestReceives);
    }).catchError((err) {
      handlerApiError(err, behavior: _savingInterestReceiveController);
    }).whenComplete(() => completeLoading());
  }

  //api reviewTransaction for open saving
  reviewOpenSavingAccount(NormalSavingAccountInfo? info) async {
    showLoading();
    cifNumber = await preferences.cifNumber;
    info?.bankCif = cifNumber;
    info?.currency = 'VND';
    info?.transactionNo = DateTime.now().formatYMDHMS;
    return repository.bankApi!.getSavingControllerApi().reviewOpenSavingAccount(
        reviewSavingAccountRequest: ReviewSavingAccountRequest((builder) {
      builder
        ..bankCif = info?.bankCif
        ..transactionNo = info?.transactionNo
        ..accountNumber = info?.accountNumber
        ..amount = info?.amount
        ..currency = info?.currency == 'VND'
            ? ReviewSavingAccountRequestCurrencyEnum.VND
            : ReviewSavingAccountRequestCurrencyEnum.USD
        ..termId = info?.termId
        ..rate = info?.rate
        ..finalTypeId = info?.finalTypeId
        ..finalAccountNumber = info?.finalAccountNumber;
    })).then((res) {
      final data = res.data?.data;
      if (data != null) {
        setAuthData(
          transactionNumber: data.transactionNo,
          step: data.transNextStep,
          messageDialog: data.content,
        );
        final info = NormalSavingAccountInfo.fromResponse(data);
        return info;
      }
    }).catchError((e) {
      handlerApiError(e);
    }).whenComplete(completeLoading);
  }

  Future<NormalSavingAccountInfo?> openSavingAccount(
      NormalSavingAccountInfo info,
      {String? otp}) async {
    showLoading();
    cifNumber = await preferences.cifNumber;
    return repository.bankApi!
        .getSavingControllerApi()
        .createOnlineSavingAccount(openOnlineSavingAccountRequest:
            OpenOnlineSavingAccountRequest((builder) {
      builder
        ..bankCif = info.bankCif
        ..transactionNo = info.transactionNo
        ..accountNumber = info.accountNumber
        ..amount = info.amount
        ..currency = info.currency == 'VND'
            ? OpenOnlineSavingAccountRequestCurrencyEnum.VND
            : OpenOnlineSavingAccountRequestCurrencyEnum.USD
        ..termId = info.termId
        ..rate = info.rate
        ..contractDate = DateTime.now().convertDate()
        ..dueDate = info.dueDate
        ..finalTypeId = info.finalTypeId
        ..finalTypeName = info.finalTypeName
        ..interestAmountEndOfTerm = info.interestAmount
        ..finalAccountNumber = info.finalAccountNumber
        ..verifySoftOtpData = VerifySoftOtpRequest((builder) {
          builder.otp = otp;
        }).toBuilder();
    })).then((res) {
      successResponse(res);
      final data = res.data?.data;
      if (data != null) {
        final accountInfo = NormalSavingAccountInfo.fromOpenResponse(data);
        return accountInfo;
      }
    }).catchError((err) {
      handlerApiError(err);
    }).whenComplete(() => completeLoading());
  }

  //api reviewTransaction for close saving
  reviewCloseSavingAccount(NormalSavingAccountInfo info) async {
    showLoading();
    cifNumber = await preferences.cifNumber;
    info.bankCif = cifNumber;
    info.currency = 'VND';
    info.transactionNo = DateTime.now().formatYMDHMS;
    return repository.bankApi!
        .getSavingControllerApi()
        .reviewCloseOnlineSavingAccount(reviewCloseOnlineSavingAccountRequest:
            ReviewCloseOnlineSavingAccountRequest((builder) {
      builder
        ..bankCif = info.bankCif
        ..transactionNo = info.transactionNo
        ..accountNumber = info.accountNumber
        ..accountName = info.accountName
        ..balance = info.balance as double?
        ..currency = info.currency == 'VND'
            ? ReviewCloseOnlineSavingAccountRequestCurrencyEnum.VND
            : ReviewCloseOnlineSavingAccountRequestCurrencyEnum.USD
        ..rate = info.rate
        ..contractDate = info.contractDate
        ..dueDate = info.dueDate
        ..finalAmount = info.finalAmount
        ..destinationAccount = info.finalAccountNumber;
    })).then((res) {
      final data = res.data?.data;
      if (data != null) {
        setAuthData(
          transactionNumber: data.transactionNo,
          step: data.transNextStep,
          messageDialog: data.content,
        );
        final info = NormalSavingAccountInfo.fromReviewCloseResponse(data);
        return info;
      }
    }).catchError((e) {
      handlerApiError(e);
    }).whenComplete(completeLoading);
  }

  Future<NormalSavingAccountInfo?> closeSavingAccount(
      NormalSavingAccountInfo info,
      {String? otp}) async {
    cifNumber = await preferences.cifNumber;
    showLoading();
    return repository.bankApi!
        .getSavingControllerApi()
        .closeOnlineSavingAccount(closeOnlineSavingAccountRequest:
            CloseOnlineSavingAccountRequest((builder) {
      builder
        ..bankCif = info.bankCif
        ..transactionNo = info.transactionNo
        ..accountNumber = info.accountNumber
        ..accountName = info.accountName
        ..balance = info.balance
        ..currency = info.currency == 'VND'
            ? CloseOnlineSavingAccountRequestCurrencyEnum.VND
            : CloseOnlineSavingAccountRequestCurrencyEnum.USD
        ..rate = info.rate
        ..contractDate = info.contractDate
        ..dueDate = info.dueDate
        ..finalAmount = info.finalAmount
        ..destinationAccount = info.finalAccountNumber
        ..verifySoftOtp = VerifySoftOtpRequest((builder) {
          builder.otp = otp;
        }).toBuilder();
    })).then((res) {
      successResponse(res);
      final data = res.data?.data;
      if (data != null) {
        final accountInfo = NormalSavingAccountInfo.fromCloseResponse(data);
        return accountInfo;
      }
    }).catchError((err) {
      handlerApiError(err);
    }).whenComplete(() => completeLoading());
  }

  getSavingAccountDetail(String accountNumber) async {
    cifNumber = await preferences.cifNumber;
    showAreaLoading(behavior: _savingDetailController);
    return repository.bankApi!
        .getSavingControllerApi()
        .getSavingAccountDetail(
            accountNumber: accountNumber, bankCif: cifNumber ?? '')
        .then((res) {
      successResponse(res);
      final data = res.data?.data;
      if (data != null) {
        final accountDetail = NormalSavingAccountInfo.fromDetailResponse(data);
        LoadingResult.success(
            behavior: _savingDetailController, data: accountDetail);
        return accountDetail;
      }
    }).catchError((err) {
      handlerApiError(err, behavior: _savingDetailController);
    }).whenComplete(() => completeLoading());
  }

  Future<NormalSavingAccountInfo?> getOfflineSavingAccountDetail(
      String accountNumber) async {
    cifNumber = await preferences.cifNumber;
    showAreaLoading(behavior: _savingDetailController);
    return repository.bankApi!
        .getSavingControllerApi()
        .getOfflineSavingAccountDetail(
            accountNo: accountNumber, bankCif: cifNumber ?? '')
        .then((res) {
      successResponse(res);
      final data = res.data?.data;
      if (data != null) {
        final accountDetail =
            NormalSavingAccountInfo.fromOfflineDetailResponse(data);
        LoadingResult.success(
            behavior: _savingDetailController, data: accountDetail);
        return accountDetail;
      }
    }).catchError((err) {
      handlerApiError(err, behavior: _savingDetailController);
      return null;
    }).whenComplete(() => completeLoading());
  }

  getCertificate(String accountNumber) async {
    cifNumber = await preferences.cifNumber;
    showLoading();
    return repository.bankApi!
        .getSavingControllerApi()
        .getOnlineSavingCertificate(
            accountNumber: accountNumber, bankCif: cifNumber ?? '')
        .then((res) {
      successResponse(res);
      final data = res.data?.data;
      if (data != null) {
        final certificate = SavingAccountCertificate.fromResponse(data);
        LoadingResult.success(
            behavior: _savingCertificateController, data: certificate);
        return certificate;
      }
    }).catchError((err) {
      handlerApiError(err, behavior: _savingCertificateController);
    }).whenComplete(completeLoading);
  }

  Future<Uint8List?> getPdf({required String accountNumber}) async {
    Completer<Uint8List?> completer = Completer();
    cifNumber = await preferences.cifNumber;
    repository.bankApi!
        .getSavingControllerApi()
        .exportOnlineSavingCertificatePdf(
            accountNumber: accountNumber, bankCif: cifNumber ?? '')
        .then((res) {
      completer.complete(res.data);
    }).catchError((err) {
      completer.complete(null);
      handlerApiError(err);
    }).whenComplete(completeLoading);
    return completer.future;
  }

  Future<StrapiTerms> getTermsAndConditions() {
    showLoading();
    return repository.strApi!.getTermsAndConditions(id: '3').then((value) {
      safeAddData(_terms, value);
      return value;
    }).catchError((e) {
      //handlerApiError(e);
    }).whenComplete(() => completeLoading());
  }

  Future<CheckIsVNPAccountResponse?> checkVpnRegister() {
    showLoading();
    return repository.bankApi!
        .getTransferApi()
        .checkVNPRegister()
        .then((value) {
      return value.data?.data;
    }).catchError((error) {
      handlerApiError(error);
    }).whenComplete(() => completeLoading());
  }

  getAccountDetail(String accountNumber) async {
    if (!accountNumber.isNullOrEmpty) {
      showLoading();
      cifNumber = await preferences.cifNumber;
      final request = AccountDetailRequest((builder) {
        builder.bankCif = cifNumber;
        builder.accountNo = accountNumber;
      });
      repository.bankApi!
          .getAccountControllerApi()
          .accountDetail(accountDetailRequest: request)
          .then((res) {
        final data = res.data?.data;
        if (data != null) {
          final detail = AccountModel.fromDetail(data);
          _accountInfo.safeAdd(detail);
        }
      }).catchError((err) {
        handlerApiError(err);
      }).whenComplete(completeLoading);
    }
  }
}
