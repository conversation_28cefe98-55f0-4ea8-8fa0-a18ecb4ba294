import 'dart:async';

import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_bloc/bloc/model/bill/bill_history.dart';
import 'package:common/model/loading_event.dart';
import 'package:ksb_common/shared/route_path.dart';
import 'package:rxdart/rxdart.dart';
import 'package:built_collection/built_collection.dart';

import '../base_authenticate_bloc.dart';

class BillBlocV2 extends BaseBloc with BaseAuthBloc {
  BillBlocV2(Repository repository, Preferences preferences, Session session)
      : super(repository, preferences, session);

  final _servicesController = BehaviorSubject<LoadingWidgetModel>();

  Stream<LoadingWidgetModel> get servicesStream => _servicesController.stream;

  final _suppliersController = BehaviorSubject<LoadingWidgetModel>();

  Stream<LoadingWidgetModel> get suppliersStream => _suppliersController.stream;

  final _invoiceController = BehaviorSubject<LoadingWidgetModel>();

  Stream<LoadingWidgetModel> get invoicesStream => _invoiceController.stream;

  final _billController = BehaviorSubject<List<BillInfo>>();

  Stream<List<BillInfo>> get billStream => _billController.stream;

  Sink<List<BillInfo>> get billSink => _billController.sink;

  final _scheduleController = BehaviorSubject<LoadingWidgetModel>();

  Stream<LoadingWidgetModel> get scheduleStream => _scheduleController.stream;

  final _accountNoController = BehaviorSubject<String?>();

  Stream<String?> get accountNoStream => _accountNoController.stream;

  final _historiesController = BehaviorSubject<LoadingWidgetModel>();

  Stream<LoadingWidgetModel> get historyStream => _historiesController.stream;

  final _idService = BehaviorSubject<String>();

  Stream<String> get idStream => _idService.stream;

  Sink<String> get idSink => _idService.sink;

  final _itemHistoryPay = BehaviorSubject<List<ItemGroupTimeHistory>>();

  Stream<List<ItemGroupTimeHistory>> get groupHistoryStream =>
      _itemHistoryPay.stream;

  Sink<List<ItemGroupTimeHistory>> get groupHistorySink => _itemHistoryPay.sink;

  final _idDelete = BehaviorSubject<String?>();

  Stream<String?> get idDeleteStream => _idDelete.stream;

  Sink<String?> get idDeleteSink => _idDelete.sink;

  final _chooseAccount = BehaviorSubject<AccountModel>();

  Stream<AccountModel> get chooseAccountStream => _chooseAccount.stream;

  final _saveBill = BehaviorSubject<bool>.seeded(true);

  Stream<bool> get saveBillStream => _saveBill.stream;

  Sink<bool> get saveBillSink => _saveBill.sink;

  bool get saveBill => _saveBill.valueOrNull ?? true;

  final _listBillSaved = BehaviorSubject<List<BillInfo>>.seeded([]);

  Stream<List<BillInfo>> get billSavedStream => _listBillSaved.stream;

  Sink<List<BillInfo>> get billSavedSink => _listBillSaved.sink;

  List<BillInfo> get listBillSaved => _listBillSaved.valueOrNull ?? [];

  final _billsSaved = BehaviorSubject<LoadingWidgetModel>();

  Stream<LoadingWidgetModel> get billsSavedStream => _billsSaved.stream;

  Sink<LoadingWidgetModel> get billsSavedSink => _billsSaved.sink;

  String? cifNumber;

  BillScheduleInfo billScheduleRequest = BillScheduleInfo();
  AccountModel? chosenAccount;
  String? fromDate;
  String? toDate;

  static String initialRoute = RoutePaths.bill;

  @override
  void dispose() {
    _billController.close();
    _invoiceController.close();
    _servicesController.close();
    _scheduleController.close();
    _accountNoController.close();
    _suppliersController.close();
    _historiesController.close();
    _idService.close();
    _itemHistoryPay.close();
    _idDelete.close();
    _chooseAccount.close();
    _saveBill.close();
    _listBillSaved.close();
    _billsSaved.close();
    super.dispose();
  }

  String get repayNum => billScheduleRequest.retry == 0
      ? 'Không thanh toán lại'
      : '${billScheduleRequest.retry.toString()} lần';

  updateAccount(AccountModel data) {
    chosenAccount = data;
    _chooseAccount.add(data);
    _accountNoController.add(data.accountNumber);
  }

  Future<List<BillService>> getServices() async {
    showAreaLoading(behavior: _servicesController);
    return repository.bankApi!.getPayBillApi().getServices1().then((res) {
      successResponse(res);
      final services = res.data?.data?.services
              ?.map((item) => BillService.fromResponseV2(item))
              .toList() ??
          <BillService>[];
      LoadingResult.success(behavior: _servicesController, data: services);
      return services;
    }).catchError((err) {
      handlerApiError(err, behavior: _servicesController);
      return <BillService>[];
    }).whenComplete(() => completeLoading());
  }

  setTimeFilter({String? from, String? to}) {
    fromDate = from;
    toDate = to;

    if (fromDate == null || toDate == null) {
      final currentDate = DateTime.now();
      fromDate = currentDate.add(const Duration(days: -30)).formatDMY;
      toDate = currentDate.formatDMY;
    }

    getHistoryInvoices();
  }

  Future<List<Supplier>> getSuppliers(String id) async {
    showAreaLoading(behavior: _suppliersController);
    return repository.bankApi!
        .getPayBillApi()
        .getSuppliers3(serviceCode: id)
        .then((res) {
      successResponse(res);
      final suppliers = res.data?.data?.suppliers
              ?.map((item) => Supplier.fromResponseV2(item))
              .toList() ??
          <Supplier>[];
      LoadingResult.success(behavior: _suppliersController, data: suppliers);
      return suppliers;
    }).catchError((err) {
      handlerApiError(err, behavior: _suppliersController);
      return <Supplier>[];
    }).whenComplete(() => completeLoading());
  }

  Future<List<BillHistories>> getHistoryInvoices() async {
    cifNumber = await preferences.cifNumber;
    showAreaLoading(behavior: _historiesController);
    return repository.bankApi!
        .getPayBillApi()
        .getBillHistories(
          fromTime: fromDate ?? DateTime.now().formatDMY,
          toTime: toDate ?? DateTime.now().formatDMY,
        )
        .then((res) {
      successResponse(res);
      final billInfo = res.data?.data?.invoiceHistories
              ?.map((item) => BillHistories.fromResponse(item))
              .toList() ??
          <BillHistories>[];
      LoadingResult.success(behavior: _historiesController, data: billInfo);
      getItemGroup(billInfo);
      return billInfo;
    }).catchError((err) {
      handlerApiError(err, behavior: _historiesController);
      return <BillHistories>[];
    }).whenComplete(() => completeLoading());
  }

  getItemGroup(List<BillHistories> bills) {
    List<ItemGroupTimeHistory> billsGroup = <ItemGroupTimeHistory>[];
    bills.sort((a, b) {
      return (b.payTime ?? DateTime.now())
          .compareTo(a.payTime ?? DateTime.now());
    });
    bills.forEach((item) {
      var indexMonth = billsGroup.indexWhere((bill) =>
          item.payTime?.year == bill.transactionAt?.year &&
          item.payTime?.month == bill.transactionAt?.month);
      if (indexMonth > -1) {
        ItemGroupTimeHistory? oldBill = billsGroup[indexMonth];
        var indexDay = oldBill.childDay?.indexWhere((bill) =>
                item.payTime?.year == bill?.transactionAt?.year &&
                item.payTime?.month == bill?.transactionAt?.month &&
                item.payTime?.day == bill?.transactionAt?.day) ??
            -1;
        if (indexDay > -1) {
          var oldDay = oldBill.childDay?[indexDay];
          oldDay?.child.add(item);
          oldBill.childDay?[indexDay] = oldDay;
          oldBill.length = (oldBill.length ?? 0) + 1;
          billsGroup[indexMonth] = oldBill;
        } else {
          var newDay = ItemGroupTimeDayHistory()..empty();
          newDay.transactionAt = item.payTime;
          newDay.child.add(item);
          oldBill.childDay?.add(newDay);
          oldBill.length = (oldBill.length ?? 0) + 1;
          billsGroup[indexMonth] = oldBill;
        }
      } else {
        var newBill = ItemGroupTimeHistory()..empty();
        newBill.transactionAt = item.payTime;
        var newDay = ItemGroupTimeDayHistory()..empty();
        newDay.transactionAt = item.payTime;
        newDay.child.add(item);
        newBill.childDay?.add(newDay);
        newBill.length = (newBill.length ?? 0) + 1;
        billsGroup.add(newBill);
      }
    });
    _itemHistoryPay.add(billsGroup);
  }

  Future<List<BillInfo>> getMyInvoices() async {
    return repository.bankApi!.getPayBillApi().getUnpaidBill().then((res) {
      successResponse(res);
      final bills = res.data?.data?.invoices
              ?.map((item) => BillInfo.fromResponseV2(item))
              .toList() ??
          <BillInfo>[];

      _billController.add(bills);
      return bills;
    }).catchError((err) {
      handlerApiError(err);
      return <BillInfo>[];
    }).whenComplete(() => completeLoading());
  }

  Future<BillInfo?> getInvoice({
    required String productCode,
    required String customerCode,
  }) async {
    showLoading();
    return repository.bankApi!
        .getPayBillApi()
        .query(productCode: productCode, customerCode: customerCode)
        .then((res) {
      successResponse(res);
      final data = res.data?.data;
      if (data != null) {
        final billInfo = BillInfo.fromResponseV2(data);
        return billInfo;
      }
    }).catchError((error) {
      handlerApiError(error);
      return null;
    }).whenComplete(() => completeLoading());
  }

  Future<BillInfo?> getReviewInvoice({
    String? productCode,
    String? customerCode,
    String? accountNo,
  }) async {
    showLoading();
    cifNumber = await preferences.cifNumber;
    if (accountNo.isNullOrEmpty) accountNo = await preferences.accountNumber;
    return repository.bankApi!.getPayBillApi().reviewSchedule(
        reviewBillScheduleRequest: ReviewBillScheduleRequest((builder) {
      builder
        ..productCode = productCode
        ..customerCode = customerCode
        ..accountNo = accountNo;
    })).then((res) {
      successResponse(res);
      if (res.data?.data != null) {
        setAuthData(
          transactionNumber: res.data?.data?.transactionNumber,
          step: res.data?.data?.transNextStep,
          messageDialog: res.data?.data?.content,
        );
        final billInfo = BillInfo.fromReviewResponseV2(res.data!.data!);
        return billInfo;
      }
    }).catchError((error) {
      handlerApiError(error);
      return null;
    }).whenComplete(() => completeLoading());
  }

  Future<PaidInfo?> payInvoice(
    BillInfo? info, {
    String? accountNumber,
    String? softOtp,
    bool? isSaved,
    String? productCode,
    String? transactionNo,
  }) async {
    showLoading();
    cifNumber = await preferences.cifNumber;
    final request = PaymentBillRequest((builder) {
      builder
        ..customerCode = info?.customerCode
        ..accountNo = accountNumber
        ..productCode = productCode
        ..transactionNo = transactionNo
        ..bills = info?.bills
            ?.map((e) => PaymentBillDto((b) => b.billId = e.billId))
            .toBuiltList()
            .toBuilder()
        ..softOtp = VerifySoftOtpRequest((builder) {
          builder.otp = softOtp;
        }).toBuilder();
    });
    return repository.bankApi!
        .getPayBillApi()
        .payment3(paymentBillRequest: request)
        .then((res) {
      successResponse(res);
      if (res.data?.data != null) {
        final billInfo = PaidInfo.fromResponseV2(res.data!.data!);
        if (info != null) {
          saveBillLocal(info);
        }
        LoadingResult.success(behavior: _invoiceController, data: billInfo);
        return billInfo;
      }
    }).catchError((err) {
      handlerApiError(err, behavior: _invoiceController);
      return null;
    }).whenComplete(completeLoading);
  }

  Future<bool?> deleteBill({String? customerCode}) {
    final request = IgnoreBillReminderRequest((build) {
      build.customerCode = customerCode ?? "";
    });
    return repository.bankApi!
        .getPayBillApi()
        .ignoreDebtsReminder(ignoreBillReminderRequest: request)
        .then((value) => true)
        .catchError((err) {
      handlerApiError(err);
      return false;
    }).whenComplete(() => completeLoading());
  }

  Future<List<ScheduleBill>> getScheduleBills() async {
    showAreaLoading(behavior: _scheduleController);
    cifNumber = await getCifNumber();
    return repository.bankApi!.getPayBillApi().getSchedules().then((res) {
      successResponse(res);
      final _bills = res.data?.data?.billSchedules
              ?.map((item) => ScheduleBill.fromResponseV2(item))
              .toList() ??
          <ScheduleBill>[];
      LoadingResult.success(behavior: _scheduleController, data: _bills);
      return _bills;
    }).catchError((err) {
      handlerApiError(err, behavior: _scheduleController);
      return <ScheduleBill>[];
    }).whenComplete(() => completeLoading());
  }

  Future<BillScheduleInfo?> setSchedule(
      BillScheduleInfo request, String value) async {
    showLoading();
    cifNumber = await preferences.cifNumber;
    return repository.bankApi!.getPayBillApi().createSchedule1(
        createBillScheduleRequest: CreateBillScheduleRequest((builder) {
      builder
        ..transactionNo = transactionNumber
        ..productCode = request.productCode
        ..accountNo = request.accountNo
        ..customerCode = request.customerCode
        ..retry = request.retry
        ..allowAnotherAcc = request.allowAnotherAcc
        ..softOtp = VerifySoftOtpRequest((otp) {
          otp.otp = value;
        }).toBuilder();
    })).then((res) {
      successResponse(res);
      if (res.data?.data != null) {
        return BillScheduleInfo.fromScheduleResponseV2(res.data!.data!);
      }
    }).catchError((err) {
      handlerApiError(err, behavior: _scheduleController);
      return null;
    }).whenComplete(() => completeLoading());
  }

  Future<int> deleteSchedule(String scheduleId, String value) async {
    showLoading();
    cifNumber = await preferences.cifNumber;
    return repository.bankApi!.getPayBillApi().removeSchedule1(
        removeBillScheduleRequest: RemoveBillScheduleRequest((builder) {
      builder
        ..scheduleId = scheduleId
        ..softOtp = VerifySoftOtpRequest((otp) {
          otp.otp = value;
        }).toBuilder();
    })).then((res) {
      successResponse(res);
      _idDelete.add(res.data?.data?.scheduleId);
      return toInt(res.data?.data?.scheduleId);
    }).catchError((e) {
      handlerApiError(e);
      return -1;
    }).whenComplete(() => completeLoading());
  }

  Future<void> saveBillLocal(BillInfo billInfo) async {
    final bills = await preferences.getSaveBills();
    final index = bills
        .indexWhere((element) => element.customerCode == billInfo.customerCode);
    if (index == -1) {
      bills.insert(0, billInfo..canPaid = false);
    } else {
      bills.removeAt(index);
      bills.insert(index, billInfo..canPaid = false);
    }
    preferences.setBillInfo(bills);
  }

  Future<void> getBillSaved(BillService? service) async {
    final bills = (await preferences.getSaveBills());

    final temp = List.of(bills)
        .where((element) => element.serviceName == service?.name)
        .toList();
    _listBillSaved.add(temp);
    if (bills.isNotEmpty) {
      final remoteBills = await getMyInvoices();
      for (var bill in remoteBills) {
        final index = temp.indexWhere(
          (element) => element.customerCode == bill.customerCode,
        );
        if (index != -1) {
          bill.canPaid == true;
          temp.removeAt(index);
          temp.insert(index, bill);
          bills.removeAt(index);
          bills.insert(index, bill);
        }
      }
      preferences.setBillInfo(bills);
    }
    _listBillSaved.add(temp);

    // showAreaLoading(behavior: _billsSaved);
    // repository.bankApi!
    //     .getInvoiceManagementApi()
    //     .getDueInvoices(provider: 'PAYOO')
    //     .then((res) {
    //   successResponse(res);
    //   final billInfo = res.data?.data?.invoices
    //           ?.map((item) => BillInfo.fromResponse(item))
    //           .toList() ??
    //       <BillInfo>[];
    //
    //   LoadingResult.success(behavior: _billsSaved, data: billInfo);
    // }).catchError((err) {
    //   handlerApiError(err);
    //   LoadingResult.success(behavior: _billsSaved, data: <BillInfo>[]);
    // }).whenComplete(completeLoading);
  }
}
