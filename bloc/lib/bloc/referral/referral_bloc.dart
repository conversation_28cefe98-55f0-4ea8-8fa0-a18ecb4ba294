import 'package:common/model/view_data_model.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:rxdart/rxdart.dart';

import '../model/referral/referral_model.dart';

typedef VDMReferral = ViewDataModel<ReferralModel>;

class ReferralBloc extends BaseBloc {
  ReferralBloc(Repository repository, Preferences preferences, Session session)
      : super(repository, preferences, session);

  final _referral = BehaviorSubject<VDMReferral>();

  Stream<VDMReferral> get referralStream => _referral.stream;

  Sink<VDMReferral> get referralSink => _referral.sink;

  ViewDataModel<ReferralModel>? get referral => _referral.valueOrNull;

  final _disableBtn = BehaviorSubject<bool>.seeded(false);

  Stream<bool> get disableBtnStream => _disableBtn.stream;

  Sink<bool> get disableBtnSink => _disableBtn.sink;

  @override
  dispose() {
    _referral.close();
    _disableBtn.close();
  }

  setEmptyModel() {
    final model = VDMReferral.success(null);
    _referral.sink.add(model);
  }

  Future<ReferralModel?> getReferral({
    String? referralCode,
    CheckReferralCodeRequestFeatureEnum? feature,
  }) async {
    safeAddData<ViewDataModel<ReferralModel>>(
      _referral,
      ViewDataModel<ReferralModel>.loading(),
    );
    try {
      var res =
          await repository.loyaltyApi!.getReferralUserApi().checkReferralCode(
                checkReferralCodeRequest: CheckReferralCodeRequest((builder) {
                  builder.code = referralCode;
                  builder.feature = feature;
                }),
                headers: getHttpHeader(language: preferences.languageCode),
              );
      final result = res.data?.data;
      if (result != null) {
        final model = ReferralModel.fromCheckReferralCodeResponse(result);
        safeAddData<ViewDataModel<ReferralModel>>(
          _referral,
          ViewDataModel<ReferralModel>.success(model),
        );
        return model;
      }
      return null;
    } catch (error) {
      // logger.e(error);
      handlerApiError(error);
      safeAddData<ViewDataModel<ReferralModel>>(
        _referral,
        ViewDataModel<ReferralModel>.error(errorValue ?? ''),
      );
      return null;
    } finally {
      // safeAddData<VDMReferral>(_referral, VDMReferral.success());
      completeLoading();
    }
  }
}
