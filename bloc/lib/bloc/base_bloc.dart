import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:common/model/loading_event.dart';
import 'package:connectivity/connectivity.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:ks_chat/repository/chat/rocket_chat_error.dart';
import 'package:ks_chat/repository/chat/rocket_chat_models.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_bloc/bloc/validate_password.dart';
import 'package:rxdart/rxdart.dart';
import 'package:uuid/uuid.dart';

//same with use case
abstract class BaseBloc with WidgetsBindingObserver, ValidatePassword {
  final _progressVisible = BehaviorSubject<bool>.seeded(false);
  final _error = BehaviorSubject<String?>.seeded("");
  final _status = BehaviorSubject<BaseResponseModel?>();
  final _refreshVisible = BehaviorSubject<bool>();
  final _loadingScreenVisible = BehaviorSubject<LoadingWidgetModel>();
  BehaviorSubject<ConnectivityResult> networkStatus =
      BehaviorSubject<ConnectivityResult>();

  final List<StreamSubscription> streamSubs = [];

  final FAKE = false;

  @protected
  final submitEnable = PublishSubject<bool>();

  @protected
  final Preferences preferences;

  final Session session;

  final Repository repository;

  Sink<bool> get showRefresh => _refreshVisible.sink;

  Stream<bool> get refreshVisible => _refreshVisible.stream;

  BaseBloc(this.repository, this.preferences, this.session) {
    init();
  }

  Stream<bool> get submitEnableStream => submitEnable.stream;

  Stream<String?> get errorStream => _error.stream;

  String? get errorValue => _error.valueOrNull;

  set setMsgError(String error) {
    _error.safeAdd(error);
  }

  Sink<bool> get showProgress => _progressVisible.sink;

  Stream<bool> get progressVisible => _progressVisible.stream;

  bool get isProgressVisible => _progressVisible.valueOrNull ?? false;

  Stream<LoadingWidgetModel> get loadingScreenStream =>
      _loadingScreenVisible.stream;

  LoadingStatus get loadingStatus => _loadingScreenVisible.valueOrNull!.status;

  Stream<BaseResponseModel?> get statusStream => _status.stream;

  Sink<BaseResponseModel?> get statusSink => _status.sink;

  final refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();
  Completer refreshCompleter = Completer();

  int _loadingScreenCount = 0;
  String? _lastScreenError = "";

  String get idempotencyKey => Uuid().v1();

  String? _cifNumber;
  int? errCode = 0;
  bool? _isClose;

  bool get isClose => _isClose ?? false;

  addError(String? error) {
    _error.safeAdd(error);
  }

  Future<String?> getCifNumber() => _cifNumber.isNullOrEmpty
      ? preferences.cifNumber.then((value) {
          _cifNumber = value;
          return value;
        })
      : Future.value(_cifNumber);

  static final BaseItem KLB = BaseItem(
      id: '970452',
      title: 'KLB',
      subTitle: 'NH TMCP Kiên Long (KienlongBank)',
      image: 'https://cdn.sunshineapp.vn/smartbank/bankicons/KLB.svg',
      imageType: ImageType.NETWORK_SVG,
      commonTitle: "KienlongBank",
      data: 0,
      bankCode: "42",
      bankIdNapas: "970452");

  static final BaseItem UMEE = BaseItem(
      id: '963399',
      title: 'UMEE',
      subTitle: 'Ngân hàng số UMEE',
      image: 'https://cdn.sunshineapp.vn/smartbank/bankicons/logo_ume.svg',
      imageType: ImageType.NETWORK_SVG,
      commonTitle: "UMEE",
      data: 0,
      bankCode: "81",
      bankIdNapas: "963399");

  @protected
  void init() {
    logger.t('$runtimeType init()');
    _isClose = false;
    streamSubs.add(refreshVisible.listen((value) {
      debugPrint("refreshVisible:$value");
      if (value) {
        refreshIndicatorKey.currentState?.show();
      } else {
        if (!refreshCompleter.isCompleted) {
          refreshCompleter.complete();
        }
      }
    }));
    _handlerNetWorkChange();
    WidgetsBinding.instance.addObserver(this);
  }

  // @override
  // void didChangeAppLifecycleState(AppLifecycleState state) {
  //   if (state == AppLifecycleState.resumed) {
  //     reload();
  //   }
  // }

  void reload() {
    debugPrint('$runtimeType reload()');
    _error.safeAdd(null);
    _status.safeAdd(null);
    //_progressVisible.safeAdd(true);
  }

  @protected
  void successResponse(response, {data, results}) {
    _progressVisible.safeAdd(false);
    _refreshVisible.safeAdd(false);
    _status.add(BaseResponseModel(
        statusCode: response.statusCode, data: data, results: results));
  }

  void dispose() {
    debugPrint('$runtimeType dispose()');
    _isClose = true;
    _progressVisible.close();
    _error.close();
    _status.close();
    streamSubs.forEach((sub) => sub.cancel());
    streamSubs.clear();
    passwordDispose();
    submitEnable.close();
    _refreshVisible.close();
    networkStatus.close();
    WidgetsBinding.instance.removeObserver(this);
    _loadingScreenVisible.close();
  }

  // Future<UserModel> getUserInfo() async {
  //   final baseInfo = await session.firestore.getAccountInfo();
  //   final userDetail = await repository.gcloud.getUserDetail(getUserId()).first;
  //   if (userDetail != null) {
  //     /*final userCustomer = await repository.gcloud.getCustomerProfile(userDetail.customer_id).first;
  //     if (userCustomer != null) {
  //       return session.firestore.updateAccountInfo(userCustomer);
  //     }*/
  //     return session.firestore.updateAccountInfo(userDetail);
  //   }
  //   return baseInfo;
  // }

  Future<void> setCurrentScreen(String name, {String? className}) async {
    //session.setCurrentScreen(name, className: className);
  }

  // Refresh data
  Future<void> onRefresh() async {
    debugPrint('onRefresh');
    if (refreshCompleter.isCompleted) {
      refreshCompleter = Completer();
      debugPrint('onRefresh 1');

      reload();
    }
    return refreshCompleter.future;
  }

  showLoading() {
    _progressVisible.safeAdd(true);
  }

  completeLoading() {
    if (!_progressVisible.isClosed) {
      _progressVisible.safeAdd(false);
    }
    if (!_refreshVisible.isClosed) {
      _refreshVisible.safeAdd(false);
    }
    _status.safeAdd(null);
    completeScreenLoading();
  }

  _refreshTokenError() async {
    final token = await session.getToken();
    final newToken = await session.refreshToken(token);
    final tokenStrApi = await session.loginStrApi();
    if (newToken != null && tokenStrApi != null) {
      reload();
    } else if (newToken == null) {
      if (tokenStrApi == null) {
        preferences.setTokenStrApi("");
      }
      session.actionStatus.add(ActionStatus.restart());
    }
  }

  handlerApiError(dynamic error,
      {BehaviorSubject<dynamic>? behavior, bool autoRefresh = true}) {
    logger.t(error);
    try {
      if (error is DioException &&
          (error.response != null && error.response?.statusCode == 401) &&
          autoRefresh) {
        _refreshTokenError();
      } else if (!_error.isClosed) {
        _addError(error, behavior: behavior, autoRefresh: autoRefresh);
      }
    } catch (_) {
      logger.e(_);
    }
  }

  _addError(error,
      {BehaviorSubject<dynamic>? behavior, bool autoRefresh = true}) async {
    String? errMessage =
        "Dịch vụ tạm thời bị gián đoạn, Quý khách vui lòng thực hiện lại sau ít phút";
    var errorNetwork =
        "Kết nối mạng không ổn định, quý khách vui lòng thử lại sau";
    var errorSsl =
        'Bạn đang sử dụng đường truyền internet không đáng tin cậy, vui lòng kiểm tra lại kết nối hoặc sử dụng mạng khác!';
    final network = await getNetworkStatus();
    if (ConnectivityResult.none == network) {
      _lastScreenError = errorNetwork;
      errCode = ErrorCodeLocal.ERROR_NETWORK;
      session.networkStatus.add(NetworkStatus.none);
      _status.safeAdd(BaseResponseModel(statusCode: 201));
      return;
    }
    BaseResponseModel model = BaseResponseModel(message: errMessage);
    if (error is DioException) {
      final errorValue = error.error ?? '';
      int statusCode = error.response?.statusCode ?? 0;
      if (errorValue is String && errorValue == 'CONNECTION_NOT_SECURE') {
        errMessage =
            'Bạn đang sử dụng đường truyền internet không đáng tin cậy, vui lòng kiểm tra lại kết nối hoặc sử dụng mạng khác!';
      } else if (error.type == DioExceptionType.connectionTimeout ||
          error.type == DioExceptionType.receiveTimeout ||
          error.type == DioExceptionType.sendTimeout ||
          error.error is SocketException) {
        _lastScreenError = errorNetwork;
        errCode = ErrorCodeLocal.ERROR_NETWORK;
        session.networkStatus.add(NetworkStatus.timeout);
        errMessage = '$errMessage (${statusCode > 0 ? statusCode : 408})';
      } else if ([500, 503, 404, 301, 408].indexOf(statusCode) >= 0) {
        errCode = statusCode;
        errMessage = '$errMessage ($statusCode)';
      } else {
        model = BaseResponseModel.fromMapError(error);
        if ((model.statusCode == 401 ||
                model.statusCode == ProfileStatusCode.INVALID_TOKEN) &&
            autoRefresh) {
          _refreshTokenError();
          return;
        }
        errCode = model.statusCode;
        errMessage = '${model.message} ($errCode)';
        if (errMessage.toLowerCase().contains('handshakeexception')) {
          errMessage = errorSsl;
        }
      }
    } else if (error is RocketChatError) {
      RocketChatErrorResponse response =
          RocketChatErrorResponse.fromJson(jsonDecode(error.response!.body));
      errMessage = response.message;
    } else if (error is String) {
      final regex = RegExp(r'exception|error', caseSensitive: false);
      if (!error.contains(regex)) {
        errMessage = error;
      }
    } else if (error is FormatException) {
      errCode = ErrorCodeLocal.ERROR_FORMAT;
      errMessage = '$errMessage ($errCode)';
    }
    _error.safeAdd(errMessage);
    model.message = errMessage;
    model.statusSuccess = false;
    _status.safeAdd(model);
    _lastScreenError = errMessage;
    if (behavior != null && errCode is num) {
      LoadingResult.error(
          behavior: behavior, error: errMessage, errorCode: errCode);
    }
  }

  Future<ConnectivityResult> getNetworkStatus() async {
    var connectivityResult = await (Connectivity().checkConnectivity());
    if (!networkStatus.isClosed) {
      networkStatus.safeAdd(connectivityResult);
    }
    return connectivityResult;
  }

  _handlerNetWorkChange() {
    streamSubs.add(Connectivity()
        .onConnectivityChanged
        .listen((ConnectivityResult result) {
      debugPrint('onConnectivityChanged $result');
      networkStatus.safeAdd(result);
      if (result != ConnectivityResult.none) {
        reload();
      }
    }));
  }

  // Loading khi gọi toàn màn hình
  showScreenLoading({int maxApi = 1, bool isSubmit = false}) {
    _lastScreenError = "";
    _loadingScreenCount = maxApi;
    LoadingResult.loading(behavior: _loadingScreenVisible, isSubmit: isSubmit);
  }

  completeScreenLoading({bool isSubmit = false}) {
    _loadingScreenCount = _loadingScreenCount - 1;
    if (_loadingScreenCount == 0) {
      if (_lastScreenError!.length > 0 && isSubmit == false) {
        LoadingResult.error(
            error: _lastScreenError,
            behavior: _loadingScreenVisible,
            errorCode: errCode);
      } else {
        LoadingResult.success(behavior: _loadingScreenVisible);
      }
    }
  }

// Loading khi gọi area
  showAreaLoading({BehaviorSubject? behavior}) {
    safeAddData(
      behavior,
      LoadingWidgetModel(status: LoadingStatus.loading, isSubmit: false),
    );
  }

  safeAddData<T>(Subject<T?>? subject, T? data) {
    if (subject == null || subject.isClosed) return;
    subject.add(data);
  }

  Future<bool> hasEToken() async {
    final pinToken = await preferences.pinEtoken ?? "";
    final secretKey = await preferences.secretKey ?? "";
    if (pinToken.isNotEmpty && secretKey.isNotEmpty) {
      return true;
    }
    return false;
  }

  Future<String?> getAvatar() async {
    try {
      final profileInfo = await preferences.getProfileInfo();
      return profileInfo?.avatarUrl;
    } catch (e) {
      logger.e(e);
      return '';
    }
  }

  validateStructurePassword(String? value) {
    final isNum = validateNumberPassword(value);
    final isWord = validateWordPassword(value);
    final isLength = validateLengthPassword(value);
    if (isNum && isWord && isLength) {
      return true;
    } else {
      return false;
    }
  }
}
