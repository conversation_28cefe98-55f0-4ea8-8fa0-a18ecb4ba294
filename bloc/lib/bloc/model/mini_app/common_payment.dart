

import 'package:ksb_bloc/utils/model_util.dart';

class CommonPayment {
  double? amount;
  String? customerName;
  String? customerCode;
  String? contractCode;
  String? content;
  String? supplierId;
  String? supplierName;
  String? supplierAccount;
  String? iconSupplier;
  String? companyId;
  String? transId;
  String? type;
  String? urlCallBack;
  String? deepLinkVietQrCode;
  String? toAccount;
  String? toCard;
  String? bankCode;

  static String VIETQR = "VIETQR";
  static String TRANSFER = "TRANSFER";
  static String SECURITY = "SECURITY";

  CommonPayment({
    this.amount,
    this.supplierAccount,
    this.customerName,
    this.customerCode,
    this.contractCode,
    this.content,
    this.supplierId,
    this.supplierName,
    this.companyId,
    this.type,
    this.transId,
    this.urlCallBack,
    this.iconSupplier,
    this.deepLinkVietQrCode,
    this.toAccount,
    this.toCard,
    this.bankCode,
  });

  factory CommonPayment.fromMap(Map<String, dynamic> json) => CommonPayment(
        supplierAccount:
            json["supplier_account"] == null ? null : json["supplier_account"],
        amount: json["amount"] == null ? null : toDouble(json["amount"]),
        customerName:
            json["customer_name"] == null ? null : json["customer_name"],
        customerCode:
            json["customer_code"] == null ? null : json["customer_code"],
        content: json["content"] == null ? null : json["content"],
        contractCode:
            json["contract_code"] == null ? null : json["contract_code"],
        supplierId: json["supplier_id"] == null ? null : json["supplier_id"],
        supplierName:
            json["supplier_name"] == null ? null : json["supplier_name"],
        iconSupplier:
            json["icon_supplier"] == null ? null : json["icon_supplier"],
        companyId: json["company_id"] == null ? null : json["company_id"],
        transId: json["trans_no"] == null ? null : json["trans_no"],
        type: json["type"] == null ? "" : json["type"],
        urlCallBack: json["url_callBack"] == null ? null : json["url_callBack"],
        deepLinkVietQrCode: json["code"] == null ? null : json["code"],
        toAccount: json["to_account"] == null ? "" : json["to_account"],
        toCard: json["to_card"] == null ? null : json["to_card"],
        bankCode: json["bank_code"] == null ? null : json["bank_code"].toString(),
      );

  Map<String, dynamic> toJson() => {
        "amount": amount,
        "supplier_account": customerName,
        "customer_name": customerName,
        "customer_code": contractCode,
        "content": content,
        "contract_code": supplierId,
        "supplier_id": supplierName,
        "supplier_name": supplierAccount,
        "icon_supplier": iconSupplier,
        "company_id": companyId,
        "trans_no": transId,
        "type": type,
        "url_callBack": urlCallBack,
      };
}
