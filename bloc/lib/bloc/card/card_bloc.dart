import 'dart:async';

import 'package:collection/collection.dart' show IterableExtension;
import 'package:common/model/loading_event.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_bloc/bloc/model/card/card_model.dart';
import 'package:ksb_bloc/bloc/model/card/issue_vr_card_model.dart';
import 'package:rxdart/rxdart.dart';

import 'mixin_common_function.dart';

class CardBloc extends BaseBloc with CommonFunction {
  CardBloc(Repository repository, Preferences preferences, Session session)
      : super(repository, preferences, session);

  final _listCard = BehaviorSubject<LoadingWidgetModel>();

  Stream<LoadingWidgetModel> get listCardStream => _listCard.stream;

  final _cardDetail = BehaviorSubject<LoadingWidgetModel?>();

  Stream<LoadingWidgetModel?> get cardDetailStream => _cardDetail.stream;

  final _cardFunction = BehaviorSubject<List<CardFunction>>();

  Stream<List<CardFunction>> get cardFunctionsStream => _cardFunction.stream;

  final _listCardCredit = BehaviorSubject<LoadingWidgetModel>();

  Stream<LoadingWidgetModel> get listCardCreditRequestStream =>
      _listCardCredit.stream;

  final _activeCard = BehaviorSubject<LoadingWidgetModel>();

  Stream<LoadingWidgetModel> get activeCardStream => _activeCard.stream;

  String? cifNumber;
  String? idCardNumber;
  CardModel? currentCard;

  final _currentCard = BehaviorSubject<LoadingWidgetModel?>();

  Stream<LoadingWidgetModel?> get currentCardStream => _currentCard.stream;

  Sink<LoadingWidgetModel?> get currentCardSink => _currentCard.sink;

  final _activeVRCard = BehaviorSubject<IssueVRCardModel?>();

  Stream<IssueVRCardModel?> get activeVRCardStream => _activeVRCard.stream;

  List<CardModel> get getCards =>
      (_listCard.valueOrNull?.data ?? []) as List<CardModel>;

  @override
  void init() async {
    super.init();
    cifNumber = await preferences.cifNumber;
    idCardNumber = await preferences.idCardNumber;
    reload();
  }

  @override
  void reload() {
    super.reload();
    showScreenLoading(maxApi: 1);
    getListCard();
  }

  @override
  void dispose() {
    _listCard.close();
    _cardDetail.close();
    _cardFunction.close();
    _activeVRCard.close();
    _listCardCredit.close();
    _activeCard.close();
    _currentCard.close();
    super.dispose();
  }

  Future<int> indexCardToJump(String refCardId) async {
    await getListCard();
    if (refCardId.isNotEmpty) {
      final indexCard = getCards.indexWhere(
        (element) => element.refCardId == refCardId,
      );
      if (indexCard == 0) {
        setCurrentCard(getCards.first);
      }
      return indexCard;
    }
    return 0;
  }

  setCurrentCard(CardModel? value) {
    currentCard = value;
    LoadingResult.success(behavior: _currentCard, data: value);

    getDetailCard();
    getFunctionCard(isVirtualCard: value?.isNonePhysicCard ?? false);
  }

  Future<void> getListCard() async {
    try {
      showLoading();
      GetCardsRequest request = GetCardsRequest((builder) {});

      // Đợi kết quả từ API
      final res = await repository.bankApi!
          .getCardApi()
          .getCards(getCardsRequest: request);

      List<CardModel> cards = [];
      if (res.data?.data?.cards != null) {
        cards = res.data?.data?.cards
                ?.map((card) => CardModel.fromResponse(card))
                .toList() ??
            [];
      }

      if (cards.length > 0) {
        if (currentCard != null) {
          final card = cards
              .firstWhere((element) => currentCard?.cardNo == element.cardNo);
          currentCard?.statusCode = card.statusCode;
        }
        setCurrentCard(currentCard ?? cards.first);
      }

      LoadingResult.success(behavior: _listCard, data: cards);
    } catch (err) {
      handlerApiError(err);
    } finally {
      completeLoading();
    }
  }

  getDetailCard({bool? isDetail}) {
    final value = currentCard;
    final cardId = value?.cardId;
    if (isDetail != null && isDetail == true) {
      value?.hasDetail = !isDetail;
    }
    if (value?.hasDetail == true) {
      LoadingResult.success(
        behavior: _listCard,
        data: _listCard.valueOrNull?.data,
      );
      return;
    }

    showAreaLoading(behavior: _cardDetail);
    GetDetailCardRequest request = GetDetailCardRequest((builder) {
      builder.cifNo = cifNumber;
      builder.productTypeCode = value?.productTypeCode;
      builder.cardNo = value?.cardNo;
      builder.refCardId = value?.refCardId;
    });
    repository.bankApi!
        .getCardApi()
        .getCardDetail(getDetailCardRequest: request)
        .then((res) {
      final data = res.data?.data?.cardDetail;
      if (data != null && currentCard != null) {
        final cardDetail =
            CardModel.fromResponseDetail(res: data, card: currentCard!);
        LoadingResult.success(behavior: _cardDetail, data: cardDetail);
        _updateCardDetail(cardId, data);
      }
    }).catchError((err) {
      handlerApiError(err, behavior: _cardDetail);
    }).whenComplete(() => completeLoading());
  }

  _updateCardDetail(cardId, CardDetail model) {
    final cards = _listCard.valueOrNull?.data;
    if (cards != null && cards is Iterable<CardModel> && cards.isNotEmpty) {
      final card =
          cards.firstWhereOrNull((element) => element.cardId == cardId);
      if (card != null) {
        card.mergeResponseDetail(model);
        LoadingResult.success(behavior: _listCard, data: cards);
      }
    }
  }

  getFunctionCard({bool? isVirtualCard}) {
    final cardId = currentCard?.cardId;

    if (currentCard?.hasFunctions == true) {
      LoadingResult.success(
        behavior: _listCard,
        data: _listCard.valueOrNull?.data,
      );
      return;
    }

    if (currentCard?.cardCredit == null) {
      GetFunctionCardsRequest request = GetFunctionCardsRequest((builder) {
        builder.productTypeCode = currentCard?.productTypeCode;
        builder.virtualCard = isVirtualCard;
      });
      repository.bankApi!
          .getCardApi()
          .getFunctions(getFunctionCardsRequest: request)
          .then((res) {
        final data = res.data?.data?.functions
                ?.map((func) => CardFunction.fromResponse(func))
                .toList() ??
            [];
        _cardFunction.add(data);
        _updateCardFunction(cardId, data);
      }).catchError((err) {
        handlerApiError(err);
      }).whenComplete(() => completeLoading());
    }
  }

  _updateCardFunction(cardId, List<CardFunction> function) {
    final cards = _listCard.valueOrNull?.data;
    if (cards != null && cards is Iterable<CardModel> && cards.isNotEmpty) {
      final card =
          cards.firstWhereOrNull((element) => element.cardId == cardId);
      if (card != null) {
        card.setFunctions(function);
        LoadingResult.success(behavior: _listCard, data: cards);
      }
    }
  }

  // Khoa the
  Future<bool> setLockCard({String? otp}) async {
    var completer = new Completer<bool>();
    // showScreenLoading(isSubmit: true);
    // final softOtp = VerifySoftOtpRequest((builder) {
    //   builder.otp = otp;
    // });
    showAreaLoading(behavior: _currentCard);
    final otpRequestBuilder = VerifySoftOtpRequestBuilder();
    otpRequestBuilder.otp = otp;
    repository.bankApi!.getCardApi().lockCard(
        lockCardRequest: LockCardRequest((builder) {
      builder.cifNo = cifNumber;
      builder.productTypeCode = currentCard?.productTypeCode;
      builder.cardNo = currentCard?.cardNo;
      builder.refCardId = currentCard?.refCardId;
      builder.verifySoftOtp = otpRequestBuilder;
    })).then((res) {
      currentCard?.isLocked = true;
      currentCard?.statusCode = CardModel.LOCK;
      LoadingResult.success(behavior: _currentCard, data: currentCard);
      completer.complete(true);
    }).catchError((err) {
      completer.complete(false);
      handlerApiError(err);
    }).whenComplete(() {
      // completeScreenLoading(isSubmit: true);
      completeLoading();
    });
    return completer.future;
  }

  // Mo the
  Future<bool> setUnLockCard({String? otp}) async {
    var completer = new Completer<bool>();
    showAreaLoading(behavior: _currentCard);
    final otpRequestBuilder = VerifySoftOtpRequestBuilder();
    otpRequestBuilder.otp = otp;

    repository.bankApi!.getCardApi().unlockCard(
        unlockCardRequest: UnlockCardRequest((builder) {
      builder.cifNo = cifNumber;
      builder.productTypeCode = currentCard?.productTypeCode;
      builder.cardNo = currentCard?.cardNo;
      builder.refCardId = currentCard?.refCardId;
      builder.verifySoftOtp = otpRequestBuilder;
    })).then((res) {
      currentCard?.isLocked = false;
      currentCard?.statusCode = '';
      LoadingResult.success(behavior: _currentCard, data: currentCard);
      completer.complete(true);
    }).catchError((err) {
      setLockCardCancel();
      completer.complete(false);
      handlerApiError(err);
    }).whenComplete(() {
      completeLoading();
    });
    return completer.future;
  }

  setLockCardCancel() {
    _cardDetail.safeAdd(_cardDetail.valueOrNull);
  }

  checkActivatedCard(String fourLastDigit, String idNbr) async {
    var completer = new Completer<CheckCardForActiveResponse?>();
    showLoading();
    repository.bankApi!
        .getCardApi()
        .checkCardForActive(idNbr: idNbr, fourLastDigit: fourLastDigit)
        .then((value) {
      successResponse(value);
      completer.complete(value.data?.data);
    }).catchError((err) {
      handlerApiError(err);
      completer.complete(null);
    }).whenComplete(() => completeLoading());
    return completer.future;
  }

  activeCreditCard(CheckCardForActiveResponse card, {String? otp}) async {
    var completer = new Completer<ActiveCreditCardResponse?>();
    final otpRequestBuilder = VerifySoftOtpRequestBuilder();
    otpRequestBuilder.otp = otp;
    showLoading();
    repository.bankApi!.getCardApi().activeCreditCard(
      activeCreditCardRequest: ActiveCreditCardRequest((builder) {
        builder.cardNo = card.cardNo;
        builder.cardId = card.cardId;
        builder.idNbr = card.idNbr;
        builder.verifySoftOtp = otpRequestBuilder;
      }),
    ).then((value) {
      successResponse(value);
      completer.complete(value.data?.data);
    }).catchError((err) {
      handlerApiError(err);
      completer.complete(null);
    }).whenComplete(() => completeLoading());
    return completer.future;
  }

  activeDebitCard(CheckCardForActiveResponse card, {String? otp}) async {
    var completer = new Completer<ActiveDebitCardResponse?>();
    final otpRequestBuilder = VerifySoftOtpRequestBuilder();
    otpRequestBuilder.otp = otp;
    showLoading();
    repository.bankApi!.getCardApi().activeDebitCard(
      activeDebitCardRequest: ActiveDebitCardRequest((builder) {
        builder.cardId = card.cardId;
        builder.cardNo = card.cardNo;
        builder.idNbr = card.idNbr;
        builder.verifySoftOtp = otpRequestBuilder;
      }),
    ).then((value) {
      successResponse(value);
      completer.complete(value.data?.data);
    }).catchError((err) {
      handlerApiError(err);
      completer.complete(null);
    }).whenComplete(() => completeLoading());
    return completer.future;
  }

  Future<IssueVRCardModel?> activeVirtualCard(String cardId, String otp) async {
    showScreenLoading(isSubmit: true);

    var completer = new Completer<IssueVRCardModel?>();
    final otpRequestBuilder = VerifySoftOtpRequestBuilder();
    otpRequestBuilder.otp = otp;
    final ActiveVirtualCardRequest request = ActiveVirtualCardRequest((b) {
      b.cardId = cardId;
      b.verifySoftOtp = otpRequestBuilder;
    });

    repository.bankApi!
        .getCardApi()
        .activeVirtualCard(activeVirtualCardRequest: request)
        .then((value) {
      if (value.data?.data != null) {
        final result = IssueVRCardModel.fromActiveVRCardResponse(
          value.data?.data,
        );
        completer.complete(result);
      }
    }).catchError((error) {
      handlerApiError(error);
      completer.complete(null);
    }).whenComplete(() => completeScreenLoading(isSubmit: true));
    return completer.future;
  }
}
