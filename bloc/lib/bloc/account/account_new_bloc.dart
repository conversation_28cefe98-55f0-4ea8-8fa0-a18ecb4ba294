import 'package:ksb_bloc/bloc.dart';
import 'package:common/model/loading_event.dart';
import 'package:common/model/strapi/strapi_models.dart';
import 'package:ksb_bloc/bloc/base_authenticate_bloc.dart';
import 'package:ksb_common/model/account/account_open_model.dart';
import 'package:rxdart/subjects.dart';

class AccountNewBloc extends BaseBloc with BaseAuthBloc {
  AccountNewBloc(
      Repository repository, Preferences preferences, Session session)
      : super(repository, preferences, session);

  final _openAccountModel = BehaviorSubject<OpenAccountModel>();
  OpenAccountModel openAccountModel = OpenAccountModel()..empty();

  Stream<OpenAccountModel> get modelStream => _openAccountModel.stream;

  Sink<OpenAccountModel> get modetSink => _openAccountModel.sink;

  OpenAccountModel get model => openAccountModel;

  Stream<OpenAccountModel> get data => _openAccountModel.stream;

  final _checkVipAccount = BehaviorSubject<LoadingWidgetModel>();

  Stream<LoadingWidgetModel> get checkVipAccount => _checkVipAccount.stream;

  final _queryVipAccount = BehaviorSubject<LoadingWidgetModel>();

  Stream<LoadingWidgetModel> get queryVipAccount => _queryVipAccount.stream;

  final _queryOptionAccount = BehaviorSubject<LoadingWidgetModel>();

  Stream<LoadingWidgetModel> get queryOptionAccount =>
      _queryOptionAccount.stream;

  final _openVipAccount = BehaviorSubject<LoadingWidgetModel>();

  Stream<LoadingWidgetModel> get openVipAccount => _openVipAccount.stream;

  final _openNoVipAccount = BehaviorSubject<LoadingWidgetModel>();

  Stream<LoadingWidgetModel> get openNoVipAccount => _openNoVipAccount.stream;

  final _showNote = BehaviorSubject<bool>()..add(false);

  Stream<bool> get showNote => _showNote.stream;

  final _disableButton = BehaviorSubject<bool>()..add(false);

  Stream<bool> get disableButtonStream => _disableButton.stream;

  String? cifNumber;
  String? fullName;
  List<OpenAccountModel> modelList = [];
  int page = 1;
  int size = 30;
  bool isLoadMore = true;

  @override
  void init() async {
    super.init();
    cifNumber = await preferences.cifNumber;
    fullName = preferences.fullName;
    fullName = TiengViet.parse(fullName ?? "").toLowerCase();
  }

  @override
  void reload() {
    super.reload();
  }

  setAccountNumber(AccountModel model) {
    openAccountModel.accountNo = model.accountNumber;
    openAccountModel.accountName = model.accountName;
    openAccountModel.customerName = model.aliasname;
    notifyModelChange();
  }

  setVipAccount(OpenAccountModel model) {
    openAccountModel = model;
    notifyModelChange();
  }

  resetModel() {
    if (openAccountModel.vipAccountNo != null &&
        openAccountModel.vipAccountNo!.isNotEmpty) {
      openAccountModel.vipAccountNo = '';
      openAccountModel.feeAmountStr = '';
      openAccountModel.feeAmount = 0;
      openAccountModel.aliasName = '';
      notifyModelChange();
    }
  }

  notifyModelChange() {
    safeAddData(_openAccountModel, openAccountModel);
  }

  setNote({String? value}) {
    if (value != null && value.length > 0)
      _showNote.add(true);
    else
      _showNote.add(false);
  }

  setAliasName({String? aliasName}) {
    openAccountModel.aliasName = aliasName;
  }

  Future<OpenAccountModel?> checkVipAccountNo(
      {required String vipAccountNo}) async {
    showLoading();
    return repository.bankApi!
        .getAccountControllerApi()
        .checkVipAccountV1(accountNo: vipAccountNo)
        .then((res) {
      final data = res.data?.data;
      if (data != null) {
        final model = OpenAccountModel.fromResponse(data);
        LoadingResult.success(data: model, behavior: _checkVipAccount);
        return model;
      }
    }).catchError((error) {
      handlerApiError(error);
    }).whenComplete(() {
      completeLoading();
    });
  }

  queryAccount({String? filter, int? tabIndex}) {
    page = 1;
    LoadingResult.success(data: [], behavior: _queryOptionAccount);
    LoadingResult.success(data: [], behavior: _queryVipAccount);
    showLoading();
    showAreaLoading(behavior: _queryOptionAccount);
    showAreaLoading(behavior: _queryVipAccount);
    if (tabIndex == 0) {
      queryListOptionAccount(stringQuery: filter);
    } else {
      queryListVipAccountNo(stringQuery: filter);
    }
  }

  queryListOptionAccount({String? stringQuery}) {
    repository.bankApi!
        .getAccountControllerApi()
        .searchAccountBeautifulNumber(
            birthdayFlg: 0, keySearch: stringQuery, mobileFlg: 0)
        .then((res) {
      final data = res.data?.data?.accountDtoList ?? <AccountNumberInfo>[];
      final model = data
          .map(
            (e) => OpenAccountModel.fromResponseOption(e),
          )
          .toList();
      LoadingResult.success(data: model, behavior: _queryOptionAccount);
    }).catchError((error) {
      handlerApiError(error, behavior: _queryOptionAccount);
    }).whenComplete(() {
      completeScreenLoading(isSubmit: true);
      completeLoading();
    });
  }

  queryPublicAccount({
    String? stringQuery,
    int birthdayFlg = 0,
    int mobileFlg = 0,
  }) {
    repository.bankApi!
        .getAccountControllerApi()
        .searchAccountBeautifulNumberEKYC(
            birthdayFlg: birthdayFlg,
            keySearch: stringQuery,
            mobileFlg: mobileFlg)
        .then((res) {
      final data = res.data?.data?.accountDtoList ?? <AccountNumberInfo>[];
      final model = data
          .map(
            (e) => OpenAccountModel.fromResponseOption(e),
          )
          .toList();
      LoadingResult.success(data: model, behavior: _queryOptionAccount);
    }).catchError((error) {
      handlerApiError(error, behavior: _queryOptionAccount);
    }).whenComplete(() {
      completeScreenLoading(isSubmit: true);
    });
  }

  queryListVipAccountNo({String? stringQuery}) async {
    repository.bankApi!
        .getAccountControllerApi()
        .getVipAccountList(
            filterAccountNo: stringQuery,
            page: page.toString(),
            size: size.toString())
        .then((res) {
      final data = res.data?.data?.vipAccountList ?? <VipAccountInfoDto>[];
      List<VipAccountInfoDto> result = data.toList();
      if (result.length < size) {
        isLoadMore = false;
      }
      List<OpenAccountModel> currentData =
          _queryVipAccount.valueOrNull?.data ?? <OpenAccountModel>[];
      List<OpenAccountModel> newData = result
          .map<OpenAccountModel>(
            (e) => OpenAccountModel.fromResponseDTO(e),
          )
          .toList();
      currentData.addAll(newData);
      LoadingResult.success(data: currentData, behavior: _queryVipAccount);
    }).catchError((error) {
      handlerApiError(error, behavior: _queryVipAccount);
    }).whenComplete(() {
      completeScreenLoading(isSubmit: true);
      completeLoading();
    });
  }

  Future<OpenAccountModel?> payVipAccountNo({
    String? otp,
    double? amount,
    String? referralCode,
  }) async {
    showLoading();
    final otpRequestBuilder = VerifySoftOtpRequestBuilder();
    otpRequestBuilder.otp = otp;
    final openRequest = OpenVipAccountRequest((builder) {
      builder.accountNo = openAccountModel.vipAccountNo;
      builder.fromAccount = openAccountModel.accountNo;
      builder.aliasName = openAccountModel.aliasName;
      builder.verifySoftOtp = otpRequestBuilder;
      builder.freeAmount = toInt(amount);
      builder.referralCode = referralCode;
      builder.transactionNo = transactionNumber;
    });
    return repository.bankApi!
        .getAccountControllerApi()
        .openVipAccount(
          openVipAccountRequest: openRequest,
        )
        .then((res) {
      final data = res.data?.data;
      if (data != null) {
        final acc = OpenAccountModel.fromResponseCreate(data);
        if (acc.accountName!.isEmpty) {
          acc.accountName = fullName;
          notifyModelChange();
        }
        LoadingResult.success(behavior: _openVipAccount, data: acc);
        return acc;
      }
    }).catchError((error) {
      handlerApiError(error);
    }).whenComplete(() {
      completeLoading();
    });
  }

  Future<OpenAccountModel?> payVipNoVipAccount() async {
    showLoading();
    final openRequest = OpenAccountRequest((builder) {
      builder.bankCif = cifNumber;
      builder.branchCode = "7";
      builder.minor = "G001";
      builder.wrnMessage = "TK TGTT mo truc tuyen tren MOBILE";
      builder.alias = openAccountModel.aliasName;
    });
    return repository.bankApi!
        .getAccountControllerApi()
        .openAccount(openAccountRequest: openRequest)
        .then((res) {
      final data = res.data?.data;
      if (data != null) {
        final model = OpenAccountModel.fromResponseNoVipAccountCreate(data);
        if (model.accountName.isNullOrEmpty) {
          model.accountName = fullName;
          notifyModelChange();
        }
        LoadingResult.success(data: model, behavior: _openNoVipAccount);
        return model;
      }
    }).catchError((error) {
      handlerApiError(error);
    }).whenComplete(() {
      completeLoading();
    });
  }

  Future<StrapiTerms?> getTermsAndConditions() {
    showLoading();
    return repository.strApi!.getTermsAndConditions(id: '5').then((value) {
      return value;
    }).catchError((e) {
      handlerApiError(e);
    }).whenComplete(() => completeLoading());
  }

  @override
  void dispose() {
    _openAccountModel.close();
    _queryVipAccount.close();
    _checkVipAccount.close();
    _openVipAccount.close();
    _disableButton.close();
    _openNoVipAccount.close();
    _queryOptionAccount.close();
    super.dispose();
  }
}
