import 'dart:async';

import 'package:ksb_bloc/bloc.dart';

import 'package:rxdart/rxdart.dart';

class TransferTuitionFeeBloc extends BaseBloc {
  TransferTuitionFeeBloc(
      Repository repository, Preferences preferences, Session session)
      : super(repository, preferences, session);

  final _schoolProvidersBehaviorSubject = BehaviorSubject<List<ProviderDto>>();
  Stream<List<ProviderDto>> get schoolProvidersStream =>
      _schoolProvidersBehaviorSubject.stream;
  List<ProviderDto>? get schoolProviders =>
      _schoolProvidersBehaviorSubject.valueOrNull;

  final _studentInfoBehaviorSubject = BehaviorSubject<TuitionFeeDto?>();
  Stream<TuitionFeeDto?> get studentInfoStream =>
      _studentInfoBehaviorSubject.stream;

  @override
  void dispose() {
    _schoolProvidersBehaviorSubject.close();
    _studentInfoBehaviorSubject.close();
    super.dispose();
  }

  void showLoadingScreen() => showLoading();

  void completeLoadingScreen() => completeLoading();

  void resetTransaction() => _studentInfoBehaviorSubject.add(null);

  void getSchoolProviders() async {
    return repository.bankApi!
        .getTuitionFeeControllerImplApi()
        .getProviderList()
        .then((response) {
      safeAddData(
          _schoolProvidersBehaviorSubject, response.data?.data?.toList());
    }).catchError((e) {
      handlerApiError(e, behavior: _schoolProvidersBehaviorSubject);
    }).whenComplete(completeLoading);
  }

  void getStudentInfo(
      {required String providerCode, required String studentCode}) async {
    showLoading();
    _studentInfoBehaviorSubject.add(null);
    return repository.bankApi!
        .getTuitionFeeControllerImplApi()
        .getStudentTuitionFee(
            providerCode: providerCode, studentCode: studentCode)
        .then((response) {
      safeAddData(_studentInfoBehaviorSubject, response.data?.data);
    }).catchError((e) {
      handlerApiError(e);
    }).whenComplete(completeLoading);
  }
}
