import 'dart:convert';
import 'dart:io';

import 'package:common/environment.dart' as common;
import 'package:ekyc/di/injection.dart' as eKycInjector;
import 'package:ekyc_bloc/bloc/live_ness_bloc.dart' as eKycLiveNessBloc;
import 'package:ks_chat/repository/repository_chat.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_bloc/onesignal.dart';
import 'package:package_info/package_info.dart';
import 'package:rxdart/rxdart.dart';

class LoginBloc extends OtpVerifyBloc {
  LoginBloc(Repository repository, Preferences preferences, Session session,
      this.repositoryChat)
      : super(repository, preferences, session);
  RepositoryChat repositoryChat;

  final _username = BehaviorSubject<String?>();
  final _forceUpdateVersion = BehaviorSubject<ForceUpdateAppVersionResponse>();
  final _currentUser = BehaviorSubject<String>();
  final _password = BehaviorSubject<String?>();
  final _language = BehaviorSubject<String>();
  final _currentEnvironment = BehaviorSubject<Environment?>();

  Stream<String?> get usernameStream => _username.stream;

  Stream<ForceUpdateAppVersionResponse> get forceUpdateVersionStream =>
      _forceUpdateVersion.stream;

  Stream<String?> get passwordStream => _password.stream;

  Stream<String> get languageStream => _language.stream;

  Stream<String> get currentUserStream => _currentUser.stream;

  String? get currentUser => _currentUser.valueOrNull;

  Stream<Environment?> get currentEnvironment => _currentEnvironment.stream;

  Sink<String?> get usernameSink => _username.sink;

  Sink<String?> get passwordSink => _password.sink;

  Sink<String> get languageSink => _language.sink;

  String? get username => _username.valueOrNull;

  String? get password => _password.valueOrNull;

  bool? get enableBiometrics => preferences.userSetting.isBiometrics;

  Preferences get prefs => preferences;

  @override
  void init() async {
    super.init();
    _language.safeAdd(preferences.languageCode);
    _username.safeAdd(preferences.userName);
    _currentUser.safeAdd(preferences.fullName);
    Environment? currentEnv = await preferences.getEnvironment();
    _currentEnvironment.safeAdd(currentEnv);
    repositoryChat.setApiUri(currentEnv?.chatServerUrl ?? Uri());
    repositoryChat.setSocketUrl(currentEnv?.chatSocketServer ?? '');
    common.Environment.staging()
        .setChatServerUrl(currentEnv?.chatServerUrl.toString() ?? '');
    common.Environment.staging()
        .setChatSocketUrl(currentEnv?.chatSocketServer ?? '');
    checkForceUpdateApp();
  }

  String? get getProxy => _currentEnvironment.valueOrNull?.proxy;

  @override
  dispose() {
    _username.close();
    _password.close();
    _currentEnvironment.close();
    _language.close();
    _currentUser.close();
    _forceUpdateVersion.close();
    super.dispose();
  }

  bool validatePassword(String value) {
    String pattern =
        r'(([àáạảãâầấậẩẫăằắặẳẵÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴèéẹẻẽêềếệểễÈÉẸẺẼÊỀẾỆỂỄòóọỏõôồốộổỗơờớợởỡÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠùúụủũưừứựửữÙÚỤỦŨƯỪỨỰỬỮìíịỉĩÌÍỊỈĨđĐỳýỵỷỹỲÝỴỶỸ]))';
    RegExp regex = RegExp(pattern);
    if (regex.hasMatch(value)) {
      return true;
    }
    return false;
  }

  login() {
    showScreenLoading(maxApi: 1, isSubmit: true);

    return session
        .signIn(
          username: _username.valueOrNull ?? '',
          password: _password.valueOrNull ?? '',
        )
        .then((token) => repository.profileApi!
                .getUserBankInfoResourceApi()
                .getUserBankInfo(
                  headers: getHttpHeader(
                    token: token?.accessTokenRequest,
                    language: preferences.languageCode,
                  ),
                )
                .then(
              (infoBank) async {
                final bankInfo = infoBank.data?.data;
                preferences
                  ..setUserId(bankInfo?.userId)
                  ..setAccountNumber(bankInfo?.accountNumber)
                  ..setIdCardNumber(bankInfo?.idCardNumber)
                  ..setCifNumber(bankInfo?.cifNumber);

                await _loginStrApi();

                return repository.profileApi!
                    .getUserQueryResourceApi()
                    .getUserInfoV2(
                      headers: getHttpHeader(
                        token: token?.accessTokenRequest,
                        language: preferences.languageCode,
                      ),
                    );
              },
            ).then((response) {
              final user = response.data?.data;
              if (user != null) {
                String info =
                    json.encode(ProfileInfo.fromResponseV2(user).toJson());
                preferences
                  ..setUserName(user.username)
                  ..setFullName(user.fullName)
                  ..setProfileInfo(info);
              }

              successResponse(response, data: token?.requireChangePass);
            }))
        .catchError((error) {
      handlerApiError(error);
    }).whenComplete(() {
      completeScreenLoading(isSubmit: true);
    });
  }

  refreshToken() async {
    showScreenLoading(maxApi: 1, isSubmit: true);
    try {
      final token = await session.getToken();
      final newToken = await session.refreshToken(token);
      if (newToken != null) {
        final tokenStrApi = await preferences.getTokenStrApi();
        if (tokenStrApi == null || tokenStrApi.isEmpty) {
          await _loginStrApi();
        }
        successResponse(BaseResponseModel(statusCode: 200));
        return newToken;
      } else {
        successResponse(
            BaseResponseModel(statusCode: ProfileStatusCode.INVALID_TOKEN));
      }
    } catch (e) {
      handlerApiError(e, autoRefresh: false);
    } finally {
      completeScreenLoading(isSubmit: true);
    }
  }

  Future _loginStrApi() async {
    try {
      await session.loginStrApi().timeout(
        Duration(seconds: 30),
        onTimeout: () {
          logger.e('loginStrApi timeout');
          return null;
        },
      );
    } catch (e) {
      logger.e(e);
    }
  }

  Future changeEnvironment(String value) async {
    Environment? newEnv = Environment.serverNameToEnv[value];
    if (newEnv != null) {
      _currentEnvironment.safeAdd(newEnv);
      repositoryChat.setApiUri(newEnv.chatServerUrl);
      repositoryChat.setSocketUrl(newEnv.chatSocketServer);
      common.Environment.staging()
          .setChatServerUrl(newEnv.chatServerUrl.toString());
      common.Environment.staging().setChatSocketUrl(newEnv.chatSocketServer);
      eKycInjector.Injection.injector
          .get<eKycLiveNessBloc.LiveNessBloc>()
          .changeEnv(baseUrl: newEnv.apiAi, newToken: newEnv.liveNessToken);
      repository.profileApi!.dio.options.baseUrl = '${newEnv.endPoint}/profile';
      repository.bankApi!.dio.options.baseUrl = '${newEnv.endPoint}/smartbank';
      repository.mediaApi!.dio.options.baseUrl = '${newEnv.endPoint}/media';
      repository.stocksApi!.dio.options.baseUrl = '${newEnv.endPoint}/stocks';
      repository.notificationApi!.dio.options.baseUrl =
          '${newEnv.endPoint}/notification';
      repository.stmApi!.dio.options.baseUrl = '${newEnv.endPoint}/stm';
      repository.strApi!.dio.options.baseUrl = newEnv.strApiUrl;
      repository.loyaltyApi!.dio.options.baseUrl = '${newEnv.endPoint}/loyalty';
      repository.maintenanceApi!.dio.options.baseUrl =
          '${newEnv.endPoint}/maintenance';
      repository.crmApi!.dio.options.baseUrl = '${newEnv.endPoint}/crm';
      repository.umeeApiCollab!.dio.options.baseUrl =
          '${newEnv.endPoint}/collab';
      repository.umeeApiCredit!.dio.options.baseUrl =
          '${newEnv.endPoint}/credit';
      repository.nicknameApi!.dio.options.baseUrl =
          '${newEnv.endPoint}/nickname';
      await clearAllUser();
      initPlatformState(newEnv);
    }
  }

  Future<bool> clearAllUser() async {
    return session.clearAllUser().then((value) {
      return Future.value(true);
    }).catchError((error) {
      handlerApiError(error);
      return Future.value(false);
    }).whenComplete(() {
      _username.safeAdd(null);
      _password.safeAdd(null);
      _currentUser.safeAdd("");
      if (_currentEnvironment.valueOrNull != null) {
        preferences.setEnvironment(_currentEnvironment.valueOrNull!.toJson());
      }
    });
  }

  Future<bool> logout() {
    showLoading();
    return disablePush().then((value) {
      session.logout();
      return clearAllUser().then((value) => Future.value(true));
    }).catchError((error) {
      handlerApiError(error);
      return Future.value(false);
    }).whenComplete(() {
      completeLoading();
    });
  }

  void changeProxy(String text) {
    Environment? env = _currentEnvironment.valueOrNull?..proxyValue = text;
    if (env != null) {
      preferences.setEnvironment(env.toJson());
    }
  }

  Future<void> disablePush() async {
    try {
      String? token = await preferences.getPushTokenOnesignal();
      var authInfo = await repositoryChat.authApi.getAuthInfo();
      if (authInfo != null && !token.isNullOrEmpty) {
        await repositoryChat.roomApi.deletePush(token!, authInfo);
      }
    } catch (e) {
      handlerApiError(e);
    } finally {
      completeLoading();
    }
  }

  void checkForceUpdateApp() {
    PackageInfo.fromPlatform()
        .then((version) => session
                .getDeviceInfo()
                .then((device) => repository.maintenanceApi!
                    .getAppVersionSettingControllerApi()
                    .checkAppVersion(
                        device: Platform.isAndroid
                            ? "ANDROID"
                            : Platform.isIOS
                                ? "IOS"
                                : "",
                        version: version.version))
                .then((res) {
              final data = res.data?.data;
              if (data != null) {
                _forceUpdateVersion.safeAdd(data);
              }
            }))
        .catchError((e) {
      handlerApiError(e);
    });
  }
}
