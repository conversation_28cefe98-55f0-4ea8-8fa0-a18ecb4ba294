import 'dart:async';

import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_bloc/bloc/model/cash_withdrawal/address_model.dart';
import 'package:ksb_bloc/bloc/terms_and_conditions/terms_and_conditions_bloc.dart';
import 'package:rxdart/rxdart.dart';

class AccountCreateBloc extends TermsAndConditionsBloc {
  AccountCreateBloc(
      Repository repository, Preferences preferences, Session session)
      : super(repository, preferences, session);
  final _phoneNumber = BehaviorSubject<String>();
  final _email = BehaviorSubject<String?>();
  final _password = BehaviorSubject<String>();
  final _rePassword = BehaviorSubject<String>();
  final _validatePassword = BehaviorSubject<bool>();
  final _validateEmail = BehaviorSubject<bool>();
  final _validateRePassword = BehaviorSubject<bool?>();
  final _createStatus = BehaviorSubject<bool>();
  final _accountUser = BehaviorSubject<AccountModel>();
  final _districtCode = BehaviorSubject<String?>();
  final _wardCode = BehaviorSubject<String?>();
  final _streetFull = BehaviorSubject<String?>();
  final _coreProvinceCode = BehaviorSubject<String?>();
  final _coreDistrictCode = BehaviorSubject<String?>();
  final _branchCode = BehaviorSubject<String?>();
  final _provinceCode = BehaviorSubject<String?>();
  final _validateButton = BehaviorSubject<bool>();

  Stream<String> get phoneNumberStream => _phoneNumber.stream;

  Stream<String?> get emailStream => _email.stream;

  Stream<String> get passwordStream => _password.stream;

  Stream<String> get rePasswordStream => _rePassword.stream;

  Stream<bool> get validatePasswordStream => _validatePassword.stream;

  Stream<bool> get validateEmailStream => _validateEmail.stream;

  Stream<bool?> get validateRePasswordStream => _validateRePassword.stream;

  Stream<bool> get createStatusStream => _createStatus.stream;

  Stream<AccountModel?> get accountUserStream => _accountUser.stream;

  Stream<bool> get validateButton => _validateButton.stream;

  Sink<String?> get districtCodeSink => _districtCode.sink;

  Sink<String?> get wardCodeSink => _wardCode.sink;

  Sink<String?> get streetFullSink => _streetFull.sink;

  Stream<String?> get branchCodeStream => _branchCode.stream;

  Sink<String?> get branchCodeSink => _branchCode.sink;

  Stream<String?> get provinceCodeStream => _provinceCode.stream;

  Stream<String?> get coreProvinceCodeStream => _coreProvinceCode.stream;

  Stream<String?> get coreDistrictCodeStream => _coreDistrictCode.stream;

  Sink<String?> get provinceCodeSink => _provinceCode.sink;

  Sink<String> get phoneNumberSink => _phoneNumber.sink;

  Sink<String?> get emailSink => _email.sink;

  Sink<String> get passwordSink => _password.sink;

  Sink<String> get rePasswordSink => _rePassword.sink;

  Sink<bool> get createStatusSink => _createStatus.sink;

  String? get email => _email.valueOrNull;

  String? get password => _password.valueOrNull;

  String? get rePassword => _rePassword.valueOrNull;
  IdentityInfo? info;

  mergerCode({
    AddressModel? addressModel,
    String? branchCode,
    IdentityInfo? identityInfo,
  }) {
    if (checkText(addressModel?.provinceCode))
      provinceCodeSink.add(addressModel?.provinceCode);
    if (checkText(addressModel?.districtCode))
      districtCodeSink.add(addressModel?.districtCode);
    if (checkText(addressModel?.wardCode))
      wardCodeSink.add(addressModel?.wardCode);
    if (checkText(addressModel?.addressFull))
      streetFullSink.add(addressModel?.addressFull);
    if (checkText(addressModel?.coreProvinceCode))
      _coreProvinceCode.add(addressModel?.coreProvinceCode);
    if (checkText(addressModel?.coreDistrictCode))
      _coreDistrictCode.add(addressModel?.coreDistrictCode);
    if (checkText(branchCode)) branchCodeSink.add(branchCode);
    info = identityInfo;
  }

  bool checkText(String? value) {
    if (!value.isNullOrEmpty) return true;
    return false;
  }

  Future createAccount({String? otp, String? accountNumber}) async {
    showLoading();
    final referralCode = await preferences.getReferralCode();
    return repository.profileApi!
        .getUserCommandResourceApi()
        .createNewUserV1(
            registrationV1Request: RegistrationV1Request((builder) {
              builder.username = _phoneNumber.valueOrNull;
              builder.password = _password.valueOrNull;
              builder.email = _email.valueOrNull ?? "";
              builder.otp = otp;
              builder.branchCode = "";
              builder.referralCode = referralCode;
              builder.districtCode = _districtCode.valueOrNull;
              builder.provinceCode = _provinceCode.valueOrNull;
              builder.branchCode = _branchCode.valueOrNull;
              builder.wardCode = _wardCode.valueOrNull;
              builder.street = _streetFull.valueOrNull;
              builder.gender = info?.gender;
              builder.coreProvinceCode = _coreProvinceCode.valueOrNull;
              builder.coreDistrictCode = _coreDistrictCode.valueOrNull;
              builder.accountNo = accountNumber;
            }),
            headers: getHttpHeader(language: preferences.languageCode))
        .then((response) {
      successResponse(response);
      //if (otp != null) {
      preferences.setReferralCode('');
      final data = response.data?.data;
      _accountUser.add(AccountModel()
        ..accountName = data?.accountName
        ..usernamel = data?.userName
        ..accountNumber = data?.accountNumber
        ..accountType = data?.accountType
        ..cifNumber = data?.cifNumber
        ..issuedDate =
            data?.issuedDate != null ? data?.issuedDate!.toDateTime() : null
        ..branch = data?.branch);
    }).catchError((err) {
      handlerApiError(err);
    }).whenComplete(() {
      completeLoading();
    });
  }

  validateRePassword(String rePassword, String? password) {
    if (rePassword.isNullOrEmpty) {
      _validateRePassword.add(null);
    } else if (rePassword.isNotEmpty && rePassword == password) {
      _validateRePassword.add(true);
    } else {
      _validateRePassword.add(false);
    }
    validateBtn();
  }

  validateBtn() {
    if (_validatePassword.valueOrNull == true &&
        _validateEmail.valueOrNull == true &&
        _validateRePassword.valueOrNull == true)
      _validateButton.add(true);
    else
      _validateButton.add(false);
  }

  validatePassword(String password) {
    if (password.isNullOrEmpty) {
      _validatePassword.add(false);
    } else if (password.isNotEmpty && validateStructurePassword(password)) {
      _validatePassword.add(true);
    } else {
      _validatePassword.add(false);
    }
    validateBtn();
  }

  validateEmail(bool email) {
    _validateEmail.add(email);
    validateBtn();
  }

  @override
  void dispose() {
    _phoneNumber.close();
    _createStatus.close();
    _email.close();
    _password.close();
    _rePassword.close();
    _validatePassword.close();
    _validateRePassword.close();
    _accountUser.close();
    _validateEmail.close();
    _branchCode.close();
    _districtCode.close();
    _wardCode.close();
    _streetFull.close();
    _validateButton.close();
    super.dispose();
  }
}
