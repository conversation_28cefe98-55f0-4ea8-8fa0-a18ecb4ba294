import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_bloc/bloc/terms_and_conditions/terms_and_conditions_bloc.dart';
import 'package:common/model/strapi/strapi_models.dart';
import 'package:rxdart/rxdart.dart';

class InputPhoneBloc extends TermsAndConditionsBloc {
  InputPhoneBloc(
      Repository repository, Preferences preferences, Session session)
      : super(repository, preferences, session);
  final _phoneNumber = BehaviorSubject<String>();
  final _referral = BehaviorSubject<String>();
  final _validatePhoneNumber = BehaviorSubject<bool>();
  final _branchCode = BehaviorSubject<String?>();
  final _referralName = BehaviorSubject<String?>();
  final _referralInfo = BehaviorSubject<CheckReferralCodeResponse?>();

  Stream<String> get phoneNumberStream => _phoneNumber.stream;

  Stream<String> get referralStream => _referral.stream;

  Stream<String?> get referralNameStream => _referralName.stream;

  Stream<CheckReferralCodeResponse?> get referralInfoStream =>
      _referralInfo.stream;

  Sink<CheckReferralCodeResponse?> get referralInfoSink => _referralInfo.sink;

  Stream<bool> get validatePhoneNumberStream => _validatePhoneNumber.stream;

  Stream<String?> get branchCodeStream => _branchCode.stream;

  Sink<String> get phoneNumberSink => _phoneNumber.sink;

  Sink<String> get referralSink => _referral.sink;

  Sink<String?> get branchCodeSink => _branchCode.sink;

  Sink<bool> get validatePhoneNumberSink => _validatePhoneNumber.sink;

  String? get phoneNumber => _phoneNumber.valueOrNull;

  String? get referral => _referral.valueOrNull;

  String? get referralName => _referralName.valueOrNull;

  String? get branchCode => _branchCode.valueOrNull;

  bool hasBranchCode = false;

  final _terms = BehaviorSubject<StrapiTerms>();

  Stream<StrapiTerms> get termsStream => _terms.stream;

  @override
  void init() {
    super.init();
    validatePhoneNumber();
  }

  @override
  void dispose() {
    _phoneNumber.close();
    _validatePhoneNumber.close();
    _referral.close();
    _referralName.close();
    _terms.close();
    _branchCode.close();
    super.dispose();
  }

  getReferralInfo(String id) {
    showScreenLoading(maxApi: 1, isSubmit: true);
    return repository.loyaltyApi!
        .getReferralUserApi()
        .checkReferralCode(
          checkReferralCodeRequest: CheckReferralCodeRequest((builder) {
            builder.code = id;
          }),
          headers: getHttpHeader(language: preferences.languageCode),
        )
        .then((res) {
      final data = res.data?.data;
      if (data != null) {
        _referralInfo.safeAdd(data);
        _referralName.safeAdd(data.referrerUsername);
      }
    }).catchError((error) {
      handlerApiError(error);
      _referralName.safeAdd(null);
    }).whenComplete(() {
      completeScreenLoading(isSubmit: true);
    });
  }

  registerUser() async {
    showScreenLoading(maxApi: 1, isSubmit: true);
    final referral =
        _referral.valueOrNull.isNullOrEmpty ? null : _referral.value;
    final deviceId = (await session.getDeviceInfo())?.deviceId;
    preferences.setReferralCode(referral);
    return repository.profileApi!
        .getUserCommandResourceApi()
        .verifyUserIdentity(
            verifyIdentityRequest: VerifyIdentityRequest((builder) {
              builder.username = _phoneNumber.valueOrNull;
              builder.udid = deviceId;
              builder.referralCode = referral;
            }),
            headers: getHttpHeader(language: preferences.languageCode))
        .then((res) {
      return res.data?.success;
    }).catchError((error) {
      handlerApiError(error);
    }).whenComplete(() {
      completeScreenLoading(isSubmit: true);
    });
  }

  registerUserV1({bool? hasSupportNFC}) async {
    showScreenLoading(maxApi: 1, isSubmit: true);
    final referral = _referral.valueOrNull.isNullOrEmpty ? null : _referral.value;
    final deviceId = (await session.getDeviceInfo())?.deviceId;
    preferences.setReferralCode(referral);
    return repository.profileApi!
        .getUserCommandResourceApi()
        .verifyUserIdentityV1(
            verifyIdentityRequest: VerifyIdentityRequest((builder) {
              builder.username = _phoneNumber.valueOrNull;
              builder.udid = deviceId;
              builder.referralCode = referral;
              builder.hasSupportNFC = hasSupportNFC;
            }),
            headers: getHttpHeader(language: preferences.languageCode))
        .then((res) {
      if (res.data?.data?.branchCode.isNullOrEmpty == false) {
        _branchCode.safeAdd(res.data?.data?.branchCode);
        hasBranchCode = true;
      } else {
        hasBranchCode = false;
      }
      return res.data?.success;
    }).catchError((error) {
      handlerApiError(error);
    }).whenComplete(() {
      completeScreenLoading(isSubmit: true);
    });
  }

  validatePhoneNumber() {
    _phoneNumber.listen((value) {
      String pattern = r'(^(?:[+0]9)?[0-9]{10,12}$)';
      RegExp regExp = new RegExp(pattern);
      if (value.length == 0) {
        _validatePhoneNumber.safeAdd(false);
      } else if (value.length < 10 || value.length > 10) {
        _validatePhoneNumber.safeAdd(false);
      } else if (!regExp.hasMatch(value) || value[0] != "0") {
        _validatePhoneNumber.safeAdd(false);
      } else
        _validatePhoneNumber.safeAdd(true);
    });
  }

// Future<StrapiTerms> getTermsAndConditions({String id}) {
//   showScreenLoading(maxApi: 1, isSubmit: true);
//   return repository.strApi.getTermsAndConditions(id: '2').then((value) {
//     safeAddData(_terms, value);
//     return value;
//   }).catchError((e) {
//     handlerApiError(e);
//   }).whenComplete(() => completeScreenLoading(isSubmit: true));
// }
}
