name: ksb_bloc
description: A new Flutter package.
version: 0.0.1
author: "Le Huy Nghia"

environment:
  sdk: ">=3.0.0 <4.0.0"
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter
  ksbank_api_profile:
    path: ../open_api/ksbank/profile
  saving_vnp_api:
    path: ../open_api/ksbank/saving_vnp
  ksbank_api_media:
    path: ../open_api/ksbank/media
  ksbank_api_smartbank:
    path: ../open_api/ksbank/smartbank
  ksbank_api_notification:
    path: ../open_api/ksbank/notification
  ksbank_api_stocks:
    path: ../open_api/ksbank/stocks
  ksbank_api_stm:
    path: ../open_api/ksbank/stm
  ksbank_api_maintenance:
    path: ../open_api/ksbank/maintenance
  ksbank_api_loyalty:
    path: ../open_api/ksbank/loyalty
  ksbank_api_nickname:
    path: ../open_api/ksbank/nickname
  lucky_money_api:
    path: ../open_api/ksbank/lucky_money
#  ekyc_api:
#    path: ../open_api/ekyc
  common:
    path: ../modules/common
  ks_chat:
    path: ../modules/ks_chat
  open_api_umee:
    path: ../modules/open-api-umee
  ksb_common:
    path: ./../modules/ksb_common
  video_ekyc_api:
    path: ../open_api/video_ekyc
  ekyc:
    path: ../modules/ekyc

  logger: ^2.3.0
  flutter_dotenv: ^5.0.2
  intl: ^0.19.0
  rxdart: ^0.27.7
  connectivity: ^3.0.3
  http: ^0.13.1
  #  cloud_firestore: ^0.14.4
  #  firebase_core: ^0.5.3
  #  firebase_auth: ^0.18.4+1
  #  firebase_storage: ^5.2.0
  #  cloud_functions: ^0.7.2
  rocket_chat_flutter_connector: ^0.1.4
  jwt_decoder: ^2.0.1
  jose: ^0.3.2
  uuid: ^4.4.0
  contacts_service: ^0.6.0
  firebase_remote_config: ^4.4.7
  tiengviet: ^0.5.0
  firebase_messaging: ^14.9.4
  uni_links: ^0.5.1
  onesignal_flutter: ^5.0.3
  package_info: ^2.0.2
  flutter_downloader: ^1.11.8
  collection: ^1.17.0
  dio: ^5.7.0
  firebase_crashlytics: ^3.5.7

dev_dependencies:
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:

# To add assets to your package, add an assets section, like this:
# assets:
#   - images/a_dot_burr.jpeg
#   - images/a_dot_ham.jpeg
#
# For details regarding assets in packages, see
# https://flutter.dev/assets-and-images/#from-packages
#
# An image asset can refer to one or more resolution-specific "variants", see
# https://flutter.dev/assets-and-images/#resolution-aware.

# To add custom fonts to your package, add a fonts section here,
# in this "flutter" section. Each entry in this list should have a
# "family" key with the font family name, and a "fonts" key with a
# list giving the asset and other descriptors for the font. For
# example:
# fonts:
#   - family: Schyler
#     fonts:
#       - asset: fonts/Schyler-Regular.ttf
#       - asset: fonts/Schyler-Italic.ttf
#         style: italic
#   - family: Trajan Pro
#     fonts:
#       - asset: fonts/TrajanPro.ttf
#       - asset: fonts/TrajanPro_Bold.ttf
#         weight: 700
#
# For details regarding fonts in packages, see
# https://flutter.dev/custom-fonts/#from-packages
