#!/bin/sh

# Stop script if error
set -eo pipefail

separator="|"
delimiter=" "

# Hiển thị menu lựa chọn
echo "Please select whether to use FVM or not:"
echo "1) Yes"
echo "2) No"
read -p "Enter your choice [1 or 2]: " use_fvm_choice

# Kiểm tra lựa chọn và đặt biến use_fvm tương ứng
case $use_fvm_choice in
    1)
        use_fvm=true
        ;;
    2)
        use_fvm=false
        ;;
    *)
        echo "Invalid choice, defaulting to No."
        use_fvm=false
        ;;
esac

# config
repository=()
repository+=(ksbank_smartbank${separator}./open_api/spec/ksbank-smartbank-spec.json${separator}https://dev-ksapi.ssf.vn/v3/api-docs/smartbank)
repository+=(ksbank_profile${separator}./open_api/spec/ksbank-profile-spec.json${separator}https://dev-ksapi.ssf.vn/v3/api-docs/profile)
repository+=(ksbank_saving_vnp${separator}./open_api/spec/ksbank-saving-vnp-spec.json${separator}https://api-flex-staging.kienlongbank.co/vnpost/v3/api-docs)
repository+=(ksbank_notification${separator}./open_api/spec/ksbank-notification-spec.json${separator}https://dev-ksapi.ssf.vn/v3/api-docs/notification)
repository+=(ksbank_stock${separator}./open_api/spec/ksbank-stocks-spec.json${separator}https://dev-ksapi.ssf.vn/v3/api-docs/stocks)
repository+=(ksbank_stm${separator}./open_api/spec/ksbank-stm-spec.json${separator}https://dev-ksapi.ssf.vn/v3/api-docs/stm)
repository+=(ksbank_maintenance${separator}./open_api/spec/ksbank-maintenance-spec.json${separator}https://dev-ksapi.ssf.vn/v3/api-docs/maintenance)
repository+=(ksbank_loyalty${separator}./open_api/spec/ksbank-loyalty-spec.json${separator}https://dev-ksapi.ssf.vn/v3/api-docs/loyalty)
repository+=(ksbank_myshop${separator}./open_api/spec/ksbank-myshop-spec.json${separator}https://api-staging.kienlongbank.co/myshop/v3/api-docs)
repository+=(ksbank_nickname${separator}./open_api/spec/ksbank-nickname-spec.json${separator}https://api-staging.kienlongbank.co/nickname/v3/api-docs)
repository+=(video_ekyc${separator}./open_api/spec/video-ekyc-spec.json${separator}https://api-staging.kienlongbank.co/video-ekyc/v3/api-docs)
repository+=(ksbank_lucky_money${separator}./open_api/spec/ksbank-lucky-money-spec.json${separator}https://api-staging.kienlongbank.co/lucky-money/v3/api-docs)
repository+=(ksbank_media${separator}./open_api/spec/ksbank-media-spec.json${separator}https://dev-ksapi.ssf.vn/v3/api-docs/media)

selection=(All ${repository[@]})

# input
echo "Select repository:"
for index in "${!selection[@]}"
do
    echo " " $index") "${selection[$index]//$separator/" - "}
done
read -p "Repository (split by \"$delimiter\"): " select_index

# convert to array
select_index=($select_index)

# validate
select_repository=()
for value in "${select_index[@]}"
do
    if [[ ! "$value" =~ ^[0-9]+$ ]] ; then
        echo "Invalid number: $value"
        exit
    fi
    if [[ "$value" -gt "$((${#selection[@]}-1))" ]] ; then
        echo "Invalid number (out of max): $value"
        exit
    fi
    if [[ "$value" -eq 0 ]] ; then
        select_repository=(${repository[@]})
        break
    fi
    select_repository+=(${selection[$value]})
done

# Tùy chọn thêm 'fvm' vào trước lệnh nếu use_fvm là true
dart_command="dart"
flutter_command="flutter"
if [ "$use_fvm" = true ]; then
    dart_command="fvm dart"
    flutter_command="fvm flutter"
fi

# generate
if [[ "${#select_repository[@]}" -gt 0 ]] ; then
    $flutter_command pub get
    echo "Running dart run build_runner clean"
    $dart_command run build_runner clean
fi

for value in "${select_repository[@]}"
do
    IFS=$separator read -r -a processing <<< "$value"
    echo "Processing ${processing[0]}:"
#    if [ ! -z "${processing[2]}" ] ; then
#        echo "  Downloading   " ${processing[2]}
#        echo "  Save to       " ${processing[1]}
#        curl -s ${processing[2]} -o ${processing[1]}
#    fi
    echo "  Running        dart run build.custom/${processing[0]}.dart"
    $dart_command run build.custom/${processing[0]}.dart
    echo "  Running        dart run build_runner build --delete-conflicting-outputs -c custom/${processing[0]}"
    $dart_command run build_runner build --delete-conflicting-outputs -c custom/${processing[0]}
done