import 'package:common/repository/strapi/strapi.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:open_api_umee/open_api_collab.dart';
import 'package:open_api_umee/open_api_credit.dart';
import 'package:open_api_umee/open_api_pay.dart';
import 'package:video_ekyc_api/video_ekyc_api.dart';

class RepositoryImpl extends Repository {
  final KsbankApiProfile? profileApi;
  final KsbankApiMedia? mediaApi;
  final KsbankApiSmartbank? bankApi;
  final KsbankApiNotification? notificationApi;
  final KsbankApiStocks? stocksApi;
  final KsbankApiStm? stmApi;
  final StrApi? strApi;
  final CRMApi? crmApi;
  final KsbankApiLoyalty? loyaltyApi;
  final KsbankApiMaintenance? maintenanceApi;
  final NetworkApi? networkApi;
  final UmeeApiCollab? umeeApiCollab;
  final UmeeApiCredit? umeeApiCredit;
  final UmeeApiCredit? creditApi;
  final KsbankApiNickname? nicknameApi;
  final VideoEkycApi? videoEKycApi;
  final SavingVnpApi? savingVnpApi;
  final UmeeApiPay? klbPayApi;
  final LuckyMoneyApi? luckyMoneyApi;

  RepositoryImpl({
    this.profileApi,
    this.mediaApi,
    this.bankApi,
    this.notificationApi,
    this.stocksApi,
    this.stmApi,
    this.strApi,
    this.crmApi,
    this.maintenanceApi,
    this.loyaltyApi,
    this.networkApi,
    this.umeeApiCollab,
    this.umeeApiCredit,
    this.creditApi,
    this.nicknameApi,
    this.videoEKycApi,
    this.savingVnpApi,
    this.klbPayApi,
    this.luckyMoneyApi,
  });
}
