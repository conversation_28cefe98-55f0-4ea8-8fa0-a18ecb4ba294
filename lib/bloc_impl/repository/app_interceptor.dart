import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:mobile_banking/di/inject.dart';
import 'package:package_info/package_info.dart';
import 'package:crypto/crypto.dart';
import 'package:uuid/uuid.dart';
import 'package:connectivity/connectivity.dart';

class AppInterceptors extends Interceptor {
  final bool isHasToken;
  final bool isStrapiToken;
  ConnectivityResult? connectivityResult;
  PackageInfo? packageInfo;
  AppInterceptors({this.isHasToken = true, this.isStrapiToken = false}) {
    PackageInfo.fromPlatform().then((value) => packageInfo = value);
    getConnectivityResult();
    _handlerNetWorkChange();
  }

  @override
  void onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    final path = options.path;
    final uri = options.uri;
    final data = options.data;
    final query = options.queryParameters;

    final shouldLogData = data != null && (data is Map || data is String);

    if (shouldLogData) {
      logger.t(data, error: uri);
    } else {
      logger.v(uri);
    }

    final prefs = Injection.preferences;
    String language = prefs.languageCode;

    final session = Injection.injector.get<Session>();
    final deviceInfo = await session.getDeviceInfo();
    if (isHasToken) {
      try {
        Token token = await session.getToken();
        options.headers[HttpHeaders.authorizationHeader] =
            token.accessTokenRequest;
      } catch (e) {
        logger.e(e);
      }
    } else if (isStrapiToken) {
      String? strApiToken = await prefs.getTokenStrApi();
      if (!strApiToken.isNullOrEmpty &&
          !options.path.contains('/auth/keycloak')) {
        options.headers[HttpHeaders.authorizationHeader] =
            'Bearer $strApiToken';
      }
    }
    options.headers['x-platform'] = Platform.isIOS ? 'ios' : 'android';
    options.headers['x-version-code'] = packageInfo?.buildNumber ?? '';
    // options.headers['x-version-code'] = "142241";
    options.headers['x-version-name'] = packageInfo?.version ?? '';

    // options.headers['x-device-name'] = deviceInfo?.deviceName ?? '';
    options.headers['x-device-id'] = deviceInfo?.deviceId ?? '';
    options.headers['x-device-version'] = deviceInfo?.deviceVersion ?? '';

    options.headers['x-device-brand'] = deviceInfo?.deviceProvider ?? '';
    options.headers['x-device-network'] = connectivityResult?.name;
    options.headers['x-timestamp'] =
        DateTime.now().millisecondsSinceEpoch.toString();

    options.headers['x-request-id'] = md5WithUUID(prefs.userName);
    options.headers[HttpHeaders.acceptLanguageHeader] = language;

    super.onRequest(options, handler);
  }

  String md5WithUUID(String input) {
    // Generate random UUID
    var uuid = const Uuid().v4();

    // Combine input string with UUID
    var combinedString = "${input}_$uuid";

    // Calculate MD5 hash of combined string
    var bytes = utf8.encode(combinedString);
    var md5Hash = md5.convert(bytes);
    var md5String = md5Hash.toString();

    return md5String;
  }

  @override
  void onError(DioError err, ErrorInterceptorHandler handler) {
    //should check null, or is Map Object
    // logger.v("DioError - ${jsonEncode(dioError.response)}", dioError.requestOptions.path);
    logger.t("DioError - ${err.response?.data ?? ''}",
        error: err.requestOptions.path);
    super.onError(err, handler);
  }

  _handlerNetWorkChange() {
    Connectivity().onConnectivityChanged.listen((ConnectivityResult result) {
      debugPrint('onConnectivityChanged $result');
      connectivityResult = result;
    });
  }

  Future getConnectivityResult() async {
    connectivityResult = await (Connectivity().checkConnectivity());
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (response.data != null && response.data is Uint8List) {
      logger.v(response.requestOptions.path);
      super.onResponse(response, handler);
      return;
    }
    logger.t(response.data, error: response.requestOptions.path);
    final successCode = [200, 201, 204, 2000000, 2000001, 2000004];

    if (successCode.contains(response.statusCode ?? 200)) {
      final data = response.data;

      final isBankOk = data != null &&
          data is Map &&
          successCode.contains(toInt(data['code'], 200)) &&
          toBool(data['success'], true);
      final isCrm = data != null && data is String;
      if (data == null || isBankOk || isStrapiToken == true || isCrm) {
        super.onResponse(response, handler);
        return;
      } else {
        throw data;
      }
    }
    throw response;
  }
}
