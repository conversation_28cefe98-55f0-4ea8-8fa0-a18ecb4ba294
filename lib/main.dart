import 'dart:isolate';
import 'dart:ui';

import 'package:common/model/server_config.dart';
import 'package:common/utils/dialog_util.dart';
import 'package:common/widgets/money_widget.dart';
import 'package:ekyc/main.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:ks_chat/bloc/bloc/chat_base_bloc.dart';
import 'package:ks_chat/bloc/push_utils.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_bloc/onesignal.dart';
import 'package:mobile_banking/bloc_impl/repository/app_interceptor.dart';
import 'package:mobile_banking/di/inject.dart';
import 'package:mobile_banking/sunshine_app.dart';
import 'package:mobile_banking/utils/screen_size.dart';
import 'package:mobile_banking/vvn_sdk.dart';
import 'package:rxdart/rxdart.dart';
import 'package:umee_shop/di/injection.dart';
import 'package:umee_shop/environment.dart' as $shop;
import 'package:umee_shop/repository/src/umee_shop_repository_impl.dart';
import 'package:ekyc_bloc/environment.dart' as ekyc;

import 'bloc_impl/repository/preferences_impl.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await initFirebaseCrashlytics();
  await FlutterDownloader.initialize();
  var _ev = Environment.newcore();
  Preferences preferences = PreferencesImpl();
  DialogUtil.defaultTitle = "KienlongBank";
  ChatBaseBloc.OAUTH_SERVICE_NAME = 'keycloak';
  await preferences.initPreferences();
  if (preferences.userName.isNullOrEmpty ||
      preferences.fullName.isNullOrEmpty) {
    await preferences.clearSecure();
  } else {
    try {
      await preferences.allValues;
    } catch (e) {
      await preferences.clearSecure();
    }
  }

  Environment? savedEnv = await preferences.getEnvironment();
  if (savedEnv != null && savedEnv.endPoint.isNotEmpty == true) {
    _ev = savedEnv;
  } else {
    preferences.setEnvironment(_ev.toJson());
  }
  setShowLog(_ev.isDevelopment);
  MoneyWidget.unitDefault = ' VND';
  await Injection.initInjection(_ev, preferences);
  await initModuleEKyc(
    env: ekyc.Environment.ksFinance(
      serverConfig: ServerConfig(apiAI: _ev.apiAi),
    ),
  );
  initPlatformState(_ev);
  VvnSdk.instance.init(
    apiKey: 'Z9Gsi9B0sOK1QVZLayoAMZ2wJPi89Es9',
    host: 'https://api.cloudekyc.com',
  );
  VvnSdk.instance.packageName = null;
  SystemChrome.setSystemUIOverlayStyle((ScreenMode.isLight == true
      ? SystemUiOverlayStyle.light
      : SystemUiOverlayStyle.dark));

  await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

  await initFirebaseMessaging();

  await initMyShop(_ev);

  runApp(const SunshineApp());

  _bindBackgroundIsolate();
  FlutterDownloader.registerCallback(downloadCallback);
}

void downloadCallback(String id, int status, int progress) {
  final SendPort? send =
      IsolateNameServer.lookupPortByName('downloader_send_port');
  send?.send([id, status, progress]);
}

ReceivePort _receivePort = ReceivePort();

final _downloadStream = BehaviorSubject();

Stream get downloadStream => _downloadStream.stream;

void _bindBackgroundIsolate() {
  IsolateNameServer.removePortNameMapping('downloader_send_port');
  IsolateNameServer.registerPortWithName(
      _receivePort.sendPort, 'downloader_send_port');
  _receivePort.pipe(_downloadStream);
}

initFirebaseMessaging() async {
  await PushUtils.init();
  // Set the background messaging handler early on, as a named top-level function
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  /// Update the iOS foreground notification presentation options to allow
  /// heads up notifications.
  await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
    alert: true,
    badge: true,
    sound: true,
  );
}

initFirebaseCrashlytics() async {
  await Firebase.initializeApp();
  FlutterError.onError = (errorDetails) {
    FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
  };
  PlatformDispatcher.instance.onError = (error, stack) {
    FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
    return true;
  };
  await FirebaseCrashlytics.instance
      .setCrashlyticsCollectionEnabled(!kDebugMode);
}

/// Define a top-level named handler which background/terminated messages will
/// call.
///
/// To verify things are working, check out the native platform logs.
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // If you're going to use other Firebase services in the background, such as Firestore,
  // make sure you call `initializeApp` before using other Firebase services.
  logger.v(message);
  await Firebase.initializeApp();
  PushUtils.sendPushChatNotification(message);
}

initMyShop(Environment env, {bool changeEnv = false}) async {
  final Map<String, String> endPoints = {
    "apiOAuth": "https://api.sunshinegroup.vn:5000",
    "apiCore": "https://apicore.sunshinetech.com.vn",
    "strApi": env.strApiUrl,
    "apiIdentity": "https://apiidentity.sunshinegroup.vn",
    "apiChat": env.chatServerUrl.toString(),
    "socketChat": env.chatSocketServer,
    "serverUrl": env.endPoint,
    "language": env.language,
    "proxy": env.proxy,
    "payboxConfigUrl": env.myShopPayboxConfigUrl
  };
  $shop.Environment environment = $shop.Environment.fromJson(endPoints);
  if (changeEnv) {
    repository.setUp(environment, AppInterceptors());
  } else {
    await configureDependencies(environment, AppInterceptors());
  }
}
