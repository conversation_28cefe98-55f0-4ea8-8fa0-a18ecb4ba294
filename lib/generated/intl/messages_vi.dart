// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a vi locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'vi';

  static String m0(s) => "${s} phân loại được chọn";

  static String m1(s) => "${s} giao dịch";

  static String m2(amount) => "Số tiền gửi tối thiểu là ${amount}";

  static String m3(amount) => "Số tiền: ${amount}";

  static String m4(card) => "Mặt sau ${card}";

  static String m5(amount) => "Số dư ${amount}";

  static String m6(version, buildNumber) =>
      "Phiên bản ${version} (${buildNumber})";

  static String m7(card) =>
      "Chụp mặt trước ${card} của quý khách ở trong khung hình";

  static String m8(card) => "Kiểm tra mặt sau ${card}";

  static String m9(card) => "Kiểm tra mặt trước ${card}";

  static String m10(date) =>
      "Lệnh chuyển tiền chỉ có thể bắt đầu từ ${date} và kết thúc không quá 1 năm. Bạn vui lòng chọn lại";

  static String m11(x) => "Sai mật khẩu eToken. Quý khách còn ${x} lần thử";

  static String m12(minute) =>
      "Chức năng tạm khoá do trước đó quý khách nhập sai mật khẩu 3 lần. Vui lòng thử lại sau ${minute} phút";

  static String m13(minute) =>
      "Chúng tôi đang tạm khóa eToken của quý khách do sai mật khẩu eToken 3 lần. Vui lòng thử lại sau ${minute} phút";

  static String m14(header) => "${header} không được để trống";

  static String m15(card) => "Mặt trước ${card}";

  static String m16(maxLength) => "Độ dài nhỏ hơn ${maxLength} ký tự";

  static String m17(minLength, maxLength) =>
      "Độ dài từ ${minLength} - ${maxLength} ký tự";

  static String m18(minLength) => "Độ dài từ ${minLength} ký tự";

  static String m19(email) => "Mã OTP đã được gửi qua Email ${email}";

  static String m20(index) => "Thanh toán lại ${index} lần";

  static String m21(name) =>
      "Vui lòng nhập thông tin nhà cung cấp và mã khách hàng được in trên hoá đơn ${name} hàng tháng của quý khách.";

  static String m22(money, unit, firstPrice, secondPrice, accumulatedAmount) =>
      "Nếu giá trị giao dịch ${money} VND thì làm tròn đến hàng ${unit}\nVD: Giao dịch trị giá ${firstPrice} làm tròn thành ${secondPrice} (số tiền tích lũy là ${accumulatedAmount} VND)";

  static String m23(accountName, amountText, accountNo, description,
          transferRequestLink) =>
      "Bạn được ${accountName} yêu cầu chuyển ${amountText} VND vào tài khoản ${accountNo} tại ngân hàng KienlongBank.\nNội dung: ${description}.\nNhấn link sau để chuyển ngay: ${transferRequestLink}";

  static String m24(posName) =>
      "Quý khách đang ở gần ${posName}, quý khách có muốn đặt lịch làm việc không?";

  static String m25(currencyFormatNotDecimal, currency) =>
      "Quý khách sẽ nhận được ${currencyFormatNotDecimal} ${currency} tiền lãi. Quý khách có muốn tiếp tục tất toán không?";

  static String m26(currencyFormatNotDecimal, currency) =>
      "Quý khách sẽ nhận được ${currencyFormatNotDecimal} ${currency}. Quý khách có muốn tiếp tục tất toán không?";

  static String m27(numberPhone) =>
      "Mã OTP sẽ được gửi đến số điện thoại ${numberPhone} để xác thực quý khách";

  static String m28(transactionNo) => "Mã giao dịch: ${transactionNo}";

  static String m29(second) => "Kết nối lại sau ${second}s";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "CCCD": MessageLookupByLibrary.simpleMessage("CCCD"),
        "CCCD2": MessageLookupByLibrary.simpleMessage("CCCD gắn chip"),
        "CMT": MessageLookupByLibrary.simpleMessage("CMND"),
        "CVV_change_after": MessageLookupByLibrary.simpleMessage(
            "CVV sẽ tự động thay đổi sau "),
        "CVV_value": MessageLookupByLibrary.simpleMessage("CVV:"),
        "Consultants":
            MessageLookupByLibrary.simpleMessage("Chuyên viên tư vấn"),
        "FATAC": MessageLookupByLibrary.simpleMessage("Quy định về FATCA"),
        "VNeID": MessageLookupByLibrary.simpleMessage(
            "Ứng dụng định danh điện tử VNeID"),
        "abolition": MessageLookupByLibrary.simpleMessage("Huỷ bỏ"),
        "above_all_convenience":
            MessageLookupByLibrary.simpleMessage("Trên cả tiện lợi."),
        "accept": MessageLookupByLibrary.simpleMessage("Chấp nhận"),
        "account": MessageLookupByLibrary.simpleMessage("Tài khoản"),
        "account_add_pay_account_label_account_number":
            MessageLookupByLibrary.simpleMessage("Số tài khoản"),
        "account_add_pay_account_label_name_account":
            MessageLookupByLibrary.simpleMessage(
                "Tên tài khoản (không bắt buộc)"),
        "account_add_pay_account_label_pay_info":
            MessageLookupByLibrary.simpleMessage("Thông tin thanh toán"),
        "account_add_pay_account_label_total":
            MessageLookupByLibrary.simpleMessage("Tổng tiền"),
        "account_add_pay_account_title_add":
            MessageLookupByLibrary.simpleMessage(
                "Mở thêm tài khoản thanh toán"),
        "account_add_receiver_button_add_more":
            MessageLookupByLibrary.simpleMessage("Thêm"),
        "account_add_receiver_des_enter_phone":
            MessageLookupByLibrary.simpleMessage(
                "Vui lòng nhập số điện thoại của người tham gia chia tiền"),
        "account_add_receiver_label_contact_list":
            MessageLookupByLibrary.simpleMessage("Danh sách liên hệ"),
        "account_add_receiver_label_phone_number":
            MessageLookupByLibrary.simpleMessage("Số điện thoại"),
        "account_address": MessageLookupByLibrary.simpleMessage("Địa chỉ"),
        "account_advance_filter_label_all":
            MessageLookupByLibrary.simpleMessage("Toàn bộ"),
        "account_advance_filter_label_cash_in":
            MessageLookupByLibrary.simpleMessage("Tiền vào"),
        "account_advance_filter_label_cash_out":
            MessageLookupByLibrary.simpleMessage("Tiền ra"),
        "account_advance_filter_label_choose_transaction":
            MessageLookupByLibrary.simpleMessage("Chọn loại giao dịch"),
        "account_and_card_type":
            MessageLookupByLibrary.simpleMessage("Loại tài khoản/Thẻ"),
        "account_balance":
            MessageLookupByLibrary.simpleMessage("Số dư tài khoản"),
        "account_cash_out_button_cancel_all":
            MessageLookupByLibrary.simpleMessage("Bỏ chọn toàn bộ"),
        "account_cash_out_button_choose_all":
            MessageLookupByLibrary.simpleMessage("Chọn toàn bộ"),
        "account_cash_out_des_category_choosen": m0,
        "account_cash_out_lable_category_choosen":
            MessageLookupByLibrary.simpleMessage("phân loại được chọn"),
        "account_cash_out_title_cash_in":
            MessageLookupByLibrary.simpleMessage("Giao dịch tiền vào"),
        "account_cash_out_title_cash_out":
            MessageLookupByLibrary.simpleMessage("Giao dịch tiền ra"),
        "account_debt_collection":
            MessageLookupByLibrary.simpleMessage("Tài khoản yêu cầu thu nợ"),
        "account_detail":
            MessageLookupByLibrary.simpleMessage("Chi tiết tài khoản"),
        "account_find_note_item1": MessageLookupByLibrary.simpleMessage(
            "Sử dụng dấu * đại diện cho 1 chuỗi số"),
        "account_find_note_item2": MessageLookupByLibrary.simpleMessage(
            "Để tìm số tài khoản bắt đầu bằng một chuỗi số quý khách nhập chuỗi số đó và thêm dấu * đằng sau. Ví dụ 22* sẽ cho ra các số tài khoản bắt đầu bằng 22"),
        "account_find_note_item3": MessageLookupByLibrary.simpleMessage(
            "Để tìm số tài khoản kết thúc bằng một chuỗi số quý khách nhập chuỗi số đó và thêm dấu * đằng trước. Ví dụ *22 sẽ cho ra các số tài khoản kết thúc bằng 22"),
        "account_find_note_item4": MessageLookupByLibrary.simpleMessage(
            "Để tìm số tài khoản bắt đầu và kết thúc bằng chuỗi số, quý khách thêm dấu * vào giữa 2 chuỗi số đó. Ví dụ 22*33 sẽ cho các tài khoản bắt đầu bằng 22 và kết thúc bằng 33"),
        "account_find_note_item5": MessageLookupByLibrary.simpleMessage(
            "Để tìm số tài khoản có chứa một chuỗi số quý khách nhập chuỗi số đó. Ví dụ 222 sẽ cho ra các tài khoản có chứa chuỗi này."),
        "account_full_name": MessageLookupByLibrary.simpleMessage("Họ và tên"),
        "account_get_money":
            MessageLookupByLibrary.simpleMessage("Tài khoản nhận tiền"),
        "account_home_button_add_account":
            MessageLookupByLibrary.simpleMessage("Mở thêm tài khoản mới"),
        "account_home_button_add_save_account":
            MessageLookupByLibrary.simpleMessage("Mở tài khoản tiết kiệm mới"),
        "account_home_button_loan_account_open":
            MessageLookupByLibrary.simpleMessage("Đăng ký khoản vay mới"),
        "account_home_label_balance_total":
            MessageLookupByLibrary.simpleMessage("Tổng số dư khả dụng"),
        "account_home_label_debit_total":
            MessageLookupByLibrary.simpleMessage("Dư nợ hiện tại"),
        "account_home_label_pay_account":
            MessageLookupByLibrary.simpleMessage("Tài khoản thanh toán"),
        "account_home_label_save_account":
            MessageLookupByLibrary.simpleMessage("Tài khoản tiết kiệm"),
        "account_home_label_total_level":
            MessageLookupByLibrary.simpleMessage("Tổng hạn mức"),
        "account_home_title_account":
            MessageLookupByLibrary.simpleMessage("Tài khoản"),
        "account_info":
            MessageLookupByLibrary.simpleMessage("Thông tin tài khoản"),
        "account_label_fee_account":
            MessageLookupByLibrary.simpleMessage("Phí chọn tài khoản"),
        "account_label_vat": MessageLookupByLibrary.simpleMessage("VAT"),
        "account_limit":
            MessageLookupByLibrary.simpleMessage("Giới hạn mức chuyển tiền"),
        "account_name": MessageLookupByLibrary.simpleMessage("Tên khách hàng"),
        "account_new":
            MessageLookupByLibrary.simpleMessage("Tài khoản thanh toán mới"),
        "account_new_nice":
            MessageLookupByLibrary.simpleMessage("Tài khoản số đẹp"),
        "account_new_search":
            MessageLookupByLibrary.simpleMessage("Tìm số tài khoản"),
        "account_nickname":
            MessageLookupByLibrary.simpleMessage("Tài khoản nickname"),
        "account_number": MessageLookupByLibrary.simpleMessage("Số tài khoản"),
        "account_number_iban":
            MessageLookupByLibrary.simpleMessage("Số tài khoản/IBAN"),
        "account_number_or_card_number":
            MessageLookupByLibrary.simpleMessage("Số tài khoản hoặc số thẻ"),
        "account_open_confirm_label_account_pay_fee":
            MessageLookupByLibrary.simpleMessage("Tài khoản trả phí"),
        "account_open_confirm_label_amount":
            MessageLookupByLibrary.simpleMessage("Số tiền giao dịch"),
        "account_open_confirm_label_info":
            MessageLookupByLibrary.simpleMessage("Thông tin tài khoản số đẹp"),
        "account_open_confirm_label_new_account":
            MessageLookupByLibrary.simpleMessage("Tài khoản mới"),
        "account_open_confirm_title_confirm":
            MessageLookupByLibrary.simpleMessage("Xác nhận mở tài khoản"),
        "account_open_successful_label_successful":
            MessageLookupByLibrary.simpleMessage("Mở tài khoản thành công"),
        "account_opening_fee":
            MessageLookupByLibrary.simpleMessage("Phí mở tài khoản"),
        "account_pay_detail_button_copy_code":
            MessageLookupByLibrary.simpleMessage("Sao chép số tài khoản"),
        "account_pay_detail_button_qr_gen":
            MessageLookupByLibrary.simpleMessage("Xuất mã QR"),
        "account_pay_detail_label_account_balance":
            MessageLookupByLibrary.simpleMessage("Số dư kế toán"),
        "account_pay_detail_label_account_detail":
            MessageLookupByLibrary.simpleMessage("Chi tiết tài khoản"),
        "account_pay_detail_label_account_type":
            MessageLookupByLibrary.simpleMessage("Loại tài khoản"),
        "account_pay_detail_label_open_date":
            MessageLookupByLibrary.simpleMessage("Ngày mở tài khoản"),
        "account_pay_detail_label_open_place":
            MessageLookupByLibrary.simpleMessage("Nơi mở tài khoản"),
        "account_pay_detail_label_overdraft":
            MessageLookupByLibrary.simpleMessage("Thấu chi còn lại"),
        "account_pay_detail_label_usable_balance":
            MessageLookupByLibrary.simpleMessage("Số dư khả dụng"),
        "account_pay_detail_snack_msg_copy_success":
            MessageLookupByLibrary.simpleMessage(
                "Đã sao chép số tài khoản vào bộ nhớ tạm"),
        "account_pay_detail_title_statement":
            MessageLookupByLibrary.simpleMessage("Sao kê"),
        "account_pay_detail_title_statistic":
            MessageLookupByLibrary.simpleMessage("Thống kê"),
        "account_pay_detail_title_transact":
            MessageLookupByLibrary.simpleMessage("Giao dịch"),
        "account_qr_bottomsheet_des_help_way":
            MessageLookupByLibrary.simpleMessage(
                "Quý khách có thể sử dụng QR Code theo các cách sau:"),
        "account_qr_bottomsheet_des_help_way1":
            MessageLookupByLibrary.simpleMessage(
                "1. Đưa mã trực tiếp cho bạn bè sử dụng KienlongBank để quét mã"),
        "account_qr_bottomsheet_des_help_way2":
            MessageLookupByLibrary.simpleMessage("2. Lưu mã về máy"),
        "account_qr_bottomsheet_des_help_way3":
            MessageLookupByLibrary.simpleMessage("3. Chia sẻ mã"),
        "account_qr_bottomsheet_des_name":
            MessageLookupByLibrary.simpleMessage("Tên gợi nhớ"),
        "account_qr_bottomsheet_title_change_name":
            MessageLookupByLibrary.simpleMessage("Đổi tên gợi nhớ"),
        "account_qr_bottomsheet_title_help":
            MessageLookupByLibrary.simpleMessage("Trợ giúp "),
        "account_qr_des_here": MessageLookupByLibrary.simpleMessage("tại đây"),
        "account_qr_des_qr_transfer":
            MessageLookupByLibrary.simpleMessage("Chuyển khoản qua mã QR"),
        "account_qr_label_account_number":
            MessageLookupByLibrary.simpleMessage("Số tài khoản"),
        "account_qr_label_account_owner":
            MessageLookupByLibrary.simpleMessage("Chủ tài khoản"),
        "account_qr_label_bank":
            MessageLookupByLibrary.simpleMessage("Ngân hàng"),
        "account_qr_snack_msg_change_success":
            MessageLookupByLibrary.simpleMessage("Đổi tên gợi nhớ thành công"),
        "account_qr_title_qr_code":
            MessageLookupByLibrary.simpleMessage("Mã QR"),
        "account_register":
            MessageLookupByLibrary.simpleMessage("Tài khoản đăng ký"),
        "account_saving":
            MessageLookupByLibrary.simpleMessage("Tài khoản tiết kiệm"),
        "account_saving_money": MessageLookupByLibrary.simpleMessage(
            "Tài khoản tiền gửi tiết kiệm"),
        "account_spend_record_label_spend_history":
            MessageLookupByLibrary.simpleMessage("Lịch sử chi tiêu"),
        "account_spend_record_title_record":
            MessageLookupByLibrary.simpleMessage("Thống kê chi tiêu"),
        "account_statement":
            MessageLookupByLibrary.simpleMessage("Sao kê tài khoản"),
        "account_statement_label_day_setting":
            MessageLookupByLibrary.simpleMessage("Tùy chọn ngày"),
        "account_statement_label_four_months":
            MessageLookupByLibrary.simpleMessage("4 tháng gần nhất"),
        "account_statement_label_recent_month":
            MessageLookupByLibrary.simpleMessage("Tháng hiện tại"),
        "account_statement_label_statement_export":
            MessageLookupByLibrary.simpleMessage("Xuất sao kê ra tập tin"),
        "account_statement_label_three_months":
            MessageLookupByLibrary.simpleMessage("3 tháng gần nhất"),
        "account_statement_snack_msg_download_successful":
            MessageLookupByLibrary.simpleMessage("Tải xuống sao kê thành công"),
        "account_statement_title_transact_type":
            MessageLookupByLibrary.simpleMessage("Loại giao dịch"),
        "account_statistic_label_receive":
            MessageLookupByLibrary.simpleMessage("Nhận tiền "),
        "account_statistic_label_record":
            MessageLookupByLibrary.simpleMessage("Thống kê thu chi"),
        "account_statistic_label_spend":
            MessageLookupByLibrary.simpleMessage("Chi tiền"),
        "account_statistic_label_user_receive":
            MessageLookupByLibrary.simpleMessage("Quý khách đã nhận được"),
        "account_statistic_label_user_spend":
            MessageLookupByLibrary.simpleMessage("Quý khách đã chi tiêu"),
        "account_time_track_button_apply":
            MessageLookupByLibrary.simpleMessage("Áp dụng"),
        "account_time_track_title_choose_time":
            MessageLookupByLibrary.simpleMessage("Chọn thời gian tra cứu"),
        "account_transact_detail_bottomsheet_hint_enter_account":
            MessageLookupByLibrary.simpleMessage("Nhập số tiền muốn chia"),
        "account_transact_detail_bottomsheet_title_edit_amount":
            MessageLookupByLibrary.simpleMessage("Chỉnh sửa số tiền"),
        "account_transact_detail_button_add_more":
            MessageLookupByLibrary.simpleMessage("Thêm người nhận"),
        "account_transact_detail_button_redo":
            MessageLookupByLibrary.simpleMessage("Thực hiện lại"),
        "account_transact_detail_button_share_bill":
            MessageLookupByLibrary.simpleMessage("Chia tiền"),
        "account_transact_detail_empty_add_more":
            MessageLookupByLibrary.simpleMessage(
                "Ấn \"Thêm người nhận\" để thêm người chia tiền"),
        "account_transact_detail_hint_text_content":
            MessageLookupByLibrary.simpleMessage("Nội dung (không bắt buộc)"),
        "account_transact_detail_label_list_share":
            MessageLookupByLibrary.simpleMessage("Danh sách chia tiền"),
        "account_transact_detail_label_number_list":
            MessageLookupByLibrary.simpleMessage("người trong danh sách"),
        "account_transact_detail_label_request_content":
            MessageLookupByLibrary.simpleMessage("Nội dung yêu cầu"),
        "account_transact_detail_snack_msg_send_successful":
            MessageLookupByLibrary.simpleMessage(
                "Đã gửi yêu cầu chia tiền thành công"),
        "account_transact_detail_title_detail":
            MessageLookupByLibrary.simpleMessage("Chi tiết giao dịch"),
        "account_transaction_empty_des_enter_search_info":
            MessageLookupByLibrary.simpleMessage(
                "Nhập thông tin giao dịch để tìm kiếm"),
        "account_transaction_empty_des_no_transact":
            MessageLookupByLibrary.simpleMessage(
                "Không có giao dịch nào trong thời gian này"),
        "account_transaction_hint_text_enter_transact":
            MessageLookupByLibrary.simpleMessage("Nhập thông tin giao dịch"),
        "account_transaction_label_advance_filter":
            MessageLookupByLibrary.simpleMessage("Lọc nâng cao"),
        "account_transaction_label_search_result":
            MessageLookupByLibrary.simpleMessage("Kết quả tìm kiếm"),
        "account_transaction_label_time":
            MessageLookupByLibrary.simpleMessage("Thời gian"),
        "account_transaction_label_transact_list":
            MessageLookupByLibrary.simpleMessage("Danh sách giao dịch"),
        "account_transaction_label_transact_number": m1,
        "account_transfer":
            MessageLookupByLibrary.simpleMessage("Tài khoản chuyển tiền"),
        "account_transfer_request_button_create_request":
            MessageLookupByLibrary.simpleMessage("Tạo yêu cầu chuyển tiền"),
        "account_transfer_request_label_amount":
            MessageLookupByLibrary.simpleMessage("Số tiền"),
        "account_transfer_request_label_content":
            MessageLookupByLibrary.simpleMessage("Nội dung"),
        "account_transfer_request_snack_msg_save_successful":
            MessageLookupByLibrary.simpleMessage(
                "Lưu mã chuyển tiền thành công"),
        "account_type": MessageLookupByLibrary.simpleMessage("Loại tài khoản"),
        "account_verify_title":
            MessageLookupByLibrary.simpleMessage("Tài khoản đang xác minh"),
        "account_withdrawal":
            MessageLookupByLibrary.simpleMessage("Tài khoản rút"),
        "accumulate": MessageLookupByLibrary.simpleMessage("Tích lũy"),
        "accumulated_interest":
            MessageLookupByLibrary.simpleMessage("Tiền lãi tích luỹ"),
        "accumulation_target_empty_des": MessageLookupByLibrary.simpleMessage(
            "Dành cho khách hàng cá nhân muốn tích luỹ từ số tiền nhỏ để đạt số tiền mục tiêu lớn trong tương lai phục vụ cho nhu cầu tài chính của mình."),
        "accumulation_target_intro_1": MessageLookupByLibrary.simpleMessage(
            "Thuận tiện với\nmục tiêu cụ thể"),
        "accumulation_target_intro_2":
            MessageLookupByLibrary.simpleMessage("An toàn,\nbảo mật"),
        "accumulation_target_intro_3":
            MessageLookupByLibrary.simpleMessage("Sinh lời\nhiệu quả"),
        "activate_card": MessageLookupByLibrary.simpleMessage("Kích hoạt thẻ"),
        "active": MessageLookupByLibrary.simpleMessage("Đang hoạt động"),
        "active_card":
            MessageLookupByLibrary.simpleMessage("Thẻ đang hoạt động"),
        "active_now": MessageLookupByLibrary.simpleMessage("Kích hoạt ngay"),
        "active_your_card_now": MessageLookupByLibrary.simpleMessage(
            "Vui lòng thực hiện kích hoạt Thẻ để bắt đầu sử dụng ngay"),
        "add": MessageLookupByLibrary.simpleMessage("Thêm"),
        "add_beneficiary":
            MessageLookupByLibrary.simpleMessage("Thêm người thụ hưởng"),
        "add_beneficiary_contact_success": MessageLookupByLibrary.simpleMessage(
            "Thêm danh bạ thụ hưởng thành công"),
        "add_by_qr_code":
            MessageLookupByLibrary.simpleMessage("Thêm bằng mã QR"),
        "add_customer": MessageLookupByLibrary.simpleMessage("Thêm khách"),
        "add_customer_now": MessageLookupByLibrary.simpleMessage(
            "Thêm khách hàng ngay hôm nay"),
        "add_job": MessageLookupByLibrary.simpleMessage("Thêm việc"),
        "add_new": MessageLookupByLibrary.simpleMessage("Thêm mới"),
        "add_new_command":
            MessageLookupByLibrary.simpleMessage("Thêm lệnh mới"),
        "add_new_customers":
            MessageLookupByLibrary.simpleMessage("Thêm khách hàng mới"),
        "add_now": MessageLookupByLibrary.simpleMessage("Bổ sung ngay"),
        "address": MessageLookupByLibrary.simpleMessage("Địa chỉ"),
        "address_full":
            MessageLookupByLibrary.simpleMessage("Số nhà, đường phố"),
        "address_unit_priority":
            MessageLookupByLibrary.simpleMessage("Địa chỉ đơn vị"),
        "affiliate_account":
            MessageLookupByLibrary.simpleMessage("Tài khoản liên kết"),
        "agent_counseling_empty": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng chọn chuyên viên tư vấn"),
        "agree": MessageLookupByLibrary.simpleMessage("Đồng ý"),
        "alert_VNeID": MessageLookupByLibrary.simpleMessage(
            "Chức năng xác thực sinh trắc học qua ứng dụng định danh điện tử VNeID đang được phát triển, vui lòng thử lại sau."),
        "alert_blance": MessageLookupByLibrary.simpleMessage(
            "Quý khách sẽ nhận được thông báo khi tài khoản có biến động số dư."),
        "alert_delete_bill": MessageLookupByLibrary.simpleMessage(
            "Quý khách có muốn bỏ qua toàn bộ thông báo nhắc nợ hóa đơn kỳ này?"),
        "alert_delete_bill_page": MessageLookupByLibrary.simpleMessage(
            "Quý khách có muốn tắt thông báo nhắc nợ hóa đơn kỳ này?"),
        "all": MessageLookupByLibrary.simpleMessage("Tất cả"),
        "allow_permission": MessageLookupByLibrary.simpleMessage("Cho phép"),
        "allows_to_locate":
            MessageLookupByLibrary.simpleMessage("Cho phép xác định vị trí"),
        "allows_to_locate_alert": MessageLookupByLibrary.simpleMessage(
            "Hiện quý khách chưa cho phép ứng dụng xác định vị trí của quý khách. Ứng dụng cần vị trí để hiển thị các phòng giao dịch gần nhất."),
        "already_paid": MessageLookupByLibrary.simpleMessage("Đã nộp tiền"),
        "amount": MessageLookupByLibrary.simpleMessage("Số tiền"),
        "amount_exceeded": MessageLookupByLibrary.simpleMessage(
            "Số tiền vượt quá mức cho phép"),
        "amount_interest_time_settlement": MessageLookupByLibrary.simpleMessage(
            "Số tiền lãi tại thời điểm tất toán"),
        "amount_minium_saving": m2,
        "amount_of_fines": MessageLookupByLibrary.simpleMessage("Số tiền nộp"),
        "amount_option":
            MessageLookupByLibrary.simpleMessage("Số tiền (không bắt buộc)"),
        "amount_paid": MessageLookupByLibrary.simpleMessage("Số tiền trả"),
        "amount_prepayment_fee": MessageLookupByLibrary.simpleMessage(
            "Số tiền phí trả nợ trước hạn"),
        "amount_saving": MessageLookupByLibrary.simpleMessage("Số tiền gửi"),
        "amount_settlement":
            MessageLookupByLibrary.simpleMessage("Tiền lãi tất toán"),
        "amount_value": m3,
        "amount_withdrawal":
            MessageLookupByLibrary.simpleMessage("Số tiền rút"),
        "another_bank": MessageLookupByLibrary.simpleMessage("Ngân hàng khác"),
        "another_reason": MessageLookupByLibrary.simpleMessage("Lý do khác"),
        "anywhere_no_need_go_bank": MessageLookupByLibrary.simpleMessage(
            "Ở bất cứ nơi đâu, không cần đến Ngân hàng."),
        "app": MessageLookupByLibrary.simpleMessage("Ứng dụng"),
        "appearance_setting":
            MessageLookupByLibrary.simpleMessage("Cài đặt diện mạo"),
        "apply": MessageLookupByLibrary.simpleMessage("Áp dụng"),
        "apply_for_loans":
            MessageLookupByLibrary.simpleMessage("Đăng ký vay tại"),
        "apply_theme": MessageLookupByLibrary.simpleMessage("Áp dụng chủ đề"),
        "area": MessageLookupByLibrary.simpleMessage("Khu vực"),
        "artisan": MessageLookupByLibrary.simpleMessage("Thủ công"),
        "ask_receive_otp":
            MessageLookupByLibrary.simpleMessage("Không nhận được mã OTP?"),
        "ask_remind_biometrics": MessageLookupByLibrary.simpleMessage(
            "Bạn có muốn sử dụng đăng nhập sinh trắc học?"),
        "at_KLB_branches": MessageLookupByLibrary.simpleMessage(
            "Tại CN/PGD KienlongBank quản lý thẻ"),
        "at_customer_address":
            MessageLookupByLibrary.simpleMessage("Tại địa chỉ của khách hàng"),
        "atm_machine": MessageLookupByLibrary.simpleMessage("Máy ATM"),
        "atm_machine_address":
            MessageLookupByLibrary.simpleMessage("Địa chỉ máy ATM"),
        "attach": MessageLookupByLibrary.simpleMessage("Đính kèm"),
        "attached_images":
            MessageLookupByLibrary.simpleMessage("Hình ảnh đính kèm"),
        "attachments":
            MessageLookupByLibrary.simpleMessage("Tài liệu đính kèm"),
        "authentication": MessageLookupByLibrary.simpleMessage("Xác thực"),
        "authentication_required":
            MessageLookupByLibrary.simpleMessage("Yêu cầu xác thực"),
        "auto_payment":
            MessageLookupByLibrary.simpleMessage("Thanh toán tự động"),
        "automatic": MessageLookupByLibrary.simpleMessage("Tự động"),
        "automatic_debit":
            MessageLookupByLibrary.simpleMessage("Trích nợ tự động"),
        "availability": MessageLookupByLibrary.simpleMessage("Khả dụng"),
        "avoid_using": MessageLookupByLibrary.simpleMessage("Tránh sử dụng:"),
        "back": MessageLookupByLibrary.simpleMessage("Quay lại"),
        "back_card": m4,
        "back_face": MessageLookupByLibrary.simpleMessage("Chụp mặt sau"),
        "back_page": MessageLookupByLibrary.simpleMessage("Về trang trước"),
        "back_to_home":
            MessageLookupByLibrary.simpleMessage("Quay về trang chủ"),
        "balance": m5,
        "bank": MessageLookupByLibrary.simpleMessage("Ngân hàng"),
        "bank_address":
            MessageLookupByLibrary.simpleMessage("Địa chỉ ngân hàng"),
        "bank_branch":
            MessageLookupByLibrary.simpleMessage("Chi nhánh ngân hàng"),
        "bank_confirmation":
            MessageLookupByLibrary.simpleMessage("Xác nhận ngân hàng"),
        "bank_information_received":
            MessageLookupByLibrary.simpleMessage("Thông tin ngân hàng nhận"),
        "bank_information_received_caption": MessageLookupByLibrary.simpleMessage(
            "* Cần phải nhập mã SWIFT hoặc FEDWIRE\n* Nếu chuyển tiền AUD thì phải nhập thêm mã BSB CODE"),
        "bank_kienlong_name": MessageLookupByLibrary.simpleMessage(
            "KienlongBank - NH TMCP Kiên Long"),
        "bank_name": MessageLookupByLibrary.simpleMessage("Tên ngân hàng"),
        "bank_network": MessageLookupByLibrary.simpleMessage("Mạng lưới"),
        "beneficiaries":
            MessageLookupByLibrary.simpleMessage("Người hưởng thụ"),
        "beneficiary": MessageLookupByLibrary.simpleMessage("Người thụ hưởng"),
        "beneficiary_account":
            MessageLookupByLibrary.simpleMessage("Tài khoản thụ hưởng"),
        "beneficiary_directory":
            MessageLookupByLibrary.simpleMessage("Danh bạ thụ hưởng"),
        "beneficiary_name":
            MessageLookupByLibrary.simpleMessage("Tên người thụ hưởng"),
        "beneficiary_people":
            MessageLookupByLibrary.simpleMessage("Người thụ hưởng"),
        "beyond_info_label_debt_account":
            MessageLookupByLibrary.simpleMessage("Tài khoản trích nợ"),
        "beyond_register_bottomsheet_label_min_debt":
            MessageLookupByLibrary.simpleMessage("Dư nợ tối thiểu"),
        "beyond_register_button_discover":
            MessageLookupByLibrary.simpleMessage("Khám phá ngay"),
        "beyond_register_button_open":
            MessageLookupByLibrary.simpleMessage("Đăng ký mở thẻ"),
        "beyond_register_info_open":
            MessageLookupByLibrary.simpleMessage("Thông tin mở thẻ"),
        "beyond_register_inline_msg_chooose_district":
            MessageLookupByLibrary.simpleMessage(
                "Vui lòng chọn quận/huyện nhận thẻ"),
        "beyond_register_inline_msg_choose_address":
            MessageLookupByLibrary.simpleMessage(
                "Vui lòng nhập địa chỉ nhận thẻ"),
        "beyond_register_inline_msg_choose_city":
            MessageLookupByLibrary.simpleMessage(
                "Vui lòng chọn tỉnh/thành phố nhận thẻ"),
        "beyond_register_label_address_receive":
            MessageLookupByLibrary.simpleMessage("Địa chỉ nhận thẻ"),
        "beyond_register_label_auto_method":
            MessageLookupByLibrary.simpleMessage("Phương thức trích nợ"),
        "beyond_register_label_automatic":
            MessageLookupByLibrary.simpleMessage("Đăng ký trích nợ tự động"),
        "beyond_register_label_customer": MessageLookupByLibrary.simpleMessage(
            "Dành riêng cho KHCN của KSFinance"),
        "beyond_register_label_feature":
            MessageLookupByLibrary.simpleMessage("Đặc điểm thẻ"),
        "beyond_register_label_feature1":
            MessageLookupByLibrary.simpleMessage("Dòng thẻ thế chấp"),
        "beyond_register_label_feature2": MessageLookupByLibrary.simpleMessage(
            "Hạn mức linh hoạt theo giá trị tài sản của khách hàng tại KSFinance"),
        "beyond_register_label_feature3": MessageLookupByLibrary.simpleMessage(
            "Hạn mức tối thiểu 200 triệu, tối đa 20 tỷ"),
        "beyond_register_label_feature4": MessageLookupByLibrary.simpleMessage(
            "Vui lòng nhập hạn mức thẻ mong muốn"),
        "beyond_register_label_name": MessageLookupByLibrary.simpleMessage(
            "Thẻ đồng thương hiệu Visa Beyond KLB- KSFinance"),
        "beyond_register_label_policy":
            MessageLookupByLibrary.simpleMessage("Chính sách ưu đãi"),
        "beyond_register_label_policy2": MessageLookupByLibrary.simpleMessage(
            "Phí rút tiền mặt chỉ 1%, tối thiểu 50.000 VND"),
        "beyond_register_label_policy3": MessageLookupByLibrary.simpleMessage(
            "Phí chuyển đổi ngoại tệ: 3% giá trị giao dịch"),
        "beyond_register_label_referral":
            MessageLookupByLibrary.simpleMessage("Mã người giới thiệu"),
        "beyond_register_label_register_info":
            MessageLookupByLibrary.simpleMessage("Thông tin đăng ký"),
        "beyond_register_label_view_detail1":
            MessageLookupByLibrary.simpleMessage(
                "Xem chi tiết chính sách và ưu đãi "),
        "beyond_register_label_view_policy1":
            MessageLookupByLibrary.simpleMessage(
                "Bằng việc tiếp tục, quý khách đã đồng ý với "),
        "beyond_register_label_view_policy2":
            MessageLookupByLibrary.simpleMessage(
                "Điều khoản và Điều kiện sử dụng"),
        "beyond_register_label_view_policy3":
            MessageLookupByLibrary.simpleMessage(" của KienlongBank."),
        "beyond_register_title_register":
            MessageLookupByLibrary.simpleMessage("Đăng ký thẻ tín dụng"),
        "beyond_success_label_card_class":
            MessageLookupByLibrary.simpleMessage("Hạng thẻ"),
        "beyond_success_label_request_limit":
            MessageLookupByLibrary.simpleMessage("Hạn mức yêu cầu"),
        "bill": MessageLookupByLibrary.simpleMessage("Hóa đơn"),
        "bill_customer_code": MessageLookupByLibrary.simpleMessage("Mã KH"),
        "bill_date_money":
            MessageLookupByLibrary.simpleMessage("Đến hạn thanh toán"),
        "bill_info": MessageLookupByLibrary.simpleMessage("Thông tin hoá đơn"),
        "bill_money": MessageLookupByLibrary.simpleMessage("Tiền hoá đơn"),
        "bill_more": MessageLookupByLibrary.simpleMessage("Xem thêm tất cả"),
        "bill_payment":
            MessageLookupByLibrary.simpleMessage("Thanh toán hoá đơn"),
        "bill_ubpaid_title":
            MessageLookupByLibrary.simpleMessage("Hóa đơn chưa thanh toán"),
        "billing_period": MessageLookupByLibrary.simpleMessage("Kì hoá đơn"),
        "billion": MessageLookupByLibrary.simpleMessage("Tỷ"),
        "biometric_authentication":
            MessageLookupByLibrary.simpleMessage("Xác thực sinh trắc học"),
        "biometric_profile":
            MessageLookupByLibrary.simpleMessage("Hồ sơ sinh trắc học"),
        "biometrics": MessageLookupByLibrary.simpleMessage("Sinh trắc học"),
        "birthday": MessageLookupByLibrary.simpleMessage("Ngày sinh"),
        "blockade_status":
            MessageLookupByLibrary.simpleMessage("Tình trạng phong tỏa"),
        "book_consult": MessageLookupByLibrary.simpleMessage("Đặt lịch hẹn"),
        "branch": MessageLookupByLibrary.simpleMessage("Chi nhánh"),
        "branch_pgd_name":
            MessageLookupByLibrary.simpleMessage("Tên chi nhánh/PGD"),
        "build_version": m6,
        "business": MessageLookupByLibrary.simpleMessage("Kinh doanh"),
        "business_hour": MessageLookupByLibrary.simpleMessage("Giờ mở cửa"),
        "buy_back": MessageLookupByLibrary.simpleMessage("Mua lại"),
        "buy_gold": MessageLookupByLibrary.simpleMessage("Mua vàng"),
        "by_pressing": MessageLookupByLibrary.simpleMessage("Bằng việc nhấn"),
        "by_pressing_confirm": MessageLookupByLibrary.simpleMessage(
            "Bằng việc nhấn “Xác nhận”, Quý khách đồng ý đề nghị thực hiện tất toán khoản vay theo mẫu"),
        "calculation_table_notice": MessageLookupByLibrary.simpleMessage(
            "Bảng tính lãi này để quý khách tham khảo. Lãi suất thực tế được quy định trong hợp đồng tín dụng"),
        "calculation_table_reference":
            MessageLookupByLibrary.simpleMessage("Bảng tính lãi tham khảo"),
        "calendar": MessageLookupByLibrary.simpleMessage("Lịch"),
        "call_switchboard": MessageLookupByLibrary.simpleMessage(
            "Gọi đến tổng đài nếu quên mật khẩu đăng nhập"),
        "call_switchboard_support": MessageLookupByLibrary.simpleMessage(
            "Quý khách quên mật khẩu của ứng dụng KienlongBank cũ có thể gọi tổng đài ******** và làm theo hướng dẫn để được cấp lại mật khẩu."),
        "camera": MessageLookupByLibrary.simpleMessage("Camera"),
        "camera_permission": MessageLookupByLibrary.simpleMessage(
            "KienlongBank sử dụng máy ảnh trong chụp ảnh chứng từ, quét mã thanh toán, chuyển tiền"),
        "camera_pre":
            MessageLookupByLibrary.simpleMessage("Quyền truy cập camera"),
        "cancel": MessageLookupByLibrary.simpleMessage("Hủy"),
        "cancel_appointment":
            MessageLookupByLibrary.simpleMessage("Hủy lịch hẹn"),
        "cancel_appointment_success":
            MessageLookupByLibrary.simpleMessage("Hủy lịch hẹn thành công"),
        "cancel_card_physics": MessageLookupByLibrary.simpleMessage(
            "Quý khách chắc chắn muốn dừng mở thẻ"),
        "cancel_dialog": MessageLookupByLibrary.simpleMessage("Huỷ"),
        "cancel_reason": MessageLookupByLibrary.simpleMessage("Lý do huỷ"),
        "cancel_register": MessageLookupByLibrary.simpleMessage("Huỷ đăng ký"),
        "cancel_register_msg": MessageLookupByLibrary.simpleMessage(
            "Quý khách chắc chắn muốn hủy bỏ việc đăng ký?"),
        "cancel_update_msg": MessageLookupByLibrary.simpleMessage(
            "Quý khách chắc chắn muốn hủy bỏ việc cập nhật thông tin?"),
        "cancelation_date": MessageLookupByLibrary.simpleMessage("Ngày huỷ"),
        "capacity_data_mobile":
            MessageLookupByLibrary.simpleMessage("Dung lượng"),
        "capital_need": MessageLookupByLibrary.simpleMessage("Nhu cầu vốn"),
        "capture": MessageLookupByLibrary.simpleMessage("Chụp"),
        "capture_front_summary": m7,
        "card": MessageLookupByLibrary.simpleMessage("Thẻ"),
        "card_account": MessageLookupByLibrary.simpleMessage("Tài khoản thẻ"),
        "card_account_number":
            MessageLookupByLibrary.simpleMessage("Số tài khoản thẻ"),
        "card_activate_bottomsheet_des_enter_card":
            MessageLookupByLibrary.simpleMessage(
                "Nhập 4 số cuối của thẻ quý khách đã nhận được"),
        "card_activate_bottomsheet_title_enter_card":
            MessageLookupByLibrary.simpleMessage("Nhập 4 số cuối của thẻ"),
        "card_activate_sucess_label_usable_expire":
            MessageLookupByLibrary.simpleMessage("Hạn mức khả dụng"),
        "card_activate_sucess_title_success":
            MessageLookupByLibrary.simpleMessage("Kích hoạt thẻ thành công"),
        "card_class": MessageLookupByLibrary.simpleMessage("Hạng thẻ"),
        "card_code": MessageLookupByLibrary.simpleMessage("Mua mã thẻ"),
        "card_code_mobile": MessageLookupByLibrary.simpleMessage("Mã thẻ"),
        "card_control": MessageLookupByLibrary.simpleMessage("Kiểm soát thẻ"),
        "card_existed": MessageLookupByLibrary.simpleMessage(
            "Đã có 01 Thẻ cùng loại, không thể mở thêm"),
        "card_info": MessageLookupByLibrary.simpleMessage("Thông tin thẻ"),
        "card_info_button_activate":
            MessageLookupByLibrary.simpleMessage("Kích hoạt thẻ"),
        "card_info_button_loan_pay":
            MessageLookupByLibrary.simpleMessage("Thanh toán nợ thẻ"),
        "card_info_button_open_card":
            MessageLookupByLibrary.simpleMessage("Mở thẻ"),
        "card_info_button_pay_now":
            MessageLookupByLibrary.simpleMessage("Thanh toán ngay"),
        "card_info_button_setting":
            MessageLookupByLibrary.simpleMessage("Cài đặt thẻ"),
        "card_info_button_trouble_report":
            MessageLookupByLibrary.simpleMessage("Báo cáo sự cố"),
        "card_info_des_activate_card_receive": MessageLookupByLibrary.simpleMessage(
            "Khi nhận được thẻ, vui lòng bấm \"Kích hoạt thẻ\" và làm theo hướng dẫn để bắt đầu sử dụng. "),
        "card_info_des_in_shipping": MessageLookupByLibrary.simpleMessage(
            "Thẻ đang trong quá trình vận chuyển đến tay quý khách"),
        "card_info_dialog_button_lock_card":
            MessageLookupByLibrary.simpleMessage("Đồng ý"),
        "card_info_dialog_button_unlock_card":
            MessageLookupByLibrary.simpleMessage("Đồng ý"),
        "card_info_dialog_des_lock_card": MessageLookupByLibrary.simpleMessage(
            "Thẻ của quý khách sẽ không thể sử dụng cho đến khi mở khóa."),
        "card_info_dialog_des_lost_card": MessageLookupByLibrary.simpleMessage(
            "Thẻ đang bị báo mất hoặc thất lạc. Vui lòng liên hệ tổng đài hoặc chi nhánh để được hỗ trợ."),
        "card_info_dialog_title_lock_card":
            MessageLookupByLibrary.simpleMessage("Quý khách muốn khóa thẻ?"),
        "card_info_dialog_title_unlock_card":
            MessageLookupByLibrary.simpleMessage(
                "Quý khách có chắc chắn mở khóa thẻ này?"),
        "card_info_label_active_status":
            MessageLookupByLibrary.simpleMessage("Thẻ đang hoạt động"),
        "card_info_label_balance_pay": MessageLookupByLibrary.simpleMessage(
            "Quý khách đang có dư nợ thẻ cần thanh toán"),
        "card_info_label_card_full_name_owner":
            MessageLookupByLibrary.simpleMessage("Họ tên chủ thẻ"),
        "card_info_label_card_number":
            MessageLookupByLibrary.simpleMessage("Số thẻ"),
        "card_info_label_card_owner":
            MessageLookupByLibrary.simpleMessage("Tên chủ thẻ"),
        "card_info_label_credit_info":
            MessageLookupByLibrary.simpleMessage("Thông tin tín dụng"),
        "card_info_label_debt_calculate":
            MessageLookupByLibrary.simpleMessage("Dư nợ tạm tính"),
        "card_info_label_expired_day":
            MessageLookupByLibrary.simpleMessage("Ngày hết hạn"),
        "card_info_label_info":
            MessageLookupByLibrary.simpleMessage("Thông tin thẻ"),
        "card_info_label_limit_credit":
            MessageLookupByLibrary.simpleMessage("Hạn mức tín dụng"),
        "card_info_label_link_card":
            MessageLookupByLibrary.simpleMessage("Tài khoản liên kết"),
        "card_info_label_lock_status":
            MessageLookupByLibrary.simpleMessage("Thẻ đang khóa"),
        "card_info_label_main_card":
            MessageLookupByLibrary.simpleMessage("Thẻ chính"),
        "card_info_label_open_day":
            MessageLookupByLibrary.simpleMessage("Ngày mở thẻ"),
        "card_info_label_statement_date":
            MessageLookupByLibrary.simpleMessage("Ngày sao kê"),
        "card_info_label_supplementary_card":
            MessageLookupByLibrary.simpleMessage("Thẻ phụ"),
        "card_info_snack_msg_lock_success":
            MessageLookupByLibrary.simpleMessage("Khóa thẻ thành công"),
        "card_info_snack_msg_unlock_success":
            MessageLookupByLibrary.simpleMessage("Mở khóa thẻ thành công"),
        "card_info_title_card": MessageLookupByLibrary.simpleMessage("Thẻ"),
        "card_issuance_fee":
            MessageLookupByLibrary.simpleMessage("Phí phát hành thẻ"),
        "card_limit": MessageLookupByLibrary.simpleMessage("Giới hạn thẻ"),
        "card_limit_request":
            MessageLookupByLibrary.simpleMessage("Hạn mức yêu cầu"),
        "card_limit_spend_button_online_wallet":
            MessageLookupByLibrary.simpleMessage("Ví điện tử"),
        "card_limit_spend_label_atm_withdraw":
            MessageLookupByLibrary.simpleMessage("Rút tiền ATM"),
        "card_line": MessageLookupByLibrary.simpleMessage("Dòng thẻ"),
        "card_link_data":
            MessageLookupByLibrary.simpleMessage("Tài khoản liên kết thẻ"),
        "card_linked_account":
            MessageLookupByLibrary.simpleMessage("Tài khoản liên kết thẻ"),
        "card_management_unit":
            MessageLookupByLibrary.simpleMessage("Đơn vị quản lý thẻ"),
        "card_no_transaction_history": MessageLookupByLibrary.simpleMessage(
            "Quý khách chưa thực hiện giao dịch nào."),
        "card_number": MessageLookupByLibrary.simpleMessage("Số thẻ"),
        "card_owner": MessageLookupByLibrary.simpleMessage("Chủ thẻ"),
        "card_pay_card_empty_des_no_card": MessageLookupByLibrary.simpleMessage(
            "Đăng ký thẻ ngay để tận hưởng vô vàn ưu đãi hấp dẫn từ KienlongBank."),
        "card_pay_card_label_my_card":
            MessageLookupByLibrary.simpleMessage("Thẻ của tôi"),
        "card_pay_card_label_other_card":
            MessageLookupByLibrary.simpleMessage("Thẻ của người khác"),
        "card_pay_card_label_pay_for":
            MessageLookupByLibrary.simpleMessage("Thanh toán cho"),
        "card_pay_card_label_time":
            MessageLookupByLibrary.simpleMessage("Thời gian"),
        "card_paymented_money":
            MessageLookupByLibrary.simpleMessage("Thẻ đã mua"),
        "card_pin_success_label_card_type":
            MessageLookupByLibrary.simpleMessage("Loại thẻ"),
        "card_receiving_channel":
            MessageLookupByLibrary.simpleMessage("Kênh nhận thẻ"),
        "card_reissued_content": MessageLookupByLibrary.simpleMessage(
            "Thẻ hiện tại sẽ được thay thế bởi một Thẻ phi vật lý khác với số thẻ khác"),
        "card_reissued_successfully":
            MessageLookupByLibrary.simpleMessage("Cấp lại Thẻ thành công"),
        "card_reissued_title":
            MessageLookupByLibrary.simpleMessage("Cấp lại Thẻ"),
        "card_report_des_info":
            MessageLookupByLibrary.simpleMessage("Thông tin báo cáo"),
        "card_report_hint_text_accident":
            MessageLookupByLibrary.simpleMessage("Loại sự cố"),
        "card_setting_card_des_auto_pay": MessageLookupByLibrary.simpleMessage(
            "Sử dụng tính năng này để thanh toán tự động dư nợ thẻ tín dụng hàng tháng."),
        "card_setting_card_des_online_payment":
            MessageLookupByLibrary.simpleMessage(
                "Cho phép thẻ thanh toán trực tuyến trên các trang thương mại điện tử"),
        "card_setting_card_dialog_button_lock":
            MessageLookupByLibrary.simpleMessage("Khóa"),
        "card_setting_card_dialog_des_lock_pay_online":
            MessageLookupByLibrary.simpleMessage(
                "Quý khách đang thực hiện thao tác khóa thẻ. Quý khách sẽ không thể thanh toán trực tuyến cho đến khi mở khóa."),
        "card_setting_card_dialog_des_unlock_pay_online":
            MessageLookupByLibrary.simpleMessage(
                "Quý khách muốn mở chức năng thanh toán trực tuyến của thẻ này?"),
        "card_setting_card_dialog_title_lock_pay_online":
            MessageLookupByLibrary.simpleMessage(
                "Khóa thanh toán trực tuyến của thẻ?"),
        "card_setting_card_title_auto_pay":
            MessageLookupByLibrary.simpleMessage("Cài đặt trích nợ tự động"),
        "card_setting_card_title_online_payment":
            MessageLookupByLibrary.simpleMessage("Thanh toán trực tuyến"),
        "card_setting_loan_button_debit_balance":
            MessageLookupByLibrary.simpleMessage("Dư nợ sao kê"),
        "card_setting_loan_button_minimum":
            MessageLookupByLibrary.simpleMessage("Tối thiểu"),
        "card_setting_loan_dialog_des_turn_off_auto":
            MessageLookupByLibrary.simpleMessage(
                "Thẻ này sẽ không còn tự động thanh toán dư nợ tín dụng."),
        "card_setting_loan_dialog_des_turn_on_auto":
            MessageLookupByLibrary.simpleMessage(
                "Thẻ này sẽ có thể tự động thanh toán dư nợ tín dụng hàng tháng."),
        "card_setting_loan_dialog_title_turn_off_auto":
            MessageLookupByLibrary.simpleMessage("Tắt trích nợ tự động?"),
        "card_setting_loan_dialog_title_turn_on_auto":
            MessageLookupByLibrary.simpleMessage("Bật trích nợ tự động?"),
        "card_setting_loan_label_amount_pay":
            MessageLookupByLibrary.simpleMessage("Số tiền thanh toán"),
        "card_setting_loan_label_set_loan":
            MessageLookupByLibrary.simpleMessage("Trích nợ tự động"),
        "card_setting_loan_snack_msg_turn_off_success":
            MessageLookupByLibrary.simpleMessage(
                "Quý khách đã tắt trích nợ tự động"),
        "card_setting_loan_snack_msg_turn_on_success":
            MessageLookupByLibrary.simpleMessage(
                "Quý khách đã bật trích nợ tự động"),
        "card_spending": MessageLookupByLibrary.simpleMessage("Chi tiêu thẻ"),
        "card_statement": MessageLookupByLibrary.simpleMessage("Sao kê thẻ"),
        "card_statement_label_balance":
            MessageLookupByLibrary.simpleMessage("Dư nợ thẻ"),
        "card_statement_label_due_date":
            MessageLookupByLibrary.simpleMessage("Hạn thanh toán"),
        "card_statement_label_pay_min":
            MessageLookupByLibrary.simpleMessage("Thanh toán tối thiểu"),
        "card_statement_label_unpaid":
            MessageLookupByLibrary.simpleMessage("Chưa thanh toán"),
        "card_statement_title_statement":
            MessageLookupByLibrary.simpleMessage("Sao kê thẻ"),
        "card_type": MessageLookupByLibrary.simpleMessage("Loại giấy tờ"),
        "care_support_uni": MessageLookupByLibrary.simpleMessage(
            "Đơn vị chăm sóc/ Đơn vị hỗ trợ"),
        "care_unit_priority":
            MessageLookupByLibrary.simpleMessage("Đơn vị chăm sóc"),
        "category_not_empty": MessageLookupByLibrary.simpleMessage(
            "Vui lòng chọn ít nhất 1 phân loại"),
        "central_service":
            MessageLookupByLibrary.simpleMessage("Dịch vụ trọng tâm"),
        "chance_pass":
            MessageLookupByLibrary.simpleMessage("Thay đổi mật khẩu"),
        "chance_password_sucess": MessageLookupByLibrary.simpleMessage(
            "Thay đổi mật khẩu thành công"),
        "change": MessageLookupByLibrary.simpleMessage("Thay đổi"),
        "change_account_to_nick_name": MessageLookupByLibrary.simpleMessage(
            "Số tài khoản của bạn sẽ được ẩn đi và thay bằng nickname"),
        "change_authencation_success": MessageLookupByLibrary.simpleMessage(
            "Thay đổi phương thức xác thực thành công"),
        "change_image": MessageLookupByLibrary.simpleMessage("Thay đổi ảnh"),
        "change_pass": MessageLookupByLibrary.simpleMessage("Đổi mật khẩu"),
        "change_password_auth": MessageLookupByLibrary.simpleMessage(
            "Thay đổi mật khẩu / Xác thực"),
        "change_password_auth_content": MessageLookupByLibrary.simpleMessage(
            "Sử dụng cho các giao dịch dưới 1,000,000"),
        "change_pin_code": MessageLookupByLibrary.simpleMessage("Đổi mã PIN"),
        "change_pin_code_success":
            MessageLookupByLibrary.simpleMessage("Đổi mã pin thành công"),
        "change_service_package":
            MessageLookupByLibrary.simpleMessage("Đổi gói dịch vụ"),
        "change_to_etoken": MessageLookupByLibrary.simpleMessage(
            "Đã chuyển phương thức xác thực sang eToken (Soft Token)"),
        "change_to_physic_card_content": MessageLookupByLibrary.simpleMessage(
            "Thẻ hiện tại sẽ được thay thế bởi một Thẻ vật lý có cùng thông tin"),
        "change_to_physic_card_title":
            MessageLookupByLibrary.simpleMessage("Chuyển sang thẻ vật lý"),
        "change_user_account_msg": MessageLookupByLibrary.simpleMessage(
            "Bạn sẽ phải đăng nhập lại để thay đổi người dùng.\nChú ý: Nếu bạn đã cài đặt eToken, bạn cần phải hủy eToken trước khi chuyển sang tài khoản khác."),
        "change_user_account_title": MessageLookupByLibrary.simpleMessage(
            "Bạn có chắc muốn đổi tài khoản?"),
        "chat": MessageLookupByLibrary.simpleMessage("Trò chuyện"),
        "chatStarted":
            MessageLookupByLibrary.simpleMessage("Bắt đầu cuộc hội thoại"),
        "chat_direct": MessageLookupByLibrary.simpleMessage("Chat trực tiếp"),
        "chat_in_progress": MessageLookupByLibrary.simpleMessage("Chat hỗ trợ"),
        "chat_private": MessageLookupByLibrary.simpleMessage("Chat riêng"),
        "chat_public": MessageLookupByLibrary.simpleMessage("Kênh/Nhóm chat"),
        "chat_support_247":
            MessageLookupByLibrary.simpleMessage("Chat với tư vấn viên 24/7"),
        "check": MessageLookupByLibrary.simpleMessage("Kiểm tra"),
        "check_and_confirm_information_update_biology":
            MessageLookupByLibrary.simpleMessage(
                "Vui lòng kiểm tra kỹ và xác nhận thông tin cá nhân của quý khách để hoàn tất thủ tục xác thực."),
        "check_back_card": m8,
        "check_font_card": m9,
        "check_main_face_passport":
            MessageLookupByLibrary.simpleMessage("Kiểm tra mặt chính hộ chiếu"),
        "checkout_bill":
            MessageLookupByLibrary.simpleMessage("Thanh toán hoá đơn"),
        "children": MessageLookupByLibrary.simpleMessage("Con cái"),
        "choosePhoto": MessageLookupByLibrary.simpleMessage("Chọn ảnh"),
        "choose_account":
            MessageLookupByLibrary.simpleMessage("Chọn tài khoản"),
        "choose_bank": MessageLookupByLibrary.simpleMessage("Chọn ngân hàng"),
        "choose_beautiful_number_message": MessageLookupByLibrary.simpleMessage(
            "Quý khách có thể sở hữu cho mình những số tài khoản như ý theo lựa chọn sau:"),
        "choose_beautiful_number_title": MessageLookupByLibrary.simpleMessage(
            "Chọn ngay tài khoản đẹp miễn phí"),
        "choose_card_management_unit":
            MessageLookupByLibrary.simpleMessage("Chọn đơn vị quản lý thẻ"),
        "choose_customer_source":
            MessageLookupByLibrary.simpleMessage("Chọn nguồn khách hàng"),
        "choose_day": MessageLookupByLibrary.simpleMessage("Chọn ngày"),
        "choose_debit_payment_account": MessageLookupByLibrary.simpleMessage(
            "Chọn tài khoản thanh toán trích nợ"),
        "choose_debt_repayment_source":
            MessageLookupByLibrary.simpleMessage("Chọn nguồn trả nợ"),
        "choose_district":
            MessageLookupByLibrary.simpleMessage("Chọn quận huyện"),
        "choose_file": MessageLookupByLibrary.simpleMessage("Chọn tệp tin"),
        "choose_from_contacts":
            MessageLookupByLibrary.simpleMessage("Chọn từ danh bạ"),
        "choose_issue_date":
            MessageLookupByLibrary.simpleMessage("Chọn ngày cấp"),
        "choose_partner":
            MessageLookupByLibrary.simpleMessage("Chọn đối tác thu hộ"),
        "choose_passbook":
            MessageLookupByLibrary.simpleMessage("Chọn sổ tiết kiệm"),
        "choose_payment_card":
            MessageLookupByLibrary.simpleMessage("Chọn thẻ thanh toán"),
        "choose_reason_cancel":
            MessageLookupByLibrary.simpleMessage("Chọn lý do hủy"),
        "choose_service": MessageLookupByLibrary.simpleMessage("Chọn dịch vụ"),
        "choose_settlement_method":
            MessageLookupByLibrary.simpleMessage("Chọn hình thức tất toán"),
        "choose_share_money":
            MessageLookupByLibrary.simpleMessage("Chọn hình thức chia tiền"),
        "choose_support_department": MessageLookupByLibrary.simpleMessage(
            "Để phục vụ quý khách tốt hơn, quý khách vui lòng chọn bộ phận hỗ trợ phù hợp."),
        "choose_this_number":
            MessageLookupByLibrary.simpleMessage("Chọn số này"),
        "choose_time": MessageLookupByLibrary.simpleMessage("Chọn thời gian"),
        "choose_time_search":
            MessageLookupByLibrary.simpleMessage("Chọn thời gian tra cứu"),
        "choose_transaction_form":
            MessageLookupByLibrary.simpleMessage("Chọn hình thức giao dịch"),
        "choose_transaction_room":
            MessageLookupByLibrary.simpleMessage("Chọn phòng giao dịch"),
        "choose_transfer_type":
            MessageLookupByLibrary.simpleMessage("Hình thức giao dịch"),
        "choose_type_saving_what_you_want":
            MessageLookupByLibrary.simpleMessage(
                "Chọn loại tiết kiệm\nquý khách mong muốn"),
        "citizen_identity":
            MessageLookupByLibrary.simpleMessage("Căn cước công dân"),
        "city_province": MessageLookupByLibrary.simpleMessage("Tỉnh/Thành phố"),
        "claim_money": MessageLookupByLibrary.simpleMessage("Đòi tiền"),
        "classify": MessageLookupByLibrary.simpleMessage("Phân loại"),
        "click_to_retry":
            MessageLookupByLibrary.simpleMessage("Nhấn để thử lại"),
        "close": MessageLookupByLibrary.simpleMessage("Đóng"),
        "close_room": MessageLookupByLibrary.simpleMessage("Xóa phòng chat"),
        "close_room_msg": MessageLookupByLibrary.simpleMessage(
            "Bạn muốn đóng phòng chát này?"),
        "close_room_title": MessageLookupByLibrary.simpleMessage(
            "Bạn thực sự muốn đóng phòng chát này"),
        "cmnd_vnpost": MessageLookupByLibrary.simpleMessage("Số CMND/CCCD/HC"),
        "cn_pgd": MessageLookupByLibrary.simpleMessage("CN/PGD"),
        "code": MessageLookupByLibrary.simpleMessage("Mã số"),
        "code_atm": MessageLookupByLibrary.simpleMessage("Mã số máy ATM"),
        "code_info":
            MessageLookupByLibrary.simpleMessage("Số CMND/ CCCD/ Hộ chiếu"),
        "code_lookup_history":
            MessageLookupByLibrary.simpleMessage("Lịch sử tra cứu mã thu"),
        "collab": MessageLookupByLibrary.simpleMessage("Cộng tác"),
        "collaboration": MessageLookupByLibrary.simpleMessage("Cộng tác"),
        "collaborators": MessageLookupByLibrary.simpleMessage("Cộng tác viên"),
        "collapse": MessageLookupByLibrary.simpleMessage("Thu gọn"),
        "collateral": MessageLookupByLibrary.simpleMessage("Tài sản bảo đảm"),
        "collected": MessageLookupByLibrary.simpleMessage("Đã thu thập"),
        "collection_partner":
            MessageLookupByLibrary.simpleMessage("Đối tác thu hộ"),
        "common_account_name":
            MessageLookupByLibrary.simpleMessage("Tên tài khoản"),
        "common_agree": MessageLookupByLibrary.simpleMessage("Đồng ý"),
        "common_all": MessageLookupByLibrary.simpleMessage("Tất cả"),
        "common_balance": MessageLookupByLibrary.simpleMessage("Số dư"),
        "common_cancel": MessageLookupByLibrary.simpleMessage("Hủy"),
        "common_change": MessageLookupByLibrary.simpleMessage("Thay đổi"),
        "common_choose": MessageLookupByLibrary.simpleMessage("Lựa chọn"),
        "common_choose_city":
            MessageLookupByLibrary.simpleMessage("Chọn Tỉnh/Thành phố"),
        "common_choose_day": MessageLookupByLibrary.simpleMessage("Chọn ngày"),
        "common_choose_district":
            MessageLookupByLibrary.simpleMessage("Chọn Quận/Huyện"),
        "common_choose_option":
            MessageLookupByLibrary.simpleMessage("Tuỳ Chỉnh"),
        "common_choose_original":
            MessageLookupByLibrary.simpleMessage("Chọn tài khoản nguồn"),
        "common_choose_time":
            MessageLookupByLibrary.simpleMessage("Chọn thời gian"),
        "common_choose_wards":
            MessageLookupByLibrary.simpleMessage("Chọn Phường/Xã"),
        "common_classify": MessageLookupByLibrary.simpleMessage("Phân loại"),
        "common_comeback": MessageLookupByLibrary.simpleMessage("Quay lại"),
        "common_complete": MessageLookupByLibrary.simpleMessage("Hoàn thành"),
        "common_confirm": MessageLookupByLibrary.simpleMessage("Xác nhận"),
        "common_continue": MessageLookupByLibrary.simpleMessage("Tiếp tục"),
        "common_day_short": MessageLookupByLibrary.simpleMessage("ngày"),
        "common_detail_address":
            MessageLookupByLibrary.simpleMessage("Địa chỉ cụ thể"),
        "common_edit": MessageLookupByLibrary.simpleMessage("Chỉnh sửa"),
        "common_email": MessageLookupByLibrary.simpleMessage("Email"),
        "common_email_verify":
            MessageLookupByLibrary.simpleMessage("Xác thực email OTP"),
        "common_etoken_code": MessageLookupByLibrary.simpleMessage("Mã eToken"),
        "common_etoken_des_renew": MessageLookupByLibrary.simpleMessage(
            "Mã eToken được tự động làm mới sau 30 giây."),
        "common_etoken_reset_auto": MessageLookupByLibrary.simpleMessage(
            "Mã eToken tự động được làm mới sau mỗi"),
        "common_etoken_reset_auto_thirty":
            MessageLookupByLibrary.simpleMessage(" 30 giây"),
        "common_etoken_verify_des_type": MessageLookupByLibrary.simpleMessage(
            "Nhập mật khẩu eToken của quý khách để tiếp tục giao dịch"),
        "common_etoken_verify_title":
            MessageLookupByLibrary.simpleMessage("Xác thực eToken"),
        "common_fix": MessageLookupByLibrary.simpleMessage("Sửa"),
        "common_free": MessageLookupByLibrary.simpleMessage("Miễn phí"),
        "common_from": MessageLookupByLibrary.simpleMessage("từ"),
        "common_from_day": MessageLookupByLibrary.simpleMessage("Từ ngày"),
        "common_home": MessageLookupByLibrary.simpleMessage("Trang chủ"),
        "common_later": MessageLookupByLibrary.simpleMessage("Để sau"),
        "common_month_short": MessageLookupByLibrary.simpleMessage("tháng"),
        "common_name": MessageLookupByLibrary.simpleMessage("Họ và tên"),
        "common_network_connection_error_des": MessageLookupByLibrary.simpleMessage(
            "Có thể do mạng yếu hoặc chưa kết nối internet, quý khách vui lòng kiểm tra và thử lại nhé."),
        "common_network_connection_error_title":
            MessageLookupByLibrary.simpleMessage("Kiểm tra kết nối mạng"),
        "common_nice_account_guide": MessageLookupByLibrary.simpleMessage(
            "Để tìm các số tài khoản có dãy số mong muốn là"),
        "common_nice_account_hintext":
            MessageLookupByLibrary.simpleMessage("Nhập dãy số mong muốn"),
        "common_nice_account_title":
            MessageLookupByLibrary.simpleMessage("Tìm số tài khoản đẹp"),
        "common_niceaccount_bottomsheet_des_note":
            MessageLookupByLibrary.simpleMessage("Lưu ý:"),
        "common_niceaccount_bottomsheet_des_search":
            MessageLookupByLibrary.simpleMessage(
                "Nhập dãy số mong muốn của số tài khoản cần tìm:"),
        "common_niceaccount_bottomsheet_des_search1":
            MessageLookupByLibrary.simpleMessage(
                "Để tìm các số tài khoản có dãy số mong muốn là 22"),
        "common_niceaccount_bottomsheet_des_search2":
            MessageLookupByLibrary.simpleMessage(
                "Để tìm các số tài khoản có dãy số mong muốn là 2345"),
        "common_niceaccount_bottomsheet_des_search3":
            MessageLookupByLibrary.simpleMessage(
                "Để tìm các số tài khoản có dãy số mong muốn là 686868"),
        "common_niceaccount_bottomsheet_title_guide":
            MessageLookupByLibrary.simpleMessage(
                "Hướng dẫn tìm số tài khoản đẹp"),
        "common_niceaccount_des_list":
            MessageLookupByLibrary.simpleMessage("Danh sách số tài khoản"),
        "common_niceaccount_empty_des_nosearch":
            MessageLookupByLibrary.simpleMessage(
                "Nhập dãy số mong muốn của số tài khoản cần tìm lên ô tìm kiếm để chọn ra tài khoản yêu thích."),
        "common_niceaccount_empty_title_nosearch":
            MessageLookupByLibrary.simpleMessage("Không tìm thấy số tài khoản"),
        "common_niceaccount_empty_title_search":
            MessageLookupByLibrary.simpleMessage(
                "Sở hữu ngay tài khoản đẹp nhất!"),
        "common_niceaccount_hintext_search":
            MessageLookupByLibrary.simpleMessage(
                "Sở hữu ngay tài khoản đẹp nhất!"),
        "common_niceaccount_title_nice":
            MessageLookupByLibrary.simpleMessage("Tài khoản tự chọn"),
        "common_niceaccount_title_vip":
            MessageLookupByLibrary.simpleMessage("Tài khoản VIP"),
        "common_no": MessageLookupByLibrary.simpleMessage("Không"),
        "common_one_month":
            MessageLookupByLibrary.simpleMessage("1 tháng gần nhất"),
        "common_open_new": MessageLookupByLibrary.simpleMessage("Mở mới"),
        "common_original_account":
            MessageLookupByLibrary.simpleMessage("Tài khoản nguồn"),
        "common_other": MessageLookupByLibrary.simpleMessage("Khác"),
        "common_other_transact":
            MessageLookupByLibrary.simpleMessage("Giao dịch khác"),
        "common_otp_verify_title":
            MessageLookupByLibrary.simpleMessage("Xác thực SMS OTP"),
        "common_passcondition1":
            MessageLookupByLibrary.simpleMessage("Có 8-14 ký tự"),
        "common_passcondition2": MessageLookupByLibrary.simpleMessage(
            "Bao gồm chữ hoa và chữ thường"),
        "common_pay_account":
            MessageLookupByLibrary.simpleMessage("Tài khoản thanh toán"),
        "common_phone_number":
            MessageLookupByLibrary.simpleMessage("Số điện thoại"),
        "common_register": MessageLookupByLibrary.simpleMessage("Đăng ký"),
        "common_save": MessageLookupByLibrary.simpleMessage("Lưu"),
        "common_save_code": MessageLookupByLibrary.simpleMessage("Lưu mã"),
        "common_save_photo": MessageLookupByLibrary.simpleMessage("Lưu ảnh"),
        "common_search": MessageLookupByLibrary.simpleMessage("Tìm kiếm"),
        "common_seven_days":
            MessageLookupByLibrary.simpleMessage("7 ngày gần nhất"),
        "common_share": MessageLookupByLibrary.simpleMessage("Chia sẻ"),
        "common_take_picture":
            MessageLookupByLibrary.simpleMessage("Lấy ảnh này"),
        "common_term_condition1": MessageLookupByLibrary.simpleMessage(
            "Bằng việc đăng ký mở tài khoản, quý khách đã chấp nhận "),
        "common_term_condition2":
            MessageLookupByLibrary.simpleMessage("điều kiện và điều khoản "),
        "common_term_condition3":
            MessageLookupByLibrary.simpleMessage("của KienlongBank "),
        "common_text_understand":
            MessageLookupByLibrary.simpleMessage("Đã hiểu"),
        "common_three_months":
            MessageLookupByLibrary.simpleMessage("3 tháng gần nhất"),
        "common_title_app":
            MessageLookupByLibrary.simpleMessage("KienlongBank"),
        "common_to": MessageLookupByLibrary.simpleMessage("đến"),
        "common_to_day": MessageLookupByLibrary.simpleMessage("Đến ngày"),
        "common_today": MessageLookupByLibrary.simpleMessage("Hôm nay"),
        "common_update": MessageLookupByLibrary.simpleMessage("Cập nhật"),
        "common_update_app_des": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng cập nhật phiên bản mới nhất của ứng dụng để trải nghiệm thêm nhiều tính năng thú vị nhé!"),
        "common_update_app_title":
            MessageLookupByLibrary.simpleMessage("Cập nhật phiên bản mới"),
        "common_update_now":
            MessageLookupByLibrary.simpleMessage("Cập nhật ngay"),
        "common_verify": MessageLookupByLibrary.simpleMessage("Xác thực"),
        "common_view_detail":
            MessageLookupByLibrary.simpleMessage("Xem chi tiết"),
        "common_withdraw": MessageLookupByLibrary.simpleMessage("Rút tiền"),
        "common_year_short": MessageLookupByLibrary.simpleMessage("năm"),
        "common_year_text": MessageLookupByLibrary.simpleMessage("Năm"),
        "common_yes": MessageLookupByLibrary.simpleMessage("Có"),
        "common_yesterday": MessageLookupByLibrary.simpleMessage("Hôm qua"),
        "complete": MessageLookupByLibrary.simpleMessage("Hoàn thành"),
        "completed": MessageLookupByLibrary.simpleMessage("Hoàn tất"),
        "conditions":
            MessageLookupByLibrary.simpleMessage("điều kiện và điều khoản"),
        "confirm": MessageLookupByLibrary.simpleMessage("Xác nhận"),
        "confirm1": MessageLookupByLibrary.simpleMessage("Xác nhận"),
        "confirm_accumulation":
            MessageLookupByLibrary.simpleMessage("Xác nhận tích lũy"),
        "confirm_after": MessageLookupByLibrary.simpleMessage("Để sau"),
        "confirm_cancel_appointment":
            MessageLookupByLibrary.simpleMessage("Xác nhận huỷ lịch"),
        "confirm_cancel_appointment_question":
            MessageLookupByLibrary.simpleMessage(
                "Bạn có chắc chắn muốn huỷ lịch hẹn này không?"),
        "confirm_counseling_cancel": MessageLookupByLibrary.simpleMessage(
            "Quý khách có chắc chắn muốn hủy lịch tư vấn này?"),
        "confirm_ctv": MessageLookupByLibrary.simpleMessage(
            "Quý khách chưa nhận được hoa hồng do chưa xác nhận CTV. Nhấn vào đây để xác nhận ngay nhé."),
        "confirm_delete_transfer_schedule":
            MessageLookupByLibrary.simpleMessage(
                "Bạn muốn xoá lịch chuyển tiền này?"),
        "confirm_delete_transfer_template":
            MessageLookupByLibrary.simpleMessage(
                "Bạn muốn xoá mẫu chuyển tiền này?"),
        "confirm_info":
            MessageLookupByLibrary.simpleMessage("Xác nhận thông tin"),
        "confirm_message_cancel_review_transaction":
            MessageLookupByLibrary.simpleMessage(
                "Khách hàng đồng ý hủy tra soát thì giao dịch này sẽ không được tra soát lần 2 trên App KienlongBank Plus, khách hàng thắc mắc liên hệ hotline hoặc CN/PGD gần nhất để được hỗ trợ."),
        "confirm_new_password":
            MessageLookupByLibrary.simpleMessage("Xác nhận mật khẩu mới"),
        "confirm_new_pin_code":
            MessageLookupByLibrary.simpleMessage("Xác nhận PIN mới"),
        "confirm_pay":
            MessageLookupByLibrary.simpleMessage("Xác nhận thanh toán"),
        "confirm_payment":
            MessageLookupByLibrary.simpleMessage("Xác nhận chuyển khoản"),
        "confirm_payment_invest":
            MessageLookupByLibrary.simpleMessage("Xác nhận Đầu tư"),
        "confirm_payment_residence":
            MessageLookupByLibrary.simpleMessage("Xác nhận đặt mua"),
        "confirm_read_understood": MessageLookupByLibrary.simpleMessage(
            "tôi xác nhận rằng đã đọc và hiểu các thông tin của sản phẩm vay cầm cố Tiền gửi tiết kiệm online."),
        "confirm_term_1": MessageLookupByLibrary.simpleMessage(
            "Tôi đã đọc, hiểu rõ và đồng ý với"),
        "confirm_tranfer":
            MessageLookupByLibrary.simpleMessage("Xác nhận chuyển tiền"),
        "confirm_transaction":
            MessageLookupByLibrary.simpleMessage("Xác nhận giao dịch"),
        "confirm_use_account_ksbank_for_login":
            MessageLookupByLibrary.simpleMessage(
                "Sử dụng tài khoản KienlongBank của bạn để đăng nhập vào ứng dụng"),
        "connect_now": MessageLookupByLibrary.simpleMessage("Kết nối ngay"),
        "connected": MessageLookupByLibrary.simpleMessage("Đã kết nối"),
        "connection_is_interrupted":
            MessageLookupByLibrary.simpleMessage("Kết nối bị gián đoạn"),
        "consultation_schedule":
            MessageLookupByLibrary.simpleMessage("Lịch tư vấn"),
        "consumer_loan": MessageLookupByLibrary.simpleMessage("Vay tiêu dùng"),
        "contact": MessageLookupByLibrary.simpleMessage("Danh bạ"),
        "contact2": MessageLookupByLibrary.simpleMessage("Liên hệ"),
        "contact_address":
            MessageLookupByLibrary.simpleMessage("Địa chỉ liên lạc"),
        "contact_customer_service":
            MessageLookupByLibrary.simpleMessage("Liên hệ CSKH"),
        "contact_get_money":
            MessageLookupByLibrary.simpleMessage("Danh sách người nhận"),
        "contact_list":
            MessageLookupByLibrary.simpleMessage("Danh sách liên hệ"),
        "contact_of_beneficiaries":
            MessageLookupByLibrary.simpleMessage("Danh bạ người thụ hưởng"),
        "contact_permission": MessageLookupByLibrary.simpleMessage(
            "KienlongBank App cần truy cập danh bạ để lấy số điện thoại"),
        "contact_phone_priority":
            MessageLookupByLibrary.simpleMessage("Số điện thoại liên hệ"),
        "contact_switchboard":
            MessageLookupByLibrary.simpleMessage("Liên hệ tổng đài"),
        "content": MessageLookupByLibrary.simpleMessage("Nội dung"),
        "content_branch": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng cung cấp địa chỉ để KienlongBank có thể kết nối tới chi nhánh gần nhất nhằm tư vấn và phục vụ tốt hơn"),
        "content_card_option_POST_payment":
            MessageLookupByLibrary.simpleMessage(
                "Cho phép thẻ thanh toán qua các thiết bị POS"),
        "content_card_option_withdraw_money_ATM":
            MessageLookupByLibrary.simpleMessage(
                "Cho phép rút tiền tại các máy ATM"),
        "content_contact_permission": MessageLookupByLibrary.simpleMessage(
            "Vui lòng cấp quyền truy cập cho KienlongBank tại mục Cài đặt trên thiết bị quý khách"),
        "content_dialog_off_POST_payment": MessageLookupByLibrary.simpleMessage(
            "Quý khách có chắc chắn muốn tắt tính năng Thanh toán qua POS"),
        "content_dialog_off_withdraw_money_ATM":
            MessageLookupByLibrary.simpleMessage(
                "Quý khách có chắc chắn muốn tắt tính năng Rút tiền qua ATM"),
        "content_dialog_on_POST_payment": MessageLookupByLibrary.simpleMessage(
            "Quý khách có chắc chắn muốn bật tính năng Thanh toán qua POS"),
        "content_dialog_on_withdraw_money_ATM":
            MessageLookupByLibrary.simpleMessage(
                "Quý khách có chắc chắn muốn bật tính năng Rút tiền qua ATM"),
        "content_header_1":
            MessageLookupByLibrary.simpleMessage("Miễn phí phát hành thẻ"),
        "content_header_2": MessageLookupByLibrary.simpleMessage(
            "Miễn phí rút tiền mặt tại tất cả cây ATM trên toàn quốc"),
        "content_header_jcb": MessageLookupByLibrary.simpleMessage(
            "Giao dịch lên đến 500 triệu VND/ngày"),
        "content_header_napas": MessageLookupByLibrary.simpleMessage(
            "Giao dịch lên đến 200 triệu VND/ngày"),
        "content_like_this":
            MessageLookupByLibrary.simpleMessage("Nội dung trông như thế này"),
        "content_non_physical_card": MessageLookupByLibrary.simpleMessage(
            "Khả năng thanh toán như thẻ vật lý, giảm thiểu rủi ro mất thẻ và lộ thông tin thẻ."),
        "content_of_payment_request": MessageLookupByLibrary.simpleMessage(
            "Yêu cầu tất toán khoản vay của quý khách đã được tiếp nhận với nội dung sau:"),
        "content_physical_card": MessageLookupByLibrary.simpleMessage(
            "Đăng ký online nhanh chóng, nhận thẻ tận tay sau 5-7 ngày."),
        "content_qr_scaner": MessageLookupByLibrary.simpleMessage(
            "Quét mã QR/Mã vạch/Mã sản phẩm & dịch vụ KienlongBank"),
        "content_receive_money":
            MessageLookupByLibrary.simpleMessage("Nội dung nhận tiền"),
        "content_title_guide": MessageLookupByLibrary.simpleMessage(
            "Giới thiệu bạn bè, người thân dùng mã giới thiệu theo hướng dẫn bên dưới nhé:"),
        "context_successful_authentication": MessageLookupByLibrary.simpleMessage(
            "Thông tin của bạn đã được cập nhật thành công, hệ thống đang tiến hành xác minh dữ liệu."),
        "continue_send_gift":
            MessageLookupByLibrary.simpleMessage("Tiếp tục gửi quà tặng"),
        "continue_send_lucky_money":
            MessageLookupByLibrary.simpleMessage("Tiếp tục gửi lì xì"),
        "continue_ui": MessageLookupByLibrary.simpleMessage("Tiếp tục"),
        "contract_code": MessageLookupByLibrary.simpleMessage("Số hợp đồng"),
        "control_spending":
            MessageLookupByLibrary.simpleMessage("Kiểm soát chi tiêu"),
        "convert_card_success": MessageLookupByLibrary.simpleMessage(
            "Chuyển sang Thẻ vật lý thành công"),
        "convert_ptxt_to_sms": MessageLookupByLibrary.simpleMessage(
            "Quý khách đang thực hiện chuyển đổi PTXT về SMS. Một số chức năng giao dịch sẽ không được hỗ trợ PTXT này."),
        "copied": MessageLookupByLibrary.simpleMessage(
            "Đã sao chép mục vào Bộ nhớ tạm."),
        "copy": MessageLookupByLibrary.simpleMessage("Sao chép"),
        "copy_acc_success": MessageLookupByLibrary.simpleMessage(
            "Sao chép số tài khoản thành công"),
        "copy_account_number": MessageLookupByLibrary.simpleMessage(
            "Số tài khoản đã lưu vào bộ nhớ tạm"),
        "copy_card": MessageLookupByLibrary.simpleMessage("Sao chép mã thẻ"),
        "copy_card_code":
            MessageLookupByLibrary.simpleMessage("Sao chép mã thẻ"),
        "copy_card_code_and_serial_number":
            MessageLookupByLibrary.simpleMessage("Sao chép mã thẻ và số seri"),
        "copy_code":
            MessageLookupByLibrary.simpleMessage("Sao chép mã nạp tiền"),
        "copy_code_success": MessageLookupByLibrary.simpleMessage(
            "Đã sao chép mã giới thiệu thành công"),
        "copy_nickname_success": MessageLookupByLibrary.simpleMessage(
            "Sao chép nickname thành công"),
        "copy_serial_number":
            MessageLookupByLibrary.simpleMessage("Sao chép số seri"),
        "copy_success":
            MessageLookupByLibrary.simpleMessage("Đã sao chép thành công!"),
        "counseling_empty": MessageLookupByLibrary.simpleMessage(
            "Quý khách chưa có lịch hẹn tư vấn"),
        "counseling_empty_message": MessageLookupByLibrary.simpleMessage(
            "Đặt lịch hẹn ngay để tư vấn viên của chúng tôi có thể phục vụ quý khách hiệu quả nhất."),
        "counselors": MessageLookupByLibrary.simpleMessage("Tư vấn viên"),
        "counters_ekyc_open_account_msg": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng xác thực định danh tại quầy để mở thêm tài khoản"),
        "create_account_success":
            MessageLookupByLibrary.simpleMessage("Mở tài khoản thành công"),
        "create_account_success_summary": MessageLookupByLibrary.simpleMessage(
            "Chúc mừng Quý khách đã mở tài khoản ngân hàng KienlongBank thành công. Tài khoản có thể nhận tiền đến. Chúng tôi đang rà soát để mở chức năng chuyển tiền đi. Xin vui lòng đợi trong ít phút. Trân trọng cảm ơn Quý khách."),
        "create_appointment_success":
            MessageLookupByLibrary.simpleMessage("Tạo lịch hẹn thành công"),
        "create_calendar_transaction_success":
            MessageLookupByLibrary.simpleMessage(
                "Lập lịch chuyển tiền thành công"),
        "create_counseling":
            MessageLookupByLibrary.simpleMessage("Tạo lịch hẹn"),
        "create_date": MessageLookupByLibrary.simpleMessage("Ngày tạo"),
        "create_email_password":
            MessageLookupByLibrary.simpleMessage("Cài đặt thông tin bảo mật"),
        "create_etoken_content": MessageLookupByLibrary.simpleMessage(
            "Thuật toán xác thực giao dịch sau khi khách hàng chọn Xác nhận, Kết quả xác thực được thông báo tức thời."),
        "create_inquiry_success": MessageLookupByLibrary.simpleMessage(
            "Khởi tạo tra soát thành công!"),
        "create_message": MessageLookupByLibrary.simpleMessage("Tạo tin nhắn"),
        "create_new": MessageLookupByLibrary.simpleMessage("Tạo mới"),
        "create_new_password":
            MessageLookupByLibrary.simpleMessage("Tạo mật khẩu mới"),
        "create_new_request_transfer_money":
            MessageLookupByLibrary.simpleMessage("Tạo yêu cầu chuyển tiền"),
        "create_nickname": MessageLookupByLibrary.simpleMessage(
            "Sáng tạo nickname, đặt tên tài khoản"),
        "create_nickname_describe": MessageLookupByLibrary.simpleMessage(
            "Tạo ngay nickname cá tính để thay số tài khoản giúp dễ nhớ, dễ giao dịch."),
        "create_request_review_transaction":
            MessageLookupByLibrary.simpleMessage("Tạo yêu cầu tra soát"),
        "create_review_transaction_message": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng chọn loại giao dịch cần thực hiện tra soát từ danh sách bên dưới."),
        "create_schedule": MessageLookupByLibrary.simpleMessage("Tạo lịch"),
        "create_schedule_transfer":
            MessageLookupByLibrary.simpleMessage("Tạo lịch chuyển tiền"),
        "created_time_request":
            MessageLookupByLibrary.simpleMessage("Thời gian tạo yêu cầu"),
        "credit_agreement":
            MessageLookupByLibrary.simpleMessage("Hợp đồng tín dụng"),
        "credit_card": MessageLookupByLibrary.simpleMessage("Thẻ tín dụng"),
        "credit_info_empty_des_no_card": MessageLookupByLibrary.simpleMessage(
            "Đăng ký thẻ ngay để tận hưởng vô vàn ưu đãi hấp dẫn từ KienlongBank. "),
        "credit_info_empty_title_no_card":
            MessageLookupByLibrary.simpleMessage("Quý khách chưa có thẻ nào"),
        "credit_mortgage_contract": MessageLookupByLibrary.simpleMessage(
            "Hợp đồng tín dụng kiêm hợp đồng cầm cố và khế ước nhận nợ"),
        "criteria_priority":
            MessageLookupByLibrary.simpleMessage("Tiêu chí định danh"),
        "criteria_priority_klb": MessageLookupByLibrary.simpleMessage(
            "Các tiêu chí/điều kiện gia nhập hội viên KienlongBank Priority"),
        "current_pin_code":
            MessageLookupByLibrary.simpleMessage("PIN hiện tại"),
        "current_version_app_not_support": MessageLookupByLibrary.simpleMessage(
            "Phiên bản hiện tại của ứng dụng hiện không được hỗ trợ. Vui lòng cập nhật ứng dụng để tiếp tục sử dụng."),
        "custom": MessageLookupByLibrary.simpleMessage("Tùy chọn"),
        "customer": MessageLookupByLibrary.simpleMessage("Khách hàng"),
        "customer_code": MessageLookupByLibrary.simpleMessage("Mã khách hàng"),
        "customer_code_guide": MessageLookupByLibrary.simpleMessage(
            "Mã khách hàng được in trên hoá đơn của quý khách. Trong trường hợp thanh toán tiền điện thoại, mã khách hàng chính là số điện thoại"),
        "customer_contact_number":
            MessageLookupByLibrary.simpleMessage("Số liên lạc của khách hàng"),
        "customer_created_successfully":
            MessageLookupByLibrary.simpleMessage("Tạo khách hàng thành công"),
        "customer_detail":
            MessageLookupByLibrary.simpleMessage("Chi tiết khách hàng"),
        "customer_have": MessageLookupByLibrary.simpleMessage("Quý khách có"),
        "customer_information":
            MessageLookupByLibrary.simpleMessage("Thông tin khách hàng"),
        "customer_resources":
            MessageLookupByLibrary.simpleMessage("Nguồn khách hàng"),
        "daily": MessageLookupByLibrary.simpleMessage("Hằng ngày"),
        "dark": MessageLookupByLibrary.simpleMessage("Tối"),
        "data_card_phone": MessageLookupByLibrary.simpleMessage("Thẻ 3G/4G"),
        "data_package": MessageLookupByLibrary.simpleMessage("Gói dữ liệu"),
        "data_topup_info":
            MessageLookupByLibrary.simpleMessage("Thông tin nạp Data 3G/4G"),
        "date_and_time": MessageLookupByLibrary.simpleMessage("Ngày & giờ"),
        "date_aturity": MessageLookupByLibrary.simpleMessage("Ngày đến hạn"),
        "date_counseling_before": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng chọn thời gian đặt lịch lớn hơn hiện tại."),
        "date_counseling_empty": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng chọn ngày tư vấn"),
        "date_due": MessageLookupByLibrary.simpleMessage("Ngày đáo hạn"),
        "date_format": MessageLookupByLibrary.simpleMessage("dd/mm/yyyy"),
        "date_identity": MessageLookupByLibrary.simpleMessage("Ngày cấp"),
        "date_not_empty": MessageLookupByLibrary.simpleMessage(
            "Xin lỗi bạn không thể để thời gian trống."),
        "date_of_purchase": MessageLookupByLibrary.simpleMessage("Ngày mua"),
        "day": MessageLookupByLibrary.simpleMessage("Hàng ngày"),
        "day1": MessageLookupByLibrary.simpleMessage("Ngày"),
        "day_month": MessageLookupByLibrary.simpleMessage("Ngày tháng"),
        "days": MessageLookupByLibrary.simpleMessage(" ngày"),
        "deadline": MessageLookupByLibrary.simpleMessage("Hạn trả nợ sắp tới"),
        "debit_card": MessageLookupByLibrary.simpleMessage("Thẻ ghi nợ"),
        "debit_card_terms_conditions": MessageLookupByLibrary.simpleMessage(
            "Điều khoản và điều kiện phát hành, sử dụng và thanh toán Thẻ ghi nợ KienlongBank"),
        "debit_payment_account": MessageLookupByLibrary.simpleMessage(
            "Tài khoản thanh toán trích nợ"),
        "debit_setting":
            MessageLookupByLibrary.simpleMessage("Cài đặt trích nợ tự động"),
        "debt_credit": MessageLookupByLibrary.simpleMessage("Dư nợ"),
        "debt_payment_method":
            MessageLookupByLibrary.simpleMessage("Phương thức trả nợ"),
        "debt_repayment_source":
            MessageLookupByLibrary.simpleMessage("Nguồn trả nợ"),
        "deducting_account":
            MessageLookupByLibrary.simpleMessage("Tài khoản trích tiền"),
        "default_theme":
            MessageLookupByLibrary.simpleMessage("Chủ đề mặc định"),
        "delete": MessageLookupByLibrary.simpleMessage("Xóa"),
        "delete_account": MessageLookupByLibrary.simpleMessage("Xoá tài khoản"),
        "delete_account_content": MessageLookupByLibrary.simpleMessage(
            "Theo quy định của pháp luật hiện hành, xin mời bạn đến các điểm giao dịch của KienlongBank để hoàn tất thủ tục xoá tài khoản. Trường hợp cần thêm thông tin vui lòng liên hệ tư vấn viên của KienlongBank để được hỗ trợ."),
        "delete_client_confirm": MessageLookupByLibrary.simpleMessage(
            "Thông tin về khách hàng này sẽ không còn được lưu lại trong hồ sơ."),
        "delete_command": MessageLookupByLibrary.simpleMessage("Xoá lệnh"),
        "delete_confirm": MessageLookupByLibrary.simpleMessage(
            "Quý khách có chắc chắn muốn xoá ?"),
        "delete_count_error_pin":
            MessageLookupByLibrary.simpleMessage("Xóa số lần nhập sai PIN"),
        "delete_customer":
            MessageLookupByLibrary.simpleMessage("Xoá khách hàng này?"),
        "delete_image": MessageLookupByLibrary.simpleMessage("Xoá ảnh"),
        "delete_inquiry": MessageLookupByLibrary.simpleMessage("Huỷ tra soát"),
        "delete_saving_VNP": MessageLookupByLibrary.simpleMessage(
            "Bạn có chắc chắn muốn xoá thông tin này?"),
        "denominations": MessageLookupByLibrary.simpleMessage("Mệnh giá"),
        "deny": MessageLookupByLibrary.simpleMessage("Từ chối"),
        "deny_permission": MessageLookupByLibrary.simpleMessage("Từ chối"),
        "department": MessageLookupByLibrary.simpleMessage("Phòng ban"),
        "deposit": MessageLookupByLibrary.simpleMessage("Tiền gửi"),
        "deposit_amount": MessageLookupByLibrary.simpleMessage("Số tiền nạp"),
        "deposit_certificate_confirm_info_label_download_device":
            MessageLookupByLibrary.simpleMessage("Tải về máy"),
        "deposit_certificate_detail_label_state":
            MessageLookupByLibrary.simpleMessage("Trạng thái"),
        "deposit_certificate_label_due_date":
            MessageLookupByLibrary.simpleMessage("Ngày đến hạn"),
        "deposit_certificate_open_account_label_send_account":
            MessageLookupByLibrary.simpleMessage("Tài khoản gửi tiền"),
        "deposit_confirmation":
            MessageLookupByLibrary.simpleMessage("Giấy xác nhận tiền gửi"),
        "deposit_to_target_saving_success":
            MessageLookupByLibrary.simpleMessage(
                "Nạp tiền vào tài khoản tích luỹ mục tiêu thành công"),
        "describe_validity_period": MessageLookupByLibrary.simpleMessage(
            "Qua thời gian hiệu lực, số tiền quà tặng sẽ được tự động chuyển đến người nhận quà."),
        "description_content":
            MessageLookupByLibrary.simpleMessage("Mô tả nội dung"),
        "description_update_customer_vnpost": MessageLookupByLibrary.simpleMessage(
            "Vui lòng bổ sung thêm thông tin dưới đây để giúp KienlongBank phục vụ quý khách hiệu quả nhất"),
        "description_verify_adress_email": MessageLookupByLibrary.simpleMessage(
            "Xác thực email giúp quý khách tăng cường bảo mật tài khoản và nhận đầy đủ thông báo giao dịch qua email."),
        "descriptiont_chance_password": MessageLookupByLibrary.simpleMessage(
            "Mật khẩu có độ dài ít nhất 8 ký tự, bao gồm chữ hoa và chữ thường, ký tự số và ký tự đặc biệt"),
        "detail": MessageLookupByLibrary.simpleMessage("Chi tiết"),
        "detail_goals": MessageLookupByLibrary.simpleMessage(
            "Chi tiết Tích Tiểu Thành Đại"),
        "detail_transaction":
            MessageLookupByLibrary.simpleMessage("Chi tiết giao dịch"),
        "device_enable_accessibility_service": MessageLookupByLibrary.simpleMessage(
            "Thiết bị đang bật quyền trợ năng (Accessibility) cho các ứng dụng:"),
        "device_list":
            MessageLookupByLibrary.simpleMessage("Danh sách thiết bị"),
        "device_manager":
            MessageLookupByLibrary.simpleMessage("Quản lý thiết bị"),
        "device_no_login": MessageLookupByLibrary.simpleMessage(
            "Xác thực vân tay/ gương mặt của quý khách đã thay đổi.Vui lòng sử dụng mật khẩu để đăng nhập."),
        "dialog_confirm_no": MessageLookupByLibrary.simpleMessage("Không"),
        "dialog_confirm_yes": MessageLookupByLibrary.simpleMessage("Có"),
        "dialog_select_day": m10,
        "digital_entertainment_world":
            MessageLookupByLibrary.simpleMessage("Thế giới giải trí số HBO"),
        "direct": MessageLookupByLibrary.simpleMessage("Chỉ đường"),
        "directions": MessageLookupByLibrary.simpleMessage("Đường đi"),
        "disable_etoken":
            MessageLookupByLibrary.simpleMessage("Vô hiệu hóa eToken"),
        "disable_etoken_msg": MessageLookupByLibrary.simpleMessage(
            "Quý khách đang thực hiện vô hiệu hóa phương thức xác thực bằng eToken và chuyển sang phương thức xác thực bằng SMS OTP."),
        "disable_etoken_note_msg": MessageLookupByLibrary.simpleMessage(
            "Xác thực bằng SMS OTP chỉ áp dụng trên lãnh thổ Việt Nam và có áp dụng hạn mức tối đa."),
        "disable_etoken_warning_msg": MessageLookupByLibrary.simpleMessage(
            "Tôi hiểu rõ các hạn chế và rủi ro khi sử dụng xác thực SMS OTP"),
        "disable_nickname":
            MessageLookupByLibrary.simpleMessage("Tắt hoạt động nickname?"),
        "disable_nickname_content": MessageLookupByLibrary.simpleMessage(
            "Người khác sẽ không thể chuyển tiền cho bạn qua nickname này."),
        "disbursed": MessageLookupByLibrary.simpleMessage("Giải ngân"),
        "disbursed_account":
            MessageLookupByLibrary.simpleMessage("Tài khoản nhận giải ngân"),
        "disbursement_account":
            MessageLookupByLibrary.simpleMessage("Tài khoản giải ngân"),
        "disbursement_bank":
            MessageLookupByLibrary.simpleMessage("Ngân hàng giải ngân"),
        "disbursement_date":
            MessageLookupByLibrary.simpleMessage("Ngày giải ngân"),
        "disbursement_details":
            MessageLookupByLibrary.simpleMessage("Chi tiết giải ngân"),
        "disbursement_info":
            MessageLookupByLibrary.simpleMessage("Thông tin giải ngân"),
        "disbursement_information":
            MessageLookupByLibrary.simpleMessage("Thông tin giải ngân"),
        "discover": MessageLookupByLibrary.simpleMessage("Khám phá"),
        "discover_now": MessageLookupByLibrary.simpleMessage("Khám phá ngay"),
        "distric_branch": MessageLookupByLibrary.simpleMessage("Quận huyện"),
        "district": MessageLookupByLibrary.simpleMessage("Quận/Huyện"),
        "district_select": MessageLookupByLibrary.simpleMessage("Quận/Huyện"),
        "do_not_have": MessageLookupByLibrary.simpleMessage("Không có"),
        "do_not_scanned_copies_msg": MessageLookupByLibrary.simpleMessage(
            "•  Chụp trong môi trường đủ ánh sáng."),
        "document_authentication":
            MessageLookupByLibrary.simpleMessage("Xác thực giấy tờ"),
        "donations": MessageLookupByLibrary.simpleMessage("Quyên góp"),
        "done": MessageLookupByLibrary.simpleMessage("Xong"),
        "dont_have_credit_card": MessageLookupByLibrary.simpleMessage(
            "Quý khách chưa có thẻ tín dụng nào"),
        "dont_have_todo_work":
            MessageLookupByLibrary.simpleMessage("Chưa có việc cần làm"),
        "dont_have_todo_work_content": MessageLookupByLibrary.simpleMessage(
            "Ấn nút thêm việc để đặt ra kế hoạch làm việc tối ưu và hiệu quả nhất"),
        "down_image_qr_scan":
            MessageLookupByLibrary.simpleMessage("Tải ảnh Qr"),
        "download": MessageLookupByLibrary.simpleMessage("Tải về"),
        "download_state_not_access": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng cấp quyền cho ứng dụng lưu file về máy."),
        "download_successfully":
            MessageLookupByLibrary.simpleMessage("Tải xuống thành công"),
        "drinks": MessageLookupByLibrary.simpleMessage("Đồ uống"),
        "drinks_place_of_withdrawal":
            MessageLookupByLibrary.simpleMessage("Nơi rút tiền"),
        "eKYC": MessageLookupByLibrary.simpleMessage("eKYC"),
        "eToken": MessageLookupByLibrary.simpleMessage("eToken"),
        "e_citizen_identity":
            MessageLookupByLibrary.simpleMessage("Căn cước công dân điện tử"),
        "e_token_failure": m11,
        "e_token_locked":
            MessageLookupByLibrary.simpleMessage("Chức năng eToken tạm khoá"),
        "e_token_locked_remain": m12,
        "early_payment_warning": MessageLookupByLibrary.simpleMessage(
            "Vui lòng cân nhắc trước khi thực hiện tất toán."),
        "easy_loan": MessageLookupByLibrary.simpleMessage(
            "Vay vốn dễ dàng, click ngay tầm tay"),
        "ebank_package_by_day":
            MessageLookupByLibrary.simpleMessage("Hạn mức mỗi ngày"),
        "ebank_package_by_transaction":
            MessageLookupByLibrary.simpleMessage("Hạn mức giao dịch"),
        "edit": MessageLookupByLibrary.simpleMessage("Sửa"),
        "edit_amount":
            MessageLookupByLibrary.simpleMessage("Chỉnh sửa số tiền"),
        "edit_client_of_collab_success": MessageLookupByLibrary.simpleMessage(
            "Chỉnh sửa khách hàng thành công"),
        "edit_ekyc": MessageLookupByLibrary.simpleMessage("Chỉnh sửa"),
        "edit_general_info":
            MessageLookupByLibrary.simpleMessage("Chỉnh sửa thông tin chung"),
        "edit_information": MessageLookupByLibrary.simpleMessage(
            "Trường hợp muốn sửa lại thông tin đã nhập: Khách hàng chọn \"biểu tượng quay lại\" để trở về màn hình trước đó."),
        "edit_name": MessageLookupByLibrary.simpleMessage("Sửa tên"),
        "edit_nickname": MessageLookupByLibrary.simpleMessage(
            "Chỉnh sửa tài khoản nickname"),
        "edit_remind_info":
            MessageLookupByLibrary.simpleMessage("Chỉnh sửa thông tin gợi nhớ"),
        "edit_s": MessageLookupByLibrary.simpleMessage("Chỉnh sửa"),
        "edit_v2": MessageLookupByLibrary.simpleMessage("Chỉnh sửa"),
        "ekyc_face_success": MessageLookupByLibrary.simpleMessage(
            "Nhận diện thông tin thành công"),
        "electric": MessageLookupByLibrary.simpleMessage("Điện máy"),
        "email": MessageLookupByLibrary.simpleMessage("Email"),
        "email_address": MessageLookupByLibrary.simpleMessage("Địa chỉ email"),
        "email_not_verify": MessageLookupByLibrary.simpleMessage(
            "Do Quý khách chưa xác thực email. Quý khách vui lòng gọi tổng đài để được hỗ trợ"),
        "email_option":
            MessageLookupByLibrary.simpleMessage("Email (không bắt buộc)"),
        "empty_service":
            MessageLookupByLibrary.simpleMessage("Không có dịch vụ nào"),
        "enable_nickname":
            MessageLookupByLibrary.simpleMessage("Bật hoạt động nickname?"),
        "enable_nickname_content": MessageLookupByLibrary.simpleMessage(
            "Khi bật hoạt động, bạn có thể sử dụng nickname này để nhận tiền đến."),
        "end_month": MessageLookupByLibrary.simpleMessage("Tháng kết thúc"),
        "enddate": MessageLookupByLibrary.simpleMessage("Ngày kết thúc"),
        "enter_account_number":
            MessageLookupByLibrary.simpleMessage("Nhập số tài khoản"),
        "enter_accumulate_amount": MessageLookupByLibrary.simpleMessage(
            "Vui lòng nhập số tiền tích luỹ"),
        "enter_amount": MessageLookupByLibrary.simpleMessage("Nhập số tiền"),
        "enter_card_account_number":
            MessageLookupByLibrary.simpleMessage("Nhập số thẻ/tài khoản"),
        "enter_card_name": MessageLookupByLibrary.simpleMessage("Nhập số thẻ"),
        "enter_code": MessageLookupByLibrary.simpleMessage("Nhập mã"),
        "enter_content": MessageLookupByLibrary.simpleMessage("Nhập nội dung"),
        "enter_current_pin": MessageLookupByLibrary.simpleMessage(
            "Nhập mã PIN hiện tại của quý khách"),
        "enter_deposit_amount":
            MessageLookupByLibrary.simpleMessage("Vui lòng nhập số tiền nạp"),
        "enter_identification_number": MessageLookupByLibrary.simpleMessage(
            "Nhập số CMND/CCCD để xác nhận"),
        "enter_minimum":
            MessageLookupByLibrary.simpleMessage("Vui lòng nhập tối thiểu"),
        "enter_new_pin": MessageLookupByLibrary.simpleMessage(
            "Nhập lại mã PIN mới của quý khách"),
        "enter_otp": MessageLookupByLibrary.simpleMessage(
            "Nhập số OTP đã gửi đến số điện thoại"),
        "enter_otp_continue_transaction": MessageLookupByLibrary.simpleMessage(
            "Nhập mật khẩu OTP của quý khách để tiếp tục giao dịch"),
        "enter_password": MessageLookupByLibrary.simpleMessage("Nhập mật khẩu"),
        "enter_password_new_etoken": MessageLookupByLibrary.simpleMessage(
            "Nhập lại mật khẩu etoken mới"),
        "enter_referal_code":
            MessageLookupByLibrary.simpleMessage("Nhập mã người giới thiệu"),
        "enter_referal_code_content": MessageLookupByLibrary.simpleMessage(
            "Nhập mã người giới thiệu sẽ giúp bạn đảm bảo quyền lợi đặt mua và các ưu đãi."),
        "enter_referal_code_tool_tip": MessageLookupByLibrary.simpleMessage(
            "Nhập mã người giới thiệu tại đây để đảm bảo quyền lợi đặt mua của bạn."),
        "enter_referral_code":
            MessageLookupByLibrary.simpleMessage("Nhập mã giới thiệu"),
        "enter_vietnamese_without_accent": MessageLookupByLibrary.simpleMessage(
            "Vui lòng nhập tiếng Việt không dấu và không có ký tự đặc biệt"),
        "enter_your_account_number_or_card_number":
            MessageLookupByLibrary.simpleMessage(
                "Nhập số tài khoản hoặc số thẻ"),
        "equal_share": MessageLookupByLibrary.simpleMessage("Chia đều"),
        "equity_capital": MessageLookupByLibrary.simpleMessage("Vốn tự có"),
        "error": MessageLookupByLibrary.simpleMessage("Đã có lỗi xảy ra"),
        "error_account_input": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng điền đầy đủ thông tin"),
        "error_account_not_surplus": MessageLookupByLibrary.simpleMessage(
            "Tài khoản không đủ số dư. Vui lòng chọn lại tài khoản"),
        "error_account_not_surplus_mount": MessageLookupByLibrary.simpleMessage(
            "Tài khoản không đủ số dư. Vui lòng chọn lại tài khoản hoặc số tiền"),
        "error_alert_again": MessageLookupByLibrary.simpleMessage(
            "Thiết bị chưa nhận diện được FaceId/vân tay của bạn. Xin vui lòng thiết lập lần sau"),
        "error_android_biometrics_not_recognized":
            MessageLookupByLibrary.simpleMessage(
                "Xác thực không thành công. Xin vui lòng thử lại"),
        "error_android_required_biometrics_des":
            MessageLookupByLibrary.simpleMessage(
                "Xác thực sinh trắc học chưa được thiết lập. Vui lòng đi đến Cài đặt > Bảo mật để thiết lập xác thực sinh trắc học"),
        "error_android_required_biometrics_title":
            MessageLookupByLibrary.simpleMessage(
                "Bạn chưa thiết lập bảo mật bằng sinh trắc học"),
        "error_android_setting_biometrics_des":
            MessageLookupByLibrary.simpleMessage(
                "Vui lòng thiết lập sinh trắc học của bạn."),
        "error_balance_not_enough_bill_msg": MessageLookupByLibrary.simpleMessage(
            "Tài khoản không đủ số dư để thanh toán. Quý khách vui lòng nộp tiền vào tài khoản"),
        "error_balance_not_enough_payment_msg":
            MessageLookupByLibrary.simpleMessage(
                "Tài khoản không đủ số dư. Vui lòng chọn lại mệnh giá"),
        "error_bill_sorry_blocked": MessageLookupByLibrary.simpleMessage(
            "Xin lỗi! Không thể tất toán do tài khoản tiết kiệm của quý khách đang bị phong tỏa"),
        "error_bill_sorry_blocked_text": MessageLookupByLibrary.simpleMessage(
            "Tài khoản này đang bị phong tỏa. Quý khách không thể thực hiện tất toán"),
        "error_bill_sorry_no_account": MessageLookupByLibrary.simpleMessage(
            "Không có tài khoản để nhận gốc, lãi. Vui lòng kiểm tra lại."),
        "error_bill_sorry_not_support": MessageLookupByLibrary.simpleMessage(
            "Xin lỗi! Chức năng này không hỗ trợ đối với sổ tiết kiệm tại quầy"),
        "error_choose_pay_retry_count":
            MessageLookupByLibrary.simpleMessage("Chọn số lần thanh toán lại"),
        "error_confirm_customer_password_failed_msg":
            MessageLookupByLibrary.simpleMessage(
                "Xác nhận mật khẩu của quý khách chưa đúng"),
        "error_confirm_pin":
            MessageLookupByLibrary.simpleMessage("Mã PIN không khớp"),
        "error_current_pin": MessageLookupByLibrary.simpleMessage(
            "Hệ thống sẽ tự động đăng xuất\nsau 03 lần sai liên tiếp"),
        "error_customer_enter_password_msg":
            MessageLookupByLibrary.simpleMessage(
                "Quý khách vui lòng nhập mật khẩu mới"),
        "error_customer_enter_password_number_msg":
            MessageLookupByLibrary.simpleMessage(
                "Quý khách vui lòng nhập mật khẩu là kiểu số"),
        "error_device_can_not_used_msg": MessageLookupByLibrary.simpleMessage(
            "Thiết bị này đã bị can thiệp hệ điều hành. Quý khách không thể đăng nhập"),
        "error_device_no_support": MessageLookupByLibrary.simpleMessage(
            "Xin lỗi! Thiết bị không hỗ trợ đăng nhập vân tay/ gương mặt."),
        "error_download_state_fdf_fail": MessageLookupByLibrary.simpleMessage(
            "Tải xuống không thành công. Quý khách vui lòng thử lại."),
        "error_email": MessageLookupByLibrary.simpleMessage(
            "Email nhập không đúng định dạng! Bạn vui lòng nhập lại email đúng định dạng."),
        "error_email_empty":
            MessageLookupByLibrary.simpleMessage("Bạn vui lòng nhập Email"),
        "error_empty_pass":
            MessageLookupByLibrary.simpleMessage("Bạn vui lòng nhập mật khẩu"),
        "error_empty_province": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng chọn Tỉnh/Thành phố"),
        "error_empty_re_pass": MessageLookupByLibrary.simpleMessage(
            "Bạn vui lòng nhập lại mật khẩu"),
        "error_empty_request": MessageLookupByLibrary.simpleMessage(
            "Chưa có yêu cầu chuyển tiền nào"),
        "error_empty_ward": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng chọn Phường/Xã"),
        "error_enter_identification_number": MessageLookupByLibrary.simpleMessage(
            "Đã nhập sai CMND/CCCD. Vui lòng nhập đúng CMND/CCCD đã đăng kí với KienlongBank"),
        "error_enter_money_payment_msg": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng nhập số tiền để thanh toán."),
        "error_etoken_enter_password_msg": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng nhập mật khẩu cho Etoken"),
        "error_etoken_exist_other_devices_msg":
            MessageLookupByLibrary.simpleMessage(
                "Có lỗi xảy ra do eToken được thiết lập trên thiết bị khác. Để bảo mật cho tài khoản, Quý khách vui lòng thiết lập lại theo hướng dẫn sau (hoặc liên hệ tổng đài/ chi nhánh để được hỗ trợ)"),
        "error_etoken_exist_other_devices_msg_update":
            MessageLookupByLibrary.simpleMessage(
                "eToken đã được thiết lập trên thiết bị khác hoặc chính thiết bị này (do quý khách chưa huỷ eToken trước đó). Để bảo mật cho tài khoản, Quý khách vui lòng thiết lập lại theo hướng dẫn sau (hoặc liên hệ tổng đài/ chi nhánh để được hỗ trợ)"),
        "error_fail_password": MessageLookupByLibrary.simpleMessage(
            "Sai mật khẩu. Xác thực giao dịch bằng eToken sẽ tạm khóa trong 15 phút nếu sai mật khẩu 3 lần"),
        "error_faild_login": MessageLookupByLibrary.simpleMessage(
            "Vân tay/ gương mặt không đúng. Vui lòng sử dụng mật khẩu để đăng nhập"),
        "error_false_password_etoken": m13,
        "error_feature_not_activated_title":
            MessageLookupByLibrary.simpleMessage(
                "Chức năng này chưa được kích hoạt"),
        "error_format_issue_date":
            MessageLookupByLibrary.simpleMessage("Ngày cấp sai định dạng"),
        "error_image_payee_not_support": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng sử dụng những ảnh có đuôi như sau: .jpg, .jpeg, .png"),
        "error_input_card_empty": MessageLookupByLibrary.simpleMessage(
            "Vui lòng nhập số CMND/CCCD/Hộ chiếu"),
        "error_input_etoken":
            MessageLookupByLibrary.simpleMessage("Chưa nhập mật khẩu"),
        "error_input_new_etoken": MessageLookupByLibrary.simpleMessage(
            "Quý khách chưa nhập mật khẩu"),
        "error_input_password_etoken": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng điền đầy đủ các trường thông tin mật khẩu để tiếp tục."),
        "error_input_repass": MessageLookupByLibrary.simpleMessage(
            "Mật khẩu không trùng khớp. Vui lòng nhập lại"),
        "error_invalid_password_msg":
            MessageLookupByLibrary.simpleMessage("Mật khẩu không hợp lệ"),
        "error_invalid_re_password_msg":
            MessageLookupByLibrary.simpleMessage("Nhập lại mật khẩu chưa đúng"),
        "error_ios_lockout_biometrics_msg": MessageLookupByLibrary.simpleMessage(
            "Xác thực sinh trắc học bị tắt. Vui lòng khóa và mở khóa màn hình của bạn để bật nó"),
        "error_ios_setting_biometrics_des": MessageLookupByLibrary.simpleMessage(
            "Xác thực sinh trắc học chưa được thiết lập . Vui lòng kích hoạt Face id/Touch id trên thiết bị của bạn."),
        "error_login_biometrics_expires": MessageLookupByLibrary.simpleMessage(
            "Phiên đăng nhập bằng sinh trắc học đã hết hạn, quý khách vui lòng nhập mật khẩu để tiếp tục."),
        "error_login_chat": MessageLookupByLibrary.simpleMessage(
            "Bạn chưa đăng nhập vào dịch vụ chat"),
        "error_max_auto_paid_retry_count": MessageLookupByLibrary.simpleMessage(
            "Số lần thanh toán lại nếu thất bại"),
        "error_money_withdraw":
            MessageLookupByLibrary.simpleMessage("Số tiền không hợp lệ"),
        "error_money_withdraw_surplus":
            MessageLookupByLibrary.simpleMessage("Số dư tài khoản không đủ"),
        "error_name_empty_goals":
            MessageLookupByLibrary.simpleMessage("Vui lòng nhập tên mục tiêu"),
        "error_name_reminiscent":
            MessageLookupByLibrary.simpleMessage("Vui lòng nhập tên gợi nhớ"),
        "error_no_bill_left": MessageLookupByLibrary.simpleMessage(
            "không có hóa đơn cần thanh toán"),
        "error_no_data":
            MessageLookupByLibrary.simpleMessage("Không có dữ liệu"),
        "error_no_empty": m14,
        "error_no_vietnamese": MessageLookupByLibrary.simpleMessage(
            "Vui lòng chỉ nhập số và tiếng việt không dấu"),
        "error_not_map_install": MessageLookupByLibrary.simpleMessage(
            "Không có ứng dụng bản đồ nào đang được cài trên máy của bạn"),
        "error_not_match_new_password": MessageLookupByLibrary.simpleMessage(
            "Mật khẩu mới không trùng khớp"),
        "error_not_match_old_password": MessageLookupByLibrary.simpleMessage(
            "Mật khẩu mới không được trùng mật khẩu cũ. Vui lòng thử lại."),
        "error_number_phone": MessageLookupByLibrary.simpleMessage(
            "Số điện thoại nhập không đúng."),
        "error_number_phone_empty": MessageLookupByLibrary.simpleMessage(
            "Bạn chưa nhập số điện thoại mới"),
        "error_number_phone_error":
            MessageLookupByLibrary.simpleMessage("Số điện thoại không hợp lệ"),
        "error_number_phone_validator": MessageLookupByLibrary.simpleMessage(
            "Số điện thoại nhập sai định dạng!Bạn vui lòng nhập đúng định dạng"),
        "error_paid_to_other_account_if_current_not_enought":
            MessageLookupByLibrary.simpleMessage(
                "Lấy tiền từ các tài khoản khác trong trường hợp tài khoản này không đủ số dư"),
        "error_pass_contain_tiengviet": MessageLookupByLibrary.simpleMessage(
            "Mật khẩu có chứa ký tự tiếng Việt. Nếu bạn chắc chắn đã nhập đúng mật khẩu, vui lòng nhấn"),
        "error_password_no_english": MessageLookupByLibrary.simpleMessage(
            "Vui lòng nhập password không dấu"),
        "error_password_number_six": MessageLookupByLibrary.simpleMessage(
            "Mật khẩu eToken phải có 6 số"),
        "error_password_six_number_msg":
            MessageLookupByLibrary.simpleMessage("Mật khẩu phải có 6 số"),
        "error_payee_new_account_empty": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng nhập số tài khoản"),
        "error_payee_new_bank_empty": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng chọn ngân hàng"),
        "error_payee_new_card_empty": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng nhập số thẻ"),
        "error_payee_new_phone_empty": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng nhập số điện thoại"),
        "error_please_check_your_input": MessageLookupByLibrary.simpleMessage(
            "Vui lòng kiểm tra lại thông tin đã nhập"),
        "error_please_choose_purpose":
            MessageLookupByLibrary.simpleMessage("Vui chọn nhu cầu tư vấn"),
        "error_please_choose_service_to_pay":
            MessageLookupByLibrary.simpleMessage(
                "Vui lòng chọn dịch vụ muốn thanh toán"),
        "error_please_input_content":
            MessageLookupByLibrary.simpleMessage("Vui lòng nhập nội dung"),
        "error_please_input_password": MessageLookupByLibrary.simpleMessage(
            "Vui lòng nhập mật khẩu đăng nhập"),
        "error_please_input_title":
            MessageLookupByLibrary.simpleMessage("Vui lòng nhập tiêu đề"),
        "error_please_input_username":
            MessageLookupByLibrary.simpleMessage("Vui lòng nhập tên tài khoản"),
        "error_please_verify_biometrics_msg":
            MessageLookupByLibrary.simpleMessage(
                "Vui lòng xác thực sinh trắc học của bạn"),
        "error_qr_code_invalid": MessageLookupByLibrary.simpleMessage(
            "Mã QR không hợp lệ. Quý khách vui lòng kiểm tra lại."),
        "error_retry_server": MessageLookupByLibrary.simpleMessage(
            "Dịch vụ không thể thực hiện được lúc này, quý khách vui lòng quay lại sau."),
        "error_select_account_payment_msg":
            MessageLookupByLibrary.simpleMessage(
                "Bạn cần chọn một tài khoản để thanh toán."),
        "error_setting_touch": MessageLookupByLibrary.simpleMessage(
            "Vui lòng thực hiện cài đặt ở thiết bị trước khi thử lại"),
        "error_turn_on_setting_app_msg": MessageLookupByLibrary.simpleMessage(
            "Để sử dụng bạn cần đăng nhập và kích hoạt chức năng này trong phần cài đặt ứng dụng."),
        "error_validate_birthday_profile_info_msg":
            MessageLookupByLibrary.simpleMessage("Ngày sinh không được trống"),
        "error_validate_edit_number": MessageLookupByLibrary.simpleMessage(
            "Số điện thoại không hợp lệ. Vui lòng thử lại."),
        "error_validate_identity_profile_info_msg":
            MessageLookupByLibrary.simpleMessage(
                "Số CMND/CCCD/Hộ chiếu không được trống"),
        "error_validate_issue_date_profile_info_msg":
            MessageLookupByLibrary.simpleMessage("Ngày cấp không được trống"),
        "error_validator_dialog_withdraw": MessageLookupByLibrary.simpleMessage(
            "Mã QR hết hạn. Vui lòng thử lại trên máy ATM"),
        "error_validator_request": MessageLookupByLibrary.simpleMessage(
            "Vui lòng nhập nội dung chuyển tiền tiếng việt không dấu và không có ký tự đặc biệt"),
        "error_validator_request_dialog": MessageLookupByLibrary.simpleMessage(
            "Yêu cầu chuyển tiền đã bị hủy"),
        "establish": MessageLookupByLibrary.simpleMessage("Thiết lập"),
        "ethnicity": MessageLookupByLibrary.simpleMessage("Dân tộc"),
        "etoken_device":
            MessageLookupByLibrary.simpleMessage("Thiết bị gắn eToken"),
        "exist": MessageLookupByLibrary.simpleMessage("Hiện hữu"),
        "exist_commission":
            MessageLookupByLibrary.simpleMessage("Hoa hồng hiện có"),
        "exit": MessageLookupByLibrary.simpleMessage("Thoát"),
        "expense": MessageLookupByLibrary.simpleMessage("Chi tiêu"),
        "expiration_date": MessageLookupByLibrary.simpleMessage("Ngày hết hạn"),
        "expire": MessageLookupByLibrary.simpleMessage("Đáo hạn"),
        "expire_qr_stm": MessageLookupByLibrary.simpleMessage(
            "Mã QR hết hạn. Vui lòng thử lại trên máy ATM"),
        "expired": MessageLookupByLibrary.simpleMessage("Hết hạn"),
        "explore_cards": MessageLookupByLibrary.simpleMessage("Khám phá thêm"),
        "export": MessageLookupByLibrary.simpleMessage("Xuất"),
        "export_year": MessageLookupByLibrary.simpleMessage("Sao kê năm"),
        "failure": MessageLookupByLibrary.simpleMessage("Thất bại"),
        "fast": MessageLookupByLibrary.simpleMessage("Nhanh"),
        "fast_auth": MessageLookupByLibrary.simpleMessage("Xác thực nhanh"),
        "fast_auth_alert": MessageLookupByLibrary.simpleMessage(
            "Vui lòng thực hiện thiết lập Vân tay/ Gương mặt trên thiết bị trước khi sử dụng."),
        "fast_transfer_247":
            MessageLookupByLibrary.simpleMessage("Chuyển khoản nhanh 24/7"),
        "favorite": MessageLookupByLibrary.simpleMessage("Yêu thích"),
        "favourite_list":
            MessageLookupByLibrary.simpleMessage("Danh sách yêu thích"),
        "fchat_with_consultant":
            MessageLookupByLibrary.simpleMessage("FChat với tư vấn viên 24/7"),
        "fee": MessageLookupByLibrary.simpleMessage("Phí"),
        "fee_amount_vnpost":
            MessageLookupByLibrary.simpleMessage("Thuế và phí giao dịch"),
        "fee_transaction":
            MessageLookupByLibrary.simpleMessage("Phí giao dịch"),
        "fee_vnd":
            MessageLookupByLibrary.simpleMessage("Phí mở (VND) chưa VAT"),
        "female": MessageLookupByLibrary.simpleMessage("Nữ"),
        "file": MessageLookupByLibrary.simpleMessage("Hồ sơ"),
        "filler_advanced":
            MessageLookupByLibrary.simpleMessage("Bộ lọc nâng cao"),
        "filter": MessageLookupByLibrary.simpleMessage("Bộ lọc"),
        "final_paid": MessageLookupByLibrary.simpleMessage("Đã thanh toán"),
        "find_account_number":
            MessageLookupByLibrary.simpleMessage("Tìm số tài khoản đẹp"),
        "find_around": MessageLookupByLibrary.simpleMessage("Tìm xung quanh"),
        "fingerprint_face":
            MessageLookupByLibrary.simpleMessage("Vân tay, Khuôn mặt"),
        "finish": MessageLookupByLibrary.simpleMessage("Hoàn thành"),
        "font_cart": m15,
        "font_size": MessageLookupByLibrary.simpleMessage("Cỡ chữ"),
        "for_safety": MessageLookupByLibrary.simpleMessage(
            "Để đảm bảo an toàn, sau 120 ngày phát hành, nếu Quý khách vẫn chưa nhận Thẻ, Thẻ sẽ bị hủy bỏ."),
        "forgot_etoken":
            MessageLookupByLibrary.simpleMessage("Quên mật khẩu eToken?"),
        "forgot_password":
            MessageLookupByLibrary.simpleMessage("Quên mật khẩu"),
        "forgot_pin_code":
            MessageLookupByLibrary.simpleMessage("Cấp mới/Quên mã PIN"),
        "forgot_pin_code_title":
            MessageLookupByLibrary.simpleMessage("Quên PIN"),
        "form_of_fraud": MessageLookupByLibrary.simpleMessage(
            "Quý khách có thể xem thêm các hình thức lừa đảo phổ biến "),
        "form_of_payment":
            MessageLookupByLibrary.simpleMessage("Hình thức chi trả"),
        "free": MessageLookupByLibrary.simpleMessage("Miễn phí"),
        "frequency": MessageLookupByLibrary.simpleMessage("Tần suất"),
        "friday": MessageLookupByLibrary.simpleMessage("Thứ 6"),
        "from": MessageLookupByLibrary.simpleMessage("từ"),
        "from_account": MessageLookupByLibrary.simpleMessage("Từ tài khoản"),
        "from_business_income":
            MessageLookupByLibrary.simpleMessage("Từ thu nhập kinh doanh"),
        "from_card": MessageLookupByLibrary.simpleMessage("Từ thẻ"),
        "from_day": MessageLookupByLibrary.simpleMessage("Từ ngày"),
        "from_number_phone":
            MessageLookupByLibrary.simpleMessage("Đến số điện thoại"),
        "from_online_saving": MessageLookupByLibrary.simpleMessage(
            "Từ chính tiền gửi tiết kiệm online"),
        "from_real_estate_rental":
            MessageLookupByLibrary.simpleMessage("Từ cho thuê bất động sản"),
        "from_salary": MessageLookupByLibrary.simpleMessage("Từ lương"),
        "front_face": MessageLookupByLibrary.simpleMessage("Chụp mặt trước"),
        "full_name": MessageLookupByLibrary.simpleMessage("Họ và tên"),
        "full_name_customer":
            MessageLookupByLibrary.simpleMessage("Họ và tên Khách hàng"),
        "furniture": MessageLookupByLibrary.simpleMessage("Nội thất"),
        "gallery": MessageLookupByLibrary.simpleMessage("Bộ sưu tập"),
        "gallery_permission": MessageLookupByLibrary.simpleMessage(
            "KienlongBank cần truy cập kho ảnh trong thanh toán, chuyển tiền, cá nhân hóa ứng dụng"),
        "gender": MessageLookupByLibrary.simpleMessage("Giới tính"),
        "general": MessageLookupByLibrary.simpleMessage("Chung"),
        "general_information":
            MessageLookupByLibrary.simpleMessage("Thông tin chung"),
        "general_trading_conditions":
            MessageLookupByLibrary.simpleMessage("Điều kiện giao dịch chung"),
        "get_commission": MessageLookupByLibrary.simpleMessage("Nhận hoa hồng"),
        "get_etoken_code":
            MessageLookupByLibrary.simpleMessage("Lấy mã eToken"),
        "gift": MessageLookupByLibrary.simpleMessage("Quà tặng"),
        "give_gift": MessageLookupByLibrary.simpleMessage("Trao Quà - Gửi Lộc"),
        "give_love": MessageLookupByLibrary.simpleMessage("Gửi Yêu Thương"),
        "go_app": MessageLookupByLibrary.simpleMessage("Vào ứng dụng"),
        "go_term": MessageLookupByLibrary.simpleMessage("Ngày đến hạn"),
        "go_to_account": MessageLookupByLibrary.simpleMessage("Đến tài khoản"),
        "go_to_account_number":
            MessageLookupByLibrary.simpleMessage("Đến số tài khoản"),
        "go_to_card_number": MessageLookupByLibrary.simpleMessage("Đến số thẻ"),
        "go_to_my_account":
            MessageLookupByLibrary.simpleMessage("Đến tài khoản của tôi"),
        "go_to_setting_accessibility_service": MessageLookupByLibrary.simpleMessage(
            "Để tránh rủi ro đối với tài khoản, Quý khách vui lòng vào mục \"Cài đặt > Hỗ trợ > Ứng dụng đã cài đặt\" trên thiết bị và thực hiện Tắt quyền trợ năng cho các ứng dụng trên"),
        "go_to_stock":
            MessageLookupByLibrary.simpleMessage("Chuyển tiền chứng khoán"),
        "go_to_the_nearest_collection": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng đến điểm thu hộ gần nhất, cung cấp mã thu dưới đây và hoàn tất thủ tục mở sổ tiết kiệm trong vòng 48 giờ tiếp theo."),
        "goal": MessageLookupByLibrary.simpleMessage("Tiền gửi"),
        "got_it": MessageLookupByLibrary.simpleMessage("Tôi đã hiểu"),
        "goto_setting": MessageLookupByLibrary.simpleMessage("Đi đến cài đặt"),
        "group": MessageLookupByLibrary.simpleMessage("Nhóm"),
        "group_customer_title":
            MessageLookupByLibrary.simpleMessage("Nhóm khách hàng"),
        "group_empty_message": MessageLookupByLibrary.simpleMessage(
            "Tháng này quý khách chưa thực hiện các giao dịch nào."),
        "group_empty_title":
            MessageLookupByLibrary.simpleMessage("Chưa có giao dịch"),
        "gttt_expiry_date":
            MessageLookupByLibrary.simpleMessage("Ngày hết hạn GTTT"),
        "guest_has_not_agent": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng chọn chuyên viên tư vấn ở danh sách dưới."),
        "guide_name_reminiscent": MessageLookupByLibrary.simpleMessage(
            "Nhập tên gợi nhớ giúp quý khách dễ dàng tìm trong danh bạ"),
        "guide_referral_code_describe": MessageLookupByLibrary.simpleMessage(
            "Nhập mã người giới thiệu tại đây để đảm bảo quyền lợi tại KienlongBank."),
        "have_no_recipient_in_contacts": MessageLookupByLibrary.simpleMessage(
            "Quý khách hiện chưa có người nhận nào trong danh bạ"),
        "have_not_request_money_transfer": MessageLookupByLibrary.simpleMessage(
            "Quý khách hiện chưa có yêu cầu chuyển tiền."),
        "have_not_statistic_in_time": MessageLookupByLibrary.simpleMessage(
            "Không có thống kê nào trong thời gian này"),
        "have_not_transaction": MessageLookupByLibrary.simpleMessage(
            "Không có giao dịch trong từ khóa tìm kiếm"),
        "hello": MessageLookupByLibrary.simpleMessage("Xin chào"),
        "help": MessageLookupByLibrary.simpleMessage("Trợ giúp"),
        "here": MessageLookupByLibrary.simpleMessage("Đây"),
        "hint_text": MessageLookupByLibrary.simpleMessage("Vui lòng nhập"),
        "history": MessageLookupByLibrary.simpleMessage("Lịch sử"),
        "history_review_transaction":
            MessageLookupByLibrary.simpleMessage("Lịch sử tra soát"),
        "home": MessageLookupByLibrary.simpleMessage("Trang chủ"),
        "home_town": MessageLookupByLibrary.simpleMessage("Địa chỉ thường trú"),
        "hometown": MessageLookupByLibrary.simpleMessage("Quê quán"),
        "hour": MessageLookupByLibrary.simpleMessage("giờ"),
        "id_number":
            MessageLookupByLibrary.simpleMessage("Số giấy tờ tùy thân"),
        "identification_date":
            MessageLookupByLibrary.simpleMessage("Ngày xếp hạng"),
        "identification_expiration_date":
            MessageLookupByLibrary.simpleMessage("Hiệu lực đến"),
        "identification_number":
            MessageLookupByLibrary.simpleMessage("Số CMT/CCCD/Hộ chiếu"),
        "identify_paper":
            MessageLookupByLibrary.simpleMessage("Giấy tờ tuỳ thân"),
        "identity_documents":
            MessageLookupByLibrary.simpleMessage("Giấy tờ tuỳ thân"),
        "image": MessageLookupByLibrary.simpleMessage("Ảnh"),
        "implemented_on": MessageLookupByLibrary.simpleMessage("Thực hiện vào"),
        "in_bill": MessageLookupByLibrary.simpleMessage("In biên lai"),
        "in_month": MessageLookupByLibrary.simpleMessage("trong tháng"),
        "inbox": MessageLookupByLibrary.simpleMessage("Hộp thư"),
        "income": MessageLookupByLibrary.simpleMessage("Nguồn thu nhập"),
        "incoming_call": MessageLookupByLibrary.simpleMessage("Cuộc gọi đến"),
        "info_card_cvv":
            MessageLookupByLibrary.simpleMessage("Xem thông tin số thẻ/CVV"),
        "info_receive_money":
            MessageLookupByLibrary.simpleMessage("Thông tin nhận tiền"),
        "info_verify_ekyc_provide": MessageLookupByLibrary.simpleMessage(
            "Các thông tin định danh eKYC của quý khách được"),
        "infor_payee":
            MessageLookupByLibrary.simpleMessage("Thông tin người thụ hưởng"),
        "information": MessageLookupByLibrary.simpleMessage("Thông tin"),
        "information_on_receiving_money_at_vnpost":
            MessageLookupByLibrary.simpleMessage(
                "Thông tin nhận tiền tại VNPost"),
        "input_card_title":
            MessageLookupByLibrary.simpleMessage("Nhập số CMND/CCCD/Hộ chiếu"),
        "input_here": MessageLookupByLibrary.simpleMessage("Nhập vào đây"),
        "input_new_password":
            MessageLookupByLibrary.simpleMessage("Nhập mật khẩu mới"),
        "input_otp_email":
            MessageLookupByLibrary.simpleMessage("Nhập mã OTP Email"),
        "input_otp_sms":
            MessageLookupByLibrary.simpleMessage("Nhập mã OTP SMS"),
        "input_phone":
            MessageLookupByLibrary.simpleMessage("Nhập số điện thoại"),
        "input_phone_summary": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng nhập đúng số điện thoại để xác thực OTP"),
        "input_referral_code":
            MessageLookupByLibrary.simpleMessage("Nhập mã giới thiệu (nếu có)"),
        "inquiry_amount":
            MessageLookupByLibrary.simpleMessage("Số tiền tra soát"),
        "inquiry_amount_label_textfield":
            MessageLookupByLibrary.simpleMessage("Số tiền cần tra soát"),
        "inquiry_code": MessageLookupByLibrary.simpleMessage("Mã tra soát"),
        "inquiry_detail":
            MessageLookupByLibrary.simpleMessage("Chi tiết tra soát"),
        "inquiry_info":
            MessageLookupByLibrary.simpleMessage("Thông tin tra soát"),
        "inquiry_message_intro": MessageLookupByLibrary.simpleMessage(
            "Tra soát giao dịch online trên app hỗ trợ khách hàng xác minh tình trạng giao dịch tiện lợi và nhanh chóng."),
        "inquiry_result":
            MessageLookupByLibrary.simpleMessage("Kết quả tra soát"),
        "inquiry_status":
            MessageLookupByLibrary.simpleMessage("Trạng thái tra soát"),
        "inquiry_transaction_empty_message":
            MessageLookupByLibrary.simpleMessage("Danh sách giao dịch trống"),
        "insurrance": MessageLookupByLibrary.simpleMessage("Bảo hiểm"),
        "interbank": MessageLookupByLibrary.simpleMessage("Liên ngân hàng"),
        "interbank_account":
            MessageLookupByLibrary.simpleMessage("Tài khoản liên ngân hàng"),
        "interest_rate": MessageLookupByLibrary.simpleMessage("Lãi suất"),
        "interest_rates":
            MessageLookupByLibrary.simpleMessage("Lãi suất cho vay"),
        "internal": MessageLookupByLibrary.simpleMessage("Nội bộ"),
        "internal_account":
            MessageLookupByLibrary.simpleMessage("Tài khoản nội bộ"),
        "international_money_transfer":
            MessageLookupByLibrary.simpleMessage("Chuyển tiền quốc tế"),
        "intro_app_features": MessageLookupByLibrary.simpleMessage(
            "Giới thiệu các tính năng của app"),
        "invest": MessageLookupByLibrary.simpleMessage("Đầu tư"),
        "invest_success":
            MessageLookupByLibrary.simpleMessage("Giới thiệu thành công"),
        "invitation_code_optional": MessageLookupByLibrary.simpleMessage(
            "Mã giới thiệu (không bắt buộc)"),
        "invite_customers": MessageLookupByLibrary.simpleMessage(
            "Mời khách hàng để tăng thu nhập, phát triển doanh thu cùng KienlongBank."),
        "invoice_number": MessageLookupByLibrary.simpleMessage("Số hoá đơn"),
        "invoice_period": MessageLookupByLibrary.simpleMessage("Kỳ hoá đơn"),
        "invoice_saved": MessageLookupByLibrary.simpleMessage("Hóa đơn đã lưu"),
        "invoice_type": MessageLookupByLibrary.simpleMessage("Loại hoá đơn"),
        "issued_by": MessageLookupByLibrary.simpleMessage("Nơi cấp"),
        "issued_date": MessageLookupByLibrary.simpleMessage("Ngày cấp"),
        "job_todo_today":
            MessageLookupByLibrary.simpleMessage("Việc cần làm hôm nay"),
        "join_video_call":
            MessageLookupByLibrary.simpleMessage("Join Video Call"),
        "kienlongbank": MessageLookupByLibrary.simpleMessage("KienlongBank"),
        "kind_of_payment": MessageLookupByLibrary.simpleMessage("Loại hóa đơn"),
        "klb_always_listen": MessageLookupByLibrary.simpleMessage(
            "KienlongBank sẽ luôn ở đây lắng nghe và giải đáp mọi nhu cầu, mong muốn của quý khách."),
        "ks_credit": MessageLookupByLibrary.simpleMessage("KS.Credit"),
        "ksb_transaction_point":
            MessageLookupByLibrary.simpleMessage("Điểm giao dịch KienlongBank"),
        "ksf_network": MessageLookupByLibrary.simpleMessage("KLB Support"),
        "last_month":
            MessageLookupByLibrary.simpleMessage("Một tháng gần nhất"),
        "last_updated":
            MessageLookupByLibrary.simpleMessage("Cập nhật lần cuối"),
        "learn_more": MessageLookupByLibrary.simpleMessage("Tìm hiểu thêm"),
        "lend_book_register_button_discover_now":
            MessageLookupByLibrary.simpleMessage("Khám phá ngay"),
        "lend_book_register_des_dialog1_info_time_handle":
            MessageLookupByLibrary.simpleMessage(
                "Giao dịch đăng ký vay của quý khách sẽ được xử lý "),
        "lend_book_register_des_dialog2_info_time_timeweekday":
            MessageLookupByLibrary.simpleMessage("7:25 - 15:45"),
        "lend_book_register_des_dialog3_info_time_monday":
            MessageLookupByLibrary.simpleMessage(" thứ 2"),
        "lend_book_register_des_dialog5_info_time_friday":
            MessageLookupByLibrary.simpleMessage(" thứ 6"),
        "lend_book_register_des_dialog6_info_time_and_from":
            MessageLookupByLibrary.simpleMessage(" và từ"),
        "lend_book_register_des_dialog7_info_time_weekend":
            MessageLookupByLibrary.simpleMessage(" 7:25 - 10:30 thứ 7"),
        "lend_book_register_des_dialog9_info_time_exceptday":
            MessageLookupByLibrary.simpleMessage(
                ", trừ các ngày nghỉ và ngày lễ."),
        "lend_book_register_title_dialog_info_time":
            MessageLookupByLibrary.simpleMessage("Thông tin đăng ký vay"),
        "lend_consumption_bottomsheet_button_upload_paper":
            MessageLookupByLibrary.simpleMessage("Tải lên giấy tờ"),
        "lend_consumption_title_consumption":
            MessageLookupByLibrary.simpleMessage("Phục vụ đời sống"),
        "lend_dialog_des_not_enough_age": MessageLookupByLibrary.simpleMessage(
            "KienlongBank chỉ có thể hỗ trợ cho khách hàng đủ 18 tuổi. Vui lòng liên hệ chi nhánh gần nhất để được hỗ trợ."),
        "lend_dialog_title_not_enough_age":
            MessageLookupByLibrary.simpleMessage("Không đủ điều kiện vay"),
        "lend_home_home_label_debt_left":
            MessageLookupByLibrary.simpleMessage("Dư nợ gốc còn lại"),
        "lend_home_lend_bottomsheet_label_paper_name":
            MessageLookupByLibrary.simpleMessage("Tên chứng từ"),
        "lend_home_lend_button_add_paper":
            MessageLookupByLibrary.simpleMessage("Thêm giấy tờ"),
        "lend_home_lend_label_amount":
            MessageLookupByLibrary.simpleMessage("Số tiền vay"),
        "lend_package_button_add_now":
            MessageLookupByLibrary.simpleMessage("Bổ sung ngay"),
        "lend_package_des_lend_prove_1":
            MessageLookupByLibrary.simpleMessage("Vay tiêu dùng "),
        "lend_package_des_lend_prove_2": MessageLookupByLibrary.simpleMessage(
            "không cần chứng minh thu nhập"),
        "lend_package_des_lend_prove_3": MessageLookupByLibrary.simpleMessage(
            " - thủ tục giải ngân nhanh chóng, dễ dàng trong 10 phút!"),
        "lend_package_empty_des_no_lend": MessageLookupByLibrary.simpleMessage(
            "Đăng ký vay ngay để nhận thêm nhiều ưu đãi hấp dẫn từ KienlongBank. "),
        "lend_package_empty_des_no_settle": MessageLookupByLibrary.simpleMessage(
            "Thông tin về các khoản vay đã tất toán của quý khách sẽ được liệt kê tại đây."),
        "lend_package_empty_title_no_lend":
            MessageLookupByLibrary.simpleMessage("Quý khách chưa có khoản vay"),
        "lend_package_empty_title_no_settle":
            MessageLookupByLibrary.simpleMessage(
                "Quý khách chưa tất toán khoản vay nào"),
        "lend_package_label_amount_upcoming":
            MessageLookupByLibrary.simpleMessage("Số tiền phải trả sắp tới"),
        "lend_package_label_due_date":
            MessageLookupByLibrary.simpleMessage("Kỳ hạn (tháng)"),
        "lend_package_label_pay_upcoming":
            MessageLookupByLibrary.simpleMessage("Hạn trả nợ sắp tới"),
        "lend_package_title_existence":
            MessageLookupByLibrary.simpleMessage("Hiện hữu"),
        "lend_package_title_settled":
            MessageLookupByLibrary.simpleMessage("Đã tất toán"),
        "lend_register_title_register":
            MessageLookupByLibrary.simpleMessage("Đăng ký khoản vay"),
        "lending_collab_collab_button_new_lend":
            MessageLookupByLibrary.simpleMessage("Khoản vay mới"),
        "lending_collab_collab_button_turn_on_location":
            MessageLookupByLibrary.simpleMessage("Bật định vị"),
        "lending_collab_collab_des_turn_on_location":
            MessageLookupByLibrary.simpleMessage(
                "Vui lòng bật định vị vị trí để chúng tôi có thể tìm kiếm cộng tác viên gần quý khách nhất"),
        "lending_collab_collab_dialog_des_collab_cannot_register":
            MessageLookupByLibrary.simpleMessage(
                "Quý khách đã là cộng tác viên nên không thể đăng ký khoản vay này."),
        "lending_collab_collab_dialog_title_collab_cannot_register":
            MessageLookupByLibrary.simpleMessage("Không đủ điều kiện vay"),
        "lending_collab_collab_label_amount_lend":
            MessageLookupByLibrary.simpleMessage("Số tiền vay"),
        "lending_collab_collab_label_collab":
            MessageLookupByLibrary.simpleMessage("Cộng tác viên"),
        "lending_collab_collab_label_collaborator":
            MessageLookupByLibrary.simpleMessage("Cộng tác viên"),
        "lending_collab_collab_label_lend_time":
            MessageLookupByLibrary.simpleMessage("Thời gian vay"),
        "lending_collab_collab_title_collab":
            MessageLookupByLibrary.simpleMessage("Vay cộng tác viên"),
        "lending_collab_collab_title_lend":
            MessageLookupByLibrary.simpleMessage("Khoản vay"),
        "lending_collab_collab_title_lend_file":
            MessageLookupByLibrary.simpleMessage("Hồ sơ vay"),
        "lending_collab_collaborator_button_choose":
            MessageLookupByLibrary.simpleMessage("Chọn"),
        "lending_collab_collaborator_button_message":
            MessageLookupByLibrary.simpleMessage("Nhắn tin"),
        "lending_collab_collaborator_title_introduce":
            MessageLookupByLibrary.simpleMessage("Giới thiệu"),
        "lending_collab_collaborator_title_overview":
            MessageLookupByLibrary.simpleMessage("Tổng quan"),
        "lending_collab_home_des_collab_lend":
            MessageLookupByLibrary.simpleMessage("Khoản vay cộng tác viên"),
        "lending_collab_home_des_for_individual":
            MessageLookupByLibrary.simpleMessage(
                "Dành riêng cho KHCN của KienlongBank"),
        "lending_collab_home_des_for_investor":
            MessageLookupByLibrary.simpleMessage(
                "Dành cho khách hàng muốn đầu tư sinh lời"),
        "lending_collab_home_title_lend":
            MessageLookupByLibrary.simpleMessage("Vay vốn"),
        "lending_collab_home_title_traditional_lend":
            MessageLookupByLibrary.simpleMessage("Khoản vay truyền thống"),
        "lending_each_time":
            MessageLookupByLibrary.simpleMessage("Cho vay từng lần"),
        "length_416_characters":
            MessageLookupByLibrary.simpleMessage("Độ dài từ 4-16 ký tự"),
        "length_8_characters":
            MessageLookupByLibrary.simpleMessage("Độ dài từ 8 ký tự"),
        "license_plates":
            MessageLookupByLibrary.simpleMessage("Biển số xe/Mã khách hàng"),
        "license_plates_type":
            MessageLookupByLibrary.simpleMessage("Loại biển số xe"),
        "light": MessageLookupByLibrary.simpleMessage("Sáng"),
        "light_dark_mode":
            MessageLookupByLibrary.simpleMessage("Giao diện Sáng / Tối"),
        "light_dark_mode_changed_device_setting":
            MessageLookupByLibrary.simpleMessage(
                "Chế độ sáng và tối sẽ thay đổi bởi cài đặt thiết bị"),
        "limit_error_input_pin_confirm": MessageLookupByLibrary.simpleMessage(
            "Quý khách đã nhập lại PIN\nkhông khớp 3 lần"),
        "link_money": MessageLookupByLibrary.simpleMessage("Link nhận tiền"),
        "link_web":
            MessageLookupByLibrary.simpleMessage(" www.kienlongbank.com."),
        "linking_sevice_payment":
            MessageLookupByLibrary.simpleMessage("Dịch vụ liên kết"),
        "list": MessageLookupByLibrary.simpleMessage("Người được giới thiệu"),
        "list_account_empty":
            MessageLookupByLibrary.simpleMessage("Danh sách tài khoản trống"),
        "list_bank_vietqr": MessageLookupByLibrary.simpleMessage(
            "Danh sách ngân hàng hỗ trợ quét mã VietQR"),
        "list_of_beneficiaries":
            MessageLookupByLibrary.simpleMessage("Danh sách người thụ hưởng"),
        "list_of_beneficiaries_capiton": MessageLookupByLibrary.simpleMessage(
            "Trong thường hợp bạn chuyển khoản dến người thụ hưởng đã được lưu trước đây, bạn có thể chọn nhanh từ danh sách này."),
        "list_of_consultants": MessageLookupByLibrary.simpleMessage(
            "Danh sách chuyên viên tư vấn"),
        "list_of_locations":
            MessageLookupByLibrary.simpleMessage("Danh sách các địa điểm"),
        "list_of_transfer":
            MessageLookupByLibrary.simpleMessage("Danh sách thường chuyển"),
        "living_bill":
            MessageLookupByLibrary.simpleMessage("Hoá đơn sinh hoạt"),
        "lixi": MessageLookupByLibrary.simpleMessage("Lì xì"),
        "loading_data_please_wait": MessageLookupByLibrary.simpleMessage(
            "Đang tải dữ liệu, xin vui lòng đợi trong giây lát"),
        "loading_utility": MessageLookupByLibrary.simpleMessage("Tiện ích nạp"),
        "loan": MessageLookupByLibrary.simpleMessage("Khoản vay"),
        "loan_account":
            MessageLookupByLibrary.simpleMessage("Tài khoản tiền vay"),
        "loan_account_number":
            MessageLookupByLibrary.simpleMessage("Số tài khoản vay"),
        "loan_amount": MessageLookupByLibrary.simpleMessage("Khoản vay"),
        "loan_amount_up": MessageLookupByLibrary.simpleMessage(
            "Số tiền vay lên đến 100 triệu đồng."),
        "loan_application_letter":
            MessageLookupByLibrary.simpleMessage("Đề nghị vay"),
        "loan_balance_note": MessageLookupByLibrary.simpleMessage(
            "Tài sản bảo đảm cho khoản vay phải là số dư TGTK online VNĐ. Quý khách hiện không có tài sản bảo đảm thỏa yêu cầu"),
        "loan_date": MessageLookupByLibrary.simpleMessage("Ngày đăng ký vay"),
        "loan_date2": MessageLookupByLibrary.simpleMessage("Ngày vay"),
        "loan_details":
            MessageLookupByLibrary.simpleMessage("Chi tiết khoản vay"),
        "loan_empty":
            MessageLookupByLibrary.simpleMessage("Quý khách chưa có khoản vay"),
        "loan_exceed_allow_limit": MessageLookupByLibrary.simpleMessage(
            "Số tiền vay vượt quá hạn mức cho phép"),
        "loan_from_klb": MessageLookupByLibrary.simpleMessage("Vốn vay từ KLB"),
        "loan_information":
            MessageLookupByLibrary.simpleMessage("Thông tin khoản vay"),
        "loan_information2":
            MessageLookupByLibrary.simpleMessage("Thông tin vay"),
        "loan_period": MessageLookupByLibrary.simpleMessage("Kỳ hạn (tháng)"),
        "loan_proposal_disbursement_request":
            MessageLookupByLibrary.simpleMessage(
                "Đề nghị vay kiêm đề nghị giải ngân, khế ước nhận nợ"),
        "loan_purpose": MessageLookupByLibrary.simpleMessage("Nhu cầu vay"),
        "loan_purpose_s": MessageLookupByLibrary.simpleMessage("Mục đích vay"),
        "loan_registration_notice": MessageLookupByLibrary.simpleMessage(
            "KienlongBank xin thông báo Yêu cầu đăng ký vay vốn của Quý khách được phê duyệt chi tiết như sau:"),
        "loan_registration_successful_msg":
            MessageLookupByLibrary.simpleMessage(
                "Quý khách vui lòng chờ KienlongBank giải ngân."),
        "loan_time": MessageLookupByLibrary.simpleMessage("Thời gian vay"),
        "location": MessageLookupByLibrary.simpleMessage("Vị trí"),
        "lock_old_app_message": MessageLookupByLibrary.simpleMessage(
            "Sau 31/12, quý khách sẽ không thể đăng nhập ứng dụng cũ. Để tránh lỗi phát sinh, quý khách vui lòng khoá đăng nhập trên ứng dụng cũ."),
        "lock_old_app_title": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng khoá đăng nhập trên ứng dụng cũ"),
        "lockdown": MessageLookupByLibrary.simpleMessage("Phong tỏa"),
        "locked": MessageLookupByLibrary.simpleMessage("Đã khóa"),
        "locked_account_old_app": MessageLookupByLibrary.simpleMessage(
            "Đã khóa tài khoản trên ứng dụng cũ thành công."),
        "login": MessageLookupByLibrary.simpleMessage("Đăng nhập"),
        "login_bio_not_available": MessageLookupByLibrary.simpleMessage(
            "Đăng nhập nhập sinh trắc học không khả dụng, vui lòng sử dụng mật khẩu để đăng nhập!"),
        "login_name": MessageLookupByLibrary.simpleMessage("Tên đăng nhập"),
        "login_now": MessageLookupByLibrary.simpleMessage("Đăng nhập ngay"),
        "login_with_ksb_account": MessageLookupByLibrary.simpleMessage(
            "Đăng nhập bằng tài khoản KienlongBank"),
        "login_with_old_account": MessageLookupByLibrary.simpleMessage(
            "Đăng nhập bằng tài khoản của ứng dụng KienlongBank cũ"),
        "login_with_old_app": MessageLookupByLibrary.simpleMessage(
            "Quý khách vào KienlongBank Plus có thể dùng tên đăng nhập và mật khẩu tương ứng với số điện thoại và mật khẩu dùng ở ứng dụng KienlongBank cũ."),
        "logout": MessageLookupByLibrary.simpleMessage("Đăng xuất"),
        "logout_chat_msg": MessageLookupByLibrary.simpleMessage(
            "Bạn muốn thoát khỏi tài khoản chat?"),
        "lost": MessageLookupByLibrary.simpleMessage("Thẻ thất lạc"),
        "loyalty": MessageLookupByLibrary.simpleMessage("Loyalty"),
        "lucky_money": MessageLookupByLibrary.simpleMessage("Lì xì Phát Lộc"),
        "lucky_money_intro": MessageLookupByLibrary.simpleMessage(
            "<p>Cùng <b>KienlongBank</b> gửi những lời chúc ý nghĩa đến Người thân yêu của bạn!</p>"),
        "machine_stm": MessageLookupByLibrary.simpleMessage("Máy STM"),
        "male": MessageLookupByLibrary.simpleMessage("Nam"),
        "management_unit":
            MessageLookupByLibrary.simpleMessage("Đơn vị quản lý"),
        "manager_card": MessageLookupByLibrary.simpleMessage("Quản lý thẻ"),
        "maple_bear_intercollegiate_school":
            MessageLookupByLibrary.simpleMessage("Trường liên cấp Maple Bear"),
        "max_20_lucky_money": MessageLookupByLibrary.simpleMessage(
            "Chỉ được tạo tối đa 20 bao lì xì"),
        "max_length": m16,
        "max_transfer_vnpost": MessageLookupByLibrary.simpleMessage(
            "Số tiền vượt hạn mức cho phép là 10.000.000 VND"),
        "maximum_loan_book": MessageLookupByLibrary.simpleMessage(
            "Số tiền cho vay tối đa trên sổ"),
        "maybe_interested":
            MessageLookupByLibrary.simpleMessage("Có thể bạn quan tâm"),
        "media_not_supported": MessageLookupByLibrary.simpleMessage(
            "Định dạng không được hỗ trợ. Vui lòng chọn file khác"),
        "meet_schedule_detail":
            MessageLookupByLibrary.simpleMessage("Chi tiết lịch hẹn"),
        "meet_schedule_status":
            MessageLookupByLibrary.simpleMessage("Trạng thái lịch hẹn"),
        "member": MessageLookupByLibrary.simpleMessage("Thành viên"),
        "membership_class":
            MessageLookupByLibrary.simpleMessage("Hạng hội viên"),
        "membership_class_priority":
            MessageLookupByLibrary.simpleMessage("Hạng hội viên"),
        "message": MessageLookupByLibrary.simpleMessage("Tin nhắn"),
        "message_1": MessageLookupByLibrary.simpleMessage("Lời nhắn"),
        "message_1_empty": MessageLookupByLibrary.simpleMessage(
            "Lời nhắn không được để trống"),
        "method": MessageLookupByLibrary.simpleMessage("Cách thức"),
        "method_lending_capital":
            MessageLookupByLibrary.simpleMessage("Phương thức cho vay vốn"),
        "method_open_card":
            MessageLookupByLibrary.simpleMessage("Phương thức mở thẻ"),
        "method_validation_transaction": MessageLookupByLibrary.simpleMessage(
            "Phương thức xác thực Giao dịch"),
        "micro_finance": MessageLookupByLibrary.simpleMessage("Micro Finance"),
        "million": MessageLookupByLibrary.simpleMessage("Triệu"),
        "min_10m":
            MessageLookupByLibrary.simpleMessage("Tối thiểu 10.000.000 VND"),
        "min_10m_loan": MessageLookupByLibrary.simpleMessage(
            "Số tiền vay tối thiểu là 10 triệu"),
        "min_and_max_length": m17,
        "min_length": m18,
        "mindfull": MessageLookupByLibrary.simpleMessage("Lưu ý:"),
        "minimum": MessageLookupByLibrary.simpleMessage("Tối thiểu"),
        "minimum_amount_goals": MessageLookupByLibrary.simpleMessage(
            "Số tiền tích lũy tối thiểu là 50.000 VND."),
        "minimum_transfer_vnpost":
            MessageLookupByLibrary.simpleMessage("Tối thiểu 50.000 VND"),
        "minute": MessageLookupByLibrary.simpleMessage("phút"),
        "mobile_banking":
            MessageLookupByLibrary.simpleMessage("Ngân hàng lưu động"),
        "mobile_card_code": MessageLookupByLibrary.simpleMessage("Mã số"),
        "mobile_seri": MessageLookupByLibrary.simpleMessage("Số seri"),
        "mobile_topup":
            MessageLookupByLibrary.simpleMessage("Nạp tiền điện thoại"),
        "monday": MessageLookupByLibrary.simpleMessage("Thứ 2"),
        "money_in": MessageLookupByLibrary.simpleMessage("Tiền vào"),
        "money_transfer_purpose":
            MessageLookupByLibrary.simpleMessage("Mục đích chuyển tiền"),
        "month": MessageLookupByLibrary.simpleMessage("Tháng"),
        "monthly": MessageLookupByLibrary.simpleMessage("Hằng tháng"),
        "more": MessageLookupByLibrary.simpleMessage("Thêm"),
        "mortgage_loan":
            MessageLookupByLibrary.simpleMessage("Vay cầm cố sổ tiết kiệm"),
        "mortgage_loan_online": MessageLookupByLibrary.simpleMessage(
            "Vay cầm cố sổ tiết kiệm online"),
        "most_recent_statement_period":
            MessageLookupByLibrary.simpleMessage("Kỳ sao kê hiện tại"),
        "mrz_error": MessageLookupByLibrary.simpleMessage(
            "Không nhận diện được thông tin mặt sau CCCD. Vui lòng chụp lại"),
        "my_buill": MessageLookupByLibrary.simpleMessage("Tôi"),
        "my_card": MessageLookupByLibrary.simpleMessage("Thẻ của mình"),
        "my_customer":
            MessageLookupByLibrary.simpleMessage("Khách hàng của tôi"),
        "my_qr_code": MessageLookupByLibrary.simpleMessage("Mã QR của tôi"),
        "my_qr_scan": MessageLookupByLibrary.simpleMessage("Qr của tôi"),
        "name_reminiscent": MessageLookupByLibrary.simpleMessage("Tên gợi nhớ"),
        "national": MessageLookupByLibrary.simpleMessage("Quốc tịch"),
        "nationality": MessageLookupByLibrary.simpleMessage("Quốc tịch"),
        "need_my_location_permission": MessageLookupByLibrary.simpleMessage(
            "Hiện quý khách chưa cho phép ứng dụng xác định vị trí của quý khách.\nỨng dụng cần vị trí để xác định điểm giao dịch gần nhất"),
        "network_counseling_empty": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng chọn văn phòng giao dịch"),
        "network_login_alert": MessageLookupByLibrary.simpleMessage(
            "Quý khách hàng chưa đăng nhập. Vui lòng đăng nhập tài khoản trước khi sử dụng chức năng"),
        "new_account": MessageLookupByLibrary.simpleMessage("Tài khoản mới"),
        "new_credit_info_empty_des_no_card": MessageLookupByLibrary.simpleMessage(
            "Mở thẻ ngay để tận hưởng vô vàn ưu đãi hấp dẫn từ KienlongBank"),
        "new_password": MessageLookupByLibrary.simpleMessage("Mật khẩu mới"),
        "new_password_changed": MessageLookupByLibrary.simpleMessage(
            "Mật khẩu đăng nhập đã được thay đổi"),
        "new_pin_code": MessageLookupByLibrary.simpleMessage("Nhập PIN mới"),
        "new_video_call":
            MessageLookupByLibrary.simpleMessage("Cuộc gọi video mới"),
        "news": MessageLookupByLibrary.simpleMessage("Tin tức"),
        "next": MessageLookupByLibrary.simpleMessage("Tiếp tục"),
        "next_month": MessageLookupByLibrary.simpleMessage("tháng tiếp theo"),
        "next_payment_period_not_yet": MessageLookupByLibrary.simpleMessage(
            "Chưa đến kì thanh toán tiếp theo"),
        "nick_name_on_boarding_des": MessageLookupByLibrary.simpleMessage(
            "Quý khách có thể dễ dàng tạo nickname của riêng mình, thay thế số tài khoản giúp dễ nhớ, thuận tiện trong giao dịch."),
        "nickname": MessageLookupByLibrary.simpleMessage("Nickname"),
        "nickname_denied":
            MessageLookupByLibrary.simpleMessage("Nickname bị từ chối"),
        "nickname_info":
            MessageLookupByLibrary.simpleMessage("Thông tin nickname"),
        "nickname_pending":
            MessageLookupByLibrary.simpleMessage("Nickname đang chờ duyệt"),
        "nickname_require":
            MessageLookupByLibrary.simpleMessage("Nickname yêu cầu"),
        "nicknames_for_customers":
            MessageLookupByLibrary.simpleMessage("Tên bạn gọi khách hàng"),
        "no": MessageLookupByLibrary.simpleMessage("Không"),
        "no_bill": MessageLookupByLibrary.simpleMessage("Không in biên lai"),
        "no_branches_found": MessageLookupByLibrary.simpleMessage(
            "Không tìm thấy chi nhánh nào trong khu vực này"),
        "no_data": MessageLookupByLibrary.simpleMessage("Không có dữ liệu"),
        "no_match_result":
            MessageLookupByLibrary.simpleMessage("Không có kết quả trùng khớp"),
        "no_refunds":
            MessageLookupByLibrary.simpleMessage("Không thanh toán lại"),
        "no_schedule": MessageLookupByLibrary.simpleMessage("Không lặp"),
        "no_verify_infor":
            MessageLookupByLibrary.simpleMessage("chưa xác thực"),
        "non_physical_card":
            MessageLookupByLibrary.simpleMessage("Thẻ phi vật lý"),
        "none_account_nickname":
            MessageLookupByLibrary.simpleMessage("Chưa có tài khoản nickname"),
        "normal": MessageLookupByLibrary.simpleMessage("Thường"),
        "not_activated": MessageLookupByLibrary.simpleMessage("Chưa kích hoạt"),
        "not_agree_content_approval": MessageLookupByLibrary.simpleMessage(
            "Trường hợp Khách hàng không đồng ý chọn \"Huỷ bỏ\" để trở về Trang chủ."),
        "not_auth": MessageLookupByLibrary.simpleMessage("Chưa xác thực"),
        "not_collected_yet":
            MessageLookupByLibrary.simpleMessage("Chưa thu thập"),
        "not_create_and_settlement_loan_in_on_day":
            MessageLookupByLibrary.simpleMessage(
                "Khách hàng không thực hiện mở và tất toán trước hạn cùng 01 khoản vay trong cùng 01 ngày."),
        "not_enough_money":
            MessageLookupByLibrary.simpleMessage("Không đủ số dư"),
        "not_have_beneficiaries_on_list": MessageLookupByLibrary.simpleMessage(
            "Quý khách hiện chưa có người thụ hưởng nào trong danh sách"),
        "not_include_accents_and_special_characters":
            MessageLookupByLibrary.simpleMessage(
                "Không bao gồm dấu, chữ cái in hoa và ký tự đặc biệt"),
        "not_lose_corners":
            MessageLookupByLibrary.simpleMessage("•  Ảnh không bị mất góc"),
        "not_map_install": MessageLookupByLibrary.simpleMessage(
            "Không có ứng dụng bản đồ nào đang được cài trên máy của bạn"),
        "not_permission_access_contacts": MessageLookupByLibrary.simpleMessage(
            "KienlongBank App chưa được cấp quyền truy cập danh bạ. Vui lòng cấp quyền truy cập trước khi thử lại"),
        "not_received_code": MessageLookupByLibrary.simpleMessage(
            "Quý khách chưa nhận được mã?"),
        "not_support_247": MessageLookupByLibrary.simpleMessage(
            "Ngân hàng thụ hưởng tạm thời chưa hỗ trợ chuyển tiền nhanh 24/7. Vui lòng thực hiện giao dịch chuyển thường."),
        "not_support_247_via_card_number": MessageLookupByLibrary.simpleMessage(
            "Ngân hàng thụ hưởng tạm thời chưa hỗ trợ chuyển tiền đến số thẻ. Có thể bạn cần chuyển tiền đến số tài khoản"),
        "note": MessageLookupByLibrary.simpleMessage("Ghi chú"),
        "note2": MessageLookupByLibrary.simpleMessage("Lưu ý"),
        "note_commitment":
            MessageLookupByLibrary.simpleMessage("Lưu ý và cam kết"),
        "note_like_this":
            MessageLookupByLibrary.simpleMessage("Ghi chú trông như thế này"),
        "notification": MessageLookupByLibrary.simpleMessage("Thông báo"),
        "now": MessageLookupByLibrary.simpleMessage("Bây giờ"),
        "number_id": MessageLookupByLibrary.simpleMessage("Số giấy tờ"),
        "number_id_code": MessageLookupByLibrary.simpleMessage("Mã số giấy tờ"),
        "number_identification":
            MessageLookupByLibrary.simpleMessage("Số giấy tờ"),
        "number_money": MessageLookupByLibrary.simpleMessage("Số bao lì xì"),
        "number_receiver":
            MessageLookupByLibrary.simpleMessage("Số người nhận"),
        "number_repay":
            MessageLookupByLibrary.simpleMessage("Số lần thanh toán lại"),
        "of_ksbank": MessageLookupByLibrary.simpleMessage(" của KienlongBank"),
        "of_you": MessageLookupByLibrary.simpleMessage("của bạn."),
        "offline": MessageLookupByLibrary.simpleMessage("Offline"),
        "ok": MessageLookupByLibrary.simpleMessage("Chấp nhận"),
        "old_password": MessageLookupByLibrary.simpleMessage("Mật khẩu cũ"),
        "onboart_etoken_7": MessageLookupByLibrary.simpleMessage(
            "Thuật toán xác thực giao dịch sau khi khách hàng chọn Xác nhận, Kết quả xác thực được thông báo tức thời."),
        "one_input_code_guide":
            MessageLookupByLibrary.simpleMessage("1. Nhập mã giới thiệu"),
        "one_input_code_guide_content": MessageLookupByLibrary.simpleMessage(
            "Trong quá trình đăng ký, quý khách có thể nhập mã giới thiệu thủ công, hoặc sử dụng chức năng quét mã QR bằng cách bấm nút trong khoanh đỏ ở hình dưới."),
        "online_deposit_confirmation": MessageLookupByLibrary.simpleMessage(
            "Giấy xác nhận tiền gửi Online"),
        "online_loan_contract":
            MessageLookupByLibrary.simpleMessage("Hợp đồng vay online"),
        "online_mortgage_loan_product": MessageLookupByLibrary.simpleMessage(
            "Đó là, sản phẩm cho vay online cầm cố tiền gửi tiết kiệm online tại KienlongBank:"),
        "online_payment":
            MessageLookupByLibrary.simpleMessage("Thanh toán trực tuyến"),
        "online_saving_still_have_money": MessageLookupByLibrary.simpleMessage(
            "Vừa có thu nhập từ gửi tiền gửi tiết kiệm online, vẫn có tiền chi tiêu, phục vụ đời sống khi cần nhanh chóng trong ngày."),
        "online_savings_account": MessageLookupByLibrary.simpleMessage(
            "Tài khoản tiết kiệm trực tuyến"),
        "open_account": MessageLookupByLibrary.simpleMessage("Mở tài khoản"),
        "open_account_nickname":
            MessageLookupByLibrary.simpleMessage("Mở tài khoản nickname"),
        "open_card": MessageLookupByLibrary.simpleMessage("Mở thẻ"),
        "open_card_now": MessageLookupByLibrary.simpleMessage("Mở thẻ ngay"),
        "open_card_physics":
            MessageLookupByLibrary.simpleMessage("Mở thẻ vật lý"),
        "open_card_stm":
            MessageLookupByLibrary.simpleMessage("Mở thẻ siêu tốc tại STM"),
        "open_card_stm_describe": MessageLookupByLibrary.simpleMessage(
            "Chỉ với 5 phút, mở và nhận thẻ ngay tại STM KienlongBank."),
        "open_card_success":
            MessageLookupByLibrary.simpleMessage("Mở thẻ thành công"),
        "open_new": MessageLookupByLibrary.simpleMessage("Mở mới"),
        "open_new_card": MessageLookupByLibrary.simpleMessage("Mở thẻ mới"),
        "open_offline": MessageLookupByLibrary.simpleMessage("Mở tại quầy"),
        "open_online": MessageLookupByLibrary.simpleMessage("Mở online"),
        "opened": MessageLookupByLibrary.simpleMessage(" đã mở"),
        "opening_date": MessageLookupByLibrary.simpleMessage("Ngày mở"),
        "option": MessageLookupByLibrary.simpleMessage("Tùy chỉnh"),
        "option_birthday_beautiful_number":
            MessageLookupByLibrary.simpleMessage(
                "Tài khoản số đẹp theo ngày sinh"),
        "option_phone_beautiful_number": MessageLookupByLibrary.simpleMessage(
            "Tài khoản số đẹp theo số điện thoại"),
        "optional": MessageLookupByLibrary.simpleMessage("Không bắt buộc"),
        "or": MessageLookupByLibrary.simpleMessage("OR"),
        "order": MessageLookupByLibrary.simpleMessage("Sắp xếp"),
        "original_account":
            MessageLookupByLibrary.simpleMessage("Tài khoản gốc"),
        "other_amount": MessageLookupByLibrary.simpleMessage("Số tiền khác"),
        "other_offers": MessageLookupByLibrary.simpleMessage("Ưu đãi khác"),
        "other_services": MessageLookupByLibrary.simpleMessage("Dịch vụ Khác"),
        "otp_auth": MessageLookupByLibrary.simpleMessage("Xác thực OTP"),
        "otp_auth_summary": MessageLookupByLibrary.simpleMessage(
            "OTP đã được gửi đến số điện thoại của Quý khách. Vui lòng nhập OTP vào ô dưới đây để xác thực."),
        "otp_code_six_digits": MessageLookupByLibrary.simpleMessage(
            "Mã OTP gồm 6 chữ số và được gửi về số điện thoại"),
        "otp_send_to_email_msg": m19,
        "otp_send_to_phone_msg": MessageLookupByLibrary.simpleMessage(
            "Mã OTP đã được gửi đến số điện thoại của quý khách"),
        "otp_sent_via_email": MessageLookupByLibrary.simpleMessage(
            "Mã OTP đã được gửi qua email"),
        "outgoing_call": MessageLookupByLibrary.simpleMessage("Cuộc gọi đi"),
        "overview": MessageLookupByLibrary.simpleMessage("Tổng quan"),
        "own_account": MessageLookupByLibrary.simpleMessage("Chủ tài khoản"),
        "own_capital": MessageLookupByLibrary.simpleMessage("Vốn tự có"),
        "package_name_data": MessageLookupByLibrary.simpleMessage("Tên gói"),
        "paid": MessageLookupByLibrary.simpleMessage("Đã trả"),
        "paper_info": MessageLookupByLibrary.simpleMessage("Thông tin giấy tờ"),
        "papers": MessageLookupByLibrary.simpleMessage("Giấy tờ"),
        "partnerCD_empty": MessageLookupByLibrary.simpleMessage(
            "Đối tác thu hộ không được để trống."),
        "passport": MessageLookupByLibrary.simpleMessage("Hộ chiếu"),
        "passport_expiration_date":
            MessageLookupByLibrary.simpleMessage("Ngày hết hạn hộ chiếu"),
        "password": MessageLookupByLibrary.simpleMessage("Mật khẩu"),
        "password_authentication":
            MessageLookupByLibrary.simpleMessage("Xác thực mật khẩu"),
        "password_changed_msg": MessageLookupByLibrary.simpleMessage(
            "Mật khẩu đăng nhập đã được thay đổi"),
        "password_etoken":
            MessageLookupByLibrary.simpleMessage("Mật khẩu eToken"),
        "pay": MessageLookupByLibrary.simpleMessage("Thanh toán"),
        "pay_bill": MessageLookupByLibrary.simpleMessage("Trả hóa đơn"),
        "pay_calendar": MessageLookupByLibrary.simpleMessage("Lịch trả nợ"),
        "pay_card": MessageLookupByLibrary.simpleMessage("Trả nợ thẻ tín dụng"),
        "pay_card_mobile": MessageLookupByLibrary.simpleMessage("Nạp ngay"),
        "pay_loan": MessageLookupByLibrary.simpleMessage("Trả khoản vay"),
        "pay_now": MessageLookupByLibrary.simpleMessage("Tất toán ngay"),
        "pay_off_loan_before_maturity": MessageLookupByLibrary.simpleMessage(
            "Quý khách đang thực hiện tất toán khoản vay trước hạn"),
        "payer_returns":
            MessageLookupByLibrary.simpleMessage("Người chuyển trả"),
        "payment": MessageLookupByLibrary.simpleMessage("Nộp tiền"),
        "payment_account":
            MessageLookupByLibrary.simpleMessage("Tài khoản thanh toán"),
        "payment_amount":
            MessageLookupByLibrary.simpleMessage("Số tiền thanh toán"),
        "payment_confirmation":
            MessageLookupByLibrary.simpleMessage("Xác nhận tất toán"),
        "payment_deadline":
            MessageLookupByLibrary.simpleMessage("Thời hạn nộp tiền"),
        "payment_history":
            MessageLookupByLibrary.simpleMessage("Lịch sử thanh toán"),
        "payment_information":
            MessageLookupByLibrary.simpleMessage("Thông tin tất toán"),
        "payment_invest":
            MessageLookupByLibrary.simpleMessage("Thanh toán Đầu tư"),
        "payment_now": MessageLookupByLibrary.simpleMessage("Thanh toán ngay"),
        "payment_period": MessageLookupByLibrary.simpleMessage("Kỳ thanh toán"),
        "payment_residence":
            MessageLookupByLibrary.simpleMessage("Thanh toán đặt mua"),
        "payment_someone_number": MessageLookupByLibrary.simpleMessage(
            "Nhập số thẻ bạn muốn thanh toán"),
        "payoo_transaction_code":
            MessageLookupByLibrary.simpleMessage("Mã giao dịch Payoo"),
        "pending": MessageLookupByLibrary.simpleMessage("Chờ duyệt"),
        "people": MessageLookupByLibrary.simpleMessage("người"),
        "people_id":
            MessageLookupByLibrary.simpleMessage("Chứng minh nhân dân"),
        "perform_during_selected_time": MessageLookupByLibrary.simpleMessage(
            "sẽ được thực hiện trong thời gian đã chọn"),
        "period": MessageLookupByLibrary.simpleMessage("Kỳ hạn"),
        "periodic_command":
            MessageLookupByLibrary.simpleMessage("Lệnh định kỳ"),
        "permanent_address":
            MessageLookupByLibrary.simpleMessage("Địa chỉ thường trú"),
        "permission_to_operate":
            MessageLookupByLibrary.simpleMessage("Cho phép hoạt động"),
        "person_information":
            MessageLookupByLibrary.simpleMessage("Thông tin cá nhân"),
        "person_involved":
            MessageLookupByLibrary.simpleMessage("Người liên đới"),
        "personal": MessageLookupByLibrary.simpleMessage("Cá nhân"),
        "personal_consultant":
            MessageLookupByLibrary.simpleMessage("Chuyên viên tư vấn riêng"),
        "personal_info":
            MessageLookupByLibrary.simpleMessage("Thông tin cá nhân"),
        "phone_number": MessageLookupByLibrary.simpleMessage("Số điện thoại"),
        "phone_number_bill":
            MessageLookupByLibrary.simpleMessage("Số điện thoại"),
        "phone_number_optional": MessageLookupByLibrary.simpleMessage(
            "Số điện thoại (không bắt buộc)"),
        "phone_recharge":
            MessageLookupByLibrary.simpleMessage("Nạp điện thoại"),
        "phone_recharge_card":
            MessageLookupByLibrary.simpleMessage("Nạp thẻ điện thoại"),
        "physical_card": MessageLookupByLibrary.simpleMessage("Thẻ vật lý"),
        "pin_code": MessageLookupByLibrary.simpleMessage("Mã pin"),
        "place": MessageLookupByLibrary.simpleMessage("Địa điểm"),
        "place_of_issue": MessageLookupByLibrary.simpleMessage("Nơi cấp"),
        "place_of_residence":
            MessageLookupByLibrary.simpleMessage("Nơi thường trú"),
        "please_active_card": MessageLookupByLibrary.simpleMessage(
            "Thẻ chưa được kích hoạt, quý khách vui lòng kích hoạt thẻ để sử dụng"),
        "please_active_card_now": MessageLookupByLibrary.simpleMessage(
            "Vui lòng thực hiện kích hoạt Thẻ để bắt đầu sử dụng ngay"),
        "please_check_and_choose_another_numbers":
            MessageLookupByLibrary.simpleMessage(
                "Quý khách vui lòng kiểm tra và chọn lại dãy số khác."),
        "please_choose_receiver":
            MessageLookupByLibrary.simpleMessage("Vui lòng chọn người nhận"),
        "please_choose_the_settlement_method": MessageLookupByLibrary.simpleMessage(
            "Vui lòng chọn hình thức nhận tiền mà quý khách thấy thuận tiện nhất"),
        "please_complete": MessageLookupByLibrary.simpleMessage(
            "Vui lòng hoàn thành các trường còn thiếu"),
        "please_enter_account_number":
            MessageLookupByLibrary.simpleMessage("Vui lòng nhập số tài khoản"),
        "please_enter_and_continue": MessageLookupByLibrary.simpleMessage(
            ", vui lòng nhập mã để tiếp tục."),
        "please_enter_referral_code": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng nhập mã giới thiệu của nhân viên kinh doanh, hoặc bỏ qua nếu không có mã."),
        "please_input_address": MessageLookupByLibrary.simpleMessage(
            "Vui lòng cung cấp địa chỉ đầy đủ và chính xác"),
        "please_input_money":
            MessageLookupByLibrary.simpleMessage("Vui lòng nhập số tiền"),
        "please_input_number":
            MessageLookupByLibrary.simpleMessage("Vui lòng nhập số bao"),
        "please_login_continue": MessageLookupByLibrary.simpleMessage(
            "Vui lòng đăng nhập để tiếp tục"),
        "please_select_city_district": MessageLookupByLibrary.simpleMessage(
            "Bạn cần chọn Tỉnh/Thành phố trước"),
        "please_select_continue":
            MessageLookupByLibrary.simpleMessage("Vui lòng chọn \"Tiếp tục\""),
        "please_try_again": MessageLookupByLibrary.simpleMessage(
            "Vui lòng thử lại sau ít phút"),
        "pls_fill_all_info":
            MessageLookupByLibrary.simpleMessage("Vui lòng điền đủ thông tin"),
        "position": MessageLookupByLibrary.simpleMessage("Chức danh"),
        "post_office_name": MessageLookupByLibrary.simpleMessage("Tên bưu cục"),
        "pre_payment":
            MessageLookupByLibrary.simpleMessage("Tất toán trước hạn"),
        "prepay": MessageLookupByLibrary.simpleMessage("Dịch vụ Trả trước"),
        "prepayment_fee":
            MessageLookupByLibrary.simpleMessage("Phí trả nợ trước hạn"),
        "preview": MessageLookupByLibrary.simpleMessage("Xem trước"),
        "principal_amount": MessageLookupByLibrary.simpleMessage("Số tiền gốc"),
        "principal_amount_settled":
            MessageLookupByLibrary.simpleMessage("Số tiền gốc tất toán"),
        "privileges_of_priority_members": MessageLookupByLibrary.simpleMessage(
            "Đặc quyền của phân hạng hội viên ưu tiên"),
        "privileges_of_priority_membership":
            MessageLookupByLibrary.simpleMessage(
                "Đặc quyền của phân hạng hội viên ưu tiên"),
        "processing": MessageLookupByLibrary.simpleMessage("Đang xử lý"),
        "product": MessageLookupByLibrary.simpleMessage("Sản phẩm"),
        "profile_account_info_snack_msg_update_successful":
            MessageLookupByLibrary.simpleMessage(
                "Cập nhật thông tin thành công"),
        "profile_address_des_contact_enter":
            MessageLookupByLibrary.simpleMessage(
                "Vui lòng nhập địa chỉ liên hệ mới của quý khách"),
        "profile_address_inline_msg_district":
            MessageLookupByLibrary.simpleMessage(
                "Quý khách vui lòng chọn quận, huyện"),
        "profile_address_inline_msg_ward": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng chọn phường, xã"),
        "profile_change_etoken_label_current_etoken":
            MessageLookupByLibrary.simpleMessage("Mật khẩu eToken hiện tại"),
        "profile_change_etoken_label_new_etoken":
            MessageLookupByLibrary.simpleMessage("Mật khẩu eToken mới"),
        "profile_change_etoken_title_change_etoken":
            MessageLookupByLibrary.simpleMessage("Đổi mật khẩu eToken"),
        "profile_change_pass_label_new_pass":
            MessageLookupByLibrary.simpleMessage("Mật khẩu mới"),
        "profile_change_pass_label_old_pass":
            MessageLookupByLibrary.simpleMessage("Mật khẩu cũ"),
        "profile_change_pass_label_pass_request":
            MessageLookupByLibrary.simpleMessage("Yêu cầu mật khẩu"),
        "profile_change_pass_title_change":
            MessageLookupByLibrary.simpleMessage("Thay đổi mật khẩu"),
        "profile_code": MessageLookupByLibrary.simpleMessage("Mã hồ sơ"),
        "profile_collaborator_confirm_label_collaborator_code":
            MessageLookupByLibrary.simpleMessage("Mã CTV"),
        "profile_collaborator_confirm_title_collaborator_confirm":
            MessageLookupByLibrary.simpleMessage("Xác nhận cộng tác viên"),
        "profile_consult_bottomsheet_title_request_topic":
            MessageLookupByLibrary.simpleMessage("Quý khách cần hỗ trợ gì?"),
        "profile_consult_cta_send_request":
            MessageLookupByLibrary.simpleMessage("Gửi yêu cầu"),
        "profile_consult_des_request_here": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng để lại yêu cầu trợ giúp tại đây, KienlongBank sẽ trả lời lại trong 8 giờ làm việc"),
        "profile_consult_label_content":
            MessageLookupByLibrary.simpleMessage("Nội dung"),
        "profile_consult_label_processing":
            MessageLookupByLibrary.simpleMessage("Đang xử lý"),
        "profile_consult_label_request":
            MessageLookupByLibrary.simpleMessage("Yêu cầu của quý khách"),
        "profile_consult_snack_msg_send_successful":
            MessageLookupByLibrary.simpleMessage("Gửi yêu cầu thành công"),
        "profile_consult_title_consultant":
            MessageLookupByLibrary.simpleMessage("Tư vấn"),
        "profile_etoken_guide_des_guide1": MessageLookupByLibrary.simpleMessage(
            "eToken là bảo mật 2 lớp của ứng dụng KienlongBank được cài đặt trên thiết bị khách hàng."),
        "profile_etoken_guide_des_guide2": MessageLookupByLibrary.simpleMessage(
            "Phương thức này không cần kết nối sóng điện thoại, nên không thể chặn, giả dạng. Cho phép khách hàng thực hiện giao dịch ở Việt Nam và nước ngoài."),
        "profile_etoken_guide_des_guide3": MessageLookupByLibrary.simpleMessage(
            "eToken được gắn vào quy trình giao dịch để tăng tính tiện lợi & bảo mật cho thanh toán của khách hàng."),
        "profile_etoken_guide_des_guide4": MessageLookupByLibrary.simpleMessage(
            "Để lấy mã eToken xác thực giao dich, khách hàng nhập đúng mật khẩu truy cập eToken đã thiết lập"),
        "profile_etoken_guide_des_guide5": MessageLookupByLibrary.simpleMessage(
            "Là chuỗi ngẫu nhiên được tạo bởi thuật toán. Mã này được thay đổi mỗi 30 giây để tăng cường bảo mật cho khách hàng."),
        "profile_etoken_guide_des_question":
            MessageLookupByLibrary.simpleMessage("eToken là gì?"),
        "profile_etoken_guide_title_guide":
            MessageLookupByLibrary.simpleMessage("Hướng dẫn sử dụng eToken"),
        "profile_home_button_sign_out":
            MessageLookupByLibrary.simpleMessage("Đăng xuất"),
        "profile_home_label_answer":
            MessageLookupByLibrary.simpleMessage("Giải đáp"),
        "profile_home_label_bank_service":
            MessageLookupByLibrary.simpleMessage("Gói dịch vụ ngân hàng số"),
        "profile_home_label_change_pass":
            MessageLookupByLibrary.simpleMessage("Thay đổi mật khẩu"),
        "profile_home_label_common_question":
            MessageLookupByLibrary.simpleMessage("Câu hỏi thường gặp"),
        "profile_home_label_contact_support":
            MessageLookupByLibrary.simpleMessage("Liên hệ hỗ trợ"),
        "profile_home_label_device_manage":
            MessageLookupByLibrary.simpleMessage("Quản lý thiết bị"),
        "profile_home_label_my_qr":
            MessageLookupByLibrary.simpleMessage("Mã QR của tôi"),
        "profile_home_label_personal_info":
            MessageLookupByLibrary.simpleMessage("Thông tin cá nhân"),
        "profile_home_label_security":
            MessageLookupByLibrary.simpleMessage("Bảo mật"),
        "profile_home_label_setting":
            MessageLookupByLibrary.simpleMessage("Cài đặt"),
        "profile_home_label_support":
            MessageLookupByLibrary.simpleMessage("Hỗ trợ"),
        "profile_home_label_theme":
            MessageLookupByLibrary.simpleMessage("Diện mạo"),
        "profile_home_label_utility":
            MessageLookupByLibrary.simpleMessage("Tiện ích"),
        "profile_home_label_verify_method":
            MessageLookupByLibrary.simpleMessage("Phương thức xác thực"),
        "profile_home_title_account":
            MessageLookupByLibrary.simpleMessage("Tài khoản"),
        "profile_home_tooltip_update_email": MessageLookupByLibrary.simpleMessage(
            "Vui lòng cập nhật email để nhận các thông báo biến động số dư tài khoản"),
        "profile_my_qr_button_require_transaction":
            MessageLookupByLibrary.simpleMessage("Yêu cầu chuyển tiền"),
        "profile_my_qr_label_list_bank": MessageLookupByLibrary.simpleMessage(
            "Xem danh sách các ngân hàng hỗ trợ "),
        "profile_my_qr_label_personal":
            MessageLookupByLibrary.simpleMessage("Cá nhân"),
        "profile_notice_balance_label_balance":
            MessageLookupByLibrary.simpleMessage("Số dư"),
        "profile_notice_balance_label_email":
            MessageLookupByLibrary.simpleMessage("Email"),
        "profile_notice_balance_label_new":
            MessageLookupByLibrary.simpleMessage("Thêm mới"),
        "profile_notice_balance_label_send_message":
            MessageLookupByLibrary.simpleMessage("Gửi tin nhắn"),
        "profile_personal_info_bottomsheet_des_pass_confirm":
            MessageLookupByLibrary.simpleMessage(
                "Vì sự an toàn của quý khách, vui lòng xác nhận bằng cách nhập mật khẩu đăng nhập ứng dụng"),
        "profile_personal_info_button_pass_enter":
            MessageLookupByLibrary.simpleMessage("Nhập mật khẩu"),
        "profile_personal_info_label_account":
            MessageLookupByLibrary.simpleMessage("Thông tin tài khoản"),
        "profile_personal_info_label_account_name":
            MessageLookupByLibrary.simpleMessage("Tên tài khoản"),
        "profile_personal_info_label_address":
            MessageLookupByLibrary.simpleMessage("Địa chỉ"),
        "profile_personal_info_label_contact_address":
            MessageLookupByLibrary.simpleMessage("Địa chỉ liên hệ"),
        "profile_personal_info_label_date_issue":
            MessageLookupByLibrary.simpleMessage("Ngày cấp"),
        "profile_personal_info_label_email":
            MessageLookupByLibrary.simpleMessage("Email"),
        "profile_personal_info_label_name":
            MessageLookupByLibrary.simpleMessage("Họ và tên"),
        "profile_personal_info_label_paper_info":
            MessageLookupByLibrary.simpleMessage("Thông tin giấy tờ"),
        "profile_personal_info_label_phone_number":
            MessageLookupByLibrary.simpleMessage("Số điện thoại"),
        "profile_personal_info_label_place_issue":
            MessageLookupByLibrary.simpleMessage("Nơi cấp"),
        "profile_personal_info_label_residence":
            MessageLookupByLibrary.simpleMessage("Địa chỉ thường trú"),
        "profile_personal_info_title_personal_info":
            MessageLookupByLibrary.simpleMessage("Thông tin cá nhân"),
        "profile_referral_code_button_get_commission":
            MessageLookupByLibrary.simpleMessage("Rút hoa hồng"),
        "profile_referral_code_button_policy":
            MessageLookupByLibrary.simpleMessage("Chính sách hoa hồng"),
        "profile_referral_code_button_save_qr":
            MessageLookupByLibrary.simpleMessage("Lưu QR"),
        "profile_referral_code_des_scan":
            MessageLookupByLibrary.simpleMessage("Quét mã QR cá nhân"),
        "profile_referral_code_des_type_code":
            MessageLookupByLibrary.simpleMessage(
                "Hoặc nhập mã giới thiệu bên dưới"),
        "profile_referral_code_label_invite_list":
            MessageLookupByLibrary.simpleMessage("Danh sách đã mời"),
        "profile_referral_code_label_received_commission":
            MessageLookupByLibrary.simpleMessage("Hoa hồng đã nhận"),
        "profile_referral_code_label_success_referral":
            MessageLookupByLibrary.simpleMessage("Giới thiệu thành công"),
        "profile_referral_code_label_unconfirmed":
            MessageLookupByLibrary.simpleMessage("Chưa xác nhận CTV"),
        "profile_referral_code_label_upcoming_commision":
            MessageLookupByLibrary.simpleMessage("Hoa hồng chưa nhận"),
        "profile_referral_code_title_code":
            MessageLookupByLibrary.simpleMessage("Mã giới thiệu"),
        "profile_referral_code_title_commission":
            MessageLookupByLibrary.simpleMessage("Hoa hồng"),
        "profile_referral_code_title_referral_person":
            MessageLookupByLibrary.simpleMessage("Người được giới thiệu"),
        "profile_service_package_label_another_services":
            MessageLookupByLibrary.simpleMessage("Chọn gói dịch vụ khác"),
        "profile_service_package_label_current_service":
            MessageLookupByLibrary.simpleMessage("Gói dịch vụ đang dùng"),
        "profile_service_package_title_eBank":
            MessageLookupByLibrary.simpleMessage("Gói dịch vụ eBank"),
        "profile_set_etoken_des_set_etoken":
            MessageLookupByLibrary.simpleMessage("Thiết lập mật khẩu eToken"),
        "profile_set_etoken_inline_msg_pass_not_match":
            MessageLookupByLibrary.simpleMessage("Mật khẩu không trùng khớp"),
        "profile_set_etoken_label_pass":
            MessageLookupByLibrary.simpleMessage("Mật khẩu eToken"),
        "profile_set_etoken_snack_msg_success":
            MessageLookupByLibrary.simpleMessage("Thiết lập eToken thành công"),
        "profile_set_etoken_title_set_etoken":
            MessageLookupByLibrary.simpleMessage("Tạo mật khẩu eToken"),
        "profile_setting_button_apply_topic":
            MessageLookupByLibrary.simpleMessage("Áp dụng chủ đề"),
        "profile_setting_des_device_list": MessageLookupByLibrary.simpleMessage(
            "Danh sách các thiết bị mà quý khách đã từng sử dụng để đăng nhập vào ứng dụng."),
        "profile_setting_des_topic_applied":
            MessageLookupByLibrary.simpleMessage("Chủ đề đang được áp dụng"),
        "profile_setting_error_noti": MessageLookupByLibrary.simpleMessage(
            "Vui lòng đăng nhập ứng dụng để xem Thông báo Biến động số dư"),
        "profile_setting_label_link_cancel":
            MessageLookupByLibrary.simpleMessage("Hủy liên kết"),
        "profile_setting_label_signing_in":
            MessageLookupByLibrary.simpleMessage("Đang đăng nhập"),
        "profile_setting_snack_msg_apply_successful":
            MessageLookupByLibrary.simpleMessage("Áp dụng chủ đề thành công"),
        "profile_setting_title_balance_noti":
            MessageLookupByLibrary.simpleMessage("Biến động số dư"),
        "profile_setting_title_manage_device":
            MessageLookupByLibrary.simpleMessage("Quản lý thiết bị"),
        "profile_setting_title_noti_set":
            MessageLookupByLibrary.simpleMessage("Cài đặt thông báo"),
        "profile_setting_title_promotion_noti":
            MessageLookupByLibrary.simpleMessage("Khuyến mãi"),
        "profile_setting_title_system_noti":
            MessageLookupByLibrary.simpleMessage("Hệ thống"),
        "profile_setting_title_topic":
            MessageLookupByLibrary.simpleMessage("Chủ đề"),
        "profile_sms_setting_account_registed":
            MessageLookupByLibrary.simpleMessage("Số tài khoản đăng ký"),
        "profile_sms_setting_auto_fees": MessageLookupByLibrary.simpleMessage(
            "Phí được thu tự động hàng tháng."),
        "profile_sms_setting_cancel":
            MessageLookupByLibrary.simpleMessage("Hủy dịch vụ SMS Banking"),
        "profile_sms_setting_cancel_desc": MessageLookupByLibrary.simpleMessage(
            "Quý khách vẫn nhận được SMS OTP và các thông báo quan trọng từ KienlongBank khi hủy đăng ký SMS Banking."),
        "profile_sms_setting_cancel_message": MessageLookupByLibrary.simpleMessage(
            "Quý khách vẫn nhận được SMS OTP qua SĐT đăng ký. Phí dịch vụ SMS Banking vẫn được tính cho đến hết ngày"),
        "profile_sms_setting_confirm_register":
            MessageLookupByLibrary.simpleMessage(
                "Quý khách có chắc chắn muốn đăng ký SMS Banking?"),
        "profile_sms_setting_des1_sms_setting":
            MessageLookupByLibrary.simpleMessage(
                "SMS Banking là hình thức gửi thông báo biến động số dư qua tin nhắn tới các số điện thoại đăng ký với KienlongBank"),
        "profile_sms_setting_des3_here_link":
            MessageLookupByLibrary.simpleMessage("tại đây"),
        "profile_sms_setting_des_term": MessageLookupByLibrary.simpleMessage(
            "Bằng việc đăng ký sử dụng dịch vụ SMS Banking, Quý khách đã chấp nhận các điều kiện và điều khoản của chúng tôi."),
        "profile_sms_setting_des_term_link":
            MessageLookupByLibrary.simpleMessage("điều kiện và điều khoản"),
        "profile_sms_setting_empty": MessageLookupByLibrary.simpleMessage(
            "Hiện tại Quý khách chưa đăng ký dịch vụ SMS Banking.\r\nĐăng ký ngay để trải nghiệm dịch vụ."),
        "profile_sms_setting_error_account_empty":
            MessageLookupByLibrary.simpleMessage(
                "Quý khách cần mở tài khoản thanh toán để có thể sử dụng dịch vụ SMS Banking."),
        "profile_sms_setting_fees":
            MessageLookupByLibrary.simpleMessage("Biểu phí SMS Banking"),
        "profile_sms_setting_fees_VAT": MessageLookupByLibrary.simpleMessage(
            "Biểu phí đã bao gồm thuế VAT"),
        "profile_sms_setting_fees_VAT_2": MessageLookupByLibrary.simpleMessage(
            "Phí dịch vụ (Đã bao gồm VAT)"),
        "profile_sms_setting_fees_common":
            MessageLookupByLibrary.simpleMessage("Biểu phí"),
        "profile_sms_setting_register_more":
            MessageLookupByLibrary.simpleMessage("Đăng ký thêm SĐT"),
        "profile_sms_setting_select_account":
            MessageLookupByLibrary.simpleMessage("Chọn số tài khoản"),
        "profile_sms_setting_select_account_desc":
            MessageLookupByLibrary.simpleMessage(
                "Lựa chọn số tài khoản thanh toán để nhận thông báo biến động số dư qua SMS"),
        "profile_sms_setting_select_phone_cancel":
            MessageLookupByLibrary.simpleMessage("Chọn số điện thoại cần hủy"),
        "profile_sms_setting_success_cancel":
            MessageLookupByLibrary.simpleMessage("Hủy SMS Banking thành công"),
        "profile_sms_setting_success_change":
            MessageLookupByLibrary.simpleMessage(
                "Thay đổi số tài khoản SMS Banking thành công"),
        "profile_sms_setting_success_register":
            MessageLookupByLibrary.simpleMessage(
                "Đăng ký SMS Banking thành công với SĐT:"),
        "profile_sms_setting_title_sms_register":
            MessageLookupByLibrary.simpleMessage("Đăng ký dịch vụ SMS Banking"),
        "profile_sms_setting_title_sms_setting":
            MessageLookupByLibrary.simpleMessage("Dịch vụ SMS Banking"),
        "profile_sms_setting_validate_phone_duplicate":
            MessageLookupByLibrary.simpleMessage(
                "Hai số điện thoại đăng ký dịch vụ không được trùng nhau. Quý khách vui lòng kiểm tra lại."),
        "profile_sms_setting_validate_phone_null":
            MessageLookupByLibrary.simpleMessage(
                "Quý khách vui lòng bổ sung thông tin SĐT đăng ký dịch vụ."),
        "profile_sms_setting_validate_phone_wrong":
            MessageLookupByLibrary.simpleMessage(
                "SĐT chưa chính xác. Quý khách vui lòng kiểm tra lại"),
        "profile_success_trans_label_collaborator":
            MessageLookupByLibrary.simpleMessage("Cộng tác viên"),
        "profile_success_trans_label_date_transaction":
            MessageLookupByLibrary.simpleMessage("Ngày giao dịch"),
        "profile_success_trans_label_success_transaction":
            MessageLookupByLibrary.simpleMessage("Giao dịch thành công"),
        "profile_success_trans_label_transaction":
            MessageLookupByLibrary.simpleMessage("Giao dịch"),
        "profile_verify_info_label_birth_date":
            MessageLookupByLibrary.simpleMessage("Ngày sinh"),
        "profile_verify_info_label_paper":
            MessageLookupByLibrary.simpleMessage("CMND/CCCD/Hộ chiếu"),
        "profile_verify_info_label_paper_ID":
            MessageLookupByLibrary.simpleMessage("Số giấy tờ"),
        "profile_verify_info_label_personal_info":
            MessageLookupByLibrary.simpleMessage("Thông tin cá nhân"),
        "profile_verify_info_title_info":
            MessageLookupByLibrary.simpleMessage("Xác thực thông tin"),
        "profile_verify_method_button_change_etoken":
            MessageLookupByLibrary.simpleMessage("Đổi mã mở khóa"),
        "profile_verify_method_button_forgot_etoken":
            MessageLookupByLibrary.simpleMessage("Quên mã mở khóa"),
        "profile_verify_method_des_method_now":
            MessageLookupByLibrary.simpleMessage(
                "Phương thức xác thực hiện tại"),
        "profile_verify_method_label_avoid_fraud1":
            MessageLookupByLibrary.simpleMessage(
                "Để tránh bị kẻ gian lợi dụng, đề nghị quý khách không cung cấp tên đăng nhập, mật khẩu đăng nhập và mã OTP cho người khác. "),
        "profile_verify_method_label_avoid_fraud2":
            MessageLookupByLibrary.simpleMessage(
                "Đồng thời, quý khách chỉ thực hiện đăng nhập hoặc nhập mã OTP trên ứng dụng này hoặc "),
        "profile_verify_method_label_eToken":
            MessageLookupByLibrary.simpleMessage("eToken"),
        "profile_verify_method_label_guide":
            MessageLookupByLibrary.simpleMessage(
                "Hướng dẫn sử dụng eToken: xem "),
        "profile_verify_method_label_link_web":
            MessageLookupByLibrary.simpleMessage("https://kienlongbank.com"),
        "profile_verify_method_label_quick_verify":
            MessageLookupByLibrary.simpleMessage("Xác thực nhanh"),
        "profile_verify_method_title_set_etoken":
            MessageLookupByLibrary.simpleMessage("Thiết lập eToken"),
        "profile_withdraw_commission_label_receiving_account":
            MessageLookupByLibrary.simpleMessage("Tài khoản nhận tiền"),
        "profile_withdraw_commission_title_choose_account":
            MessageLookupByLibrary.simpleMessage("Chọn tài khoản nhận tiền"),
        "profit": MessageLookupByLibrary.simpleMessage("Lợi nhuận"),
        "profit_amount": MessageLookupByLibrary.simpleMessage("Số tiền lãi"),
        "project": MessageLookupByLibrary.simpleMessage("Dự án"),
        "promotion_detail":
            MessageLookupByLibrary.simpleMessage("Chi tiết ưu đãi"),
        "provice_branch": MessageLookupByLibrary.simpleMessage("Tỉnh thành"),
        "province": MessageLookupByLibrary.simpleMessage("Tỉnh thành"),
        "province_select":
            MessageLookupByLibrary.simpleMessage("Tỉnh/Thành phố"),
        "provisions_online_loan": MessageLookupByLibrary.simpleMessage(
            "và các quy định tại hợp đồng vay online."),
        "purchased_mobile_service_history":
            MessageLookupByLibrary.simpleMessage("Lịch sử đã mua"),
        "purpose": MessageLookupByLibrary.simpleMessage("Nhu cầu"),
        "push_notification":
            MessageLookupByLibrary.simpleMessage("Thông báo đẩy"),
        "push_notification_title": MessageLookupByLibrary.simpleMessage(
            "Thông báo đẩy (Push Notification)"),
        "qr_code_error_message": MessageLookupByLibrary.simpleMessage(
            "Thật ngại quá! Dường như KienlongBank không thể đọc nội dung QR Code."),
        "qr_code_login": MessageLookupByLibrary.simpleMessage(
            "Bạn đăng nhập vào App để tiếp tục tính năng này"),
        "qr_payment": MessageLookupByLibrary.simpleMessage("Thanh toán QR"),
        "qr_scan": MessageLookupByLibrary.simpleMessage("QR Scan"),
        "quantity": MessageLookupByLibrary.simpleMessage("Số lượng"),
        "quick_verification":
            MessageLookupByLibrary.simpleMessage("Xác minh nhanh"),
        "rating_service":
            MessageLookupByLibrary.simpleMessage("Đánh giá dịch vụ"),
        "rating_service_alert": MessageLookupByLibrary.simpleMessage(
            "Hãy cho chúng tôi biết cảm nhận của Quý khách về chuyên viên tư vấn"),
        "re_login": MessageLookupByLibrary.simpleMessage("Đăng nhập lại"),
        "re_password_msg":
            MessageLookupByLibrary.simpleMessage("Nhập lại mật khẩu"),
        "re_payment": m20,
        "read_and_agree":
            MessageLookupByLibrary.simpleMessage("Tôi đã đọc và đồng ý với"),
        "read_research_benefits": MessageLookupByLibrary.simpleMessage(
            "Trường hợp Khách hàng đồng ý với nội dung phê duyệt nêu trên, "),
        "real_profit_received":
            MessageLookupByLibrary.simpleMessage("Tiền lãi thực nhận"),
        "reason_cancel_empty": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng chọn lý do hủy giao dịch"),
        "reason_cancel_enter": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng nhập lý do hủy giao dịch"),
        "reason_cancellation":
            MessageLookupByLibrary.simpleMessage("Lý do hủy"),
        "recapture": MessageLookupByLibrary.simpleMessage("Chụp lại"),
        "receive": MessageLookupByLibrary.simpleMessage("Nhận được"),
        "receive_card": MessageLookupByLibrary.simpleMessage(
            "Quý khách có thể nhận Thẻ sau 5-7 ngày làm việc."),
        "receive_money": MessageLookupByLibrary.simpleMessage("Nhận tiền đến"),
        "received": MessageLookupByLibrary.simpleMessage("Còn hạn"),
        "receiver": MessageLookupByLibrary.simpleMessage("Người nhận"),
        "receiver_returns":
            MessageLookupByLibrary.simpleMessage("Phí người nhận trả"),
        "recently": MessageLookupByLibrary.simpleMessage("Gần đây"),
        "recharge_information":
            MessageLookupByLibrary.simpleMessage("Thông tin nạp tiền"),
        "recharge_phone":
            MessageLookupByLibrary.simpleMessage("Nạp điện thoại"),
        "recharge_postpaid_card":
            MessageLookupByLibrary.simpleMessage("Nạp thẻ trả sau"),
        "recipient_name":
            MessageLookupByLibrary.simpleMessage("Tên người nhận"),
        "recommend_settlement":
            MessageLookupByLibrary.simpleMessage("Đề nghị tất toán"),
        "recommended_card_title":
            MessageLookupByLibrary.simpleMessage("Các sản phẩm thẻ nổi bật"),
        "record": MessageLookupByLibrary.simpleMessage("Ghi lại"),
        "recurring_payment_order":
            MessageLookupByLibrary.simpleMessage("Lệnh thanh toán định kỳ"),
        "refer_friends":
            MessageLookupByLibrary.simpleMessage("Giới thiệu bạn bè"),
        "referer": MessageLookupByLibrary.simpleMessage("Người giới thiệu"),
        "referer_code":
            MessageLookupByLibrary.simpleMessage("Mã người giới thiệu"),
        "referer_if_any":
            MessageLookupByLibrary.simpleMessage("Người giới thiệu (nếu có)"),
        "referral_code":
            MessageLookupByLibrary.simpleMessage("Mã người giới thiệu"),
        "referral_code_guide":
            MessageLookupByLibrary.simpleMessage("Hướng dẫn mã giới thiệu"),
        "referral_code_message": MessageLookupByLibrary.simpleMessage(
            "Nhập mã người giới thiệu sẽ giúp bạn đảm bảo quyền lợi"),
        "referral_code_vr_card":
            MessageLookupByLibrary.simpleMessage("Mã giới thiệu"),
        "referral_commission":
            MessageLookupByLibrary.simpleMessage("Thù lao tích lũy"),
        "referral_info":
            MessageLookupByLibrary.simpleMessage("Thông tin người giới thiệu"),
        "referral_reward":
            MessageLookupByLibrary.simpleMessage("Tiền thưởng tích luỹ"),
        "referral_tooltip_message": MessageLookupByLibrary.simpleMessage(
            "Nhập mã người giới thiệu tại đây để đảm bảo quyền lợi"),
        "register": MessageLookupByLibrary.simpleMessage("Đăng ký"),
        "register_credit_success":
            MessageLookupByLibrary.simpleMessage("Đăng ký mở thẻ thành công"),
        "register_ctv":
            MessageLookupByLibrary.simpleMessage("Đăng ký cộng tác viên"),
        "register_ctv_success": MessageLookupByLibrary.simpleMessage(
            "Đăng ký cộng tác viên thành công"),
        "register_for_card_issuance":
            MessageLookupByLibrary.simpleMessage("Đăng ký phát hành thẻ"),
        "register_info":
            MessageLookupByLibrary.simpleMessage("Thông tin đăng ký"),
        "register_info_describe": MessageLookupByLibrary.simpleMessage(
            "Mỗi tài khoản thanh toán chỉ có 1 nickname, bạn vui lòng kiểm tra kỹ trước khi xác nhận"),
        "register_now": MessageLookupByLibrary.simpleMessage("Đăng ký ngay"),
        "register_success":
            MessageLookupByLibrary.simpleMessage("Đăng ký thành công"),
        "register_success_nickname_des": MessageLookupByLibrary.simpleMessage(
            "Nickname của quý khách cần được xác minh. Trong thời gian chờ xác minh, quý khách vẫn có thể nhận tiền đến bằng nickname này."),
        "relate_add_dialog_des_delete_relate": MessageLookupByLibrary.simpleMessage(
            "Quý khách sẽ phải nhập lại thông tin người liên đới nếu muốn thêm sau này."),
        "relate_add_dialog_title_delete_relate":
            MessageLookupByLibrary.simpleMessage(
                "Quý khách muốn xoá người liên đới?"),
        "relate_add_title_add_relate":
            MessageLookupByLibrary.simpleMessage("Thêm người liên đới"),
        "relate_home_empty_des_no_relate_yet": MessageLookupByLibrary.simpleMessage(
            "Vui lòng ấn nút \"thêm mới\" để gắn người liên đới cho khách hàng."),
        "relate_home_empty_title_no_relate_yet":
            MessageLookupByLibrary.simpleMessage("Chưa có người liên đới"),
        "relate_home_title_list":
            MessageLookupByLibrary.simpleMessage("Danh sách người liên đới"),
        "related_news":
            MessageLookupByLibrary.simpleMessage("Tin tức liên quan"),
        "relationship": MessageLookupByLibrary.simpleMessage("Mối quan hệ"),
        "relationship_with_transferors":
            MessageLookupByLibrary.simpleMessage("Quan hệ với người chuyển"),
        "release_information":
            MessageLookupByLibrary.simpleMessage("Thông tin phát hành"),
        "religion": MessageLookupByLibrary.simpleMessage("Tôn giáo"),
        "remain_referral_commission":
            MessageLookupByLibrary.simpleMessage("Thù lao chưa nhận"),
        "remain_referral_reward":
            MessageLookupByLibrary.simpleMessage("Tiền thưởng chưa nhận"),
        "remind_biometrics": MessageLookupByLibrary.simpleMessage(
            "Sử dụng đăng nhập bằng sinh trắc học giúp quý khách tiếp kiệm thời gian khi đăng nhập lần sau"),
        "remind_later": MessageLookupByLibrary.simpleMessage("Nhắc tôi sau"),
        "reminder_name": MessageLookupByLibrary.simpleMessage(
            "Tên gợi nhớ (không bắt buộc)"),
        "reminiscent_name": MessageLookupByLibrary.simpleMessage("Tên gợi nhớ"),
        "remove_card_phone": MessageLookupByLibrary.simpleMessage("Xoá thẻ"),
        "rent": MessageLookupByLibrary.simpleMessage(" Thuê nhà"),
        "repeat": MessageLookupByLibrary.simpleMessage("Lặp lại"),
        "request_cancel": MessageLookupByLibrary.simpleMessage(
            "Nếu có đối với yêu cầu bị hủy bởi NH"),
        "request_money_transfer":
            MessageLookupByLibrary.simpleMessage("Yêu cầu chuyển tiền"),
        "request_permission_biometrics": MessageLookupByLibrary.simpleMessage(
            "Cho phép sử dụng đăng nhập bằng vân tay/khuôn mặt trên KienlongBank"),
        "required_choose_passbook": MessageLookupByLibrary.simpleMessage(
            "Vui lòng chọn một sổ tiết kiệm"),
        "required_enter_new_email": MessageLookupByLibrary.simpleMessage(
            "Vui lòng nhập email mới của quý khách"),
        "required_enter_new_password": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng nhập mật khẩu mới và ghi nhớ cho những lần đăng nhập sau"),
        "required_enter_supplier_customer_code": m21,
        "required_record_video": MessageLookupByLibrary.simpleMessage(
            "Cuộc gọi của quý khách có thể được ghi lại để cải thiện chất lượng dịch vụ"),
        "resend_code_success":
            MessageLookupByLibrary.simpleMessage("Gửi lại mã thành công"),
        "resend_otp": MessageLookupByLibrary.simpleMessage(" Gửi lại OTP"),
        "reset_etoken":
            MessageLookupByLibrary.simpleMessage("Đặt lại mã eToken"),
        "resort": MessageLookupByLibrary.simpleMessage("Nghỉ dưỡng"),
        "retry": MessageLookupByLibrary.simpleMessage("Thử lại"),
        "review_card": MessageLookupByLibrary.simpleMessage("Xem lại"),
        "review_service":
            MessageLookupByLibrary.simpleMessage("Đánh giá dịch vụ"),
        "review_service_desc": MessageLookupByLibrary.simpleMessage(
            "Hãy đánh giá sản phẩm và dịch vụ của chúng tôi để nâng cao chất lượng dịch vụ"),
        "review_transaction":
            MessageLookupByLibrary.simpleMessage("Tra soát giao dịch"),
        "reviews": MessageLookupByLibrary.simpleMessage("đánh giá"),
        "rose": MessageLookupByLibrary.simpleMessage("Hoa hồng"),
        "rose_receive":
            MessageLookupByLibrary.simpleMessage("Hoa hồng nhận được"),
        "rounding": MessageLookupByLibrary.simpleMessage(
            "Hệ thống sẽ làm tròn phần lẻ từ 1 giao dịch chi tiêu thành số chẵn và gửi vào mục tiêu này"),
        "rounding_mechanism":
            MessageLookupByLibrary.simpleMessage("Cơ chế làm tròn"),
        "rounding_mechanism_content": m22,
        "ruler_of_my": MessageLookupByLibrary.simpleMessage(" của chúng tôi"),
        "s_coffee_plus": MessageLookupByLibrary.simpleMessage("S.Coffee Plus"),
        "salary_payment":
            MessageLookupByLibrary.simpleMessage("Thanh toán lương"),
        "sales_doc": MessageLookupByLibrary.simpleMessage("Tài liệu bán hàng"),
        "save": MessageLookupByLibrary.simpleMessage("Lưu"),
        "save_change": MessageLookupByLibrary.simpleMessage("Lưu thay đổi"),
        "save_code": MessageLookupByLibrary.simpleMessage("Lưu mã"),
        "save_customer_code":
            MessageLookupByLibrary.simpleMessage("Lưu mã khách hàng"),
        "save_my_qr_code_success": MessageLookupByLibrary.simpleMessage(
            "Lưu mã QR cá nhân thành công"),
        "save_photo": MessageLookupByLibrary.simpleMessage("Lưu ảnh"),
        "save_qr": MessageLookupByLibrary.simpleMessage("Lưu QR"),
        "save_screenshot_success": MessageLookupByLibrary.simpleMessage(
            "Lưu thành công! Ảnh đã được lưu trong bộ sưu tập của bạn"),
        "save_tranfer_template":
            MessageLookupByLibrary.simpleMessage("Lưu mẫu chuyển tiền"),
        "save_transaction_photo_suc": MessageLookupByLibrary.simpleMessage(
            "Lưu ảnh giao dịch thành công"),
        "save_transfer_success": MessageLookupByLibrary.simpleMessage(
            "Chụp ảnh giao dịch thành công."),
        "save_vietqr": MessageLookupByLibrary.simpleMessage("Lưu ảnh VietQR"),
        "saved": MessageLookupByLibrary.simpleMessage("Đã lưu"),
        "saving": MessageLookupByLibrary.simpleMessage("Tiết kiệm"),
        "saving_accumulation_target":
            MessageLookupByLibrary.simpleMessage("Tiết kiệm tích lũy mục tiêu"),
        "saving_add_money_label_amount":
            MessageLookupByLibrary.simpleMessage("Số tiền"),
        "saving_add_money_title_add":
            MessageLookupByLibrary.simpleMessage("Nạp tiền"),
        "saving_amount_min":
            MessageLookupByLibrary.simpleMessage("Tối thiểu 500.000 VND"),
        "saving_and_accumulate":
            MessageLookupByLibrary.simpleMessage("Tiết kiệm & Tích lũy"),
        "saving_book_loan_guarantee": MessageLookupByLibrary.simpleMessage(
            "Sổ tiết kiệm bảo đảm cho khoản vay"),
        "saving_button_settle":
            MessageLookupByLibrary.simpleMessage("Tất toán"),
        "saving_code": MessageLookupByLibrary.simpleMessage("Mã thu hộ"),
        "saving_confirm_info_des_term1": MessageLookupByLibrary.simpleMessage(
            "Bằng việc thực hiện mở tiền gửi, quý khách đã xác nhận đồng ý "),
        "saving_confirm_info_title_confirm":
            MessageLookupByLibrary.simpleMessage("Xác nhận thông tin"),
        "saving_detail":
            MessageLookupByLibrary.simpleMessage("Thông tin khoản tiết kiệm"),
        "saving_detail_label_original_account":
            MessageLookupByLibrary.simpleMessage("Tài khoản nộp gốc"),
        "saving_detail_title_save_detail": MessageLookupByLibrary.simpleMessage(
            "Chi tiết tài khoản tiết kiệm"),
        "saving_fix_detail_hint_text_enter_target":
            MessageLookupByLibrary.simpleMessage("Nhập tên mục tiêu"),
        "saving_fix_detail_label_add_from_transact":
            MessageLookupByLibrary.simpleMessage("Thêm từ các giao dịch"),
        "saving_fix_detail_label_allow_auto":
            MessageLookupByLibrary.simpleMessage(
                "Cho phép tự động tích lũy tiền vào sổ tiết kiệm"),
        "saving_fix_detail_label_auto_accumulate":
            MessageLookupByLibrary.simpleMessage("Tự động tích lũy"),
        "saving_fix_detail_label_choose_rounding":
            MessageLookupByLibrary.simpleMessage(
                "Để đạt mục tiêu nhanh hơn, quý khách có thể chọn hệ số làm tròn dưới đây:"),
        "saving_fix_detail_title_fix":
            MessageLookupByLibrary.simpleMessage("Sửa Tích Tiểu Thành Đại"),
        "saving_history_empty_des_no_saving": MessageLookupByLibrary.simpleMessage(
            "Khởi động ngay các kế hoạch tiết kiệm cho bản thân và gia đình để đảm bảo tài chính vững vàng trong tương lai."),
        "saving_history_empty_title_no_saving":
            MessageLookupByLibrary.simpleMessage(
                "Quý khách chưa có khoản tiết kiệm"),
        "saving_history_title_active":
            MessageLookupByLibrary.simpleMessage("Đang hoạt động"),
        "saving_history_title_settled":
            MessageLookupByLibrary.simpleMessage("Đã tất toán"),
        "saving_home_des_save_accumulate": MessageLookupByLibrary.simpleMessage(
            "Đặt mục tiêu cụ thể và tích lũy định kỳ giúp tạo khoản tiền lớn hiện thực hóa ước mơ trong tương lai."),
        "saving_home_des_save_desk": MessageLookupByLibrary.simpleMessage(
            "Mở sổ tiết kiệm tại quầy với gói tiết kiệm đa dạng, kỳ hạn linh hoạt và tính bảo mật cao"),
        "saving_home_des_save_online": MessageLookupByLibrary.simpleMessage(
            "Mở sổ tiết kiệm online có kỳ hạn, kỳ hạn linh hoạt, lãi suất tối ưu và có thể tất toán bất cứ lúc nào."),
        "saving_home_title_save_accumulate":
            MessageLookupByLibrary.simpleMessage("Tích lũy mục tiêu"),
        "saving_home_title_save_desk":
            MessageLookupByLibrary.simpleMessage("Sổ tiết kiệm tại quầy"),
        "saving_home_title_save_online":
            MessageLookupByLibrary.simpleMessage("Tiết kiệm online"),
        "saving_home_title_saving":
            MessageLookupByLibrary.simpleMessage("Tiết kiệm"),
        "saving_label_active_date":
            MessageLookupByLibrary.simpleMessage("Ngày hiệu lực"),
        "saving_label_due_date": MessageLookupByLibrary.simpleMessage("Kỳ hạn"),
        "saving_label_interest":
            MessageLookupByLibrary.simpleMessage("Lãi suất"),
        "saving_label_interest_accumulate":
            MessageLookupByLibrary.simpleMessage("Tiền lãi tích luỹ"),
        "saving_label_lockdown_state":
            MessageLookupByLibrary.simpleMessage("Tình trạng phong tỏa"),
        "saving_list_save_label_open_save_book":
            MessageLookupByLibrary.simpleMessage("Mở sổ tiết kiệm"),
        "saving_online":
            MessageLookupByLibrary.simpleMessage("Tiết kiệm Online"),
        "saving_open_online_bottomsheet_title_choose_due":
            MessageLookupByLibrary.simpleMessage("Chọn kỳ hạn gửi"),
        "saving_open_online_label_due_date":
            MessageLookupByLibrary.simpleMessage("Kỳ hạn gửi"),
        "saving_open_online_label_interest_expect":
            MessageLookupByLibrary.simpleMessage("Tiền lãi cuối kỳ dự kiến"),
        "saving_open_online_label_save_info":
            MessageLookupByLibrary.simpleMessage("Thông tin tiết kiệm"),
        "saving_open_online_title_pay_form":
            MessageLookupByLibrary.simpleMessage("Hình thức chi trả"),
        "saving_open_success_label_from_account":
            MessageLookupByLibrary.simpleMessage("Từ tài khoản"),
        "saving_open_success_label_id_transact":
            MessageLookupByLibrary.simpleMessage("Mã giao dịch:"),
        "saving_open_success_label_interest_last_term":
            MessageLookupByLibrary.simpleMessage("Tiền lãi cuối kỳ"),
        "saving_open_success_title_success":
            MessageLookupByLibrary.simpleMessage(
                "Mở tài khoản tiết kiệm online thành công"),
        "saving_open_target_button_create_target":
            MessageLookupByLibrary.simpleMessage("Tạo mục tiêu"),
        "saving_open_target_des_choose_original":
            MessageLookupByLibrary.simpleMessage(
                "Quý khách vui lòng lựa chọn tài khoản nguồn và nhập số tiền gửi để tạo tài khoản tích lũy"),
        "saving_open_target_des_name_benefit": MessageLookupByLibrary.simpleMessage(
            "Đặt tên cho mục tiêu giúp cụ thể hóa kế hoạch tiết kiệm của bản thân"),
        "saving_open_target_des_round_target": MessageLookupByLibrary.simpleMessage(
            "Hệ thống sẽ làm tròn phần lẻ từ 1 giao dịch chi tiêu thành số chẵn và gửi vào mục tiêu này."),
        "saving_open_target_des_withdraw_anytime":
            MessageLookupByLibrary.simpleMessage(
                "Quý khách có thể rút lại tiền từ sổ tiết kiệm mục tiêu vào tài khoản của mình bất cứ lúc nào"),
        "saving_open_target_hint_text_enter_amount":
            MessageLookupByLibrary.simpleMessage("Nhập số tiền tích lũy"),
        "saving_open_target_label_accumulate_amount":
            MessageLookupByLibrary.simpleMessage("Số tiền tích lũy"),
        "saving_open_target_label_accumulate_form":
            MessageLookupByLibrary.simpleMessage("Hình thức tích lũy"),
        "saving_open_target_label_add_amount":
            MessageLookupByLibrary.simpleMessage("Số tiền nạp"),
        "saving_open_target_label_id_transact":
            MessageLookupByLibrary.simpleMessage("Mã giao dịch"),
        "saving_open_target_title_create_success":
            MessageLookupByLibrary.simpleMessage(
                "Tạo mục tiêu tích lũy thành công"),
        "saving_open_target_title_name":
            MessageLookupByLibrary.simpleMessage("Đặt tên mục tiêu"),
        "saving_settle_success_label_date":
            MessageLookupByLibrary.simpleMessage("Ngày tất toán"),
        "saving_settle_success_label_saving_account":
            MessageLookupByLibrary.simpleMessage("Số tài khoản tiết kiệm"),
        "saving_settle_success_title_settle":
            MessageLookupByLibrary.simpleMessage("Tất toán thành công"),
        "saving_target_label_amount":
            MessageLookupByLibrary.simpleMessage("Số tiền mục tiêu"),
        "saving_target_label_automatic":
            MessageLookupByLibrary.simpleMessage("Tự động tích lũy"),
        "saving_target_label_date":
            MessageLookupByLibrary.simpleMessage("Ngày tạo mục tiêu"),
        "saving_target_label_name":
            MessageLookupByLibrary.simpleMessage("Tên mục tiêu"),
        "saving_target_label_pass": MessageLookupByLibrary.simpleMessage("Đạt"),
        "saving_target_title_account":
            MessageLookupByLibrary.simpleMessage("Tài khoản mục tiêu"),
        "saving_target_title_detail":
            MessageLookupByLibrary.simpleMessage("Chi tiết tích lũy mục tiêu"),
        "saving_vnp_open_success_title_success":
            MessageLookupByLibrary.simpleMessage(
                "Đăng ký mở sổ tiết kiệm thành công"),
        "saving_withdraw_confirm_button_settle":
            MessageLookupByLibrary.simpleMessage("Tất toán"),
        "saving_withdraw_confirm_label_interest_before_due":
            MessageLookupByLibrary.simpleMessage("Tiền lãi trước hạn"),
        "saving_withdraw_confirm_label_receive_total":
            MessageLookupByLibrary.simpleMessage("Tổng tiền nhận"),
        "saving_withdraw_confirm_label_send_money":
            MessageLookupByLibrary.simpleMessage("Số tiền gửi"),
        "saving_withdraw_confirm_title_confirm":
            MessageLookupByLibrary.simpleMessage("Xác nhận thông tin"),
        "saving_withdraw_hint_text_enter_amount":
            MessageLookupByLibrary.simpleMessage("Nhập số tiền"),
        "savings_book_opening_date":
            MessageLookupByLibrary.simpleMessage("Ngày mở sổ"),
        "savings_collection":
            MessageLookupByLibrary.simpleMessage("Tiết kiệm thu hộ"),
        "savings_collection_code_content": MessageLookupByLibrary.simpleMessage(
            "Mở sổ tiết kiệm online có kỳ hạn, kỳ hạn linh hoạt, lãi suất tối ưu và có thể tất toán bất cứ lúc nào"),
        "savings_collection_code_title":
            MessageLookupByLibrary.simpleMessage("Mã thu hộ tiết kiệm"),
        "savings_tooltip_referral": MessageLookupByLibrary.simpleMessage(
            "Nhập mã người giới thiệu tại đây để đảm bảo quyền lợi khi gửi tiết kiệm."),
        "scan_lucky_money_qr":
            MessageLookupByLibrary.simpleMessage("Quét QR để nhận lì xì từ "),
        "scan_my_qr_code": MessageLookupByLibrary.simpleMessage(
            "Quét mã QR cá nhân để kết bạn"),
        "scan_qr": MessageLookupByLibrary.simpleMessage("Quét mã QR"),
        "schedule": MessageLookupByLibrary.simpleMessage("Đặt lịch"),
        "schedule_transfer":
            MessageLookupByLibrary.simpleMessage("Lên lịch chuyển khoản"),
        "schedule_vnpost": MessageLookupByLibrary.simpleMessage("Lập lịch"),
        "search": MessageLookupByLibrary.simpleMessage("Tìm kiếm"),
        "search2": MessageLookupByLibrary.simpleMessage("Tìm"),
        "search_bank":
            MessageLookupByLibrary.simpleMessage("Tìm kiếm ngân hàng"),
        "search_consultant": MessageLookupByLibrary.simpleMessage(
            "Tìm theo tên hoặc số điện thoại"),
        "search_customer":
            MessageLookupByLibrary.simpleMessage("Tìm khách hàng"),
        "search_history":
            MessageLookupByLibrary.simpleMessage("Lịch sử tra cứu"),
        "search_transaction":
            MessageLookupByLibrary.simpleMessage("Tìm giao dịch"),
        "securities": MessageLookupByLibrary.simpleMessage("Chứng khoán"),
        "security": MessageLookupByLibrary.simpleMessage("Bảo mật"),
        "see_all": MessageLookupByLibrary.simpleMessage("Xem tất cả"),
        "select_linked_account":
            MessageLookupByLibrary.simpleMessage("Chọn tài khoản liên kết"),
        "select_number": MessageLookupByLibrary.simpleMessage("Chọn số"),
        "select_reason_inquiry":
            MessageLookupByLibrary.simpleMessage("Chọn lý do tra soát"),
        "select_sort":
            MessageLookupByLibrary.simpleMessage("Chọn cách sắp xếp"),
        "select_telecom": MessageLookupByLibrary.simpleMessage("Chọn nhà mạng"),
        "selected_day": MessageLookupByLibrary.simpleMessage("Đặt ngày"),
        "selected_vip_account": MessageLookupByLibrary.simpleMessage("Chọn"),
        "send": MessageLookupByLibrary.simpleMessage("Gửi"),
        "send_gift": MessageLookupByLibrary.simpleMessage("Gửi quà tặng"),
        "send_lucky_money": MessageLookupByLibrary.simpleMessage("Gửi Lì Xì"),
        "send_message": MessageLookupByLibrary.simpleMessage("Gửi tin nhắn"),
        "send_messaged": m23,
        "send_money": MessageLookupByLibrary.simpleMessage("Chi tiền"),
        "send_photo_qr_code":
            MessageLookupByLibrary.simpleMessage("Gửi ảnh mã QR"),
        "send_request": MessageLookupByLibrary.simpleMessage("Gửi yêu cầu"),
        "send_transfer": MessageLookupByLibrary.simpleMessage("Gửi yêu cầu"),
        "send_transfer_vnpost_success": MessageLookupByLibrary.simpleMessage(
            "Gửi yêu cầu chuyển tiền thành công"),
        "separation_fee": MessageLookupByLibrary.simpleMessage("Phí tách lệnh"),
        "service": MessageLookupByLibrary.simpleMessage("Dịch vụ"),
        "service_counseling_empty": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng chọn dịch vụ tư vấn"),
        "service_with_payoo":
            MessageLookupByLibrary.simpleMessage("Dịch vụ liên kết với Payoo"),
        "session_login_expired_msg": MessageLookupByLibrary.simpleMessage(
            "Phiên đăng nhập của bạn đã hết hạn. Vui lòng đăng nhập lại để sử dụng"),
        "set_counseling_success": MessageLookupByLibrary.simpleMessage(
            "Quý khách đã đặt lịch tư vấn thành công"),
        "set_schedule_counseling":
            MessageLookupByLibrary.simpleMessage("Đặt lịch tư vấn"),
        "set_transfer_schedule":
            MessageLookupByLibrary.simpleMessage("Đặt lịch chuyển"),
        "set_work_schedule":
            MessageLookupByLibrary.simpleMessage("Đặt lịch làm việc"),
        "set_work_schedule_alert": m24,
        "setting": MessageLookupByLibrary.simpleMessage("Cài đặt"),
        "setting_card": MessageLookupByLibrary.simpleMessage("Cài đặt thẻ"),
        "setting_current_pin": MessageLookupByLibrary.simpleMessage(
            "Thiết lập mã PIN mới của quý khách"),
        "setting_pin_code_online":
            MessageLookupByLibrary.simpleMessage("Thiết lập PIN online"),
        "setting_system": MessageLookupByLibrary.simpleMessage(
            "Quý khách sẽ nhận được thông báo liên quan đến hệ thống, vận hành."),
        "setting_system1":
            MessageLookupByLibrary.simpleMessage("Chương trình khuyến mại"),
        "setting_system2": MessageLookupByLibrary.simpleMessage(
            "Quý khách sẽ nhận được thông báo liên quan đến các chương trình khuyến mại, ưu đãi dành riêng cho quý khách."),
        "settled": MessageLookupByLibrary.simpleMessage("Đã tất toán"),
        "settlement": MessageLookupByLibrary.simpleMessage("Tất toán"),
        "settlement_note": MessageLookupByLibrary.simpleMessage(
            "Nếu ngày trả nợ trùng vào ngày nghỉ hàng tuần, nghỉ Lễ, Tết theo quy định của KienlongBank, Pháp luật thì thời hạn cho vay được kéo dài sang ngày làm việc tiếp theo liền kề sau ngày nghỉ hàng tuần, nghỉ Lễ, Tết đó."),
        "settlement_on_payment_account": MessageLookupByLibrary.simpleMessage(
            "Tất toán về tài khoản thanh toán"),
        "settlement_saving": MessageLookupByLibrary.simpleMessage("Tất toán"),
        "settlement_saving_confirm": m25,
        "settlement_success":
            MessageLookupByLibrary.simpleMessage("Tất toán thành công"),
        "settlement_target_accumulation_confirm": m26,
        "settlement_via_vnpost":
            MessageLookupByLibrary.simpleMessage("Tất toán qua VNPost"),
        "setup_auto_payment_success": MessageLookupByLibrary.simpleMessage(
            "Thiết lập lệnh thanh toán tự động thành công!"),
        "setup_nickname":
            MessageLookupByLibrary.simpleMessage("Đặt nickname cho tài khoản"),
        "setup_password_success": MessageLookupByLibrary.simpleMessage(
            "Thiết lập mật khẩu mới thành công"),
        "setup_password_success_msg": MessageLookupByLibrary.simpleMessage(
            "Thiết lập mật khẩu đăng nhập thành công"),
        "setup_pay_auto_every_month": MessageLookupByLibrary.simpleMessage(
            "Thiết lập các giao dịch định kỳ thanh toán tự động hàng tháng"),
        "sex": MessageLookupByLibrary.simpleMessage("Sex"),
        "share": MessageLookupByLibrary.simpleMessage("Chia sẻ"),
        "share1": MessageLookupByLibrary.simpleMessage("Hình thức"),
        "share_code_account_empty": MessageLookupByLibrary.simpleMessage(
            "Quý khách chưa mời được khách hàng nào"),
        "share_code_text": MessageLookupByLibrary.simpleMessage(
            "Quý khách nhận được lời mời sử dụng ứng dụng KienlongBank. Để đăng ký và nhận nhiều ưu đãi hấp dẫn từ ngân hàng, hãy tải app KienlongBank và nhập mã giới thiệu: 87HGXS nhé!"),
        "share_money":
            MessageLookupByLibrary.simpleMessage("Hình thức chia tiền"),
        "share_transaction_photos":
            MessageLookupByLibrary.simpleMessage("Chia sẻ ảnh giao dịch"),
        "show_nick_name":
            MessageLookupByLibrary.simpleMessage("Hiển thị nickname"),
        "show_partner_page":
            MessageLookupByLibrary.simpleMessage("Hiển thị Trang Đối tác"),
        "show_partner_page_msg": MessageLookupByLibrary.simpleMessage(
            "Trang dịch vụ cung cấp bởi đối tác liên kết"),
        "show_tooltip_edit": MessageLookupByLibrary.simpleMessage(
            "Quý khách có thể chỉnh sửa các thông tin cá nhân tại đây."),
        "shuffle": MessageLookupByLibrary.simpleMessage("Ngẫu nhiên"),
        "sign_in": MessageLookupByLibrary.simpleMessage("Đăng nhập"),
        "sign_in_faceid":
            MessageLookupByLibrary.simpleMessage("Đăng nhập Sinh trắc học"),
        "sign_up_in_security_des_set_security":
            MessageLookupByLibrary.simpleMessage(
                "Vui lòng nhập email quý khách và cài đặt mật khẩu cho ứng dụng để hoàn tất việc đăng ký"),
        "skip": MessageLookupByLibrary.simpleMessage("Bỏ qua"),
        "skip_bill_payment":
            MessageLookupByLibrary.simpleMessage("Bỏ qua tất cả"),
        "small_accumulation_becomes_great":
            MessageLookupByLibrary.simpleMessage("Tích Tiểu Thành Đại"),
        "smart_invest": MessageLookupByLibrary.simpleMessage("Smart Invest"),
        "sms": MessageLookupByLibrary.simpleMessage("SMS"),
        "sms_banking": MessageLookupByLibrary.simpleMessage("SMS Banking"),
        "sms_otp": MessageLookupByLibrary.simpleMessage("SMS OTP"),
        "sort": MessageLookupByLibrary.simpleMessage("Sắp xếp"),
        "spent": MessageLookupByLibrary.simpleMessage("Đã chi"),
        "start_after": MessageLookupByLibrary.simpleMessage("Bắt đầu sau"),
        "start_alphabetic_character":
            MessageLookupByLibrary.simpleMessage("Bắt đầu bằng ký tự chữ cái"),
        "start_date": MessageLookupByLibrary.simpleMessage("Ngày hiệu lực"),
        "start_month": MessageLookupByLibrary.simpleMessage("Tháng bắt đầu"),
        "startdate": MessageLookupByLibrary.simpleMessage("Ngày bắt đầu"),
        "startday": MessageLookupByLibrary.simpleMessage("Ngày thực hiện"),
        "statement": MessageLookupByLibrary.simpleMessage("Sao kê"),
        "statement_date": MessageLookupByLibrary.simpleMessage("Ngày sao kê"),
        "statement_period": MessageLookupByLibrary.simpleMessage("Kỳ sao kê"),
        "statistical": MessageLookupByLibrary.simpleMessage("Thống kê"),
        "statistical_code_lookup":
            MessageLookupByLibrary.simpleMessage("Thống kê tra cứu mã thu"),
        "status": MessageLookupByLibrary.simpleMessage("Trạng thái"),
        "status_nickname":
            MessageLookupByLibrary.simpleMessage("Trạng thái nickname"),
        "still": MessageLookupByLibrary.simpleMessage("Còn "),
        "stm_machine_info":
            MessageLookupByLibrary.simpleMessage("Thông tin máy STM"),
        "stock_account":
            MessageLookupByLibrary.simpleMessage("Số tài khoản & Tiểu khoản"),
        "stock_comany_title":
            MessageLookupByLibrary.simpleMessage("Công ty chứng khoán"),
        "stop_create_credit_card": MessageLookupByLibrary.simpleMessage(
            "Quý khách có chắc chắn muốn dừng mở thẻ?"),
        "stop_loop": MessageLookupByLibrary.simpleMessage("Kết thúc lặp"),
        "storage": MessageLookupByLibrary.simpleMessage("Bộ nhớ"),
        "sub_register_credit_success": MessageLookupByLibrary.simpleMessage(
            "Tư vấn viên của chúng tôi sẽ liên hệ ngay với quý khách để tiến hành các thủ tục cần thiết."),
        "subjects_of_use":
            MessageLookupByLibrary.simpleMessage("Đối tượng sử dụng"),
        "subtitle_accumulation_target": MessageLookupByLibrary.simpleMessage(
            "Một kỳ nghỉ? Sửa chữa nhà? Mua một chiếc xe ô tô? Hãy bắt đầu bằng việc tạo các mục tiêu tiết kiệm."),
        "success": MessageLookupByLibrary.simpleMessage("Thành công"),
        "success_message_live_ness": MessageLookupByLibrary.simpleMessage(
            "Thu thập dữ liệu khuôn mặt thành công"),
        "success_transaction":
            MessageLookupByLibrary.simpleMessage("Giao dịch thành công"),
        "successful_authentication":
            MessageLookupByLibrary.simpleMessage("Xác thực thành công"),
        "successful_loan_application": MessageLookupByLibrary.simpleMessage(
            "Đăng ký khoản vay thành công"),
        "successful_loan_settlement": MessageLookupByLibrary.simpleMessage(
            "Tất toán khoản vay thành công"),
        "successful_loan_settlement_request":
            MessageLookupByLibrary.simpleMessage(
                "Yêu cầu tất toán khoản vay thành công"),
        "successful_transaction":
            MessageLookupByLibrary.simpleMessage("Giao dịch Thành công"),
        "successfully_change_service": MessageLookupByLibrary.simpleMessage(
            "Thay đổi gói dịch vụ ngân hàng số thành công"),
        "summary_send_otp": m27,
        "sunshine_deco": MessageLookupByLibrary.simpleMessage("SDecoro"),
        "sunshine_marina":
            MessageLookupByLibrary.simpleMessage("Sunshine Marina"),
        "sunshine_mart": MessageLookupByLibrary.simpleMessage("Sunshine Mart"),
        "sunshine_renting":
            MessageLookupByLibrary.simpleMessage("Sunshine Renting"),
        "sunshine_retail":
            MessageLookupByLibrary.simpleMessage("Sunshine Retail"),
        "sunshine_school":
            MessageLookupByLibrary.simpleMessage("Sunshine School"),
        "sunshine_tv": MessageLookupByLibrary.simpleMessage("Sunshine TV"),
        "supermarket": MessageLookupByLibrary.simpleMessage("Siêu thị"),
        "supplier": MessageLookupByLibrary.simpleMessage("Nhà cung cấp"),
        "support": MessageLookupByLibrary.simpleMessage("Hỗ trợ"),
        "support_app": MessageLookupByLibrary.simpleMessage(
            "Hướng dẫn chuyển đổi ứng dụng"),
        "support_service":
            MessageLookupByLibrary.simpleMessage("Dịch vụ hỗ trợ"),
        "support_switchboard":
            MessageLookupByLibrary.simpleMessage("Tổng đài hỗ trợ"),
        "support_within_three_days": MessageLookupByLibrary.simpleMessage(
            "Nhân viên hỗ trợ sẽ liên hệ quý khách trong vòng 3 ngày làm việc để lên lịch gặp sớm nhất"),
        "surplus": MessageLookupByLibrary.simpleMessage("Số dư"),
        "sync": MessageLookupByLibrary.simpleMessage("Đồng bộ"),
        "take_photo_transaction":
            MessageLookupByLibrary.simpleMessage("Chụp ảnh giao dịch"),
        "target": MessageLookupByLibrary.simpleMessage("Mục tiêu"),
        "target_name": MessageLookupByLibrary.simpleMessage("Đặt tên mục tiêu"),
        "telecom": MessageLookupByLibrary.simpleMessage("Nhà mạng"),
        "telephone_card":
            MessageLookupByLibrary.simpleMessage("Thẻ điện thoại"),
        "temporarily_lock_card": MessageLookupByLibrary.simpleMessage(
            "KienlongBank có thể tạm khóa Thẻ nếu cần thiết để đảm bảo an toàn trước khi Quý khách nhận được Thẻ vật lý. Quý khách vẫn có thể tự mở khóa để sử dụng Thẻ khi cần thiết"),
        "tenor": MessageLookupByLibrary.simpleMessage("Thời hạn vay"),
        "term_and_condition":
            MessageLookupByLibrary.simpleMessage("Điều khoản và Điều kiện"),
        "term_and_condition_use_account": MessageLookupByLibrary.simpleMessage(
            "Điều kiện, điều khoản mở và sử dụng tài khoản thanh toán"),
        "term_and_condition_use_service": MessageLookupByLibrary.simpleMessage(
            "Điều kiện, điều khoản sử dụng dịch vụ ngân hàng điện tử"),
        "term_condition_collaborator": MessageLookupByLibrary.simpleMessage(
            "điều khoản, điều kiện đăng ký cộng tác viên"),
        "terms_and_conditions":
            MessageLookupByLibrary.simpleMessage("Điều khoản và Điều kiện"),
        "texting": MessageLookupByLibrary.simpleMessage("Nhắn tin"),
        "thank_for_using_ksb_services": MessageLookupByLibrary.simpleMessage(
            "Cảm ơn Quý khách đã sử dụng dịch vụ KienlongBank."),
        "theme": MessageLookupByLibrary.simpleMessage("Diện mạo"),
        "theme1": MessageLookupByLibrary.simpleMessage("Theme"),
        "theme_app": MessageLookupByLibrary.simpleMessage("Diện mạo"),
        "theme_applied":
            MessageLookupByLibrary.simpleMessage("Chủ đề đang được áp dụng"),
        "themes": MessageLookupByLibrary.simpleMessage("Diện mạo (Themes)"),
        "this_payment_period":
            MessageLookupByLibrary.simpleMessage("Kì thanh toán này"),
        "three_month":
            MessageLookupByLibrary.simpleMessage("Ba tháng gần nhất"),
        "time": MessageLookupByLibrary.simpleMessage("Thời gian"),
        "time_counseling_empty": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng chọn thời gian tư vấn"),
        "time_payment_bill":
            MessageLookupByLibrary.simpleMessage("Thời gian thanh toán"),
        "time_remaining":
            MessageLookupByLibrary.simpleMessage("Thời hạn còn lại"),
        "time_submission":
            MessageLookupByLibrary.simpleMessage("Thời gian gửi yêu cầu"),
        "title": MessageLookupByLibrary.simpleMessage("Tiêu đề"),
        "title_card_option_POST_payment":
            MessageLookupByLibrary.simpleMessage("Thanh toán qua POS"),
        "title_card_option_withdraw_money_ATM":
            MessageLookupByLibrary.simpleMessage("Rút tiền qua ATM"),
        "title_consultant": MessageLookupByLibrary.simpleMessage("Tư vấn"),
        "title_contact_permission":
            MessageLookupByLibrary.simpleMessage("Cấp quyền truy cập danh bạ"),
        "title_header_jcb":
            MessageLookupByLibrary.simpleMessage("JCB Platinum Debit"),
        "title_header_napas":
            MessageLookupByLibrary.simpleMessage("UniCard Napas"),
        "title_like_this":
            MessageLookupByLibrary.simpleMessage("Tiêu đề trông như thế này"),
        "title_lock_old_app":
            MessageLookupByLibrary.simpleMessage("Khoá tài khoản ứng dụng cũ"),
        "title_onboard_etoken":
            MessageLookupByLibrary.simpleMessage("Giới thiệu eToken"),
        "title_system": MessageLookupByLibrary.simpleMessage("Hệ thống"),
        "tittle_mode": MessageLookupByLibrary.simpleMessage("Chế độ"),
        "to": MessageLookupByLibrary.simpleMessage("Đến"),
        "to_account": MessageLookupByLibrary.simpleMessage("Đến tài khoản"),
        "to_continue_acknowledge": MessageLookupByLibrary.simpleMessage(
            "để tiếp tục, tôi xác nhận rằng đã đọc, hiểu, đồng ý với"),
        "to_day": MessageLookupByLibrary.simpleMessage("Đến ngày"),
        "today": MessageLookupByLibrary.simpleMessage("Hôm nay"),
        "todo_job": MessageLookupByLibrary.simpleMessage("Việc cần làm"),
        "tomorrow": MessageLookupByLibrary.simpleMessage("ngày mai"),
        "tools": MessageLookupByLibrary.simpleMessage("Công cụ"),
        "top_up_prepaid_card":
            MessageLookupByLibrary.simpleMessage("Nạp thẻ trả trước"),
        "topic": MessageLookupByLibrary.simpleMessage("Chủ đề"),
        "topup": MessageLookupByLibrary.simpleMessage("Nạp"),
        "topup_according_denominations": MessageLookupByLibrary.simpleMessage(
            "Nạp điện thoại theo các mệnh giá"),
        "topup_data": MessageLookupByLibrary.simpleMessage("Nạp 3G/4G"),
        "total_accumulated_interest":
            MessageLookupByLibrary.simpleMessage("Lãi tích luỹ"),
        "total_amount": MessageLookupByLibrary.simpleMessage("Tổng số tiền"),
        "total_balance": MessageLookupByLibrary.simpleMessage("Tổng số dư"),
        "total_balance_provisional":
            MessageLookupByLibrary.simpleMessage("Tổng dư nợ (tạm tính)"),
        "total_capital_need":
            MessageLookupByLibrary.simpleMessage("Tổng nhu cầu vốn"),
        "total_income": MessageLookupByLibrary.simpleMessage("Tổng thu nhập"),
        "total_money_pay":
            MessageLookupByLibrary.simpleMessage("Số tiền cần thanh toán"),
        "total_payment_amount":
            MessageLookupByLibrary.simpleMessage("Tổng số tiền thanh toán"),
        "trading_utility":
            MessageLookupByLibrary.simpleMessage("Tiện ích giao dịch"),
        "trans_acc_no":
            MessageLookupByLibrary.simpleMessage("Chuyển đến\nsố tài khoản"),
        "trans_card_no":
            MessageLookupByLibrary.simpleMessage("Chuyển đến\nsố thẻ"),
        "trans_credit":
            MessageLookupByLibrary.simpleMessage("Thanh toán\nthẻ tín dụng"),
        "trans_form": MessageLookupByLibrary.simpleMessage("Mẫu\nchuyển tiền"),
        "trans_money": MessageLookupByLibrary.simpleMessage("Số tiền GD"),
        "trans_qr":
            MessageLookupByLibrary.simpleMessage("Chuyển tiền\nbằng mã QR"),
        "trans_request":
            MessageLookupByLibrary.simpleMessage("Yêu cầu\nchuyển tiền"),
        "trans_schedule":
            MessageLookupByLibrary.simpleMessage("Lịch\nchuyển tiền"),
        "trans_securities":
            MessageLookupByLibrary.simpleMessage("Chuyển tiền\nchứng khoán"),
        "trans_tuition_fee":
            MessageLookupByLibrary.simpleMessage("Nộp\nhọc phí"),
        "trans_vn_post":
            MessageLookupByLibrary.simpleMessage("Chuyển tiền\nVNPost"),
        "transaction": MessageLookupByLibrary.simpleMessage("Giao dịch"),
        "transaction_amount":
            MessageLookupByLibrary.simpleMessage("Số tiền giao dịch"),
        "transaction_amount_not_less": MessageLookupByLibrary.simpleMessage(
            "Giao dịch không thể thực hiện do tài khoản không đủ số dư"),
        "transaction_branch":
            MessageLookupByLibrary.simpleMessage("Chi nhánh giao dịch"),
        "transaction_fee":
            MessageLookupByLibrary.simpleMessage("Phí giao dịch"),
        "transaction_history":
            MessageLookupByLibrary.simpleMessage("Lịch sử giao dịch"),
        "transaction_infor":
            MessageLookupByLibrary.simpleMessage("Thông tin giao dịch"),
        "transaction_no": m28,
        "transaction_office_details":
            MessageLookupByLibrary.simpleMessage("Chi tiết phòng giao dịch"),
        "transaction_office_information":
            MessageLookupByLibrary.simpleMessage("Thông tin phòng giao dịch"),
        "transaction_room":
            MessageLookupByLibrary.simpleMessage("Phòng giao dịch"),
        "transaction_time":
            MessageLookupByLibrary.simpleMessage("Thời gian giao dịch"),
        "transaction_type":
            MessageLookupByLibrary.simpleMessage("Loại giao dịch"),
        "transfer": MessageLookupByLibrary.simpleMessage("Chuyển khoản"),
        "transfer_category":
            MessageLookupByLibrary.simpleMessage("Phân loại giao dịch"),
        "transfer_information":
            MessageLookupByLibrary.simpleMessage("Thông tin chuyển khoản"),
        "transfer_money_success":
            MessageLookupByLibrary.simpleMessage("Chuyển tiền thành công"),
        "transfer_money_to":
            MessageLookupByLibrary.simpleMessage("Chuyển tiền đến"),
        "transfer_now": MessageLookupByLibrary.simpleMessage("Chuyển ngay"),
        "transfer_schedule":
            MessageLookupByLibrary.simpleMessage("Lịch chuyển tiền"),
        "transfer_template":
            MessageLookupByLibrary.simpleMessage("Mẫu chuyển tiền"),
        "transfer_vnpost":
            MessageLookupByLibrary.simpleMessage("Chuyển tiền VNPost"),
        "transfers": MessageLookupByLibrary.simpleMessage("Chuyển tiền"),
        "trouble": MessageLookupByLibrary.simpleMessage("Sự cố"),
        "try_after_second": m29,
        "try_again": MessageLookupByLibrary.simpleMessage("Thử lại"),
        "turn_off_flash":
            MessageLookupByLibrary.simpleMessage("Ấn để tắt đèn Flash"),
        "turn_on_flash":
            MessageLookupByLibrary.simpleMessage("Ấn để mở đèn Flash"),
        "tutorial_face": MessageLookupByLibrary.simpleMessage(
            "Hướng dẫn xác thực khuôn mặt"),
        "tutorial_face_content": MessageLookupByLibrary.simpleMessage(
            "Giữ điện thoại cố định và đưa khuôn mặt bạn vào khung hình tròn, sau đó di chuyển khuôn mặt theo hướng dẫn hiển thị trên màn hình."),
        "tutorial_face_content_1":
            MessageLookupByLibrary.simpleMessage("Góc mặt lệch tâm vòng tròn"),
        "tutorial_face_content_2":
            MessageLookupByLibrary.simpleMessage("Khuôn mặt quá tối"),
        "tutorial_face_content_3":
            MessageLookupByLibrary.simpleMessage("Khuôn mặt quá sáng"),
        "tutorial_indentity":
            MessageLookupByLibrary.simpleMessage("Hướng dẫn xác thực giấy tờ"),
        "tutorial_indentity_content_1":
            MessageLookupByLibrary.simpleMessage("Không chụp quá mờ"),
        "tutorial_indentity_content_2":
            MessageLookupByLibrary.simpleMessage("Không chụp mất góc"),
        "tutorial_indentity_content_3":
            MessageLookupByLibrary.simpleMessage("Không chụp loá sáng"),
        "tutorial_open_stm":
            MessageLookupByLibrary.simpleMessage("Hướng dẫn mở thẻ tại STM"),
        "two_scan_code_guide":
            MessageLookupByLibrary.simpleMessage("2. Quét QR code"),
        "two_scan_code_guide_content": MessageLookupByLibrary.simpleMessage(
            "Mở camera trên máy của bạn lên hướng về phía có mã QR Code , máy sẽ tự động quét mã."),
        "type_card_physics": MessageLookupByLibrary.simpleMessage("Loại thẻ"),
        "type_card_physics_jcb":
            MessageLookupByLibrary.simpleMessage("JCB Platinum Debit"),
        "type_card_physics_napas":
            MessageLookupByLibrary.simpleMessage("ATM Napas UniCard"),
        "type_identity": MessageLookupByLibrary.simpleMessage("Loại giấy tờ"),
        "type_of_service": MessageLookupByLibrary.simpleMessage("Loại dịch vụ"),
        "understand": MessageLookupByLibrary.simpleMessage("Đã hiểu"),
        "unlike": MessageLookupByLibrary.simpleMessage("Bỏ thích"),
        "unlink": MessageLookupByLibrary.simpleMessage("Hủy liên kết"),
        "unlink_devices": MessageLookupByLibrary.simpleMessage(
            "Sau khi gỡ bỏ tin cậy thiết bị, Quý khách sẽ phải xác thực OTP khi đăng nhập lại trên thiết bị đó"),
        "unpaid": MessageLookupByLibrary.simpleMessage("Chưa thanh toán"),
        "unsecured_consumer_loan":
            MessageLookupByLibrary.simpleMessage("Vay tiêu dùng tín chấp"),
        "update": MessageLookupByLibrary.simpleMessage("Cập nhật"),
        "update_account_info_success": MessageLookupByLibrary.simpleMessage(
            "Cập nhập thông tin tài khoản thành công"),
        "update_appointment_success":
            MessageLookupByLibrary.simpleMessage("Sửa lịch hẹn thành công"),
        "update_counseling_status_success":
            MessageLookupByLibrary.simpleMessage(
                "Cập nhật trạng thái thành công!"),
        "update_count_error_pin_success": MessageLookupByLibrary.simpleMessage(
            "Xóa số lần nhập sai PIN thành công"),
        "update_customer_vnpost": MessageLookupByLibrary.simpleMessage(
            "Cập nhật nhóm khách hàng VNPOST"),
        "update_email":
            MessageLookupByLibrary.simpleMessage("Cập nhật địa chỉ Email"),
        "update_success": MessageLookupByLibrary.simpleMessage(
            "Cập nhật mục tiêu thành công"),
        "use_CCCD_authentication": MessageLookupByLibrary.simpleMessage(
            "Sử dụng CCCD gắn chip để tiến hành xác thực sinh trắc học"),
        "use_VNeID_authentication": MessageLookupByLibrary.simpleMessage(
            "Quý khác sẽ được điều hướng đến ứng dụng VNeID để tiến hành xác thực sinh trắc học"),
        "use_system_font_size": MessageLookupByLibrary.simpleMessage(
            "Sử dụng cỡ font theo hệ thống"),
        "user_cancel_counseling":
            MessageLookupByLibrary.simpleMessage("Cuộc hẹn đã được hủy bởi"),
        "username": MessageLookupByLibrary.simpleMessage("Tên tài khoản"),
        "utilities": MessageLookupByLibrary.simpleMessage("Tiện ích"),
        "valid_papers_msg": MessageLookupByLibrary.simpleMessage(
            "•  Giấy tờ còn hạn sử dụng. Là hình gốc, không scan và photocopy."),
        "validate_detail_address_name": MessageLookupByLibrary.simpleMessage(
            "Địa chỉ cụ thể không được bỏ trống"),
        "validate_district_name": MessageLookupByLibrary.simpleMessage(
            "Quận/Huyện không được bỏ trống"),
        "validate_province_name": MessageLookupByLibrary.simpleMessage(
            "Tỉnh/Thành phố không được bỏ trống"),
        "validate_saving_target_name": MessageLookupByLibrary.simpleMessage(
            "Tên mục tiêu không được bỏ trống"),
        "validate_ward_name": MessageLookupByLibrary.simpleMessage(
            "Phường/Xã không được bỏ trống"),
        "validity_period":
            MessageLookupByLibrary.simpleMessage("Thời gian hiệu lực"),
        "vat": MessageLookupByLibrary.simpleMessage("VAT"),
        "verification": MessageLookupByLibrary.simpleMessage("Xác thực"),
        "verification_face_done": MessageLookupByLibrary.simpleMessage(
            "Quý khách đã thực hiện việc lấy mẫu các hình ảnh khuôn mặt xong."),
        "verification_identity":
            MessageLookupByLibrary.simpleMessage("Cung cấp giấy tờ tuỳ thân"),
        "verification_identity_summary": MessageLookupByLibrary.simpleMessage(
            "Quý khách chọn loại giấy tờ tuỳ thân dưới đây để xác thực thông tin"),
        "verify_adress_email":
            MessageLookupByLibrary.simpleMessage("Xác thực địa chỉ email"),
        "verify_face":
            MessageLookupByLibrary.simpleMessage("Xác thực khuôn mặt"),
        "verify_infor": MessageLookupByLibrary.simpleMessage("đã xác thực"),
        "verify_now": MessageLookupByLibrary.simpleMessage("Xác thực ngay"),
        "verify_password": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng nhập mật khẩu đăng nhập để xác thực"),
        "verify_password_3": MessageLookupByLibrary.simpleMessage(
            "Bao gồm chữ số và ký tự đặc biệt"),
        "video_call": MessageLookupByLibrary.simpleMessage("Cuộc gọi video"),
        "vietcombank": MessageLookupByLibrary.simpleMessage("Vietcombank"),
        "view_all": MessageLookupByLibrary.simpleMessage("Xem toàn bộ"),
        "view_detail": MessageLookupByLibrary.simpleMessage("Xem chi tiết"),
        "view_more": MessageLookupByLibrary.simpleMessage("Xem thêm"),
        "view_photo": MessageLookupByLibrary.simpleMessage("Xem ảnh"),
        "vip_account_no":
            MessageLookupByLibrary.simpleMessage("Tài khoản trả phí"),
        "virtual_card_notification":
            MessageLookupByLibrary.simpleMessage("Thông báo"),
        "visa_beyond": MessageLookupByLibrary.simpleMessage(
            "Thẻ đồng thương hiệu Visa Beyond KLB - KSFinance"),
        "visa_beyond_type": MessageLookupByLibrary.simpleMessage(
            "Visa Platinum Beyond KLB - KSFinance"),
        "vote_another_not_good_empty": MessageLookupByLibrary.simpleMessage(
            "Quý khách vui lòng nhập lý do không hài lòng"),
        "vote_consultant_success": MessageLookupByLibrary.simpleMessage(
            "Đánh giá chất lượng chuyên viên tư vấn thành công!"),
        "vote_reason_not_good": MessageLookupByLibrary.simpleMessage(
            "Lý do khiến quý khách thấy hài lòng?"),
        "vote_text_0": MessageLookupByLibrary.simpleMessage("Chưa đánh giá"),
        "vote_text_1": MessageLookupByLibrary.simpleMessage("Cần cải thiện"),
        "vote_text_2": MessageLookupByLibrary.simpleMessage("Chưa tốt"),
        "vote_text_3": MessageLookupByLibrary.simpleMessage("Khá"),
        "vote_text_4": MessageLookupByLibrary.simpleMessage("Tốt"),
        "vote_text_5": MessageLookupByLibrary.simpleMessage("Tuyệt vời"),
        "wait_check": MessageLookupByLibrary.simpleMessage("Đang chờ xử lý"),
        "waiting": MessageLookupByLibrary.simpleMessage("Đang chờ"),
        "wallet_link":
            MessageLookupByLibrary.simpleMessage("Liên kết ví điện tử"),
        "want_to_delete_recurring_order": MessageLookupByLibrary.simpleMessage(
            "Quý khách chắc chắn muốn xoá lệnh định kỳ này?"),
        "wards": MessageLookupByLibrary.simpleMessage("Phường/Xã"),
        "warning": MessageLookupByLibrary.simpleMessage("Cảnh báo"),
        "warning_transfer": MessageLookupByLibrary.simpleMessage(
            "Vui lòng nhập nội dung chuyển tiền không dấu"),
        "warning_user": MessageLookupByLibrary.simpleMessage(
            "Quý khách cần đến quầy giao dịch để kích hoạt chức năng này."),
        "warranty_asset":
            MessageLookupByLibrary.simpleMessage("Tài sản đảm bảo"),
        "water": MessageLookupByLibrary.simpleMessage("Nước"),
        "weekly": MessageLookupByLibrary.simpleMessage("Hằng tuần"),
        "what_difficult_have_klb": MessageLookupByLibrary.simpleMessage(
            "Việc gì khó đã có KienlongBank!"),
        "withdraw": MessageLookupByLibrary.simpleMessage("Rút"),
        "withdraw_atm": MessageLookupByLibrary.simpleMessage("Rút tiền ATM"),
        "withdraw_atm_amount":
            MessageLookupByLibrary.simpleMessage("Thông tin rút tiền"),
        "withdraw_atm_amount_account":
            MessageLookupByLibrary.simpleMessage("Tài khoản rút tiền"),
        "withdraw_atm_amount_maximum": MessageLookupByLibrary.simpleMessage(
            "•  Số tiền rút tối đa là 10,000,000 VND"),
        "withdraw_atm_amount_multiples": MessageLookupByLibrary.simpleMessage(
            "•  Số tiền rút phải là bội số của 50,000 VND"),
        "withdraw_atm_no_print":
            MessageLookupByLibrary.simpleMessage("Không in biên lai giao dịch"),
        "withdraw_atm_print_content": MessageLookupByLibrary.simpleMessage(
            "Quý khách có thể không bật chức năng này để chung tay bảo vệ môi trường."),
        "withdraw_atm_title":
            MessageLookupByLibrary.simpleMessage("In biên lai giao dịch"),
        "withdraw_money": MessageLookupByLibrary.simpleMessage("Rút tiền"),
        "withdrawal_of_rose":
            MessageLookupByLibrary.simpleMessage("Rút hoa hồng"),
        "withdrawal_success":
            MessageLookupByLibrary.simpleMessage("Rút tiền thành công"),
        "working_time":
            MessageLookupByLibrary.simpleMessage("Thời gian làm việc"),
        "world_of_electronics":
            MessageLookupByLibrary.simpleMessage("Thế giới điện máy, gia dụng"),
        "year": MessageLookupByLibrary.simpleMessage("Năm"),
        "yes": MessageLookupByLibrary.simpleMessage("có"),
        "yesterday": MessageLookupByLibrary.simpleMessage("Hôm qua"),
        "you_spent": MessageLookupByLibrary.simpleMessage("Bạn đã tiêu")
      };
}
