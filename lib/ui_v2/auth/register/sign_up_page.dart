import 'dart:io';

import 'package:common/router/transaction_axis_wrapper.dart';
import 'package:common/utils/dialog_util.dart';
import 'package:common/widgets/bottom_button.dart';
import 'package:common/widgets/loading/loading_widget.dart';
import 'package:common/widgets/style_app.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:ks_chat/ks_chat.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_bloc/bloc/model/cash_withdrawal/address_model.dart';
import 'package:ksb_common/shared/assets.dart' as klb_common;
import 'package:ksb_common/shared/widgets/background_app.dart';
import 'package:mobile_banking/assets/assets.dart';
import 'package:mobile_banking/assets/dimens.dart';
import 'package:mobile_banking/assets/images.dart';
import 'package:mobile_banking/di/inject.dart';
import 'package:mobile_banking/generated/l10n.dart';
import 'package:mobile_banking/ui_v2/auth/login_page_v2.dart';
import 'package:mobile_banking/ui_v2/auth/register/account_create.dart';
import 'package:mobile_banking/ui_v2/auth/register/beautiful_number/beautiful_number_list.dart';
import 'package:mobile_banking/ui_v2/auth/register/beautiful_number/beautiful_number_page.dart';
import 'package:mobile_banking/ui_v2/auth/register/branch_bank/register_branch_bank.dart';
import 'package:mobile_banking/ui_v2/auth/register/register_input_branch.dart';
import 'package:mobile_banking/ui_v2/auth/register/verification_identity.dart';
import 'package:mobile_banking/ui_v2/auth/register/verify_complete_page.dart';
import 'package:mobile_banking/ui_v2/auth/widget/float_button_chat.dart';
import 'package:mobile_banking/ui_v2/auth/widget/view_capture_warning_widget.dart';
import 'package:mobile_banking/ui_v2/otp/otp_widget.dart';
import 'package:mobile_banking/ui_v2/profile_account/etoken/custom/content_otp.dart';
import 'package:mobile_banking/ui_v2/qr_scanner/qr_scanner.dart';
import 'package:mobile_banking/ui_v2/transfer/widgets/common_textfield_content.dart';
import 'package:mobile_banking/ui_v2/widget/constant.dart';
import 'package:mobile_banking/ui_v2/widget/loading/loading_view.dart';
import 'package:mobile_banking/ui_v2/widget/progress/linear_progress_bar.dart';
import 'package:mobile_banking/utils/navigator.dart';
import 'package:passport_reader_plugin/passport_reader_plugin.dart';
import 'package:rxdart/rxdart.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../widget/dialog/sheet_utils.dart';
import 'terms_and_conditions_page.dart';

class SignUpPage extends StatefulWidget {
  const SignUpPage({Key? key}) : super(key: key);

  @override
  State<SignUpPage> createState() => _SignUpPageState();
}

class _SignUpPageState extends State<SignUpPage> {
  final _bloc = Injection.injector.get<InputPhoneBloc>();
  final _blocEkyc = Injection.injector.get<EkycVerifyBloc>();
  BehaviorSubject<int> steps = BehaviorSubject.seeded(1);
  final keyNumberPhone = GlobalKey<CommonTextFieldContentWidgetState>();
  final keyReferralCode = GlobalKey<CommonTextFieldContentWidgetState>();
  int totalStep = 8;
  String? face;
  String? frontCard;
  AddressModel? addressModel;
  IdentityInfo? identityInfo;
  FocusNode focusNode = FocusNode();
  bool isCheck = false;
  bool isListNumber = false;
  bool isPhoneNumber = true;
  String? accountNumber;
  ScanNFCModel? nfcModel;

  final _passportReaderPlugin = PassportReaderPlugin();
  bool? _checkSupportNFC;

  @override
  void initState() {
    super.initState();
    checkSupportNFC();

    _bloc.streamSubs.add(_bloc.otpSuccessStream.listen((status) async {
      if (status == true && mounted) {
        final result = await go(
          context,
          ViewCaptureWarningWidget(
            onTap: () async {
              if (mounted) Navigator.pop(context);
            },
            isSignUpFlow: true,
          ),
        );
        if (result == null) {
          int value = (steps.valueOrNull ?? 1) + 1;
          steps.add(value);
          setState(() {});
        }
      }
    }));

    _bloc.streamSubs.add(_bloc.errorStream.listen((error) {
      if (!error.isNullOrEmpty && mounted) {
        DialogUtil.alert(context, error, submit: S.of(context).agree);
      }
    }));

    _bloc.activeStream.listen((event) {
      if (event && mounted) {
        DialogUtil.alert(context, S.of(context).resend_code_success);
      }
    });
  }

  Future<void> checkSupportNFC() async {
    try {
      bool? result = true;
      if (Platform.isAndroid) {
        result = await _passportReaderPlugin.checkSupportNFC();
      }
      _checkSupportNFC = result && await SheetUtil.checkIOSVersion();
    } catch (e) {
      _checkSupportNFC = false;
    }
  }

  @override
  void dispose() {
    _bloc.dispose();
    steps.close();
    _blocEkyc.dispose();
    super.dispose();
  }

  _launchUrl() async {
    const url =
        "https://kienlongbank.com/dieu-khoan-dieu-kien-su-dung-dich-vu-ngan-hang-dien-tu";
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    } else {
      throw 'not found $url';
    }
  }

  _getTermsAndConditions(BuildContext context, String id) async {
    final getData = await _bloc.getTermsAndConditions(id: id);
    if (getData.content?.isNotEmpty == true && context.mounted) {
      goPresent(
        context,
        TermsAndConditionsPage(
          modelTerms: getData,
          titleBottomButton: 'Tôi đồng ý',
          onTap: () async {
            final result = await _bloc.registerUserV1(
              hasSupportNFC: _checkSupportNFC,
            );
            if (result == true && context.mounted) {
              logger.d(_bloc.phoneNumber);
              goPresent(
                context,
                OtpWidget(
                  bloc: _bloc,
                  title: S.of(context).common_otp_verify_title,
                  titleText: ContentOtp(phoneNumber: _bloc.phoneNumber),
                  verifyOtp: (value) async {
                    final success = await _bloc.verifyUserByPhone(
                      _bloc.phoneNumber,
                      null,
                      OtpType.REGISTER,
                    );
                    if (success != null && success) return true;
                    return false;
                  },
                  resendOtp: () => _bloc.resendOtp(username: _bloc.phoneNumber),
                ),
              );
            }
          },
        ),
      );
    } else {
      _launchUrl();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      backgroundColor: Theme.of(context).cardColor,
      body: Stack(children: [
        BackgroundAppBarImage(
          image: klb_common.ImageAssets.img_appbar,
          appBar: getAppBarDark(context,
              title: S.of(context).common_register,
              elevation: 0.0, backPage: () {
            if ((steps.valueOrNull ?? 1) <= 1) {
              popScreen(context);
            } else if (steps.valueOrNull == 3) {
              DialogUtil.confirm(
                context,
                Text(
                  S.of(context).cancel_register_msg,
                ),
                title: S.of(context).common_title_app,
                onSubmit: () {
                  goToLogin(context);
                },
                submitText: S.of(context).common_agree,
                cancelText: S.of(context).common_cancel,
              );
            } else if ((steps.valueOrNull == 6 &&
                    isListNumber == true &&
                    !_bloc.hasBranchCode) ||
                (steps.valueOrNull == 5 &&
                    isListNumber == true &&
                    _bloc.hasBranchCode)) {
              setState(() {
                isListNumber = false;
              });
            } else {
              int value = (steps.valueOrNull ?? 1) - 1;
              steps.add(value);
            }
          }),
          child: LoadingView(
            loadingStream: _bloc.progressVisible,
            child: _body(),
          ),
        ),
        Positioned.fill(
          child: LoadingWidget(stream: _bloc.loadingScreenStream),
        ),
      ]),
      floatingActionButton: LayoutBuilder(
        builder: (layoutContext, constraints) {
          if (MediaQuery.of(context).viewInsets.bottom > 100) {
            return const SizedBox();
          }
          return FloatButtonChat(
            bottomPadding: steps.valueOrNull == 1
                ? MediaQuery.of(context).size.height * 0.1
                : steps.valueOrNull == 2
                    ? MediaQuery.of(context).size.height * 0.08
                    : steps.valueOrNull == 3 ||
                            steps.valueOrNull == 4 ||
                            steps.valueOrNull == 5
                        ? MediaQuery.of(context).size.height * 0.1
                        : null,
            phone: _bloc.phoneNumber,
            stage: StageCallLog.OPEN_ACCOUNT,
          );
        },
      ),
    );
  }

  _body() {
    return Column(
      children: [
        StreamBuilder<int>(
            stream: steps.stream,
            initialData: 1,
            builder: (context, snapshot) {
              return LinearProgressBar(
                value: 1 / totalStep * (snapshot.data ?? 1),
                colorProgressBar: const Color(0xFFF04F24),
                borderRadius: 0,
              );
            }),
        Expanded(
          child: StreamBuilder<int>(
              stream: steps.stream,
              initialData: 1,
              builder: (context, snapshot) {
                if (snapshot.data == 2) {
                  return TransitionAxisWrapper(
                    widget: VerificationIdentity(
                      username: _bloc.phoneNumber,
                      refCode: _bloc.referral,
                      frontCard: (value) {
                        if (!value.isNullOrEmpty) {
                          setState(() {
                            frontCard = value;
                          });
                        }
                      },
                      faceUrl: (value, {nfc}) {
                        nfcModel = nfc;
                        setState(() {
                          face = value;
                          int stepsValue = snapshot.data! + 1;
                          steps.add(stepsValue);
                        });
                      },
                    ),
                  );
                } else if (snapshot.data == 3) {
                  return TransitionAxisWrapper(
                    widget: VerifyCompletePage(
                      username: _bloc.phoneNumber,
                      frontCardUrl: frontCard,
                      faceUrl: face,
                      infoModel: identityInfo,
                      nfcModel: nfcModel,
                      onNext: (infoModel) {
                        setState(() {
                          identityInfo = infoModel;
                          int stepsValue = snapshot.data! + 1;
                          steps.add(stepsValue);
                        });
                      },
                      onBackStep: () {
                        int stepsValue = snapshot.data! - 1;
                        steps.add(stepsValue);
                      },
                    ),
                  );
                } else if (snapshot.data == 4 && !_bloc.hasBranchCode) {
                  return TransitionAxisWrapper(
                    widget: RegisterBranchBank(
                      onSkip: () {
                        setState(() {
                          int stepsValue = snapshot.data! + 1;
                          steps.add(stepsValue);
                        });
                      },
                      onNext: (branch) {
                        setState(() {
                          _bloc.branchCodeSink.add(branch);
                          int stepsValue = snapshot.data! + 1;
                          steps.add(stepsValue);
                        });
                      },
                    ),
                  );
                } else if ((snapshot.data == 5 && !_bloc.hasBranchCode) ||
                    (snapshot.data == 4 && _bloc.hasBranchCode)) {
                  return TransitionAxisWrapper(
                    widget: RegisterInputBranch(
                      username: _bloc.phoneNumber,
                      address: (model) {
                        setState(() {
                          addressModel = model;
                          int stepsValue = snapshot.data! + 1;
                          steps.add(stepsValue);
                        });
                      },
                    ),
                  );
                } else if ((snapshot.data == 6 &&
                        isListNumber == false &&
                        !_bloc.hasBranchCode) ||
                    (snapshot.data == 5 &&
                        isListNumber == false &&
                        _bloc.hasBranchCode)) {
                  return TransitionAxisWrapper(
                      widget: BeautifulNumberPage(
                    onChooseNumber: (value) {
                      setState(() {
                        isListNumber = true;
                        isPhoneNumber = value;
                      });
                    },
                    onSkip: () {
                      setState(() {
                        int stepsValue = snapshot.data! + 1;
                        steps.add(stepsValue);
                      });
                    },
                  ));
                } else if ((snapshot.data == 6 &&
                        isListNumber == true &&
                        !_bloc.hasBranchCode) ||
                    (snapshot.data == 5 &&
                        isListNumber == true &&
                        _bloc.hasBranchCode)) {
                  return TransitionAxisWrapper(
                      widget: BeautifulNumberList(
                          query: isPhoneNumber
                              ? _bloc.phoneNumber
                              : identityInfo?.birthday.formatDMY,
                          isPhoneNumber: isPhoneNumber,
                          onNext: (number) {
                            setState(() {
                              accountNumber = number;
                              int stepsValue = snapshot.data! + 1;
                              steps.add(stepsValue);
                            });
                          }));
                } else if ((snapshot.data == 7 && !_bloc.hasBranchCode) ||
                    (snapshot.data == 6 && _bloc.hasBranchCode)) {
                  return TransitionAxisWrapper(
                    widget: AccountCreate(
                      inProgress: (value) {
                        if (value) {
                          _bloc.showScreenLoading(maxApi: 1, isSubmit: value);
                        } else {
                          _bloc.completeScreenLoading(isSubmit: true);
                        }
                      },
                      phoneNumber: _bloc.phoneNumber,
                      addressModel: addressModel,
                      identityInfo: identityInfo,
                      accountNumber: accountNumber,
                      branchCode: _bloc.branchCode,
                    ),
                  );
                } else {
                  return TransitionAxisWrapper(widget: _firstBody());
                }
              }),
        ),
      ],
    );
  }

  _firstBody() {
    return Container(
      color: Theme.of(context).cardColor,
      child: Column(children: [
        const SizedBox(height: 24),
        Expanded(
          child: SingleChildScrollView(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: kPaddingPage),
              child: Column(
                children: [
                  Container(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      S.of(context).input_phone,
                      style: StyleApp.titleStyle(context),
                    ),
                  ),
                  const SizedBox(height: 10),
                  Container(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      S.of(context).input_phone_summary,
                      style: StyleApp.descStyle(context),
                      textAlign: TextAlign.start,
                    ),
                  ),
                  const SizedBox(height: 30),
                  StreamBuilder<String>(
                      stream: _bloc.phoneNumberStream,
                      builder: (context, snapshot) {
                        return CommonTextFieldContentWidget(
                          key: keyNumberPhone,
                          labelText: S.of(context).phone_number,
                          value: snapshot.data ?? '',
                          labelStyle: StyleApp.bodyStyle(context),
                          hintStyle: StyleApp.bodyStyle(context),
                          labelColor: const Color(0xFF333333).withOpacity(0.4),
                          textInputType: TextInputType.phone,
                          maxLength: 10,
                          suffixAlwaysOn: true,
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(RegExp('[0-9]')),
                          ],
                          onProcessSuffix: () {
                            keyNumberPhone.currentState?.setErrorText(null);
                            _bloc.phoneNumberSink.add('');
                          },
                          onChanged: (value) {
                            _bloc.phoneNumberSink.add(value);
                          },
                          onChangedCheckError: (value) {
                            if (value.length == 10) {
                              return validateMobile(value);
                            } else {
                              return null;
                            }
                          },
                          onCheckError: (value) {
                            if (!snapshot.data.isNullOrEmpty) {
                              return validateMobile(snapshot.data);
                            }
                          },
                          checkNull: (value) {
                            if (snapshot.data.isNullOrEmpty) {
                              return validateMobile(snapshot.data);
                            }
                          },
                        );
                      }),
                  StreamBuilder<String>(
                      stream: _bloc.referralStream,
                      builder: (context, snapshot) {
                        return CommonTextFieldContentWidget(
                          labelText: S.of(context).input_referral_code,
                          key: keyReferralCode,
                          labelStyle: StyleApp.bodyStyle(context),
                          hintStyle: StyleApp.bodyStyle(context),
                          labelColor: const Color(0xFF333333).withOpacity(0.4),
                          suffixAlwaysOn: true,
                          value: snapshot.data ?? '',
                          onProcessSuffix: () {
                            _bloc.referralSink.add('');
                            _bloc.referralInfoSink.add(null);
                          },
                          checkNull: (text) {
                            if (text.isNullOrEmpty) {
                              _bloc.referralSink.add('');
                              _bloc.referralInfoSink.add(null);
                            }
                          },
                          onChanged: (value) {
                            _bloc.referralSink.add(value);
                            _bloc.referralInfoSink.add(null);
                            if (!value.isNullOrEmpty) {
                              totalStep = 8;
                            } else {
                              totalStep = 7;
                            }
                            setState(() {});
                          },
                          onCheckError: (val) {
                            _bloc.getReferralInfo(val);
                          },
                          inputFormatters: [
                            FilteringTextInputFormatter.deny(RegExp(r'\s')),
                          ],
                          iconButton: IconButton(
                            icon: ImageAssets.svgAssets(
                              ImagesResource.ic_scan_qr_register,
                              color: const Color(0xFF292663),
                              fit: BoxFit.cover,
                            ),
                            iconSize: 38,
                            padding: const EdgeInsets.all(0),
                            onPressed: () => popUpPage(
                              QrScanner(
                                typecall: 1,
                                myCode: true,
                                myCodeQr: (value) {
                                  FocusScope.of(context)
                                      .requestFocus(focusNode);
                                  _bloc.referralSink.add(value);
                                  _bloc.getReferralInfo(value);
                                  if (!value.isNullOrEmpty) {
                                    totalStep = 8;
                                  } else {
                                    totalStep = 7;
                                  }
                                  setState(() {});
                                },
                              ),
                              context,
                            ),
                          ),
                        );
                      }),
                  _hasReferralWidget(),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(height: 12),
        LayoutBuilder(
          builder: (layoutContext, constraints) {
            if (MediaQuery.of(context).viewInsets.bottom > 100) {
              return const SizedBox();
            }
            return StreamBuilder<bool>(
              stream: _bloc.validatePhoneNumberStream,
              builder: (context, snapshot) {
                final isVisible = snapshot.hasData && snapshot.data == true;
                return _getBottom(isVisible);
              },
            );
          },
        ),
      ]),
    );
  }

  Widget _getBottom(bool isVisible) {
    return BottomButton(
        title: S.of(context).next,
        isDisable: !isVisible,
        onTap: () => _getTermsAndConditions(context, '2_0'));
    // return isVisible
    //     ? BottomRegisterWidget(
    //     bloc: _bloc,
    //     isCheck: isCheck,
    //     onChangeCheckbox: (value) {
    //       setState(() {
    //         isCheck = !isCheck;
    //       });
    //     },
    //     onProcess: () async {
    //       final result = await _bloc.registerUserV1(
    //         hasSupportNFC: _checkSupportNFC,
    //       );
    //       if (result == true && mounted) {
    //         logger.d(_bloc.phoneNumber);
    //         goPresent(
    //           context,
    //           OtpWidget(
    //             bloc: _bloc,
    //             title: S.of(context).common_otp_verify_title,
    //             titleText: ContentOtp(
    //                 phoneNumber: _bloc.phoneNumber),
    //             verifyOtp: (value) async {
    //               final success =
    //               await _bloc.verifyUserByPhone(
    //                 _bloc.phoneNumber,
    //                 null,
    //                 OtpType.REGISTER,
    //               );
    //               if (success != null && success) return true;
    //               return false;
    //             },
    //             resendOtp: () => _bloc.resendOtp(
    //                 username: _bloc.phoneNumber),
    //           ),
    //         );
    //       }
    //     })
    //     : BottomRegisterWidget(
    //   bloc: _bloc,
    //   isDisable: true,
    //   isCheck: isCheck,
    //   onChangeCheckbox: (value) {
    //     setState(() {
    //       isCheck = !isCheck;
    //     });
    //   },
    // );
  }

  String? validateMobile(String? value) {
    String pattern = r'(^(?:[+0]9)?[0-9]{10,12}$)';
    RegExp regExp = RegExp(pattern);
    if (value.isNullOrEmpty) {
      return S.of(context).error_payee_new_phone_empty;
    } else if (value!.length < 10 || value.length > 10) {
      return S.of(context).error_validate_edit_number;
    } else if (!regExp.hasMatch(value) || value[0] != "0") {
      return S.of(context).error_validate_edit_number;
    }
    return null;
  }

  _hasReferralWidget() {
    return StreamBuilder<CheckReferralCodeResponse?>(
        stream: _bloc.referralInfoStream,
        builder: (context, snapshot) {
          if (snapshot.hasData) {
            return Column(
              children: [
                Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    color: const Color(0xFFF4F4F4),
                  ),
                  padding: EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: kSmallPadding,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Người giới thiệu',
                        style: StyleApp.captionStyle(context),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        snapshot.data?.referrerUsername ?? '',
                        style: StyleApp.subtitle1(context),
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: kSmallPadding,
                ),
                snapshot.data?.memberCompany != "VNPOST"
                    ? Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(4),
                          color: const Color(0xFFF4F4F4),
                        ),
                        padding: EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: kSmallPadding,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Đơn vị quản lý',
                              style: StyleApp.captionStyle(context),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              snapshot.data?.branchName ?? "",
                              style: StyleApp.subtitle1(context),
                            ),
                          ],
                        ),
                      )
                    : Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(4),
                          color: const Color(0xFFF4F4F4),
                        ),
                        padding: EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: kSmallPadding,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Tên bưu cục',
                              style: StyleApp.captionStyle(context),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              snapshot.data?.memberName ?? "",
                              style: StyleApp.subtitle1(context),
                            ),
                          ],
                        ),
                      ),
              ],
            );
          }
          return const SizedBox();
        });
  }
}
