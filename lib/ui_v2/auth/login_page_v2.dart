/*
 * Created by DuanV<PERSON> 
 * on 20/06/2023
 */
import 'dart:convert';
import 'dart:io';

import 'package:common/global_callback.dart';
import 'package:common/utils/dialog_util.dart';
import 'package:common/utils/extension/color.dart';
import 'package:common/widgets/common_size.dart';
import 'package:common/widgets/style_app.dart';
import 'package:connectivity/connectivity.dart';
import 'package:ekyc/utils/scanner_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_jailbreak_detection/flutter_jailbreak_detection.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ks_chat/ks_chat.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_bloc/bloc/model/home/<USER>';
import 'package:ksb_bloc/bloc/model/theme/theme_model.dart';
import 'package:ksb_common/shared/theme/custom_theme.dart';
import 'package:ksb_common/shared/widgets/background_app.dart';
import 'package:ksb_common/shared/widgets/background_image_app_bar.dart';
import 'package:ksb_common/shared/widgets/image/image_from_type.dart';
import 'package:local_auth/local_auth.dart';
import 'package:mobile_banking/assets/assets.dart';
import 'package:mobile_banking/assets/dimens.dart';
import 'package:mobile_banking/assets/images.dart';
import 'package:mobile_banking/di/inject.dart';
import 'package:mobile_banking/generated/l10n.dart';
import 'package:mobile_banking/method_channel/security/security_util.dart';
import 'package:mobile_banking/model/type_text.dart';
import 'package:mobile_banking/navigator.dart';
import 'package:mobile_banking/ui_v2/auth/forgot_pasword/forgot_password.dart';
import 'package:mobile_banking/ui_v2/auth/forgot_pasword/profile_change_pass_page.dart';
import 'package:mobile_banking/ui_v2/auth/notification_surplus/message_notification_screen.dart';
import 'package:mobile_banking/ui_v2/auth/pre_login_widget.dart';
import 'package:mobile_banking/ui_v2/auth/register/sign_up_page.dart';
import 'package:mobile_banking/ui_v2/auth/widget/custom_scroll_bottom.dart';
import 'package:mobile_banking/ui_v2/auth/widget/env_widget.dart';
import 'package:mobile_banking/ui_v2/etoken/etoken_pin_code.dart';
import 'package:mobile_banking/ui_v2/etoken/input_eToken.dart';
import 'package:mobile_banking/ui_v2/network/network_tabbar.dart';
import 'package:mobile_banking/ui_v2/otp/otp_widget.dart';
import 'package:mobile_banking/ui_v2/qr_scanner/qr_scanner.dart';
import 'package:mobile_banking/ui_v2/transfer/widgets/common_textfield_content.dart';
import 'package:mobile_banking/ui_v2/widget/button/simple_button_v1.dart';
import 'package:mobile_banking/utils/auth_fatsh.dart';
import 'package:ksb_common/ksb_common.dart' as ksb_common;
import 'package:mobile_banking/utils/biometrics.dart';
import 'package:mobile_banking/utils/dialog_error.dart';
import 'package:mobile_banking/utils/navigator.dart';
import 'package:mobile_banking/utils/validator_qr.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:url_launcher/url_launcher.dart' as url_launcher;

import '../../sunshine_app.dart';
import '../security/security_action.dart';

void goToLogin(context, {bool? isExpired, bool? autoLogin}) {
  Navigator.pushAndRemoveUntil(
    context,
    MaterialPageRoute(
      builder: (_) => _LoginPageV2(
        isExpired: isExpired ?? false,
        autoLogin: autoLogin,
      ),
    ),
    (Route<dynamic> route) => false,
  );
}

final loginShadow = [
  Shadow(
    offset: const Offset(0, 1),
    blurRadius: 1.0,
    color: Colors.black.withOpacity(0.8),
  ),
];

class _LoginPageV2 extends StatefulWidget {
  final bool isExpired;
  final bool? autoLogin;

  const _LoginPageV2({Key? key, this.isExpired = false, this.autoLogin})
      : super(key: key);

  @override
  State<_LoginPageV2> createState() => _LoginPageV2State();
}

class _LoginPageV2State extends SecurityState<_LoginPageV2>
    with AutomaticKeepAliveClientMixin {
  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  final _env = Injection.injector.get<Environment>();
  final _bloc = Injection.injector.get<LoginBloc>();
  final _notificationBloc = Injection.injector.get<NotificationBloc>();
  final _eTokenBloc = Injection.injector.get<EtokenBloc>();
  final _chatBloc = Injection.injector.get<RocketChatVisitorRoomsBloc>();
  final _preferences = Injection.preferences;
  TextEditingController? _usernameController;
  late TextEditingController _passwordController;
  bool _obscureText = true;
  final LocalAuthentication auth = LocalAuthentication();
  late Biometrics biometrics;
  bool canChangeEnv = true;
  bool _showProxy = false;
  bool _showIcon = false;
  bool? _showPreLogin;
  int _tapCount = 0;
  int countId = 0;
  final FocusNode _focusNodeUser = FocusNode();
  final FocusNode _focusNodePass = FocusNode();
  final keyUserName = GlobalKey<CommonTextFieldContentWidgetState>();
  final keyPassword = GlobalKey<CommonTextFieldContentWidgetState>();
  ThemeDataLocal? theme;
  Color? iconColor;

  @override
  void initState() {
    super.initState();
    setBgPage();
    _bloc.session.timerRefreshToken?.cancel();
    _bloc.session.isSleep.add(true);
    RepeatCountdown.isLoginPage = true;

    //fix app auto logout when user open etoken pincode
    FastAuth.isShowPinCode = false;

    biometrics = Biometrics();
    biometrics.isDeviceSupported().then((value) {
      if (value) {
        setState(() {
          _showIcon = value;
          _showPreLogin = value &&
              (_bloc.enableBiometrics == true) &&
              (_bloc.currentUser?.isNotEmpty == true);
        });
        if (_showPreLogin == true && widget.autoLogin == true) {
          _loginWithBiometrics();
        }
      }
    });
    _usernameController = TextEditingController()
      ..addListener(() {
        _bloc.usernameSink.add(_usernameController?.text.trim());
      });
    _passwordController = TextEditingController()
      ..addListener(() {
        _bloc.passwordSink.add(_passwordController.text);
      });

    _bloc.streamSubs.add(_bloc.statusStream.listen((status) async {
      final network = await _bloc.getNetworkStatus();
      if (ConnectivityResult.none == network && mounted) {
        DialogUtil.alert(
            context, S.of(context).common_network_connection_error_des,
            title: S.of(context).common_network_connection_error_title);
        return;
      }
      if (status?.error == 'CONNECTION_NOT_SECURE' && mounted) {
        DialogUtil.alert(
            context, S.of(context).common_network_connection_error_des,
            title: S.of(context).common_network_connection_error_title);
        return;
      }
      if (status?.statusCode == 200) {
        if (status?.data == true && mounted) {
          go(context, ProfileChangePassPage(
            onPressDone: () {
              goToHomePage(context);
            },
          ));
        } else {
          if (!mounted) return;
          goToHomePage(context);
        }
      } else if (status?.statusCode ==
              ProfileStatusCode.DEVICE_HAS_NOT_BEEN_REGISTERED &&
          mounted) {
        goPresent(
            context,
            OtpWidget(
              bloc: _bloc,
              title: S.of(context).common_otp_verify_title,
              title2: S.of(context).otp_auth_summary,
              verifyOtp: (value) {
                return _bloc.verifyUserByPhone(
                    _bloc.username!, _bloc.password!, OtpType.LOGIN);
              },
              resendOtp: () => _bloc.resendOtp(username: _bloc.username),
            ));
      } else if (status?.statusCode == ProfileStatusCode.INVALID_TOKEN) {
        DialogUtil.alert(
          context,
          status?.message ?? S.of(context).error_login_biometrics_expires,
          onSubmit: () => _goEnterPassWord(),
        );
      } else if (status?.statusCode == ProfileStatusCode.FORCE_UPDATE_APP) {
        DialogUtil.alert(context, status?.message,
            submit: S.of(context).common_update, onSubmit: () {
          Platform.isAndroid
              ? url_launcher.launchUrl(
                  Uri.parse(
                      'https://play.google.com/store/apps/details?id=com.sunshine.ksbank'),
                  mode: url_launcher.LaunchMode.externalApplication,
                )
              : url_launcher.launchUrl(
                  Uri.parse(
                      'https://apps.apple.com/vn/app/kienlongbank-plus/id1562823941'),
                  mode: url_launcher.LaunchMode.externalApplication,
                );
        });
      } else if (status != null &&
          status.statusCode == ProfileStatusCode.SYSTEM_MAINTENANCE) {
        DialogUtil.showFlushBar(
          context,
          status.message.toString(),
          backgroundColor: const Color(0xFFF04F24),
          duration: null,
          iconFlushBar: const Icon(
            Icons.error,
            color: Colors.white,
          ),
        );
      } else if (status != null) {
        showDialogError(context, response: status);
      }
    }));
    _bloc.streamSubs.add(_bloc.forceUpdateVersionStream.listen((value) {
      if (value.allowAppVersion != null && !value.allowAppVersion!) {
        DialogUtil.alert(context, S.of(context).current_version_app_not_support,
            submit: S.of(context).common_update, onSubmit: () {
          launchWithUrl(value.installAppLink.toString());
        });
      }
    }));
    _initOnesignalOpenHandler();
    _clearCache();
  }

  void setBgPage() {
    try {
      if (!_preferences.getNameThemeIntro().isNullOrEmpty) {
        theme = ThemeDataLocal.fromJson(
          jsonDecode(_preferences.getNameThemeIntro()),
        );
        iconColor = (theme?.iconColor ?? '').isNotEmpty
            ? HexColor.fromHex(theme?.iconColor)
            : null;
      }
    } catch (e) {
      logger.e(e);
    }
  }

  _clearCache() async {
    await ScannerUtils.deleteDirectory(path: BaseBloc.KLB.title);
    await ScannerUtils.createFolder(BaseBloc.KLB.title!);
  }

  _initOnesignalOpenHandler() async {
    OneSignal.Notifications.addClickListener(
        (OSNotificationClickEvent result) async {
      if (result.notification.additionalData != null) {
        final additionalData = result.notification.additionalData;
        logger.t(additionalData);
        if (additionalData == null) return;
        final type = additionalData['type']; // promotion, system;
        if (type == 'promotion') {
          go(
            context,
            const MessageNotificationScreen(
                index: NotificationTabIndex.promotion, isOutside: true),
          );
        } else if (type == 'system') {
          go(
            context,
            const MessageNotificationScreen(
                index: NotificationTabIndex.system, isOutside: true),
          );
        } else if (type == 'module') {
          await _onCheckLuckyMoneyOneSignal(
              additionalData['module'].toString());
        }
      }
    });
  }

  Future<void> _onCheckLuckyMoneyOneSignal(String additionalData) async {
    final data = Injection.injector
        .get<KsbankApiSmartbank>()
        .serializers
        .fromJson<AppModuleDto>(AppModuleDto.serializer, additionalData);
    final module = ModuleModel.fromList(data);
    if (module.icon == TypeModule.LUCKYMONEY &&
        !module.link.isNullOrEmpty &&
        (ModalRoute.of(context)?.isCurrent ?? false)) {
      await _bloc.prefs.setLuckyMoneyInfo(module.link!);
    }
  }

  void _onTapLogo() {
    if (canChangeEnv != true) {
      return;
    }
    _tapCount++;
    if (_tapCount > 5) {
      _tapCount = 0;
    }
    if (_tapCount == 3) {
      setState(() {
        _showProxy = true;
      });
    }
  }

  _goEnterPassWord() {
    if (_showPreLogin == true) {
      setState(() {
        if (mounted) _showPreLogin = false;
      });
    }
  }

  @override
  void dispose() {
    _bloc.dispose();
    _eTokenBloc.dispose();
    if (_usernameController != null) {
      _usernameController!.dispose();
      _usernameController = null;
    }
    _passwordController.dispose();
    _focusNodeUser.dispose();
    _focusNodePass.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: theme?.brightLogo == true
          ? SystemUiOverlayStyle.dark
          : SystemUiOverlayStyle.light,
      child: BackgroundAppBarImage(
        image: (theme?.bgLoginUrl ?? "").isNotEmpty
            ? getImageWidgetFromType(
                theme?.bgLoginUrl,
                ksb_common.getImageTypeFrom(theme?.bgLoginType),
                fit: BoxFit.cover,
                emptyUrl: ImageAssets.svgAssets(
                  ImagesResource.bgLoginNew,
                  fit: BoxFit.cover,
                ),
              )
            : ImageAssets.svgAssets(
                ImagesResource.bgLoginNew,
                fit: BoxFit.cover,
              ),
        imageHeight: MediaQuery.of(context).size.height,
        stream: _bloc.loadingScreenStream,
        child: Container(
          margin: const EdgeInsets.only(top: 20),
          child: Stack(
            children: [
              Positioned(
                left: 0,
                right: 0,
                bottom: 0,
                child: LayoutBuilder(
                  builder: (p0, constraints) {
                    if (MediaQuery.of(context).viewInsets.bottom > 50) {
                      return const SizedBox();
                    }
                    return (theme?.bgLoginUrl ?? "").isNotEmpty
                        ? const SizedBox()
                        : ImageAssets.svgAssets(
                            ImagesResource.img_wave_svg,
                            fit: BoxFit.cover,
                          );
                  },
                ),
              ),
              Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          _logo(),
                          SizedBox(
                            height: _showPreLogin == true ? 47.h : 112.h,
                          ),
                          _showPreLogin == true
                              ? PreLoginWidget(
                                  loginBloc: _bloc,
                                  autoLogin: widget.autoLogin,
                                  onChangeAcc: () => _changeAccountSubmit(),
                                  onLogin: () => _loginWithBiometrics(),
                                  onTransfer: () => _onTransfer(),
                                  onGoMyShop: () => _onGoMyShop(),
                                  onQrScan: () => _onQrTransfer(),
                                  onPay: () => _onPay(),
                                  onCardManagement: () => _onCardManagement(),
                                  textColor: (theme?.textColorBtnLogin ?? "")
                                          .isNotEmpty
                                      ? HexColor.fromHex(
                                          theme?.textColorBtnLogin)
                                      : null,
                                  bgColor: (theme?.bgColorBtnLogin ?? "")
                                          .isNotEmpty
                                      ? HexColor.fromHex(theme?.bgColorBtnLogin)
                                      : null,
                                  iconColor: iconColor,
                                )
                              : _formLogin(),
                          SizedBox(
                            height: MediaQuery.of(context).size.height * 0.14,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: KeyboardVisibilityBuilder(
                  builder: (context, isKeyboardVisible) {
                    return !isKeyboardVisible
                        ? CustomScrollBottom(
                            bloc: _bloc,
                            showPreLogin: _showPreLogin,
                            onGetEToken: () => _onGetEToken(),
                            onScanQrTransfer: () => _onQrTransfer(),
                            onChatSupport: () => _onChatSupport(),
                            onBankNetworking: () {
                              _onBankNetworking();
                            },
                            iconColor: iconColor,
                          )
                        : const SizedBox();
                  },
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildIconNotification() {
    return StreamBuilder<bool>(
        stream: _notificationBloc.hasNotification,
        builder: (context, snapshot) {
          return Visibility(
            visible: snapshot.data == true && _showPreLogin != true,
            child: IconButton(
                icon: ImageAssets.svgAssets(ImagesResource.ic_message_01),
                onPressed: () {
                  go(
                    context,
                    const MessageNotificationScreen(isOutside: true),
                  );
                }),
          );
        });
  }

  Widget _logo() {
    return Padding(
      padding: const EdgeInsets.only(top: 40),
      child: GestureDetector(
        onTap: () {
          _onTapLogo();
        },
        child: (theme?.logoLoginUrl ?? "").isNotEmpty
            ? getImageWidgetFromType(
                theme?.logoLoginUrl,
                ksb_common.getImageTypeFrom(theme?.logoLoginType),
                height: toSp(40),
              )
            : ImageAssets.svgAssets(
                ImagesResource.ic_logo_home_kienlongbank,
                height: toSp(40),
              ),
      ),
    );
  }

  Widget _formLogin() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          StreamBuilder<String>(
              stream: _bloc.currentUserStream,
              builder: (context, snapshot) {
                if (!snapshot.hasData || snapshot.data.isNullOrEmpty) {
                  return const SizedBox();
                }
                return Container(
                  width: double.infinity,
                  padding: const EdgeInsets.only(bottom: 16),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(bottom: 4),
                        child: Text(
                          S.of(context).hello,
                          style: StyleApp.settingStyle(
                            context,
                            color: iconColor ?? Colors.white,
                            fontSize: 14,
                          )?.copyWith(
                            shadows: loginShadow,
                          ),
                        ),
                      ),
                      Row(
                        children: [
                          Text(
                            snapshot.data.toString(),
                            style: StyleApp.settingStyle(
                              context,
                              color: iconColor ?? Colors.white,
                              fontSize: 16,
                            )?.copyWith(
                              shadows: loginShadow,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          StreamBuilder<String>(
                              stream: _bloc.currentUserStream,
                              builder: (context, snapshot) {
                                if (!snapshot.hasData ||
                                    snapshot.data.isNullOrEmpty) {
                                  return const SizedBox();
                                }
                                return SizedBox(
                                  width: 30.w,
                                  child: IconButton(
                                    onPressed: () {
                                      _changeAccountSubmit();
                                    },
                                    constraints: BoxConstraints(maxWidth: 30.w),
                                    padding: EdgeInsets.zero,
                                    icon: ImageAssets.svgAssets(
                                      ImagesResource.ic_icon_back,
                                      color: iconColor,
                                    ),
                                  ),
                                );
                              }),
                        ],
                      )
                    ],
                  ),
                );
              }),
          if (canChangeEnv == true)
            EnvWidget(
              loginBloc: _bloc,
              env: _env,
              showProxy: _showProxy,
              onEnvChange: (value) {
                if (!value.isNullOrEmpty) {
                  cleanText();
                  _bloc.changeEnvironment(value!);
                  _chatBloc.getLiveDepartments();
                  _bloc.addError("");
                }
              },
            ),
          if (!canChangeEnv)
            StreamBuilder<Environment?>(
                stream: _bloc.currentEnvironment,
                builder: (context, snapshot) {
                  if (!snapshot.hasData ||
                      (snapshot.hasData &&
                          snapshot.data!.name == "production")) {
                    return const SizedBox();
                  }
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 16.0),
                    child: Text(
                      snapshot.data!.name.toUpperCase(),
                      style: StyleApp.bodyStyle(context, color: Colors.white),
                    ),
                  );
                }),
          StreamBuilder<String>(
              stream: _bloc.currentUserStream,
              builder: (context, snapshot) {
                if (!snapshot.hasData || snapshot.data.isNullOrEmpty) {
                  return Padding(
                    padding: const EdgeInsets.only(bottom: kPaddingSmall),
                    child: CommonTextFieldContentWidget(
                      key: keyUserName,
                      value: _bloc.username ?? "",
                      focusNode: _focusNodeUser,
                      labelText: S.of(context).common_account_name,
                      labelStyle: StyleApp.textFieldStyle(
                        context,
                        color: (iconColor ?? Colors.white).withOpacity(0.4),
                      ),
                      focusColor: iconColor ?? Colors.white,
                      unFocusColor:
                          (iconColor ?? Colors.white).withOpacity(0.2),
                      valueStyle: StyleApp.bodyStyle(
                        context,
                        color: iconColor ?? Colors.white,
                      ),
                      isMultiCharacter: false,
                      backgroundColor: Colors.white.withOpacity(0.05),
                      onTap: () {
                        _bloc.addError("");
                        keyUserName.currentState?.setErrorText(null);
                      },
                      cursorColor: iconColor ?? Colors.white,
                      labelColor: (iconColor ?? Colors.white).withOpacity(0.4),
                      textController: _usernameController,
                      onSubmitted: (value) {
                        _bloc.addError("");
                      },
                      onChanged: (text) {
                        _bloc.usernameSink.add(text);
                        setState(() {});
                      },
                      contentPadding: kInnerPadding,
                      onProcessSuffix: () => _bloc.usernameSink.add(''),
                      iconColor: const Color(0xFFFFFFFF).withOpacity(0.68),
                    ),
                  );
                } else {
                  return const SizedBox();
                }
              }),
          CommonTextFieldContentWidget(
            key: keyPassword,
            value: _bloc.password ?? "",
            focusNode: _focusNodePass,
            labelText: S.of(context).password,
            labelStyle: StyleApp.textFieldStyle(
              context,
              color: (iconColor ?? Colors.white).withOpacity(0.4),
            ),
            focusColor: iconColor ?? Colors.white,
            unFocusColor: (iconColor ?? Colors.white).withOpacity(0.2),
            valueStyle:
                StyleApp.bodyStyle(context, color: iconColor ?? Colors.white),
            obscureText: _obscureText,
            backgroundColor: Colors.white.withOpacity(0.05),
            cursorColor: iconColor ?? Colors.white,
            labelColor: (iconColor ?? Colors.white).withOpacity(0.4),
            isMultiCharacter: false,
            contentPadding: kInnerPadding,
            onTap: () {
              _bloc.addError("");
              keyPassword.currentState?.setErrorText(null);
            },
            onChanged: (text) {
              _bloc.passwordSink.add(text);
              if (!text.trim().isNullOrEmpty) {
                setState(() {});
              }
            },
            iconButton: !_bloc.password.isNullOrEmpty
                ? Row(
                    children: [
                      InkWell(
                        onTap: () {
                          setState(() {});
                          _bloc.passwordSink.add(null);
                          keyPassword.currentState?.setCleanText();
                        },
                        child: Icon(
                          Icons.cancel_rounded,
                          color: (iconColor ?? Colors.white).withOpacity(0.68),
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        height: 18,
                        width: 1,
                        color: (iconColor ?? Colors.white).withOpacity(0.4),
                      ),
                      const SizedBox(width: 8),
                      InkWell(
                        onTap: () {
                          setState(() {
                            _obscureText = !_obscureText;
                          });
                        },
                        child: Icon(
                          _obscureText
                              ? Icons.visibility
                              : Icons.visibility_off,
                          color: (iconColor ?? Colors.white).withOpacity(0.68),
                          size: 20,
                        ),
                      ),
                    ],
                  )
                : null,
          ),
          SizedBox(height: toSp(kPaddingPage)),
          StreamBuilder<String>(
              stream: _bloc.currentUserStream,
              builder: (context, snapshot) {
                return Row(
                  children: [
                    const Spacer(),
                    InkWell(
                      onTap: () => go(context, const ForgotPassword()),
                      child: Text(
                        S.of(context).forgot_password,
                        style: StyleApp.settingStyle(
                          context,
                          color: iconColor ?? Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: 10),
                    if (!snapshot.hasData || snapshot.data.isNullOrEmpty) ...[
                      Container(
                        width: 1,
                        height: 16,
                        color: (iconColor ?? Colors.white).withOpacity(0.4),
                      ),
                      const SizedBox(width: 10),
                      goPageAnimation(
                        page: const SignUpPage(),
                        onCallBack: (openContainer) => InkWell(
                          onTap: () async {
                            final isDeviceJailbreak =
                                await FlutterJailbreakDetection.jailbroken;
                            if (isDeviceJailbreak == true) {
                              _showAlertJailbreak();
                            } else {
                              openContainer();
                            }
                          },
                          child: Text(
                            S.of(context).common_register,
                            style: StyleApp.settingStyle(
                              context,
                              color: iconColor ?? Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ],
                );
              }),
          SizedBox(height: toSp(kPaddingPage)),
          Padding(
            padding: const EdgeInsets.only(top: 16),
            child: SimpleButtonV1(
              borderRadius: 4,
              title: S.of(context).sign_in,
              color: (theme?.bgColorBtnLogin ?? "").isNotEmpty
                  ? HexColor.fromHex(theme?.bgColorBtnLogin)
                  : const Color(0xFF13C7EE),
              textColor: (theme?.textColorBtnLogin ?? "").isNotEmpty
                  ? HexColor.fromHex(theme?.textColorBtnLogin)
                  : Colors.white,
              onPressed: () async {
                FocusScopeNode currentFocus = FocusScope.of(context);
                if (!currentFocus.hasPrimaryFocus) {
                  currentFocus.unfocus();
                }
                final isDeviceJailbreak =
                    await FlutterJailbreakDetection.jailbroken;

                if (isDeviceJailbreak == true) {
                  _showAlertJailbreak();
                } else {
                  _login();
                }
              },
            ),
          ),
          // TextSupportApp(bloc: _bloc), // update app từ app cũ sang app mới
          SizedBox(height: toSp(36)),
          _showIcon
              ? StreamBuilder<String>(
                  stream: _bloc.currentUserStream,
                  builder: (context, snapshot) {
                    if (!snapshot.hasData || snapshot.data.isNullOrEmpty) {
                      return SizedBox(
                        height: MediaQuery.of(context).size.height * 0.1,
                      );
                    }
                    return Column(
                      children: [
                        GestureDetector(
                          onTap: _loginWithBiometrics,
                          child: (theme?.faceIdImageUrl ?? "").isEmpty
                              ? Container(
                                  padding: const EdgeInsets.all(9),
                                  width: 40,
                                  height: 40,
                                  decoration: BoxDecoration(
                                    color: const Color(0xFF292663).withOpacity(
                                      (theme?.bgLoginUrl ?? "").isNotEmpty
                                          ? 0.4
                                          : 1,
                                    ),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: ImageAssets.svgAssets(
                                    ImagesResource.icLoginFaceId,
                                  ),
                                )
                              : getImageWidgetFromType(
                                  theme?.faceIdImageUrl,
                                  ksb_common
                                      .getImageTypeFrom(theme?.faceIdImgType),
                                  height: 40,
                                  width: 40,
                                ),
                        ),
                        SizedBox(height: toSp(kPaddingPage)),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 15),
                          child: Text(
                            S.of(context).sign_in_faceid,
                            style: StyleApp.settingStyle(
                              context,
                              color: iconColor ?? Colors.white,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        )
                      ],
                    );
                  },
                )
              : const SizedBox(),
        ],
      ),
    );
  }

  _login() {
    if (_bloc.username.isNullOrEmpty) {
      keyUserName.currentState
          ?.setErrorText(S.of(context).error_please_input_username);
    }
    if (_bloc.password.isNullOrEmpty) {
      _bloc.addError("");
      keyPassword.currentState
          ?.setErrorText(S.of(context).error_please_input_password);
    }
    if (!_bloc.username.isNullOrEmpty && !_bloc.password.isNullOrEmpty) {
      if (_bloc.validatePassword(_bloc.password!)) {
        DialogUtil.confirm(
          context,
          RichText(
            textAlign: Platform.isIOS ? TextAlign.center : TextAlign.start,
            text: TextSpan(
              style: StyleApp.bodyText2(context)?.copyWith(
                color: DynamicTheme.of(context)?.customColor.black,
              ),
              children: [
                TextSpan(
                    text: '${S.of(context).error_pass_contain_tiengviet} '),
                TextSpan(
                  text: S.of(context).next,
                  style: StyleApp.bodyText2(context)?.copyWith(
                    color: DynamicTheme.of(context)?.customColor.black,
                    fontWeight: FontWeight.w600,
                  ),
                )
              ],
            ),
          ),
          submitText: S.of(context).next,
          onSubmit: () => _bloc.login(),
        );
      } else {
        _bloc.login();
      }
    }
  }

  _onBio() {
    if (_bloc.enableBiometrics == true) {
      _loginWithBiometrics();
    } else if (mounted) {
      DialogUtil.alert(
        context,
        S.of(context).please_login_continue,
        title: S.of(context).notification,
        submit: S.of(context).close,
      );
    }
  }

  _onTransfer() {
    _bloc.prefs.setModuleTypeFromLogin(TypeModule.TRANSFER);
    _onBio();
  }

  _onGoMyShop() {
    _bloc.prefs.setModuleTypeFromLogin(TypeModule.MYSHOP);
    _onBio();
  }

  _onQrTransfer() async {
    if (!mounted) return;
    final result = await popUpPage(const QrScanner(typecall: 1), context);
    if (Platform.isAndroid) {
      await Future.delayed(const Duration(milliseconds: 1000));
    }
    if (ValidatorQr.checkVietQr(qrCode: result ?? '')) {
      _onBio();
    }
  }

  _onPay() {
    _bloc.prefs.setModuleTypeFromLogin(TypeModule.BILL);
    _onBio();
  }

  _onCardManagement() {
    _bloc.prefs.setModuleTypeFromLogin(TypeModule.CARD);
    _onBio();
  }

  _onGetEToken() async {
    final hasEToken = await _bloc.hasEToken();
    if (!hasEToken && context.mounted) {
      DialogUtil.alert(
        context,
        S.of(context).error_turn_on_setting_app_msg,
      );
    } else {
      goPresent(context, const InputEtoken());
    }
  }

  _onChatSupport() {
    showCupertinoModalPopup(
      context: context,
      builder: (context) {
        return CupertinoActionSheet(
          actions: [
            CupertinoActionSheetAction(
              onPressed: () {
                Navigator.of(context).pop();
                final Uri phoneUrl = Uri(scheme: 'tel', path: '19006929');
                url_launcher.launchUrl(
                  Uri.parse(phoneUrl.toString()),
                );
              },
              child: const Text('19006929'),
            ),
            CupertinoActionSheetAction(
              onPressed: () {
                GlobalCallback.instance.onChatOpen!(ChatModel(
                  openLiveChat: true,
                  stage: StageCallLog.SUPPORT_USER,
                ));
              },
              child: const Text('Chat với tư vấn viên 24/7'),
            ),
          ],
          cancelButton: CupertinoActionSheetAction(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: Text(S.of(context).common_cancel),
          ),
        );
      },
    );
  }

  _onBankNetworking() {
    if (_bloc.currentUser?.isNotEmpty == true) {
      go(context, const NetworkTabbar(isLogin: false));
    } else {
      DialogUtil.alert(
        context,
        "Bạn cần đăng nhập để sử dụng chức năng này.",
      );
    }
  }

  cleanText() {
    keyPassword.currentState?.setErrorText(null);
    keyPassword.currentState?.setCleanText();
    keyUserName.currentState?.setErrorText(null);
    keyUserName.currentState?.setCleanText();
    _bloc.passwordSink.add(null);
    _bloc.usernameSink.add(null);
  }

  _changeAccountSubmit() {
    if (!mounted) return;
    DialogUtil.confirm(
      context,
      Text(S.of(context).change_user_account_msg),
      title: S.of(context).change_user_account_title,
      onSubmit: () async {
        final hasEToken = await _bloc.hasEToken();
        if (hasEToken && mounted) {
          final value = await goPresent<String>(
            context,
            const EtokenPinCode(isSubmit: true),
          );
          if (!value.isNullOrEmpty) {
            final result = await _eTokenBloc.removeEtoken(value!);
            if (result != null && result == true) {
              _logout();
            }
          }
        } else {
          _logout();
        }
      },
    );
  }

  _logout() {
    _bloc.logout().then((data) {
      if (data) {
        cleanText();
        setState(() {
          _showPreLogin = false;
          if (theme?.defaultTheme == true) {
            theme = null;
            iconColor = null;
            ksbCommonProperties = null;
          }
        });
      }
    });
  }

  _loginWithBiometrics() async {
    _bloc.session.isSleep.add(true);
    if ((await SecurityUtil.getNonSystemAccessibilityApps()).isNotEmpty) return;
    if (_bloc.enableBiometrics!) {
      final isAvailable = await biometrics.isBiometricAvailable();
      if (!isAvailable && mounted) {
        DialogUtil.alert(
          context,
          S.of(context).login_bio_not_available,
          submit: S.of(context).agree,
          onSubmit: () => _goEnterPassWord(),
        );
        return;
      }

      if (!mounted) return;
      await biometrics.authenticateUser(
        context,
        onHandler: () async {
          _bloc.refreshToken();
        },
        onError: () async {
          countId++;
          if (countId == 3) {
            DialogUtil.alert(
              context,
              S.of(context).error_faild_login,
              submit: S.of(context).agree,
              onSubmit: () => _goEnterPassWord(),
            );
            countId = 0;
          }
        },
        onCancel: () => _goEnterPassWord(),
      );
    } else {
      DialogUtil.alert(
        context,
        S.of(context).device_no_login,
        title: S.of(context).error_feature_not_activated_title,
        onSubmit: () => _goEnterPassWord(),
      );
    }
  }

  _showAlertJailbreak() {
    DialogUtil.alert(
      context,
      S.of(context).error_device_can_not_used_msg,
    );
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _bloc.checkForceUpdateApp();
    }
    super.didChangeAppLifecycleState(state);
  }

  @override
  bool get wantKeepAlive => true;
}
