import 'package:common/utils/dialog_util.dart';
import 'package:common/widgets/bottom_button.dart';
import 'package:common/widgets/bottom_sheet.dart';
import 'package:common/widgets/common_dropdown.dart';
import 'package:common/widgets/common_textfield.dart';
import 'package:common/widgets/style_app.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_common/shared/assets.dart' as klbCommon;
import 'package:ksb_common/shared/widgets/auth_transaction_mixin_common.dart';
import 'package:ksb_common/shared/theme/custom_theme.dart';
import 'package:ksb_common/shared/widgets/background_app.dart';
import 'package:mobile_banking/assets/assets.dart';
import 'package:mobile_banking/assets/images.dart';
import 'package:mobile_banking/di/inject.dart';
import 'package:mobile_banking/generated/l10n.dart';
import 'package:mobile_banking/navigator.dart';
import 'package:mobile_banking/ui_v2/transfer/row_choose_account.dart';
import 'package:mobile_banking/ui_v2/transfer/widgets/common_textfield_money.dart';
import 'package:mobile_banking/ui_v2/widget/constant.dart';
import 'package:mobile_banking/ui_v2/widget/loading/loading_view.dart';
import 'package:mobile_banking/utils/auth_fatsh.dart';
import 'package:mobile_banking/utils/navigator.dart';
import 'package:mobile_banking/utils/otp_utils.dart';

import 'package:mobile_banking/ui_v2/profile_account/etoken/etoken_setup.dart';
import '../transfer/transfer_progress_page.dart';
import '../transfer/transfer_schedule.dart';
import '../transfer/transfer_success.dart';

class TransferTuitionFeePage extends StatefulWidget {
  final CommonPayment? model;

  const TransferTuitionFeePage({Key? key, this.model}) : super(key: key);

  @override
  State<TransferTuitionFeePage> createState() => _TransferTuitionFeePageState();
}

class _TransferTuitionFeePageState
    extends FlowTransactionState<TransferTuitionFeePage>
    with AuthTransactionMixin, AuthTransactionConfirmMixin {
  final _transferTuitionBloc = Injection.injector.get<TransferTuitionFeeBloc>();

  final _blocAuth = Injection.injector.get<AuthenTransactionBloc>();
  final _transferBloc = Injection.injector.get<TransferBloc>();
  final FocusNode _moneyFocusCode = FocusNode();
  final keyMoney = GlobalKey<CommonTextFieldMoneyState>();

  final TextEditingController _controllerAccountNumber =
      TextEditingController();
  final _controllerAmount = TextEditingController();

  AccountModel? _selectedAccount;
  bool _isLoadingAccount = true;
  ProviderDto? _selectedSchool;
  TuitionFeeDto? _selectedTuitionFee;

  @override
  void initState() {
    super.initState();
    _transferTuitionBloc.getSchoolProviders();

    _transferTuitionBloc.streamSubs.add(
      _transferTuitionBloc.statusStream.listen((event) {
        if (event != null) {
          listenError(event.convertModelToCommon());
        }
      }),
    );
  }

  @override
  void dispose() {
    _transferTuitionBloc.dispose();
    _controllerAccountNumber.dispose();
    _controllerAmount.dispose();
    super.dispose();
  }

  void _payTuitionFee() async {
    if (_isInvalidForm) return;
    try {
      _transferTuitionBloc.showLoadingScreen();
      await _transferBloc.mergeModel(
        TransferModel(
          is247: true,
          categoryId: 4,
          targetBankIdNapas: BaseBloc.KLB.id,
          sourceAccountNumber: _selectedAccount!.accountNumber!,
          sourceAccountName: _selectedAccount!.customerName,
          targetAccountNumber: _selectedTuitionFee!.virtualAccount,
          amount: _selectedTuitionFee!.totalAmount!.toDouble(),
          note: _selectedTuitionFee!.message,
        ),
      );
      setPassAuthFaceID(false);
      await _transferBloc.reviewTransfer();
      if ((_transferBloc.model?.transactionNumber ?? '').isNullOrEmpty) {
        _transferTuitionBloc.completeLoadingScreen();
        return;
      }
      _transferTuitionBloc.completeLoadingScreen();
      onGoToConfirm(
        nextToConfirmPage: nextToConfirmPage,
        nextStep: _transferBloc.step,
        message: _transferBloc.messageDialog,
      );
    } on DioException catch (dioError) {
      _transferTuitionBloc.completeLoadingScreen();
      final error = dioError.error;
      if (error is Map) {
        DialogUtil.alert(
            context, '${error['message']} (${error['code'] ?? '---'})');
      } else {
        DialogUtil.alert(context, error.toString());
      }
    } catch (error) {
      _transferTuitionBloc.completeLoadingScreen();
      DialogUtil.alert(context, error.toString());
    }
  }

  nextToConfirmPage() async {
    if (!passAuthFaceID) {
      await authByFaceID(
        showFaceID: _transferBloc.showFaceID,
        transactionNo: _transferBloc.transactionNumber,
      );
    }
    if (!passAuthFaceID) return;

    final hasEToken = await _transferBloc.hasEToken();
    if (hasEToken) {
      final eToken = await _blocAuth.getetoken();
      final secret = OtpUtils.generateSecretAdvanceOtp(
        cifNumber: _selectedAccount!.cifNumber,
        sourceAccountNumber: _selectedAccount!.accountNumber,
        amount: _selectedTuitionFee!.totalAmount,
        targetBankCode: BaseBloc.KLB.id,
        targetAccountNumber: _selectedTuitionFee!.virtualAccount,
        transactionNumber: _transferBloc.model?.transactionNumber,
        secretKey: eToken,
      );
      if (!mounted) return;
      final softOtp = await FastAuth().checkAuth(
        context,
        _transferBloc.model?.amount ?? 0,
        eToken,
        secret: secret,
      );
      if (!softOtp.isNullOrEmpty) {
        _transferBloc.setSoftOtp(softOtp);
        await _transferBloc.startTransfer();
        if (_transferBloc.model?.advanceTransaction != null && mounted) {
          go(
              context,
              TransferProgressPage(
                  transactionId: _transferBloc.model?.advancedTransactionId,
                  transferModel: _transferBloc.model,
                  onConfirm: (data) {
                    go(
                      context,
                      TransferSuccess(
                        model: _transferBloc.model,
                        bigModel: data,
                        onPressNew: _onNewTransaction,
                        onPressDone: () => goToHomePage(context, popOnly: true),
                        onPressCreateSchedule: _openSchedulePage,
                        onPressHome: () => goToHomePage(context, popOnly: true),
                      ),
                    );
                  }));
        } else {
          go(
            context,
            TransferSuccess(
              model: _transferBloc.model,
              onPressNew: _onNewTransaction,
              onPressDone: () => goToHomePage(context, popOnly: true),
              onPressCreateSchedule: _openSchedulePage,
              onPressHome: () => goToHomePage(context, popOnly: true),
            ),
          );
        }
      }
    } else if (mounted) {
      goToSetupToken(context);
    }
  }

  void _onNewTransaction() {
    _controllerAccountNumber.clear();
    _transferTuitionBloc.resetTransaction();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {});
    });
  }

  _openSchedulePage() {
    go(
      context,
      TransferScheduleV2(
        schedule: _transferBloc.model?.schedule,
        onScheduleChange: (schedule) {
          Navigator.of(context).pop();
          _transferBloc.setSchedule(schedule);
        },
      ),
    );
  }

  void _openSchoolList(List<ProviderDto>? data) async {
    if (data == null || data.isEmpty) return;
    final newItem = await goPresent(
        context,
        ListSchoolProviderWidget(
          items: data,
          selectedItem: _selectedSchool,
        ));
    if (newItem is ProviderDto) {
      _selectedSchool = newItem;
    }
  }

  bool get _isInvalidForm =>
      _isLoadingAccount == true ||
      _selectedAccount == null ||
      _selectedSchool == null ||
      _selectedTuitionFee == null;

  @override
  Widget build(BuildContext context) {
    return LoadingView(
        loadingStream: _transferTuitionBloc.progressVisible,
        child: BackgroundAppBarImage(
          image: klbCommon.ImageAssets.img_appbar,
          appBar: getAppBarDark(
            context,
            title: 'Nộp học phí',
          ),
          backgroundColor: Theme.of(context).cardColor,
          child: _body,
        ));
  }

  Widget get _body => Container(
        color: Theme.of(context).cardColor,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 10),
                    ChooseAccountCard(
                      onSelectAccount: (account) {
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          setState(() {
                            _selectedAccount = account;
                            _isLoadingAccount = false;
                          });
                        });
                      },
                      accountNumberSelected: _selectedAccount?.accountNumber,
                    ),
                    const SizedBox(height: 14),
                    Container(
                      height: 8,
                      color: DynamicTheme.of(context)?.customColor.dividerColor,
                    ),
                    _paymentInfo,
                  ],
                ),
              ),
            ),
            BottomButton(
              title: S.of(context).common_continue,
              isDisable: _moneyFocusCode.hasFocus || _isInvalidForm,
              onTap: _payTuitionFee,
            )
          ],
        ),
      );

  // Payment Info
  Widget get _paymentInfo => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: kPaddingStandard,
            child: Text(
              'Thông tin chuyển khoản',
              style: StyleApp.subtitle1(context, true),
            ),
          ),
          StreamBuilder<List<ProviderDto>>(
            stream: _transferTuitionBloc.schoolProvidersStream,
            builder: (context, snapshot) {
              final listSchool = snapshot.data;
              if (_selectedSchool == null &&
                  (listSchool?.isNotEmpty ?? false)) {
                _selectedSchool = listSchool?.first;
              }
              return CommonDropdown(
                leadingIconInValue: true,
                margin: const EdgeInsets.symmetric(horizontal: 16.0),
                value: _selectedSchool?.providerName,
                title: 'Trường',
                leadingIcon: ImageAssets.svgAssets(ImagesResource.icTuitionFee),
                onTap: () {
                  _openSchoolList(listSchool ?? []);
                },
                isLoading: listSchool == null,
              );
            },
          ),
          CommonTextField(
            focusNode: _moneyFocusCode,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            labelText: 'Mã học viên',
            textController: _controllerAccountNumber,
            maxLength: 20,
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp("[0-9a-zA-Z]")),
            ],
            onSubmitted: (value) {
              if (value.isNotEmpty && _selectedSchool != null) {
                _transferTuitionBloc.getStudentInfo(
                    providerCode: _selectedSchool!.providerCode!,
                    studentCode: value);
              }
            },
            onTap: () {
              setState(() {});
            },
            onBlur: (value) {
              if (value.isNotEmpty && _selectedSchool != null) {
                _transferTuitionBloc.getStudentInfo(
                    providerCode: _selectedSchool!.providerCode!,
                    studentCode: value);
              }
            },
            onChanged: (value) {
              setState(() {});
            },
            checkNull: (value) {
              return S.of(context).please_enter_account_number;
            },
            suffixEnabled: true,
            suffix: _controllerAccountNumber.text.isNotEmpty == true
                ? IconButton(
                    onPressed: () => _controllerAccountNumber.text = '',
                    icon: Icon(
                      Icons.cancel_rounded,
                      color: StyleApp.captionColor(context),
                    ),
                  )
                : null,
          ),
          _resultTuitionInfo
        ],
      );

  Widget get _resultTuitionInfo => StreamBuilder<TuitionFeeDto?>(
      stream: _transferTuitionBloc.studentInfoStream,
      builder: (context, snapshot) {
        if (snapshot.hasData && snapshot.data != null) {
          TuitionFeeDto tuitionFeeInfo = snapshot.data!;
          WidgetsBinding.instance.addPostFrameCallback((_) {
            setState(() {
              _selectedTuitionFee = tuitionFeeInfo;
            });
          });
          return Column(
            children: [
              CommonTextField(
                enabled: false,
                labelText: 'Tên học viên',
                padding: kHorizontalPaddingStandard,
                labelStyle: StyleApp.bodyStyle(context),
                backgroundColor:
                    DynamicTheme.of(context)?.customColor.bgTextFieldGrey,
                value: tuitionFeeInfo.studentName,
              ),
              SizedBox(
                height: kSmallPadding,
              ),
              CommonTextFieldMoneyWidget(
                enabled: false,
                labelText: S.of(context).saving_add_money_label_amount,
                margin: kHorizontalPaddingStandard,
                labelStyle: StyleApp.bodyStyle(context),
                backgroundColor:
                    DynamicTheme.of(context)?.customColor.bgTextFieldGrey,
                valueText: tuitionFeeInfo.totalAmount?.currencyFormat,
              ),
              SizedBox(
                height: kSmallPadding,
              ),
              CommonTextField(
                enabled: false,
                labelText: S.of(context).profile_consult_label_content,
                padding: kHorizontalPaddingStandard,
                labelStyle: StyleApp.bodyStyle(context),
                backgroundColor:
                    DynamicTheme.of(context)?.customColor.bgTextFieldGrey,
                value: tuitionFeeInfo.message,
              )
            ],
          );
        }
        WidgetsBinding.instance.addPostFrameCallback((_) {
          setState(() {
            _selectedTuitionFee = null;
          });
        });
        return const SizedBox.shrink();
      });
}

class ListSchoolProviderWidget extends StatelessWidget {
  final List<ProviderDto>? items;
  final ProviderDto? selectedItem;

  const ListSchoolProviderWidget({
    Key? key,
    this.items,
    this.selectedItem,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    double hAppbar = MediaQuery.of(context).padding.top + kToolbarHeight;
    return Container(
      margin: EdgeInsets.only(top: hAppbar),
      alignment: Alignment.bottomCenter,
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: const BorderRadius.all(Radius.circular(8.0)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const BottomSheetHeader(title: 'Chọn Trường học'),
            Expanded(
                child: ListView.separated(
              itemCount: items?.length ?? 0,
              separatorBuilder: (context, index) =>
                  const Divider(color: Colors.transparent),
              itemBuilder: (context, index) {
                final item = items![index];
                return ListTile(
                  onTap: () => Navigator.of(context).pop(item),
                  leading: ImageAssets.svgAssets(
                    ImagesResource.icTuitionFee,
                    height: 40,
                  ),
                  title: Text(item.providerName ?? ''),
                  subtitle: Text(item.providerCode ?? ''),
                  trailing: selectedItem?.providerCode == item.providerCode
                      ? Icon(
                          Icons.check_rounded,
                          color: Theme.of(context).primaryColor,
                        )
                      : null,
                );
              },
            ))
          ],
        ),
      ),
    );
  }
}
