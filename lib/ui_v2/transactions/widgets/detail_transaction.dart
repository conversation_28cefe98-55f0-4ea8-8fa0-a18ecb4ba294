import 'package:common/utils/dialog_util.dart';
import 'package:common/widgets/bottom_2_button.dart';
import 'package:flutter/material.dart';
import 'package:ksb_bloc/bloc/account/transaction_detail_bloc.dart';
import 'package:ksb_bloc/bloc/model/account/transaction_model.dart';
import 'package:ksb_bloc/bloc/model/bill/bill_service.dart';
import 'package:ksb_bloc/bloc/transfer/transfer_bloc.dart';
import 'package:ksb_bloc/utils/extension/string.dart';
import 'package:ksb_common/shared/assets.dart' as klb_common;
import 'package:ksb_common/shared/widgets/background_app.dart';
import 'package:mobile_banking/assets/dimens.dart';
import 'package:mobile_banking/di/inject.dart';
import 'package:mobile_banking/generated/l10n.dart';
import 'package:mobile_banking/ui_v2/share_bill/share_bill_page.dart';
import 'package:mobile_banking/ui_v2/transactions/widgets/account_from.dart';
import 'package:mobile_banking/ui_v2/transactions/widgets/account_to.dart';
import 'package:mobile_banking/ui_v2/transactions/widgets/header_transaction.dart';
import 'package:mobile_banking/ui_v2/transfer/transfer_accept.dart';
import 'package:mobile_banking/ui_v2/widget/loading/loading.dart';
import 'package:mobile_banking/ui_v2/widget/row_field_widget.dart';

import '../../../assets/assets.dart';
import '../../../model/bottom_sheet_model.dart';
import '../../../route.dart';
import '../../../utils/navigator.dart';
import '../../pay/v2/bill_service_page_v2.dart';

class DetailTransaction extends StatefulWidget {
  final TransactionModel? model;
  final VoidCallback? onPressDone;
  final bool isDetailBigTransaction;
  final Widget? Function(TransactionModel data)? onBottomButton;

  const DetailTransaction({
    Key? key,
    this.model,
    this.onPressDone,
    this.isDetailBigTransaction = false,
    this.onBottomButton,
  }) : super(key: key);

  @override
  State<DetailTransaction> createState() => _DetailTransactionState();
}

class _DetailTransactionState extends State<DetailTransaction> {
  final _bloc = Injection.injector.get<TransactionDetailBloc>();

  @override
  void initState() {
    super.initState();
    _bloc.streamSubs.add(_bloc.errorStream.listen((error) {
      if (error?.isNotEmpty == true) {
        DialogUtil.alert(context, error);
      }
    }));
    _bloc.setData(widget.model,
        isDetailBigTransaction: widget.isDetailBigTransaction);
  }

  @override
  void dispose() {
    super.dispose();
    _bloc.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return BackgroundAppBarImage(
      image: ImageAssets.svgAssets(
        klb_common.ImageAssets.img_appbar,
        fit: BoxFit.fill,
        width: MediaQuery.of(context).size.width,
      ),
      backgroundColor: Theme.of(context).cardColor,
      appBar: getAppBarDark(
        context,
        title: S.of(context).account_transact_detail_title_detail,
      ),
      child: StreamBuilder<TransactionModel?>(
        stream: _bloc.transactionStream,
        builder: (context, snapshot) {
          if (snapshot.hasData && snapshot.data != null) {
            final transaction = snapshot.data;
            return Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        HeaderDetailTransaction(model: transaction),
                        _buildInfoTransaction(transaction),
                        const Divider(height: 8, thickness: 8),
                        // _separationInfo(),
                        // const Divider(height: 8, thickness: 8),
                        _buildDetailTransaction(size, transaction),
                        const SizedBox(height: 50),
                      ],
                    ),
                  ),
                ),
                if (widget.onBottomButton != null)
                  widget.onBottomButton?.call(transaction!) ?? Container()
                else if (!widget.isDetailBigTransaction)
                  _onSubmit(transaction!)
              ],
            );
          }
          return Center(child: ring);
        },
      ),
    );
  }

  _gotoShareBill() async {
    if (widget.model != null) {
      await go(context, ShareBillPage(model: widget.model!));
    }
  }

  Widget _onSubmit(TransactionModel data) {
    if (data.coefficient == -1) {
      if (data.description?.endsWith(data.accountNo ?? '') == false &&
          data.transactionTypeName
                  ?.endsWith(S.of(context).goal.toLowerCase()) ==
              false &&
          data.partnerAccountNo?.isNullOrEmpty == false &&
          data.transactionTypeId != 7) {
        return Bottom2Button(
          title1: S.of(context).account_transact_detail_button_share_bill,
          title2: S.of(context).account_transact_detail_button_redo,
          onTapTitle1: () {
            _gotoShareBill();
          },
          onTapTitle2: () {
            goToTransferAcceptV2(
                context,
                TransferModel.fromTransaction(data).copyWith(
                  amount: data.amount?.abs(),
                ),
                onPressDone: () => widget.onPressDone?.call());
          },
          colorTextButton1: const Color(0xFF292663),
          colorButton1: const Color(0xFFF4F4F7),
        );
      } else if (data.supplierId?.isNullOrEmpty == false &&
          data.customerCode?.isNullOrEmpty == false) {
        return Bottom2Button(
          title1: S.of(context).account_transact_detail_button_share_bill,
          title2: S.of(context).account_transact_detail_button_redo,
          onTapTitle1: () {
            _gotoShareBill();
          },
          onTapTitle2: () {
            BillService model = BillService(
                id: data.serviceId, name: data.serviceName, icon: "");
            go(
                context,
                BillServicePageV2(
                  service: model,
                  initialRoute: RoutePaths.home,
                ));
          },
          colorTextButton1: const Color(0xFF292663),
          colorButton1: const Color(0xFFF4F4F7),
        );
      } else if (data.phoneCardValue != null &&
          data.phoneCardValue! > 0 &&
          data.partnerAccountName?.isNullOrEmpty == false) {
        return Bottom2Button(
          title1: S.of(context).account_transact_detail_button_share_bill,
          title2: S.of(context).account_transact_detail_button_redo,
          onTapTitle1: () {
            _gotoShareBill();
          },
          onTapTitle2: () {
            // go(
            //   context,
            //   MobileTopUpConfirm(
            //     accountNo: data?.accountNo,
            //     phoneNumber: data?.partnerAccountName,
            //     cardValue: data?.phoneCardValue,
            //     cardPrice: data.amount.ceil(),
            //     account:AccountModel(accountName:data.accountName,accountNumber: data.accountNo ),
            //     onPressDone: () => widget.onPressDone?.call(),
            //   ),
            // );

            goTransferMoneyV2(context, TransferType.recharge);
          },
          colorTextButton1: const Color(0xFF292663),
          colorButton1: const Color(0xFFF4F4F7),
        );
      }
    }
    return Container();
  }

  _buildInfoTransaction(TransactionModel? model) {
    return Padding(
      padding: const EdgeInsets.only(
        top: kPaddingPage,
        left: kPaddingPage,
        right: kPaddingPage,
        bottom: kInnerPadding,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          AccountFrom(model: model),
          if (model?.partnerAccountNo != null &&
              model?.partnerAccountNo?.isNotEmpty == true) ...[
            const Padding(
              padding: EdgeInsets.symmetric(
                vertical: kPaddingMedium,
              ),
              child: Divider(),
            ),
            AccountTo(
                title: model?.coefficient == 1
                    ? S.of(context).saving_open_success_label_from_account
                    : S.of(context).go_to_account,
                name: model?.partnerAccountName,
                accountNo: model?.partnerAccountNo,
                bankName: model?.partnerBankName,
                urlIcon: model?.partnerBankUrl),
          ],
          if (model?.supplierId?.isNullOrEmpty == false ||
              model?.customerCode?.isNullOrEmpty == false) ...[
            const Padding(
              padding: EdgeInsets.symmetric(
                vertical: kPaddingMedium,
              ),
              child: Divider(),
            ),
            AccountTo(
                title: S.of(context).card_pay_card_label_pay_for,
                name: model?.partnerAccountName,
                urlIcon: model?.serviceIconUrl)
          ],
          const SizedBox(height: 5),
        ],
      ),
    );
  }

  _buildDetailTransaction(Size size, TransactionModel? model) {
    return Column(
      children: [
        RowFieldWidget(
          title: S.of(context).saving_open_success_label_id_transact,
          value: model?.refTransactionNo,
        ),
        const Divider(height: 1, indent: 16, endIndent: 16),
        if (model?.description?.isNullOrEmpty == false)
          RowFieldWidget(
            title: S.of(context).account_transfer_request_label_content,
            isExpandedRight: true,
            value: model?.description,
          ),
        if (model?.description?.isNullOrEmpty == false)
          const Divider(height: 1, indent: 16, endIndent: 16),
        RowFieldWidget(
          title: S.of(context).profile_success_trans_label_date_transaction,
          value: model?.transactionAt.formatDMYHHmm,
        ),
        const Divider(height: 1, indent: 16, endIndent: 16),
        RowFieldWidget(
          title: S.of(context).account_statement_title_transact_type,
          value: model?.transactionTypeName,
        ),
        const Divider(height: 1, indent: 16, endIndent: 16),
        RowFieldWidget(
          title: S.of(context).deposit_certificate_detail_label_state,
          value: Container(
            color: model?.statusBgColor,
            child: Text(
              model?.statusName ?? '',
              style: Theme.of(context)
                  .textTheme
                  .titleMedium
                  ?.copyWith(color: model?.statusColor),
              textAlign: TextAlign.end,
            ),
          ),
        ),
      ],
    );
  }
}
