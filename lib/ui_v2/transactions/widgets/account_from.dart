import 'package:ksb_bloc/bloc/model/account/transaction_model.dart';
import 'package:common/widgets/style_app.dart';
import 'package:flutter/material.dart';
import 'package:mobile_banking/assets/assets.dart';
import 'package:mobile_banking/assets/dimens.dart';
import 'package:mobile_banking/assets/images.dart';
import 'package:mobile_banking/generated/l10n.dart';

class AccountFrom extends StatelessWidget {
  final TransactionModel? model;
  final String? label;

  const AccountFrom({Key? key, this.model, this.label}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        SizedBox(
          width: 40,
          height: 40,
          child: ImageAssets.svgAssets(
            ImagesResource.ic_logo,
          ),
        ),
        const SizedBox(width: kInnerPadding, height: 0.0),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              label ??
                  (model?.coefficient == 1
                      ? S.of(context).go_to_account
                      : S.of(context).saving_open_success_label_from_account),
              style: StyleApp.descStyle(context),
            ),
            const SizedBox(width: 0.0, height: 4),
            Text(
              model?.accountName ?? "",
              style: StyleApp.bodyText1(context, true),
            ),
            const SizedBox(width: 0.0, height: 4),
            Text(
              model?.accountNo ?? "",
              style: StyleApp.descStyle(context),
            ),
            const SizedBox(width: 0.0, height: 4),
            Text(
              S.of(context).bank_kienlong_name,
              style: StyleApp.descStyle(context),
            ),
          ],
        )
      ],
    );
  }
}
