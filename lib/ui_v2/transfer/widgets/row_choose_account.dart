import 'package:ksb_bloc/bloc.dart';
import 'package:common/widgets/bottom_sheet_widget.dart';
import 'package:common/widgets/common_dropdown.dart';
import 'package:common/widgets/loading/loading_card.dart';
import 'package:flutter/material.dart';
import 'package:mobile_banking/assets/dimens.dart';
import 'package:mobile_banking/di/inject.dart';
import 'package:mobile_banking/generated/l10n.dart';
import 'package:mobile_banking/model/enum_type.dart';
import 'package:mobile_banking/ui_v2/account/account_source_widget.dart';
import 'package:mobile_banking/ui_v2/account_detail/account_all_page.dart';
import 'package:mobile_banking/ui_v2/home/<USER>';
import 'package:mobile_banking/utils/navigator.dart';
import 'package:collection/collection.dart';

class RowChooseAccount extends StatefulWidget {
  final String? accountNumberSelected;
  final ValueChanged<AccountModel>? onSelectAccount;
  final bool? selectMin;
  final String? title;
  final Function? currentNoSelectedAccount;
  final Function? onTap;
  final bool inRequestTransfer;
  final bool qrCodeOpenCard;
  const RowChooseAccount({
    Key? key,
    this.accountNumberSelected,
    this.onSelectAccount,
    this.selectMin,
    this.title,
    this.currentNoSelectedAccount,
    this.onTap,
    this.inRequestTransfer = false,
    this.qrCodeOpenCard = false,
  }) : super(key: key);

  @override
  State<RowChooseAccount> createState() => _RowChooseAccountState();
}

class _RowChooseAccountState extends State<RowChooseAccount> {
  final _bloc = Injection.injector.get<ListAccountsBloc>();
  final _env = Injection.injector.get<Environment>();
  var _firstInit = true;
  String? titleTemp;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<AccountModel>>(
      stream: _bloc.items,
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const LoadingCard(height: 90);
        }
        final accounts = snapshot.data ?? [];
        var lastOrDefaultAccount = _bloc.selectedItem;
        AccountModel? currentAccount;
        logger.t("${widget.accountNumberSelected}");
        if (!widget.accountNumberSelected.isNullOrEmpty) {
          currentAccount = accounts.firstWhereOrNull(
            (element) => element.accountNumber == widget.accountNumberSelected,
          );
        }

        if (currentAccount == null && lastOrDefaultAccount != null) {
          widget.onSelectAccount?.call(lastOrDefaultAccount);
          currentAccount = lastOrDefaultAccount;
        } else if (currentAccount != lastOrDefaultAccount) {
          _bloc.selectedItem = currentAccount;
        }
        if (currentAccount != null && _firstInit) {
          widget.onSelectAccount?.call(currentAccount);
          _firstInit = false;
        }
        if (!widget.title.isNullOrEmpty) {
          titleTemp = "${widget.title}";
        }
        return widget.inRequestTransfer
            ? BankAccountWidget(
                accountName: titleTemp ?? currentAccount?.displayName ?? '',
                balanceAccount: currentAccount?.availableBalance ?? 0,
                onPress: () async {
                  widget.onTap?.call();
                  if ((_bloc.items.valueOrNull?.length ?? 0) > 1) {
                    final result = await goPresent<AccountModel>(
                      context,
                      HomeV3Account(
                        title: S.of(context).choose_account,
                        data: _bloc.items.valueOrNull,
                        type: HomeCardType.USER,
                      ),
                    );
                    if (result != null) {
                      widget.onSelectAccount?.call(result);
                    } else {
                      widget.currentNoSelectedAccount?.call();
                    }
                  }
                },
              )
            : Container(
                padding: const EdgeInsets.only(
                    top: kPaddingPage, left: kPaddingPage, right: kPaddingPage),
                child: CommonDropdown(
                  title: titleTemp,
                  value: widget.qrCodeOpenCard
                      ? currentAccount?.accountNumber
                      : currentAccount?.aliasname?.isNullOrEmpty == true
                          ? currentAccount?.accountName
                          : currentAccount?.aliasname,
                  onTap: () async {
                    widget.onTap?.call();
                    if ((_bloc.items.valueOrNull?.length ?? 0) > 1) {
                      final result = await goPresent(
                        context,
                        BottomSheetWidget(
                          safeAreaTop: false,
                          safeAreaBottom: false,
                          title: S.of(context).choose_account,
                          child: AccountAllPage(
                            selected: currentAccount,
                            data: _bloc.items.valueOrNull,
                            type: HomeCardType.USER,
                          ),
                        ),
                      );
                      if (result is AccountModel) {
                        widget.onSelectAccount?.call(result);
                      } else {
                        widget.currentNoSelectedAccount?.call();
                      }
                    }
                  },
                ),
              );
      },
    );
  }

  @override
  void initState() {
    super.initState();
    _bloc.streamSubs.add(_env.appEventStream.listen((event) {
      if (event == AppEvent.MONEY_CHANGE) {
        _bloc.reload();
      }
    }));
    _bloc.getAccountList(
      selectMin: widget.selectMin,
      isOnlyVND: true,
    );
  }

  @override
  void dispose() {
    super.dispose();
    _bloc.dispose();
  }
}
