import 'package:flutter/material.dart';
import 'package:mobile_banking/assets.dart';
import 'package:mobile_banking/generated/l10n.dart';

class TransferWarningVietNameseWiget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(top: 10, bottom: 5, left: 15, right: 15),
      margin: const EdgeInsets.only(left: 15, right: 15, top: 10.0),
      child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
        ImageAssets.svgAssets(ImageAssets.ic_warning, height: 14, width: 14),
        const SizedBox(
          width: 10,
        ),
        Expanded(
          child: Text(
            S.of(context).warning_transfer,
            style: StyleApp.bodyStyle(context, color: Colors.red, fontSize: 14),
          ),
        ),
      ]),
    );
  }
}
