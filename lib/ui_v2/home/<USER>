import 'dart:async';
import 'dart:convert';

import 'package:common/model/strapi/strapi_models.dart';
import 'package:common/model/view_data_model.dart';
import 'package:common/widgets/common_size.dart';
import 'package:common/widgets/style_app.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ks_chat/bloc/bloc/rocket_chat_bloc.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_bloc/bloc/model/profile/biology_2345/verify_type_model.dart';
import 'package:ksb_bloc/bloc/model/theme/theme_model.dart';
import 'package:ksb_bloc/bloc/theme_bloc.dart';
import 'package:ksb_bloc/bloc/transfer/transaction_id_model.dart';
import 'package:ksb_common/model/base_item.dart' as ksb_common;
import 'package:ksb_common/ksb_common.dart' as ksbCommon;
import 'package:ksb_common/shared/theme/custom_theme.dart';
import 'package:ksb_common/shared/widgets/background_app.dart';
import 'package:ksb_common/shared/widgets/background_image_app_bar.dart';
import 'package:ksb_common/shared/widgets/expanded_section_widget.dart';
import 'package:ksb_common/shared/widgets/image/image_from_type.dart';
import 'package:mobile_banking/assets/assets.dart';
import 'package:mobile_banking/assets/images.dart';
import 'package:mobile_banking/di/inject.dart';
import 'package:mobile_banking/generated/l10n.dart';
import 'package:mobile_banking/model/base_model.dart';
import 'package:mobile_banking/model/enum_type.dart';
import 'package:mobile_banking/ui_v2/account_detail/widgets/list_account_tabbar.dart';
import 'package:mobile_banking/ui_v2/home/<USER>/home_banner_item.dart';
import 'package:mobile_banking/ui_v2/home/<USER>/home_card_user_item.dart';
import 'package:mobile_banking/ui_v2/home/<USER>/noti_update_view.dart';
import 'package:mobile_banking/ui_v2/home/<USER>';
import 'package:mobile_banking/ui_v2/home/<USER>/home_card_loading_widget.dart';
import 'package:mobile_banking/ui_v2/home/<USER>/home_news_widget.dart';
import 'package:mobile_banking/ui_v2/home/<USER>/home_page_widget.dart';
import 'package:mobile_banking/ui_v2/profile_account/biology/profile_biology_mixin.dart';
import 'package:mobile_banking/ui_v2/transfer/great_value_transaction_page.dart';
import 'package:mobile_banking/ui_v2/widget/constant.dart';
import 'package:mobile_banking/ui_v2/widget/loading/loading_area.dart';
import 'package:mobile_banking/ui_v2/widget/loading/home_acc_retry_view.dart';
import 'package:mobile_banking/ui_v2/widget/state/state_keep.dart';
import 'package:mobile_banking/utils/navigator.dart';
import 'package:rxdart/rxdart.dart';

import '../../utils/extension/color.dart';
import '../auth/notification_surplus/message_notification_screen.dart';
import '../noble/noble_page.dart';
import '../priority_customers/priority_page.dart';

part "widgets/noti_update_biology_components.dart";

part "widgets/priority_customers.dart";

class HomePage extends StatefulWidget {
  final ProfileBloc? profileBloc;
  final ValueChanged<bool>? onError;
  final PriorityBloc? priorityBloc;

  const HomePage({
    Key? key,
    this.profileBloc,
    this.onError,
    this.priorityBloc,
  }) : super(key: key);

  @override
  StateKeep<HomePage> createState() => _ScreenState();
}

class _ScreenState extends StateKeep<HomePage> with ProfileBiologyMixin {
  final _env = Injection.injector.get<Environment>();
  final _bloc = Injection.injector.get<HomeBloc>();
  final _themeBloc = Injection.injector.get<ThemeBloc>();
  final _rocketBloc = Injection.injector.get<RocketChatBloc>();
  final _updateBiology2345Bloc =
      Injection.injector.get<UpdateBiology2345Bloc>();
  late PriorityBloc _priorityBloc;

  final _appbarBehavior = BehaviorSubject<bool>.seeded(false);
  final _pageBehavior = BehaviorSubject<int>.seeded(0);
  List<BaseModel>? arrInvest;
  Timer? _refreshInvestTimer;
  Offset start = const Offset(0.0, 1);
  Offset end = const Offset(0.0, 0.0);
  bool _isShowNotification = true;
  ThemeDataLocal? theme;
  final _preferences = Injection.preferences;

  void getImageBanner() {
    try {
      if (!_preferences.getNameThemeIntro().isNullOrEmpty) {
        theme = ThemeDataLocal.fromJson(
          jsonDecode(_preferences.getNameThemeIntro()),
        );
        ksbCommonProperties = KSBCommonProperties(
          backgroundImageAppBar: theme?.backgroundAppBarUrl,
          backgroundImageAppBarType: theme?.backgroundAppBarType,
          brightness: theme?.brightLogo,
          priorityProfileColor: theme?.priorityProfileColor == null
              ? null
              : HexColor.fromHex(theme?.priorityProfileColor),
        );
      }
    } catch (e) {
      logger.e(e);
    }
  }

  @override
  void initState() {
    super.initState();
    getImageBanner();
    initData();
    _setCardValue(0);
    //remove this, not necessary because using in _env.changeThemes
    // _themeBloc.getThemeDetail();

    _bloc.streamSubs.add(_bloc.statusStream.listen((event) {
      if (event != null &&
          event.statusCode != null &&
          event.statusCode! > 200) {
        widget.onError?.call(false);
      } else {
        widget.onError?.call(true);
      }
    }));

    _bloc.streamSubs.add(_env.appEventStream.listen((event) {
      if ([AppEvent.NEW_NOTIFICATION, AppEvent.MONEY_CHANGE].contains(event)) {
        //remove this because load many things...
        // _bloc.reload();
        _bloc.getAccountList();
      }
    }));

    _bloc.streamSubs.add(_env.appEventStream.listen((event) {
      if (event == AppEvent.BIG_TRANSACTION) {
        _bloc.getGreatValueTransactions();
      }
    }));

    _bloc.streamSubs.add(_env.changeThemes.listen((event) {
      _themeBloc.getThemeDetail(
        cacheImage: (imageUrl) => preCacheSvg(
          imageUrl.image,
          type: ksbCommon.getImageTypeFrom(imageUrl.metaType),
        ),
      );
    }));

    _updateBiology2345Bloc.getVerifyType();

    _bloc.streamSubs.add(_env.appEventStream.listen((event) {
      if (event == AppEvent.UPDATE_CHIP_ID) {
        _updateBiology2345Bloc.setShowNotiUpdate2345(false);
      }
    }));

    _themeBloc.streamSubs.add(_themeBloc.themeStream.listen((event) {
      ksbCommonProperties = KSBCommonProperties(
        backgroundImageAppBar: event.backgroundAppBarUrl,
        backgroundImageAppBarType: event.backgroundAppBarType,
        brightness: event.brightLogo,
        priorityProfileColor: event.priorityProfileColor == null
            ? null
            : HexColor.fromHex(event.priorityProfileColor),
      );
    }));

    _priorityBloc =
        widget.priorityBloc ?? Injection.injector.get<PriorityBloc>();
    if (widget.priorityBloc == null) {
      _priorityBloc.getPriorityCustomer();
    }

    _bloc.streamSubs.add(
      _updateBiology2345Bloc.getVerifyTypeStr.listen(
        (event) {
          if (event.type == User2345VerifyType.CHIP_ID) {
            showBottomSheetEKYC(
              context,
              event.title ?? "",
              event.content ?? "",
            );
          }
        },
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
    _appbarBehavior.close();
    _pageBehavior.close();
    _refreshInvestTimer?.cancel();
    _bloc.dispose();
    _themeBloc.dispose();
    _rocketBloc.dispose();
    _updateBiology2345Bloc.dispose();
    if (widget.priorityBloc == null) {
      _priorityBloc.dispose();
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return StreamBuilder<ThemeModel>(
      stream: _themeBloc.themeStream,
      builder: (context, snapshot) {
        if (snapshot.hasData && snapshot.data != null) {
          theme = ThemeDataLocal.fromThemeData(snapshot.data!);
        }
        return BackgroundAppBarImage(
          stream: _bloc.loadingScreenStream,
          onTryClick: _bloc.reload,
          imageHeight: toSp(195),
          image: CacheImageUrl(
            heightLoadingView: toSp(195),
            widthLoadingView: MediaQuery.sizeOf(context).width,
            url: theme?.imgBannerUrl,
            urlType: theme?.typeImgBanner,
            localImage: ImageAssets.svgAssets(ImagesResource.theme_default),
            isLoading: !snapshot.hasData && theme == null,
          ),
          child: _getBody(),
        );
      },
    );
  }

  _getBody() {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: theme?.brightLogo == false
          ? SystemUiOverlayStyle.light
          : SystemUiOverlayStyle.dark,
      child: SafeArea(
        child: RefreshIndicator(
          key: _bloc.refreshIndicatorKey,
          onRefresh: () async {
            _bloc.onRefresh();
          },
          child: _getContent(),
        ),
      ),
    );
  }

  _getContent() {
    return CustomScrollView(
      slivers: [
        SliverList(
          delegate: SliverChildListDelegate(
            [
              Stack(
                children: [
                  Positioned(
                    left: 0,
                    right: 0,
                    top: 4,
                    child: (theme?.imgLogoUrl ?? "").isEmpty
                        ? ImageAssets.svgAssets(ImagesResource.ic_home_logo_klb)
                        : getImageWidgetFromType(theme?.imgLogoUrl,
                            ksb_common.getImageTypeFrom(theme?.typeImgLogoUrl),
                            height: toSp(36)),
                  ),
                  Container(
                    margin: const EdgeInsets.only(top: 4),
                    padding: const EdgeInsets.only(right: 5),
                    alignment: Alignment.centerRight,
                    child: IconButton(
                      icon: Icon(
                        Icons.notifications_none,
                        color: theme?.brightLogo == false
                            ? Colors.white
                            : Colors.black,
                      ),
                      onPressed: () async {
                        go(
                            context,
                            const MessageNotificationScreen(
                              isOutside: false,
                            ));
                      },
                    ),
                  ),
                ],
              ),
              _getHomeHeader(),
              _greatValueTransactions(),
              _videoEKyc(),
              _notiUpdateBiology(
                bloc2345: _updateBiology2345Bloc,
                profileBloc: widget.profileBloc,
              ),
              _getPriority(
                _priorityBloc,
                widget.profileBloc?.profileInfo,
                theme,
              ),
              HomePageWidget(
                bloc: _bloc,
                env: _env,
                onPageChange: (value) {
                  _setCardValue(value);
                },
              ),
              Container(
                color: Theme.of(context).dividerColor,
                padding: const EdgeInsets.only(top: 8),
              ),
              StreamBuilder(
                stream: _priorityBloc.priorityStream,
                builder: (context, snapshot) {
                  final rank = snapshot.data;
                  return StreamBuilder<List<StrapiBanner>>(
                    stream: _bloc.bannerStream,
                    builder: (context, snapshot) {
                      final data = snapshot.data
                          ?.where((element) =>
                              element.category?.code?.toUpperCase() ==
                                  rank?.customerRank?.toUpperCase() ||
                              element.category == null)
                          .toList();
                      return Container(
                        color: Theme.of(context).cardColor,
                        child: HomeBannerItem(
                          data,
                          onTap: (value) {
                            if (value.category?.code != null &&
                                value.category?.code?.toUpperCase() ==
                                    rank?.customerRank?.toUpperCase() &&
                                rank?.customerRank?.toUpperCase() !=
                                    GetThemeDetailResponseCustomerRankEnum
                                        .NORMAL.name
                                        .toUpperCase()) {
                              go(context, NoblePage(bannerModel: value));
                            }
                          },
                        ),
                      );
                    },
                  );
                },
              ),
              StreamBuilder<List<StrapiPromotion>>(
                stream: _bloc.articlesStream,
                builder: (context, snapshot) {
                  if (snapshot.hasData && snapshot.data != null) {
                    final data = snapshot.data!;

                    if (data.isNotEmpty == true) {
                      return Container(
                        color: Theme.of(context).cardColor,
                        padding: const EdgeInsets.only(bottom: 100),
                        child: HomeNewsWidget(
                          type: NewsType.NEWS,
                          title: S.of(context).maybe_interested,
                          data: data,
                        ),
                      );
                    }
                  }
                  return Container();
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget getHomeCardItem() {
    return LoadingArea<List<AccountModel>?>(
      stream: _bloc.accountStream,
      loading: HomeCardLoadingWidget(
        logoUrl: theme?.priorityLogoUrl,
        logoType: theme?.priorityLogoType,
        isLoading: theme == null,
      ),
      onTryClick: _bloc.getAccountList,
      onSuccess: (data) {
        return HomeCardUserItem(
          currentAccount: _bloc.accountChoose,
          data: data,
          onPressed: (value) {
            go(context, ListAccountTabBar(model: _bloc.accountChoose));
          },
          onChange: (value) {
            _bloc.accountChoose = value;
            _bloc.session.changeAccountNo(value.accountNumber);
          },
          logoUrl: theme?.priorityLogoUrl,
          logoType: theme?.priorityLogoType,
          isLoading: theme == null,
        );
      },
      onError: (e) {
        return HomeAccRetryView(
          message: e,
          onReloadCallback: () {
            _bloc.getAccountList();
          },
          logoUrl: theme?.priorityLogoUrl,
          logoType: theme?.priorityLogoType,
          isLoading: theme == null,
        );
      },
    );
  }

  _setCardValue(int value) {
    _pageBehavior.add(value);
  }

  _getHomeHeader() {
    return Container(
      margin: const EdgeInsets.only(top: 4),
      child: getHomeCardItem(),
    );
  }

  _greatValueTransactions() {
    return StreamBuilder<ViewDataModel<List<TransactionIdModel>>>(
      stream: _bloc.greatValueTransactionsStream,
      builder: (context, snapshot) {
        final data = snapshot.data?.data;
        if (data?.isNotEmpty == true) {
          return Stack(
            alignment: Alignment.topRight,
            children: [
              Container(
                padding: EdgeInsets.all(kOuterPadding),
                margin: EdgeInsets.only(
                    left: kOuterPadding,
                    right: kOuterPadding,
                    bottom: kOuterPadding,
                    top: kPadding24),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(kTinyPadding),
                    color: const Color(0xFFF4F4F7)),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Giao dịch giá trị lớn",
                      style: StyleApp.subtitle1(context, true),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: kTinyPadding),
                      child: Text(
                        "Quý khách đang thực hiện giao dịch chuyển tiền giá trị lớn",
                        style: StyleApp.bodyText2(context),
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        go(context, GreatValueTransactionPage(homeBloc: _bloc));
                      },
                      child: Padding(
                        padding: EdgeInsets.only(top: kSmallPadding),
                        child: Text(
                          "Xem chi tiết",
                          style: StyleApp.bodyText2(context)
                              ?.copyWith(color: const Color(0xFF228BCC)),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Positioned(
                right: -5,
                child: IconButton(
                  onPressed: () {
                    setState(() {
                      _isShowNotification = !_isShowNotification;
                    });
                  },
                  iconSize: 20,
                  icon: Container(
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      color: Color(0xFF6D6E71),
                    ),
                    width: 20,
                    height: 20,
                    alignment: Alignment.center,
                    child: Icon(
                      Icons.clear,
                      size: 12,
                      color: Theme.of(context).cardColor,
                    ),
                  ),
                ),
              ),
            ],
          );
        }
        return const SizedBox();
      },
    );
  }

  Widget _videoEKyc() {
    return StreamBuilder<bool>(
        stream: widget.profileBloc?.videoEKycVerifiedStream,
        builder: (context, snapshot) {
          return ExpandedSectionWidget(
            expand: snapshot.data == true,
            child: Stack(
              children: [
                Container(
                  margin: kPaddingStandard,
                  padding: EdgeInsets.fromLTRB(
                    kOuterPadding,
                    kSmallPadding,
                    kOuterPadding,
                    kOuterPadding,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8.0),
                    boxShadow: [
                      BoxShadow(
                        color: DynamicTheme.of(context)!
                            .customColor
                            .shadowCard!
                            .withOpacity(0.25),
                        offset: const Offset(0, 2),
                        blurRadius: 7,
                        spreadRadius: 1,
                      )
                    ],
                  ),
                  child: Padding(
                    padding: kVerticalPaddingStandard,
                    child: Row(
                      children: [
                        SizedBox(
                          width: 64.0,
                          height: 64.0,
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8.0),
                            child: ImageAssets.svgAssets(
                                ImagesResource.icNotiSection),
                          ),
                        ),
                        SizedBox(width: kInnerPadding),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Xác thực Video KYC',
                                style: StyleApp.subtitle1(context)?.copyWith(
                                  color: DynamicTheme.of(context)
                                      ?.customColor
                                      .titleColor2,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              SizedBox(height: kTinyPadding),
                              Text(
                                'Hãy đăng ký để bảo vệ tài khoản và thuận tiện cho các giao dịch ...',
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                style: StyleApp.bodyText2(context)?.copyWith(
                                  color: DynamicTheme.of(context)
                                      ?.customColor
                                      .ink80,
                                ),
                              ),
                              SizedBox(height: kTinyPadding),
                              InkWell(
                                onTap: () {
                                  go(
                                    context,
                                    const VideoEkycVerificationPage(),
                                  );
                                },
                                child: Text(
                                  'Xác thực ngay',
                                  style: StyleApp.bodyText2(context)?.copyWith(
                                    color: Theme.of(context).primaryColor,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                ),
                Positioned(
                  top: 12.0,
                  right: 12.0,
                  child: InkWell(
                    onTap: () =>
                        widget.profileBloc?.videoEKycVerifiedSink.add(false),
                    child:
                        ImageAssets.svgAssets(ImagesResource.icCleanTextfield),
                  ),
                )
              ],
            ),
          );
        });
  }
}
