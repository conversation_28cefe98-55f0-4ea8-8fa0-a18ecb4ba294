import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:common/utils/dialog_util.dart';
import 'package:common/utils/permission_util.dart' as permission;
import 'package:common/widgets/loading/loading.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_mlkit_barcode_scanning/google_mlkit_barcode_scanning.dart';
import 'package:image_picker/image_picker.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_bloc/bloc/money_transaction/request_transaction_bloc.dart';
import 'package:ksb_bloc/bloc/open_card/open_card_bloc.dart';
import 'package:ksb_bloc/bloc/payment_qr/payment_qr_bloc.dart';
import 'package:ksb_bloc/bloc/viet_qr/viet_qr_bloc.dart';
import 'package:ksb_common/shared/widgets/index.dart';
import 'package:mobile_banking/assets.dart';
import 'package:mobile_banking/di/inject.dart';
import 'package:mobile_banking/sunshine_app.dart';
import 'package:mobile_banking/ui_v2/card_v2/withdrawal_atm.dart';
import 'package:mobile_banking/ui_v2/klb_pay_qr/klb_pay_qr.dart';
import 'package:mobile_banking/ui_v2/open_card_qr/open_card_qr_page.dart';
import 'package:mobile_banking/ui_v2/profile_account/my_qr/redesign/my_qr.dart';
import 'package:mobile_banking/ui_v2/transfer/transfer_accept.dart';
import 'package:mobile_banking/ui_v2/widget/permission/permission.dart';
import 'package:mobile_banking/ui_v2/widget/picker/image_picker_handler.dart';
import 'package:mobile_banking/utils/navigator.dart';
import 'package:mobile_banking/utils/validator_qr.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:rxdart/rxdart.dart';
import 'package:umee_shop/repository/src/model/paybox.dart';
import 'package:umee_shop/ui/magic_paybox/pages/list_magic_pair_paybox_page.dart/list_magic_pair_paybox_page.dart';

import '../../generated/l10n.dart';
import '../lucky_money/ui/receive/handle_service/lucky_money_handle_service.dart';

const flashOn = 'FLASH ON';
const flashOff = 'FLASH OFF';
const frontCamera = 'FRONT CAMERA';
const backCamera = 'BACK CAMERA';

class QrScanner extends StatefulWidget {
  final ValueChanged<TransferModel?>? onDetectedTransfer;
  final ValueChanged<String>? myCodeQr;
  final int? typecall;
  final bool? myCode;
  final bool? fromMiniApp;

  const QrScanner({
    Key? key,
    this.onDetectedTransfer,
    this.typecall,
    this.myCode = false,
    this.myCodeQr,
    this.fromMiniApp = false,
  }) : super(key: key);

  @override
  State<QrScanner> createState() => _ScreenState();
}

class _ScreenState extends State<QrScanner>
    with RouteAware, TickerProviderStateMixin, ImagePickerListener {
  late ImagePickerHandler imagePicker;
  late AnimationController _controller;
  final BarcodeScanner _barcodeDetector = BarcodeScanner();
  var flashState = flashOn;
  var cameraState = frontCamera;
  late MobileScannerController controller;
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  final _requestTransactionBloc =
      Injection.injector.get<RequestTransactionBloc>();
  final _blocQr = Injection.injector.get<PaymentQrBloc>();
  final _blocVietQr = Injection.injector.get<VietQrBloc>();
  final _openCardBloc = Injection.injector.get<OpenCardBloc>();

  // In order to get hot reload to work we need to pause the camera if the platform
  // is android, or resume the camera if the platform is iOS.
  late CompositeSubscription _subs;
  final _session = Injection.injector.get<Session>();
  final picker = ImagePicker();
  late Preferences _prefs;
  bool? isScanning = true;
  bool isCheckPermission = false;
  int tempPermission = 0;

  @override
  void initState() {
    super.initState();
    _requestPermission();

    controller = MobileScannerController(
      detectionSpeed: DetectionSpeed.normal,
      facing: CameraFacing.back,
      torchEnabled: false,
    );

    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _subs = CompositeSubscription();
    isScanning = true;
    imagePicker = ImagePickerHandler(this, _controller, isCrop: false);
    imagePicker.init();
    _prefs = Injection.preferences;

    _subs.add(_session.networkStatus.stream.listen((event) {
      if (event == NetworkStatus.none) {
        startScan();
      }
    }));

    _blocQr.streamSubs.add(_blocQr.errorStream.listen((event) {
      if (event?.isNotEmpty == true && mounted) {
        DialogUtil.alert(context, event, onSubmit: () {
          _prefs.setQrDeepLink('');
          startScan();
        });
      }
    }));

    _blocVietQr.streamSubs.add(_blocVietQr.errorStream.listen((event) {
      if (event?.isNotEmpty == true && mounted) {
        DialogUtil.alert(context, event, onSubmit: () {
          _prefs.setQrDeepLink('');
          startScan();
        });
      }
    }));

    _openCardBloc.streamSubs.add(_openCardBloc.errorStream.listen((event) {
      if (event?.isNotEmpty == true && mounted) {
        DialogUtil.alert(context, event, onSubmit: () {
          _prefs.setQrDeepLink('');
          startScan();
        });
      }
    }));
  }

  _startController() {
    ///fix: MobileScanner is not show camera preview
    /// https://github.com/juliansteenbakker/mobile_scanner/issues/1119
    if (Platform.isAndroid) {
      controller.stop();
      Future.delayed(
        const Duration(milliseconds: 1000),
        () => controller.start(),
      );
    } else {
      controller.start();
    }
  }

  _requestPermission() async {
    await checkHasPermission();
    checkPermission(permissionCamera, onAllowed: () {
      if (mounted) {
        setState(() {
          isCheckPermission = true;
        });
        _startController();
      }
    }, onDenied: () {
      if (mounted) {
        setState(() {
          isCheckPermission = false;
        });
      }
    }, onNeverAllowed: () {
      if (mounted) {
        setState(() {
          isCheckPermission = false;
        });
        dialogPermission(context, p: permissionCamera);
      }
    });
  }

  Future<void> checkHasPermission() async {
    await permission.PermissionUtil.checkPermission(
      context,
      await permissionStorage(),
      'Thư viện ảnh',
      onDenied: () async => goPresent(
        context,
        PermissionRequestWidget(
          title: 'Yêu cầu truy cập vào thư viện ảnh',
          content:
              'Cho phép KienLongBank truy cập vào ảnh và nội dung nghe nhìn trên thiết bị của bạn để hiển thị ảnh và video nhanh chóng hơn.',
          permission: await permissionStorage(),
          permissionName: '',
        ),
      ),
    );
  }

  startScan() {
    setState(() {
      isScanning = true;
    });
  }

  @override
  void dispose() {
    controller.dispose();
    _requestTransactionBloc.dispose();
    _barcodeDetector.close();
    _blocQr.dispose();
    _blocVietQr.dispose();
    _subs.dispose();
    _openCardBloc.dispose();
    routeObserver.unsubscribe(this);
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.subscribe(this, ModalRoute.of(context) as PageRoute);
  }

  @override
  void didPopNext() {
    super.didPopNext();
    startScan();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AnnotatedRegion<SystemUiOverlayStyle>(
        value: SystemUiOverlayStyle.light,
        child: Stack(
          alignment: Alignment.topCenter,
          children: <Widget>[
            isCheckPermission ? _buildQrView(context) : Container(),
            Container(
              decoration: ShapeDecoration(
                shape: QrScannerOverlayShape(
                  borderColor: Colors.white,
                  borderRadius: 10,
                  borderLength: 30,
                  borderWidth: 8,
                  cutOutSize: 250,
                ),
              ),
            ),
            SafeArea(
              child: Container(
                padding: const EdgeInsets.only(left: 15, right: 15),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    IconButton(
                      icon: Container(
                        width: 32,
                        height: 32,
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          borderRadius:
                              BorderRadius.all(Radius.circular(32 / 2.0)),
                        ),
                        child: const Center(
                          child: Icon(
                            Icons.arrow_back,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      onPressed: () => Navigator.pop(context),
                    ),
                    SizedBox(height: MediaQuery.of(context).size.height * 0.02),
                    Padding(
                      padding: const EdgeInsets.all(10),
                      child: Text(
                        S.of(context).content_qr_scaner,
                        style:
                            StyleApp.settingStyle(context, color: Colors.white),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    SizedBox(height: MediaQuery.of(context).size.height * 0.02),
                    Center(
                      child: ImageAssets.svgAssets(
                        ImageAssets.ic_logo_vietqr,
                        height: 30,
                      ),
                    ),
                    const Spacer(),
                    Container(
                      margin: const EdgeInsets.only(
                        top: 40,
                        bottom: 20,
                        left: 10,
                        right: 10,
                      ),
                      alignment: Alignment.bottomCenter,
                      child: Row(
                        children: [
                          Expanded(
                            child: Container(
                              padding: const EdgeInsets.only(top: 10),
                              child: ImageAssets.svgAssets(
                                  ImageAssets.ic_logo_napas247,
                                  height: 30),
                            ),
                          ),
                          Container(
                            width: 1,
                            height: 30,
                            color: const Color(0xFF9A9A9A),
                          ),
                          Expanded(
                            child: ImageAssets.svgAssets(
                                ImageAssets.ic_logo_home_kienlongbank,
                                height: 30),
                          )
                        ],
                      ),
                    ),
                    ValueListenableBuilder(
                        valueListenable: controller,
                        builder: (context, state, child) {
                          return Container(
                            alignment: Alignment.center,
                            padding: const EdgeInsets.all(10),
                            child: Text(
                              state.torchState == TorchState.off
                                  ? S.of(context).turn_on_flash
                                  : S.of(context).turn_off_flash,
                              style: StyleApp.settingStyle(context,
                                  color: Colors.white),
                              textAlign: TextAlign.center,
                            ),
                          );
                        }),
                    const SizedBox(height: 10),
                    ValueListenableBuilder(
                        valueListenable: controller,
                        builder: (context, state, child) {
                          return Center(
                            child: IconButton(
                              icon: state.torchState == TorchState.on
                                  ? ImageAssets.svgAssets(
                                      ImageAssets.ic_flash_scan_qr_on)
                                  : ImageAssets.svgAssets(
                                      ImageAssets.ic_flash_scan_qr_off),
                              onPressed: () async {
                                try {
                                  await controller.toggleTorch();
                                } catch (e) {
                                  logger.e(e);
                                }
                              },
                            ),
                          );
                        }),
                    const SizedBox(height: 10),
                    Padding(
                      padding: const EdgeInsets.only(top: 10, bottom: 10),
                      child: Row(
                        children: [
                          widget.myCode == false
                              ? Expanded(
                                  child: TextButton(
                                      child: Container(
                                        height: 40,
                                        alignment: Alignment.center,
                                        padding: const EdgeInsets.only(
                                            top: 10,
                                            bottom: 10,
                                            left: 20,
                                            right: 20),
                                        decoration: const BoxDecoration(
                                          color: Colors.black45,
                                          borderRadius: BorderRadius.all(
                                              Radius.circular(20.0)),
                                        ),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            ImageAssets.svgAssets(
                                                ImageAssets.ic_qr_code,
                                                color: Colors.white,
                                                width: 20),
                                            const SizedBox(
                                              width: 10,
                                            ),
                                            Text(
                                              S.of(context).my_qr_scan,
                                              style: StyleApp.bodyStyle(context,
                                                  color: Colors.white),
                                            )
                                          ],
                                        ),
                                      ),
                                      onPressed: () {
                                        setState(() {
                                          isScanning = false;
                                        });
                                        go(context, const MyQrCode()).then((_) {
                                          startScan();
                                        });
                                      }),
                                )
                              : const Spacer(),
                          Expanded(
                            flex: widget.myCode == false ? 1 : 2,
                            child: TextButton(
                              child: Container(
                                height: 40,
                                alignment: Alignment.center,
                                padding: const EdgeInsets.only(
                                    top: 10, bottom: 10, left: 20, right: 20),
                                decoration: const BoxDecoration(
                                  color: Colors.black45,
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(20.0)),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    ImageAssets.svgAssets(
                                        ImageAssets.ic_qr_card,
                                        color: Colors.white,
                                        width: 20),
                                    const SizedBox(width: 10),
                                    Text(
                                      S.of(context).down_image_qr_scan,
                                      style: StyleApp.bodyStyle(context,
                                          color: Colors.white),
                                    )
                                  ],
                                ),
                              ),
                              onPressed: () async {
                                if (Platform.isAndroid) {
                                  controller.stop();
                                  await imagePicker.openGallery();
                                  controller.start();
                                } else {
                                  checkPermission(
                                    await permissionStorage(),
                                    onAllowed: () async {
                                      await imagePicker.openGallery();
                                    },
                                    onNeverAllowed: () {
                                      dialogPermission(context,
                                          p: permissionCamera);
                                    },
                                  );
                                }
                              },
                            ),
                          ),
                          if (widget.myCode != false) const Spacer(),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            isScanning == true
                ? const SizedBox()
                : Center(
                    child: Container(
                      width: 70.0,
                      height: 70.0,
                      decoration: BoxDecoration(
                          color: Theme.of(context).cardColor,
                          borderRadius:
                              const BorderRadius.all(Radius.circular(8.0))),
                      padding: const EdgeInsets.symmetric(vertical: 5),
                      child: const CupertinoActivityIndicator(),
                    ),
                  )
          ],
        ),
      ),
    );
  }

  Widget _buildQrView(BuildContext context) {
    return MobileScanner(
      key: qrKey,
      controller: controller,
      onDetect: (BarcodeCapture barcodes) {
        _onDetect(barcodes);
      },
      placeholderBuilder: (context, widget) => Center(child: ring),
      errorBuilder: (context, exception, widget) => Center(
        child:
            Text(exception.errorDetails?.message ?? exception.errorCode.name),
      ),
    );
  }

  showDialog({Function? onSubmit}) {
    DialogUtil.alert(context, S.of(context).error_qr_code_invalid,
        onSubmit: () {
      controller.start();
      onSubmit?.call();
    });
  }

  showQrLoginDialog({Function? onSubmit}) {
    DialogUtil.alert(context, S.of(context).qr_code_login, onSubmit: () {
      controller.start();
      onSubmit?.call();
    });
  }

  Future<void> _onDetect(BarcodeCapture barcodes) async {
    final code = barcodes.barcodes.firstOrNull?.rawValue ?? '';
    logger.t("scanData.code $code");
    if (isScanning == true) {
      setState(() {
        isScanning = false;
      });
      if (widget.myCode == true) {
        final myQrBloc = Injection.injector.get<MyQrBloc>();
        final valueMyQrCode = await ValidatorQr.checkMyQrCodeV2(
            qrCode: code,
            scanQRSuccess: (code, id) async {
              final myCode =
                  await myQrBloc.getDataUrl(codeUrl: code, idUrl: id);
              if (myCode != null) {
                widget.myCodeQr?.call(myCode.code ?? '');
                myQrBloc.dispose();
                if (mounted) Navigator.of(context).pop(myCode.code ?? '');
              }
            });
        if (!valueMyQrCode) {
          myQrBloc.dispose();
          showDialog(onSubmit: () {
            startScan();
          });
        }
      } else {
        qrCodeScanner(uriString: code);
      }
    }
  }

  qrCodeScanner({String? uriString}) async {
    try {
      final data = uriString?.split('|') ?? [];
      if ("UMEE_PAIR" == data.first || "PAYBOX_PAIR" == data.first) {
        goToPayBox(qrCode: uriString);
        return;
      } else if (data.first == "UMEE_PAY_QR_LOGIN") {
        _handleScanKlbPayQr(qrCodeAttributes: data);
        return;
      }
      Uri uriQrCodeParse = Uri.parse(uriString ?? '');
      if (uriQrCodeParse.isAbsolute) {
        if (uriQrCodeParse.toString().contains('home-detail')) {
          if (widget.fromMiniApp == true) {
            Navigator.of(context).pop(uriQrCodeParse.toString());
          } else {
            goHomeLoan(
              context,
              _prefs.homeLoanUrl ?? '',
              uriQrCodeParse.toString(),
            );
          }
          return;
        }
        var indexQr = ValidatorQr.getServerQr(uri: uriQrCodeParse);
        switch (indexQr) {
          case -1:
            showDialog(onSubmit: () {
              startScan();
            });
            break;
          case 0:
            goToTransfer(
                uri: uriQrCodeParse,
                onSubmit: () {
                  startScan();
                });
            break;
          case 1:
            goToQrAtm(
                uri: uriQrCodeParse,
                onSubmit: () {
                  startScan();
                });
            break;
          case 4:
            goToQrOpenCard(
                uri: uriQrCodeParse,
                onSubmit: () {
                  startScan();
                });
            break;
          case 5:
            if (widget.typecall == 1) {
              showQrLoginDialog(onSubmit: () {
                if (uriString != null) {
                  _prefs.setQrDeepLink(uriString);
                }
                Navigator.of(context).pop();
              });
            } else {
              Navigator.of(context).pop();
              LuckyMoneyHandleService.handleAppQrScan(
                context,
                uriString: uriString,
              );
            }
        }
      } else if (ValidatorQr.checkVietQr(qrCode: uriString)) {
        goToTransfer247VietQr(
            qrCode: uriString ?? '',
            onSubmit: () {
              startScan();
            });
      } else {
        showDialog(onSubmit: () {
          startScan();
        });
      }
    } on FormatException catch (e) {
      logger.e(e);
      showDialog(onSubmit: () {
        startScan();
      });
    }
  }

  goToQrAtm({Uri? uri, Function? onSubmit}) async {
    if (widget.typecall == 0) {
      final result =
          await _blocQr.getInfoSTM(qrPaymentId: uri?.pathSegments.last ?? '');
      if (!mounted) return;
      if (result != null) {
        goToWithdrawalAtm(context, result, onPressDone: () {
          onSubmit?.call();
        }).then((value) => popScreen(context));
      } else {
        DialogUtil.alert(context, S.of(context).error_validator_dialog_withdraw,
            onSubmit: () {
          _prefs.setQrDeepLink('');
          onSubmit?.call();
        });
      }
    } else {
      showQrLoginDialog(onSubmit: () {
        _prefs.setQrDeepLink(uri.toString());
        Navigator.of(context).pop();
      });
    }
  }

  @override
  onProcessing() {}

  @override
  onError(String message) {}

  @override
  onPicked(List<File> images) async {
    await _scanImage(images.first);
  }

  Future<void> _scanImage(File imageFile) async {
    try {
      final InputImage visionImage = InputImage.fromFile(imageFile);
      final results = await _barcodeDetector.processImage(visionImage);
      logger.t("barcode  ${results.first.rawValue}");
      if (widget.myCode == true) {
        final myQrBloc = Injection.injector.get<MyQrBloc>();
        final valueMyQrCode = await ValidatorQr.checkMyQrCodeV2(
            qrCode: results.first.rawValue,
            scanQRSuccess: (code, id) async {
              final myCode =
                  await myQrBloc.getDataUrl(codeUrl: code, idUrl: id);
              if (myCode != null) {
                widget.myCodeQr?.call(myCode.code ?? '');
                myQrBloc.dispose();
                if (mounted) Navigator.of(context).pop(myCode.code ?? '');
              }
            });
        if (!valueMyQrCode) {
          myQrBloc.dispose();
          showDialog(onSubmit: () {
            startScan();
          });
        }
      } else {
        qrCodeScanner(uriString: results.first.rawValue);
      }
    } catch (err) {
      showDialog();
    }
  }

  goToTransfer({Uri? uri, Function? onSubmit}) async {
    if (widget.typecall == 0) {
      final moneyRequest = await _requestTransactionBloc.getDetailMoneyTransfer(
          id: uri?.pathSegments.last ?? '');
      if (!mounted) return;
      if (moneyRequest != null) {
        TransferModel model = TransferModel(
          targetBankName: moneyRequest.bankName,
          targetAccountNumber: moneyRequest.accountNo,
          targetAccountName: TiengViet.parse(moneyRequest.accountName ?? ''),
          amount: moneyRequest.amount,
          note: moneyRequest.description ?? '',
        );
        if (widget.onDetectedTransfer != null) {
          widget.onDetectedTransfer!(model);
        } else {
          popScreen(context);
          goToTransferAcceptV2(context, model, onPressDone: () {
            onSubmit?.call();
          }).then((value) => Navigator.of(context).pop());
        }
      } else {
        DialogUtil.alert(context, S.of(context).error_validator_request_dialog,
            onSubmit: () {
          _prefs.setQrDeepLink('');
          onSubmit?.call();
        });
      }
    } else {
      showQrLoginDialog(onSubmit: () {
        _prefs.setQrDeepLink(uri.toString());
        Navigator.of(context).pop();
      });
    }
  }

  goToTransfer247VietQr({String? qrCode, Function? onSubmit}) async {
    if (widget.typecall == 0) {
      final decode = await _blocVietQr.decodeVietQr(vietQrCode: qrCode ?? '');
      if (!mounted) return;
      if (decode != null) {
        TransferModel model = TransferModel(
            targetAccountNumber: decode.accountNo,
            targetBankCode: decode.bankIdNapas,
            targetBankShortName: decode.bankShortName,
            targetBankName: decode.bankName,
            targetBankImage: decode.url,
            targetBankImageType: decode.bankImageType,
            targetBankCitad: decode.bankCitad,
            targetBankIdNapas: decode.bankIdNapas,
            targetBankCodeId: decode.bankCode,
            is247: true,
            amount: decode.amount,
            note: decode.description,
            isDisableAfScanVietQR: (decode.amount ?? 0) > 0,
            isAccount: true,
            channelCode: "2");
        if (widget.onDetectedTransfer != null) {
          widget.onDetectedTransfer!(model);
        } else {
          popScreen(context);
          goToTransferAcceptV2(context, model, onPressDone: () {
            onSubmit?.call();
          });
        }
      }
    } else {
      _prefs.setQrDeepLink(qrCode ?? '');
      popScreen(context, qrCode);
    }
  }

  goToPayBox({String? qrCode}) {
    if (widget.typecall == 0) {
      final data = qrCode?.split('|') ?? [];
      handleScanPaybox(data);
    } else {
      showQrLoginDialog(onSubmit: () {
        if (qrCode != null) {
          _prefs.setQrDeepLink(qrCode);
        }
        Navigator.of(context).pop();
      });
    }
  }

  goToQrOpenCard({Uri? uri, Function? onSubmit}) async {
    if (widget.typecall == 0) {
      final result = await _openCardBloc.getStmInfo(
          transactionId: uri?.pathSegments.last ?? '');
      if (!mounted) return;
      if (result != null) {
        final resultGo = await go(context,
            OpenCardQrPage(transactionId: uri?.pathSegments.last ?? ''));
        if (resultGo == null && mounted) Navigator.of(context).pop();
      }
    } else {
      showQrLoginDialog(onSubmit: () {
        _prefs.setQrDeepLink(uri.toString());
        Navigator.of(context).pop();
      });
    }
  }

  void handleScanPaybox(List<String> values) {
    // data scan từ PAYBOX QR có form: UMEE_PAIR|deviceId|pairingKey|name|model|serial
    if (values.length == 6) {
      go(
          context,
          ListMagicPairPayboxPage(
            payBox: PayBox.fromScanQr(
                name: values[3],
                model: values[4],
                serial: values[5],
                deviceId: values[1],
                pairingKey: values[2]),
          ));
    } else {
      DialogUtil.alert(context, S.of(context).qr_code_error_message);
    }
  }

  Future<void> _handleScanKlbPayQr(
      {required List<String> qrCodeAttributes}) async {
    if (qrCodeAttributes.length == 5) {
      KlbPayLoginModel model = KlbPayLoginModel(
          sessionId: qrCodeAttributes[1],
          sessionKey: qrCodeAttributes[2],
          deviceName: qrCodeAttributes[3],
          location: qrCodeAttributes[4]);
      await goPresent(context, KlbPayQrPage(model: model), enableDrag: false);
    } else {
      DialogUtil.alert(context, 'Mã QR không đúng định dạng');
    }
  }
}

class QrScannerOverlayShape extends ShapeBorder {
  QrScannerOverlayShape({
    this.borderColor = Colors.red,
    this.borderWidth = 3.0,
    this.overlayColor = const Color.fromRGBO(0, 0, 0, 80),
    this.borderRadius = 0,
    this.borderLength = 40,
    double? cutOutSize,
    double? cutOutWidth,
    double? cutOutHeight,
    this.cutOutBottomOffset = 0,
  })  : cutOutWidth = cutOutWidth ?? cutOutSize ?? 250,
        cutOutHeight = cutOutHeight ?? cutOutSize ?? 250 {
    assert(
      borderLength <=
          min(this.cutOutWidth, this.cutOutHeight) / 2 + borderWidth * 2,
      "Border can't be larger than ${min(this.cutOutWidth, this.cutOutHeight) / 2 + borderWidth * 2}",
    );
    assert(
        (cutOutWidth == null && cutOutHeight == null) ||
            (cutOutSize == null && cutOutWidth != null && cutOutHeight != null),
        'Use only cutOutWidth and cutOutHeight or only cutOutSize');
  }

  final Color borderColor;
  final double borderWidth;
  final Color overlayColor;
  final double borderRadius;
  final double borderLength;
  final double cutOutWidth;
  final double cutOutHeight;
  final double cutOutBottomOffset;

  @override
  EdgeInsetsGeometry get dimensions => const EdgeInsets.all(10);

  @override
  Path getInnerPath(Rect rect, {TextDirection? textDirection}) {
    return Path()
      ..fillType = PathFillType.evenOdd
      ..addPath(getOuterPath(rect), Offset.zero);
  }

  @override
  Path getOuterPath(Rect rect, {TextDirection? textDirection}) {
    Path getLeftTopPath(Rect rect) {
      return Path()
        ..moveTo(rect.left, rect.bottom)
        ..lineTo(rect.left, rect.top)
        ..lineTo(rect.right, rect.top);
    }

    return getLeftTopPath(rect)
      ..lineTo(
        rect.right,
        rect.bottom,
      )
      ..lineTo(
        rect.left,
        rect.bottom,
      )
      ..lineTo(
        rect.left,
        rect.top,
      );
  }

  @override
  void paint(Canvas canvas, Rect rect, {TextDirection? textDirection}) {
    final width = rect.width;
    final borderWidthSize = width / 2;
    final height = rect.height;
    final borderOffset = borderWidth / 2;
    // final borderLength =
    //     borderLength > min(cutOutHeight, cutOutHeight) / 2 + borderWidth * 2
    //         ? borderWidthSize / 2
    //         : borderLength;
    // final cutOutWidth =
    //     cutOutWidth < width ? cutOutWidth : width - borderOffset;
    // final cutOutHeight =
    //     cutOutHeight < height ? cutOutHeight : height - borderOffset;

    final backgroundPaint = Paint()
      ..color = overlayColor
      ..style = PaintingStyle.fill;

    final borderPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth;

    final boxPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.fill
      ..blendMode = BlendMode.dstOut;

    final cutOutRect = Rect.fromLTWH(
      rect.left + width / 2 - cutOutWidth / 2 + borderOffset,
      -cutOutBottomOffset +
          rect.top +
          height / 2 -
          cutOutHeight / 2 +
          borderOffset,
      cutOutWidth - borderOffset * 2,
      cutOutHeight - borderOffset * 2,
    );

    canvas
      ..saveLayer(
        rect,
        backgroundPaint,
      )
      ..drawRect(
        rect,
        backgroundPaint,
      )
      // Draw top right corner
      ..drawRRect(
        RRect.fromLTRBAndCorners(
          cutOutRect.right - borderLength,
          cutOutRect.top,
          cutOutRect.right,
          cutOutRect.top + borderLength,
          topRight: Radius.circular(borderRadius),
        ),
        borderPaint,
      )
      // Draw top left corner
      ..drawRRect(
        RRect.fromLTRBAndCorners(
          cutOutRect.left,
          cutOutRect.top,
          cutOutRect.left + borderLength,
          cutOutRect.top + borderLength,
          topLeft: Radius.circular(borderRadius),
        ),
        borderPaint,
      )
      // Draw bottom right corner
      ..drawRRect(
        RRect.fromLTRBAndCorners(
          cutOutRect.right - borderLength,
          cutOutRect.bottom - borderLength,
          cutOutRect.right,
          cutOutRect.bottom,
          bottomRight: Radius.circular(borderRadius),
        ),
        borderPaint,
      )
      // Draw bottom left corner
      ..drawRRect(
        RRect.fromLTRBAndCorners(
          cutOutRect.left,
          cutOutRect.bottom - borderLength,
          cutOutRect.left + borderLength,
          cutOutRect.bottom,
          bottomLeft: Radius.circular(borderRadius),
        ),
        borderPaint,
      )
      ..drawRRect(
        RRect.fromRectAndRadius(
          cutOutRect,
          Radius.circular(borderRadius),
        ),
        boxPaint,
      )
      ..restore();
  }

  @override
  ShapeBorder scale(double t) {
    return QrScannerOverlayShape(
      borderColor: borderColor,
      borderWidth: borderWidth,
      overlayColor: overlayColor,
    );
  }
}
