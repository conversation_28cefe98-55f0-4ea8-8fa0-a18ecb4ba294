import 'package:common/navigator.dart';
import 'package:common/utils/dialog_util.dart';
import 'package:common/widgets/bottom_button.dart';
import 'package:common/widgets/common_textfield.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_bloc/bloc/terms_and_conditions/terms_and_conditions_bloc.dart';
import 'package:ksb_common/shared/assets.dart' as klb_common;
import 'package:ksb_common/shared/theme/custom_theme.dart';
import 'package:ksb_common/shared/widgets/background_app.dart';
import 'package:mobile_banking/ui_v2/profile_account/etoken/etoken_setup.dart';

import '../../../assets.dart';
import '../../../assets/images.dart';
import '../../../di/inject.dart';
import '../../../generated/l10n.dart';
import '../../../utils/auth_fatsh.dart';
import '../../home/<USER>';
import '../../otp/otp_widget.dart';
import '../../widget/constant.dart';
import '../../widget/loading/loading_view.dart';
import '../etoken/custom/content_otp.dart';
import 'widget/select_accounts_widget.dart';
import 'widget/sms_banking_fees_widget.dart';

class SmsBankingRegisterPage extends StatefulWidget {
  const SmsBankingRegisterPage({
    super.key,
    required this.accounts,
  });

  final List<AccountModel> accounts;

  @override
  State<SmsBankingRegisterPage> createState() => _SmsBankingRegisterPageState();
}

class _SmsBankingRegisterPageState extends State<SmsBankingRegisterPage> {
  final _bloc = Injection.injector.get<SmsBankingBloc>();
  final _blocTerm = Injection.injector.get<TermsAndConditionsBloc>();
  final _blocOtp = Injection.injector.get<OtpVerifyBloc>();

  List<String>? _selectedAccounts;
  PreviewUpdateSmsBankingResponse? _currentPreview;

  @override
  void initState() {
    super.initState();
    _bloc.streamSubs.add(_bloc.errorStream.listen((event) {
      if (event?.isNotEmpty == true) {
        DialogUtil.alert(context, event);
      }
    }));
    _bloc.getFees();
  }

  @override
  void dispose() {
    _bloc.dispose();
    _blocTerm.dispose();
    _blocOtp.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BackgroundAppBarImage(
      image: ImageAssets.svgAssets(
        klb_common.ImageAssets.img_appbar,
        fit: BoxFit.fill,
        width: MediaQuery.of(context).size.width,
      ),
      appBar: getAppBarDark(
        context,
        title: S.of(context).profile_sms_setting_title_sms_register,
      ),
      child: LoadingView(
        loadingStream: _bloc.progressVisible,
        child: DefaultTextStyle(
          style: StyleApp.descStyle(context),
          child: _getBody(),
        ),
      ),
    );
  }

  Widget _getBody() {
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            padding: EdgeInsets.all(kOuterPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _tips(),
                SizedBox(height: kOuterPadding),
                ..._forms(),
                SizedBox(height: kOuterPadding),
                Text(S.of(context).profile_sms_setting_auto_fees),
                InkWell(
                  onTap: _showFees,
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: kTinyPadding),
                    child: Text(
                      S.of(context).profile_sms_setting_fees_common,
                      style: TextStyle(
                        color: DynamicTheme.of(context)?.customColor.newBlue,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        StreamBuilder<bool>(
          stream: _bloc.validRegisterStream,
          builder: (_, snapshot) {
            return BottomButton(
              hint: Padding(
                padding: EdgeInsets.only(
                    left: kOuterPadding,
                    right: kOuterPadding,
                    bottom: kOuterPadding),
                child: GestureDetector(
                  onTap: () {
                    _bloc.acceptTerm
                        .add(!(_bloc.acceptTerm.valueOrNull ?? false));
                  },
                  child: _buildTerm(),
                ),
              ),
              isDisable: snapshot.data != true,
              title: S.of(context).common_register,
              onTap: _onSubmit,
            );
          },
        ),
      ],
    );
  }

  Widget _tips() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.background,
        borderRadius: BorderRadius.circular(8),
      ),
      padding: EdgeInsets.all(kInnerPadding).copyWith(right: kOuterPadding),
      child: Row(
        children: [
          ImageAssets.svgAssets(ImagesResource.imgPhone),
          SizedBox(width: kSmallPadding),
          Flexible(
            child: Text(
              S.of(context).profile_sms_setting_des1_sms_setting,
              style: TextStyle(
                color: Theme.of(context).primaryColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _forms() {
    return [
      Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _checkbox(false),
          SizedBox(width: kInnerPadding),
          Expanded(
            child: CommonTextField(
              enabled: false,
              borderRadius: kTinyPadding,
              textController: _bloc.phoneNumberController1,
              labelText: '${S.of(context).common_choose} 1',
              labelStyle: StyleApp.bodyStyle(context),
              labelColor: const Color(0xFF333333).withOpacity(0.4),
              backgroundColor: const Color(0xFF333333).withOpacity(0.05),
              unFocusColor: Colors.transparent,
            ),
          ),
        ],
      ),
      Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _checkbox(true),
          SizedBox(width: kInnerPadding),
          Expanded(
            child: CommonTextField(
              borderRadius: kTinyPadding,
              textController: _bloc.phoneNumberController2,
              labelText: '${S.of(context).common_choose} 2',
              labelStyle: StyleApp.bodyStyle(context),
              labelColor: const Color(0xFF333333).withOpacity(0.4),
              textInputType: TextInputType.number,
              maxLength: 10,
              maxLinesError: 3,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^0[0-9]*$'))
              ],
              checkNull: (value) {
                return value.isNotEmpty == true
                    ? null
                    : S.of(context).profile_sms_setting_validate_phone_null;
              },
              onCheckError: (value) {
                return _bloc.validatePhone(value)
                    ? null
                    : S.of(context).profile_sms_setting_validate_phone_wrong;
              },
            ),
          ),
        ],
      ),
    ];
  }

  Widget _checkbox(bool value) {
    return Padding(
      padding: EdgeInsets.only(top: kOuterPadding),
      child: StreamBuilder<bool>(
        stream: _bloc.useCustom,
        builder: (_, snapshot) => InkWell(
          child: snapshot.data == value
              ? ImageAssets.svgAssets(ImagesResource.ic_selected_item)
              : ImageAssets.svgAssets(ImagesResource.ic_uncheck),
          onTap: () {
            _bloc.useCustom.add(value);
          },
        ),
      ),
    );
  }

  void _showFees() async {
    if ((_bloc.feesStream.valueOrNull?.fees?.length ?? 0) < 1) {
      await _bloc.getFees();
    }
    final fees = _bloc.feesStream.valueOrNull?.fees?.asList() ?? [];
    if (mounted) {
      SmsBankingFeesWidget(fees: fees).open(context);
    }
  }

  Widget _buildTerm() {
    return StreamBuilder<bool>(
      stream: _bloc.acceptTerm,
      builder: (_, snapshot) {
        return Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: 24.0,
              width: 24.0,
              child: Checkbox(
                value: snapshot.data == true,
                activeColor: Theme.of(context).primaryColor,
                onChanged: (value) {
                  _bloc.acceptTerm.add(value == true);
                },
              ),
            ),
            const SizedBox(
              width: 5,
            ),
            Expanded(
              child: Text.rich(
                TextSpan(
                  text: S.of(context).read_and_agree,
                  children: [
                    TextSpan(
                      text:
                          " ${S.of(context).term_and_condition}${S.of(context).of_ksbank}",
                      style: TextStyle(
                        color: DynamicTheme.of(context)?.customColor.newRed,
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () => _getTermsAndConditions("2"),
                    ),
                  ],
                ),
                textAlign: TextAlign.start,
              ),
            ),
          ],
        );
      },
    );
  }

  void _getTermsAndConditions(String id) async {
    final getData = await _blocTerm.getTermsAndConditions(id: id);
    if (getData.content?.isNotEmpty == true && mounted) {
      go(context, HomeBannerContent(modelTerms: getData));
    } else {
      openUrl(
          url:
              "https://kienlongbank.com/dieu-khoan-dieu-kien-su-dung-dich-vu-ngan-hang-dien-tu");
    }
  }

  void _onSubmit() async {
    final result = await SelectAccountsWidget(
      accounts: widget.accounts,
      selectedItems: _selectedAccounts,
    ).select(context);
    if (result?.isNotEmpty == true) {
      _selectedAccounts = result;
      _onConfirm();
    }
  }

  void _onConfirm() {
    DialogUtil.confirm(
      context,
      Text(S.of(context).profile_sms_setting_confirm_register),
      title: S.of(context).confirm_info,
      cancelText: S.of(context).back,
      styleConfirm: const TextStyle(fontWeight: FontWeight.bold),
      onSubmit: _register,
    );
  }

  Future<PreviewUpdateSmsBankingResponse?> _preview(String phoneNumber) async {
    _currentPreview = await _bloc.previewUpdate(
      action: PreviewUpdateSmsBankingRequestActionEnum.REGISTER,
      phoneNumber: phoneNumber,
      accounts: _selectedAccounts,
    );
    return _currentPreview;
  }

  void _register() async {
    final phoneNumber = _bloc.useCustom.valueOrNull == true
        ? _bloc.phoneNumberController2.text
        : _bloc.phoneNumberController1.text;
    final preview = await _preview(phoneNumber);
    if (preview != null && mounted) {
      switch (preview.authType) {
        case PreviewUpdateSmsBankingResponseAuthTypeEnum.SOFT_OTP:
          final hasEToken = await _bloc.hasEToken();
          final data = await _bloc.preferences.secretKey ?? '';
          if (hasEToken && mounted) {
            final token = await FastAuth().checkAuth(context, 0, data);
            if (token != null && token.isNotEmpty) {
              final result = await _update(token);
              if (result == true) {
                _success(phoneNumber);
              }
            }
          } else if (mounted) {
            goToSetupToken(context);
          }
          break;
        case PreviewUpdateSmsBankingResponseAuthTypeEnum.SMS_OTP:
          final result = await goPresent<bool>(
            context,
            OtpWidget(
              bloc: _blocOtp,
              title: S.of(context).common_otp_verify_title,
              titleText: ContentOtp(phoneNumber: phoneNumber),
              resendOtp: () => _preview(phoneNumber),
              verifyOtp: _update,
            ),
          );
          if (result == true) {
            _success(phoneNumber);
          }
          break;
        default:
          break;
      }
    }
  }

  Future<bool> _update(String otp) {
    return _bloc.update(
      UpdateSmsBankingRequest(
        (builder) => builder
          ..requestId = _currentPreview?.requestId
          ..softOtp = VerifySoftOtpRequest((b) => b..otp = otp).toBuilder(),
      ),
    );
  }

  void _success(String phoneNumber) {
    popRoutes(context, value: true);
    DialogUtil.showFlushBar(
      context,
      '${S.of(context).profile_sms_setting_success_register} ${phoneNumber.length > 7 ? phoneNumber.replaceRange(3, 7, '****') : phoneNumber}',
    );
  }
}
