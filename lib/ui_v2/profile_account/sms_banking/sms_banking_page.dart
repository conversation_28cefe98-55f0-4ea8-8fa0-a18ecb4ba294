import 'package:common/utils/dialog_util.dart';
import 'package:common/widgets/bottom_button.dart';
import 'package:common/widgets/common_textfield.dart';
import 'package:common/widgets/empty_widget.dart';
import 'package:common/widgets/ink_well_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_bloc/bloc/model/global_model.dart';
import 'package:ksb_common/shared/assets.dart' as klb_common;
import 'package:ksb_common/shared/constant.dart';
import 'package:ksb_common/shared/theme/custom_theme.dart';
import 'package:ksb_common/shared/widgets/background_app.dart';
import 'package:mobile_banking/ui_v2/profile_account/etoken/etoken_setup.dart';
import 'package:mobile_banking/ui_v2/profile_account/sms_banking/widget/choose_phone_widget.dart';
import 'package:rxdart/rxdart.dart';

import '../../../assets.dart';
import '../../../assets/images.dart';
import '../../../di/inject.dart';
import '../../../generated/l10n.dart';
import '../../../utils/auth_fatsh.dart';
import '../../../utils/navigator.dart';
import '../../otp/otp_widget.dart';
import '../../widget/loading/loading_view.dart';
import '../etoken/custom/content_otp.dart';
import 'sms_banking_register_page.dart';
import 'widget/select_accounts_widget.dart';

class SmsBankingPage extends StatefulWidget {
  const SmsBankingPage({super.key});

  @override
  State<SmsBankingPage> createState() => _SmsBankingPageState();
}

class _SmsBankingPageState extends State<SmsBankingPage> {
  final _bloc = Injection.injector.get<SmsBankingBloc>();
  final _blocListAccounts = Injection.injector.get<ListAccountsBloc>();
  final _blocOtp = Injection.injector.get<OtpVerifyBloc>();

  late final _loadingStream = Rx.combineLatest(
    [
      _bloc.progressVisible,
      _blocListAccounts.progressVisible,
    ],
    (value) => value.contains(true),
  );

  GetSmsBankingDetailResponse? _currentDetail;
  List<String>? _selectedAccounts;
  PreviewUpdateSmsBankingResponse? _currentPreview;

  @override
  void initState() {
    super.initState();
    _blocListAccounts.streamSubs.add(_blocListAccounts.items.listen((value) {
      if (value.isEmpty == true) {
        DialogUtil.alert(
          context,
          S.of(context).profile_sms_setting_error_account_empty,
          onSubmit: () {
            Navigator.of(context).pop();
          },
        );
      }
    }));
    _blocListAccounts.getAccountList();
    _bloc.streamSubs.addAll([
      _bloc.errorStream.listen((event) {
        if (event?.isNotEmpty == true) {
          DialogUtil.alert(context, event, onSubmit: () {
            if (event!.contains('4000042') && mounted) {
              Navigator.of(context).pop();
            }
          });
        }
      }),
      _bloc.detailStream.listen((event) {
        // default _selectedAccounts is current `accounts` list
        _selectedAccounts = _bloc.addCustomAccount(event);
      }),
    ]);
    _bloc.getSmsBankingDetail();
  }

  @override
  void dispose() {
    _bloc.dispose();
    _blocListAccounts.dispose();
    _blocOtp.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BackgroundAppBarImage(
      image: ImageAssets.svgAssets(
        klb_common.ImageAssets.img_appbar,
        fit: BoxFit.fill,
        width: MediaQuery.of(context).size.width,
      ),
      appBar: getAppBarDark(
        context,
        title: S.of(context).profile_sms_setting_title_sms_setting,
      ),
      child: LoadingView(
        loadingStream: _loadingStream,
        child: _getBody(),
      ),
    );
  }

  Widget _getBody() {
    return StreamBuilder<GetSmsBankingDetailResponse?>(
      stream: _bloc.detailStream,
      builder: (_, snapshot) {
        _currentDetail = snapshot.data;
        if (_currentDetail == null) return const SizedBox();
        switch (_currentDetail?.smsPhone?.length) {
          case 1:
          case 2:
            return _detail();
        }
        return _emptyWidget();
      },
    );
  }

  Widget _detail() {
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: kPaddingStandard,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: _forms(_currentDetail?.smsPhone?.asMap()),
                  ),
                ),
                Divider(
                  height: kSmallPadding,
                  thickness: kSmallPadding,
                ),
                Padding(
                  padding: kPaddingStandard,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        S.of(context).profile_sms_setting_fees_VAT_2,
                        style: StyleApp.subtitle1(context, true),
                      ),
                      SizedBox(height: kOuterPadding),
                      CommonTextField(
                        enabled: false,
                        borderRadius: kTinyPadding,
                        value: '${GlobalModel.formatMoney(
                          _currentDetail?.fee?.toDouble(),
                          hasCurrency: false,
                        )}/${_currentDetail?.description}',
                        labelText: S.of(context).profile_sms_setting_fees_VAT_2,
                        labelStyle: StyleApp.bodyStyle(context),
                        labelColor: const Color(0xFF333333).withOpacity(0.4),
                        backgroundColor:
                            const Color(0xFF333333).withOpacity(0.05),
                        unFocusColor: Colors.transparent,
                      ),
                      SizedBox(height: kOuterPadding),
                      Text(
                        S.of(context).profile_sms_setting_cancel_desc,
                        style: StyleApp.descStyle(context),
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        const Divider(height: 1),
        SafeArea(
          top: false,
          child: Padding(
            padding: const EdgeInsets.all(15),
            child: Row(
              children: [
                Expanded(
                  child: InkWellButton(
                    title: S.of(context).cancel_register,
                    textColor: Theme.of(context).primaryColor,
                    buttonColor: const Color(0xFFF4F4F7),
                    onTap: _onConfirmCancel,
                  ),
                ),
                if (_currentDetail?.smsPhone?.length == 1) ...[
                  SizedBox(width: kOuterPadding),
                  Expanded(
                    child: StreamBuilder<bool>(
                      stream: _bloc.validAddStream,
                      builder: (_, snapshot) {
                        return InkWellButton(
                          isDisable: snapshot.data != true,
                          title:
                              S.of(context).profile_sms_setting_register_more,
                          onTap: _onSubmit,
                        );
                      },
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _onConfirmCancel() {
    final now = DateTime.now();
    final date =
        DateTime(now.year, now.month + 1, 1).subtract(const Duration(days: 1));
    DialogUtil.confirm(
      context,
      Text(
          '${S.of(context).profile_sms_setting_cancel_message} ${date.formatDMY}'),
      title: S.of(context).profile_sms_setting_cancel,
      cancelText: S.of(context).back,
      styleConfirm: const TextStyle(fontWeight: FontWeight.bold),
      onSubmit: _submitCancel,
    );
  }

  void _submitCancel() async {
    List<int>? result;
    final phones =
        (_currentDetail?.smsPhone?.map((e) => e.phone ?? '').toList() ?? [])
          ..removeWhere((e) => e.isNotEmpty != true);
    if (phones.length == 1) {
      result = [0];
    } else {
      result = await ChoosePhoneWidget(phones: phones).select(context);
    }
    if (result?.isNotEmpty == true) {
      final preview = await _bloc.previewUpdate(
        action: PreviewUpdateSmsBankingRequestActionEnum.CANCEL,
        indexPhoneRemove: result?.join(','),
        accounts: _bloc.addCustomAccount(_currentDetail),
      );
      if (preview?.requestId?.isNotEmpty == true) {
        final hasEToken = await _bloc.hasEToken();
        final data = await _bloc.pref.secretKey ?? '';
        if (hasEToken && mounted) {
          final token = await FastAuth().checkAuth(context, 0, data);
          if (token != null && token.isNotEmpty) {
            final result = await _bloc.update(
              UpdateSmsBankingRequest(
                (builder) => builder
                  ..requestId = preview!.requestId
                  ..softOtp =
                      VerifySoftOtpRequest((b) => b..otp = token).toBuilder(),
              ),
            );
            if (result == true && mounted) {
              DialogUtil.showFlushBar(
                  context, S.of(context).profile_sms_setting_success_cancel);
              _bloc.getSmsBankingDetail();
            }
          }
        } else {
          goToSetupToken(context);
        }
      }
    }
  }

  Future<bool> _update(String otp) {
    return _bloc.update(
      UpdateSmsBankingRequest(
        (builder) => builder
          ..requestId = _currentPreview?.requestId
          ..softOtp = VerifySoftOtpRequest((b) => b..otp = otp).toBuilder(),
      ),
    );
  }

  void _onSubmit() async {
    if (_blocListAccounts.items.valueOrNull?.isNotEmpty != true) {
      _blocListAccounts.reload();
      return;
    }
    final result = await SelectAccountsWidget(
      accounts: _blocListAccounts.items.valueOrNull ?? [],
      selectedItems: _selectedAccounts,
    ).select(context);
    if (result?.isNotEmpty == true) {
      _selectedAccounts = result;
      _onConfirm();
    }
  }

  void _onConfirm() {
    DialogUtil.confirm(
      context,
      Text(S.of(context).profile_sms_setting_confirm_register),
      title: S.of(context).confirm_info,
      cancelText: S.of(context).back,
      styleConfirm: const TextStyle(fontWeight: FontWeight.bold),
      onSubmit: _register,
    );
  }

  Future<PreviewUpdateSmsBankingResponse?> _preview(String phoneNumber) async {
    _currentPreview = await _bloc.previewUpdate(
      action: PreviewUpdateSmsBankingRequestActionEnum.REGISTER,
      phoneNumber: phoneNumber,
      accounts: _selectedAccounts,
    );
    return _currentPreview;
  }

  void _register() async {
    final phoneNumber = _bloc.phoneNumberController2.text;
    final preview = await _preview(phoneNumber);
    if (preview != null && mounted) {
      switch (preview.authType) {
        case PreviewUpdateSmsBankingResponseAuthTypeEnum.SOFT_OTP:
          final hasEToken = await _bloc.hasEToken();
          final data = await _bloc.pref.secretKey ?? '';
          if (hasEToken && mounted) {
            final token = await FastAuth().checkAuth(context, 0, data);
            if (token != null && token.isNotEmpty) {
              final result = await _update(token);
              if (result == true) {
                _success(phoneNumber);
              }
            }
          } else {
            goToSetupToken(context);
          }
          break;
        case PreviewUpdateSmsBankingResponseAuthTypeEnum.SMS_OTP:
          final result = await goPresent<bool>(
            context,
            OtpWidget(
              bloc: _blocOtp,
              title: S.of(context).common_otp_verify_title,
              titleText: ContentOtp(phoneNumber: phoneNumber),
              resendOtp: () => _preview(phoneNumber),
              verifyOtp: _update,
            ),
          );
          if (result == true) {
            _success(phoneNumber);
          }
          break;
        default:
          break;
      }
    }
  }

  void _success(String phoneNumber) {
    _bloc.getSmsBankingDetail();
    DialogUtil.showFlushBar(
      context,
      '${S.of(context).profile_sms_setting_success_register} ${phoneNumber.length > 7 ? phoneNumber.replaceRange(3, 7, '****') : phoneNumber}',
    );
  }

  List<Widget> _forms(Map<int, SmsPhone>? data) {
    return [
      Text(
        '${S.of(context).phone_number} 1',
        style: StyleApp.subtitle1(context, true),
      ),
      SizedBox(height: kOuterPadding),
      CommonTextField(
        enabled: false,
        borderRadius: kTinyPadding,
        value: data?[0]?.phone,
        labelText: '${S.of(context).phone_number} 1',
        labelStyle: StyleApp.bodyStyle(context),
        labelColor: const Color(0xFF333333).withOpacity(0.4),
        backgroundColor: const Color(0xFF333333).withOpacity(0.05),
        unFocusColor: Colors.transparent,
      ),
      SizedBox(height: kSmallPadding),
      _button(
        S.of(context).profile_sms_setting_account_registed,
        onTap: () => _changeAccount(data?[0]),
      ),
      SizedBox(height: kOuterPadding),
      Text(
        '${S.of(context).phone_number} 2',
        style: StyleApp.subtitle1(context, true),
      ),
      SizedBox(height: kOuterPadding),
      ...(data?.length == 2
          ? [
              CommonTextField(
                enabled: false,
                borderRadius: kTinyPadding,
                value: data?[1]?.phone,
                labelText: '${S.of(context).phone_number} 2',
                labelStyle: StyleApp.bodyStyle(context),
                labelColor: const Color(0xFF333333).withOpacity(0.4),
                backgroundColor: const Color(0xFF333333).withOpacity(0.05),
                unFocusColor: Colors.transparent,
              ),
              // two list is same, so remove one
              // SizedBox(height: kSmallPadding),
              // _button(
              //   S.of(context).profile_sms_setting_account_registed,
              //   onTap: () => _changeAccount(data?[1], index: 1),
              // ),
            ]
          : [
              CommonTextField(
                borderRadius: kTinyPadding,
                textController: _bloc.phoneNumberController2,
                labelText: '${S.of(context).phone_number} 2',
                labelStyle: StyleApp.bodyStyle(context),
                labelColor: const Color(0xFF333333).withOpacity(0.4),
                textInputType: TextInputType.number,
                maxLength: 10,
                maxLinesError: 3,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^0[0-9]*$'))
                ],
                onCheckError: (value) {
                  return _bloc.validatePhone(value)
                      ? (value == data?[0]?.phone
                          ? S
                              .of(context)
                              .profile_sms_setting_validate_phone_duplicate
                          : null)
                      : S.of(context).profile_sms_setting_validate_phone_wrong;
                },
              ),
            ]),
    ];
  }

  Widget _button(
    String text, {
    void Function()? onTap,
  }) {
    return DefaultTextStyle(
      style: TextStyle(
        color: DynamicTheme.of(context)?.customColor.newBlue,
        fontWeight: FontWeight.w500,
      ),
      child: InkWell(
        onTap: onTap,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Flexible(child: Text(text)),
            const SizedBox(width: 4),
            Icon(
              Icons.arrow_drop_down,
              color: DynamicTheme.of(context)?.customColor.newBlue,
            ),
          ],
        ),
      ),
    );
  }

  void _changeAccount(SmsPhone? smsPhone) async {
    if (smsPhone == null) return;
    if (_blocListAccounts.items.valueOrNull?.isNotEmpty != true) {
      _blocListAccounts.reload();
      return;
    }
    final selectedAccount = await SelectAccountsWidget(
      accounts: _blocListAccounts.items.valueOrNull ?? [],
      selectedItems: _bloc.addCustomAccount(_currentDetail),
      checkChanged: true,
      title: S.of(context).profile_sms_setting_account_registed,
      showDesc: false,
    ).select(context);
    if (selectedAccount?.isNotEmpty == true) {
      final preview = await _bloc.previewUpdate(
        action: PreviewUpdateSmsBankingRequestActionEnum.CHANGE_ACCOUNT,
        phoneNumber: smsPhone.phone,
        accounts: selectedAccount,
      );
      if (preview?.requestId?.isNotEmpty == true) {
        final hasEToken = await _bloc.hasEToken();
        final data = await _bloc.pref.secretKey ?? '';
        if (hasEToken && mounted) {
          final token = await FastAuth().checkAuth(context, 0, data);
          if (token != null && token.isNotEmpty) {
            final result = await _bloc.update(
              UpdateSmsBankingRequest(
                (builder) => builder
                  ..requestId = preview!.requestId
                  ..softOtp =
                      VerifySoftOtpRequest((b) => b..otp = token).toBuilder(),
              ),
            );
            if (result == true && mounted) {
              DialogUtil.showFlushBar(
                context,
                S.of(context).profile_sms_setting_success_change,
              );
              _bloc.getSmsBankingDetail();
            }
          }
        } else if (mounted) {
          goToSetupToken(context);
        }
      }
    }
  }

  Widget _emptyWidget() {
    return Column(
      children: [
        Expanded(
          child: LayoutBuilder(
            builder: (_, constrants) => SingleChildScrollView(
              child: ConstrainedBox(
                constraints: BoxConstraints(minHeight: constrants.maxHeight),
                child: Center(
                  child: EmptyWidget(
                    message: S.of(context).profile_sms_setting_empty,
                    icon: ImageAssets.svgAssets(ImagesResource.imgEmptySms),
                    maxLine: 4,
                  ),
                ),
              ),
            ),
          ),
        ),
        BottomButton(
          title: S.of(context).register_now,
          onTap: () async {
            if (_blocListAccounts.items.valueOrNull?.isNotEmpty != true) {
              _blocListAccounts.reload();
              return;
            }
            final result = await go(
              context,
              SmsBankingRegisterPage(
                accounts: _blocListAccounts.items.valueOrNull ?? [],
              ),
            );
            if (result == true) _bloc.getSmsBankingDetail();
          },
        ),
      ],
    );
  }
}
