import 'package:common/utils/dialog_util.dart';
import 'package:common/widgets/bottom_button.dart';
import 'package:common/widgets/bottom_sheet_widget.dart';
import 'package:common/widgets/common_size.dart';
import 'package:common/widgets/style_app.dart';
import 'package:flutter/material.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_bloc/bloc/model/card/card_model.dart';
import 'package:ksb_common/shared/assets.dart' as klb_common;
import 'package:ksb_common/shared/theme/custom_theme.dart';
import 'package:ksb_common/shared/widgets/background_app.dart';
import 'package:mobile_banking/assets/assets.dart';
import 'package:mobile_banking/assets/dimens.dart';
import 'package:mobile_banking/assets/images.dart';
import 'package:mobile_banking/di/inject.dart';
import 'package:mobile_banking/generated/l10n.dart';
import 'package:mobile_banking/route.dart';
import 'package:mobile_banking/ui_v2/card/card_control_page.dart';
import 'package:mobile_banking/ui_v2/card/card_transaction_history.dart';
import 'package:mobile_banking/ui_v2/card/payment_card_page.dart';
import 'package:mobile_banking/ui_v2/card/pin_settings/pin_settings_page.dart';
import 'package:mobile_banking/ui_v2/card/virtual_card/shared/utils.dart';
import 'package:mobile_banking/ui_v2/card/virtual_card/shared/widgets/vc_activate_info.dart';
import 'package:mobile_banking/ui_v2/card/virtual_card/shared/widgets/vc_detail_section_widget.dart';
import 'package:mobile_banking/ui_v2/card/virtual_card/shared/widgets/virtual_card_success.dart';
import 'package:mobile_banking/ui_v2/card/virtual_card/virtual_card_general/list_card_empty/list_card_empty.dart';
import 'package:mobile_banking/ui_v2/card/virtual_card/shared/widgets/vc_create_bottom_sheet.dart';
import 'package:mobile_banking/ui_v2/card/virtual_card/virtual_card_general/virtual_card_create/virtual_card_create.dart';
import 'package:mobile_banking/ui_v2/card/virtual_card/virtual_card_general/virtual_card_cvv.dart';
import 'package:mobile_banking/ui_v2/card/widgets/action_card_widget.dart';
import 'package:mobile_banking/ui_v2/card/widgets/card_image_widget.dart';
import 'package:mobile_banking/ui_v2/card/widgets/card_info_widget.dart';
import 'package:mobile_banking/ui_v2/card/widgets/card_status_widget.dart';
import 'package:mobile_banking/ui_v2/card/widgets/input_four_last_digit.dart';
import 'package:mobile_banking/ui_v2/etoken/etoken_pin_code.dart';
import 'package:mobile_banking/ui_v2/profile_account/etoken/etoken_setup.dart';
import 'package:ksb_common/shared/widgets/auth_transaction_mixin_common.dart';
import 'package:mobile_banking/ui_v2/widget/loading/loading_area.dart';
import 'package:mobile_banking/ui_v2/widget/loading/loading_view.dart';
import 'package:mobile_banking/ui_v2/widget/page/dots_indicator.dart';
import 'package:mobile_banking/utils/navigator.dart';
import 'package:rxdart/rxdart.dart';
import 'package:ksb_common/shared/constant.dart' as ksb_common_constant;
import 'package:url_launcher/url_launcher_string.dart';

import 'active_card_done.dart';
import 'card_automatic_debit.dart';

class CardHomePage extends StatefulWidget {
  const CardHomePage({
    Key? key,
  }) : super(key: key);

  @override
  State<CardHomePage> createState() => _CardHomePageState();
}

class _CardHomePageState extends FlowTransactionState<CardHomePage>
    with SingleTickerProviderStateMixin {
  final _bloc = Injection.injector.get<CardBloc>();
  final env = Injection.injector.get<Environment>();
  final _pageController = PageController(
    viewportFraction: 0.75,
    keepPage: false,
  );
  final _pageBehavior = BehaviorSubject<int>.seeded(0);
  final _titleBehavior = BehaviorSubject<String?>.seeded(null);
  final _env = Injection.injector.get<Environment>();
  final debouncer = Debouncer(milliseconds: 300);
  late ScrollController _scrollController;
  List<CardModel> _listCard = [];

  @override
  void initState() {
    super.initState();
    _scrollListen();
    _bloc.listCardStream.listen((event) {
      if (event != null) {
        _listCard = event.data;
      }
      setState(() {});
    });
    _bloc.streamSubs.add(
      _bloc.statusStream.listen((event) {
        if (event != null) {
          listenError(event);
        }
      }),
    );

    _env.appEventDataStream.listen(
      (event) async {
        if (event.appEvent == AppEvent.ADD_NEW_VR_CARD) {
          if (mounted) {
            final indexCard = await _bloc.indexCardToJump(event.data);
            if (indexCard > 0) {
              _pageController.animateToPage(
                indexCard,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeIn,
              );
            }
          }

          ///clean event
          env.appEventDataSink.add(AppEventData());
        }
      },
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _pageController.dispose();
    _bloc.dispose();
    _pageBehavior.close();
    _titleBehavior.close();
    debouncer.cancel();
    super.dispose();
  }

  void listenError(BaseResponseModel error) {
    if (!error.message.isNullOrEmpty && mounted) {
      if (error.statusCode == CardStatusCode.HAS_NOT_EKYC ||
          error.statusCode == CardStatusCode.HAS_VIRTUAL_CARD_DO_NOT_ACTIVE) {
        bool isActiveCard =
            error.statusCode == CardStatusCode.HAS_VIRTUAL_CARD_DO_NOT_ACTIVE;

        DialogUtil.confirm(
          context,
          Text(error.message ?? ""),
          onSubmit: () => isActiveCard
              ? launchUrlString("tel:19006929")
              : updateCCCD(route: RoutePaths.card),
          submitText: isActiveCard
              ? S.of(context).call_19006929
              : S.of(context).verify_now,
          title: S.of(context).virtual_card_notification,
        );
        return;
      }

      DialogUtil.alert(
        context,
        error.message,
        title: S.of(context).virtual_card_notification,
      );
    }
  }

  _scrollListen() {
    _scrollController = ScrollController()
      ..addListener(() {
        final offset = _scrollController.offset;
        String? title = (offset > 50
            ? _bloc.currentCard?.productName
            : S.of(context).card_info_title_card);
        if (title != _titleBehavior.valueOrNull) {
          _titleBehavior.add(title);
        }
      });
  }

  void _onTapCVV(BuildContext context) async {
    final hasEToken = await _bloc.hasEToken();
    if (hasEToken) {
      final value = await goPresent<String>(
        context,
        const EtokenPinCode(isSubmit: true),
      );
      if (value != null && value.isNotEmpty) {
        go(context, VirtualCardCVV(otp: value, card: _bloc.currentCard));
      }
    } else {
      goToSetupToken(context);
    }
  }

  void _activeCardSuccess(BuildContext context, CardModel? result) {
    go(
      context,
      VirtualCardSuccess(
        title: S.of(context).card_activate_sucess_title_success,
        hasThankful: false,
        titleTable: S.of(context).card_info,
        listFieldInfo: [
          VCDetailSectionWidget(
            title: S.of(context).card_info_label_card_full_name_owner,
            valueFlex: 2,
            value: result?.cardName,
            hasDivider: true,
          ),
          VCDetailSectionWidget(
            title: S.of(context).card_info_label_card_number,
            value: result?.cardNo,
            hasDivider: true,
          ),
          VCDetailSectionWidget(
            title: S.of(context).type_card_physics,
            value: result?.productName,
            valueFlex: 2,
            hasDivider: true,
          ),
          VCDetailSectionWidget(
            title: S.of(context).card_info_label_open_day,
            value: result?.anniversaryDateMMYY,
            hasDivider: true,
          ),
          VCDetailSectionWidget(
            title: S.of(context).expiration_date,
            value: result?.validTo,
            hasDivider: true,
          ),
          VCDetailSectionWidget(
            title: S.of(context).affiliate_account,
            value: result?.accountNo,
            valueFlex: 1,
          ),
        ],
        titleButton1: S.of(context).common_complete,
        action1: () {
          Navigator.pop(context);
          _bloc.currentCard?.hasDetail = false;
          _bloc.getDetailCard();
        },
      ),
    );
  }

  void activeNonePhysicCard(String cardId) async {
    final hasEToken = await _bloc.hasEToken();
    if (hasEToken) {
      final value = await goPresent<String>(
        context,
        const EtokenPinCode(isSubmit: true),
      );
      if (value?.isNotEmpty == true) {
        final result = await _bloc.activeVirtualCard(cardId, value ?? "");
        if (result != null) {
          _activeCardSuccess(context, _bloc.currentCard);
        }
      }
    } else {
      goToSetupToken(context);
    }
  }

  void onCreateVRCard() async {
    Navigator.of(context).pop();
    goWithRoute(
      context,
      const VirtualCardCreate(),
      RoutePaths.select_card_page, // setting route
    );
  }

  @override
  Widget build(BuildContext context) {
    return BackgroundAppBarImage(
      stream: _bloc.loadingScreenStream,
      image: klb_common.ImageAssets.img_appbar,
      appBar: _getAppBar(),
      floatingActionButton: _resolvedFAB(),
      child: LoadingView(
        backgroundColor: Colors.white,
        loadingStream: _bloc.progressVisible,
        child: _getBody(),
      ),
      onTryClick: () => _bloc.onRefresh(),
    );
  }

  _getAppBar() {
    return getAppBarDark(
      context,
      title: StreamBuilder<String?>(
        stream: _titleBehavior.stream,
        builder: (context, snapshot) {
          String title = S.of(context).card_info_title_card;
          if (snapshot.hasData) {
            title = snapshot.data ?? '';
          }
          return Text(
            title,
            style: StyleApp.titleStyle(context, color: Colors.white),
          );
        },
      ),
      elevation: 0.0,
    );
  }

  Widget? _resolvedFAB() {
    if (_listCard.isEmpty) return const SizedBox();
    return StreamBuilder(
      stream: _bloc.currentCardStream,
      builder: (context, _) {
        final currentCard = _bloc.currentCard;
        return Padding(
          padding: (currentCard?.isCreditCard == true ||
                  currentCard?.statusCode == CardModel.NOT_ACTIVATED)
              ? EdgeInsets.only(
                  bottom: minSubmitButtonSize.height +
                      kPaddingLarge +
                      kInnerPadding,
                )
              : EdgeInsets.zero,
          child: FloatingActionButton.extended(
            onPressed: () => goPresent(
              context,
              BottomSheetWidget(
                backIcon: const SizedBox.shrink(),
                isIntrinsicHeight: true,
                appBarColor: Colors.white,
                backgroundColor:
                    DynamicTheme.of(context)?.customColor.lightBlue05,
                title: S.of(context).register_for_card_issuance,
                child: VCCreateBottomSheet(selectCard: onCreateVRCard),
              ),
            ),
            extendedPadding: ksb_common_constant.kInnerPaddingStandard.copyWith(
              right: kPaddingLarge,
            ),
            icon: const Icon(Icons.add_rounded),
            label: Text(
              S.of(context).open_new_card,
              style: StyleApp.buttonStyle(
                context,
                fontWeight: FontWeight.w700,
                color: Colors.white,
              ),
            ),
          ),
        );
      },
    );
  }

  _getBody() {
    return LoadingArea<List>(
      stream: _bloc.listCardStream,
      onTryClick: _bloc.reload,
      onSuccess: (cards) {
        // final currentCard = _bloc.currentCard;
        return Container(
          color: Colors.white,
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  controller: _scrollController,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CardImageWidget(
                        background: Theme.of(context).cardColor,
                        pageController: _pageController,
                        onPageChange: (model, index) {
                          _pageBehavior.add(index);
                          debouncer.run(
                            () => _bloc.setCurrentCard(cards[index]),
                          );
                        },
                        data: cards as List<CardModel>,
                      ),
                      Container(
                        color: Theme.of(context).cardColor,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        alignment: Alignment.center,
                        height: 40,
                        //fix bugs when DotsIndicator not show
                        child: FittedBox(
                          fit: BoxFit.scaleDown,
                          child: DotsIndicator(
                            controller: _pageController,
                            itemCount: cards.length,
                            streamPageIndex: _pageBehavior,
                            kDotSpacing: 10.0,
                            color: const Color(0xFF333333).withOpacity(0.1),
                            selectedColor: getTheme(context, true).primaryColor,
                            onDotSelected: (int page) {},
                          ),
                        ),
                      ),
                      _bloc.currentCard?.statusCode == CardModel.NOT_ACTIVATED
                          ? _activeCard(_bloc.currentCard)
                          : _cardInfo(_bloc.currentCard),
                    ],
                  ),
                ),
              ),
              if (_bloc.currentCard?.statusCode == CardModel.NOT_ACTIVATED)
                BottomButton(
                  onTap: () {
                    _bloc.currentCard?.isNonePhysicCard ?? false
                        ? activeNonePhysicCard(
                            _bloc.currentCard?.refCardId ?? "")
                        : goPresent(
                            context,
                            BottomSheetWidget(
                              height:
                                  MediaQuery.of(context).size.height * 3 / 5,
                              title: S
                                  .of(context)
                                  .card_activate_bottomsheet_title_enter_card,
                              child: InputFourLastDigitWidget(
                                onChange: (value) async {
                                  final CheckCardForActiveResponse? card =
                                      await _bloc.checkActivatedCard(
                                    value,
                                    _bloc.currentCard?.idNbr ?? '',
                                  );
                                  if (card != null) {
                                    _gotoActiveDone(
                                        _bloc.currentCard, card, value);
                                  }
                                },
                              ),
                            ),
                          );
                  },
                  title: S.of(context).card_info_button_activate,
                )
              else if (_bloc.currentCard?.isCreditCard == true)
                BottomButton(
                  title: S.of(context).card_info_button_loan_pay,
                  onTap: () => go(
                    context,
                    PaymentCardPage(
                      card: _bloc.currentCard,
                      listCard: _listCard,
                    ),
                  ),
                ),
            ],
          ),
        );
      },
      onEmpty: () => ListCardEmpty(cardBloc: _bloc),
    );
  }

  _handleClickItem(CardFunction item, CardModel card) {
    switch (item.functionCode) {
      case "FN001":
        go(
          context,
          CardControlPage(
            card: _bloc.currentCard,
            onReload: () {
              _bloc.currentCard?.hasDetail = false;
              _bloc.getDetailCard();
            },
          ),
        );
        break;
      case "FN002":
        break;
      case "FN003":
        break;
      case "FN004":
        break;
      case "FN005":
        go(
          context,
          CardTransactionHistory(
            card: card,
          ),
        );
        break;
      case "FN006":
        break;
      case "FN007":
        go(context, CardAutomaticDebitPage(card: card));
        break;
      case "FN008":
        break;
      case "FN009":
        card.isNonePhysicCard
            ? _onTapCVV(context)
            : go(
                context,
                PINSettingsPage(
                  listCards: _listCard,
                  card: _bloc.currentCard,
                ),
              );
        break;
      case "FN010":
        _onTapCVV(context);
        break;
      default:
        return null;
    }
  }

  _cardInfo(CardModel? currentCard) {
    return LoadingArea<CardModel>(
      stream: _bloc.cardDetailStream,
      onTryClick: _bloc.getDetailCard,
      onEmpty: () => Container(),
      onSuccess: (_) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 10.0,
              ),
              child: _titleCard(currentCard),
            ),
            LoadingArea<CardModel?>(
              stream: _bloc.currentCardStream,
              onTryClick: _bloc.getDetailCard,
              onSuccess: (carModel) {
                return CardStatusWidget(
                  cardModel: carModel,
                  onChange: (value) {
                    if (value == false) {
                      DialogUtil.confirm(
                          context,
                          Text(
                            S.of(context).card_info_dialog_des_lock_card,
                          ),
                          cancelText: S.of(context).common_cancel,
                          submitText:
                              S.of(context).card_info_dialog_button_lock_card,
                          title: S.of(context).card_info_dialog_title_lock_card,
                          onSubmit: () async {
                        final hasEToken = await _bloc.hasEToken();
                        if (hasEToken && mounted) {
                          final value = await goPresent<String>(
                              context, const EtokenPinCode(isSubmit: true));
                          if (value?.isNotEmpty == true) {
                            final result = await _bloc.setLockCard(otp: value);
                            if (result == true && mounted) {
                              // _bloc.getDetailCard(isDetail: false);
                              DialogUtil.showFlushBar(
                                  context,
                                  S
                                      .of(context)
                                      .card_info_snack_msg_lock_success);
                            }
                          }
                        } else {
                          goToSetupToken(context).then((value) {
                            if (value == null || value == true) {
                              _bloc.setLockCardCancel();
                            }
                          });
                        }
                      }, onCancel: () {
                        _bloc.setLockCardCancel();
                      });
                    } else {
                      DialogUtil.confirm(
                          context,
                          Text(
                              S.of(context).card_info_dialog_title_unlock_card),
                          cancelText: S.of(context).common_cancel,
                          submitText:
                              S.of(context).card_info_dialog_button_unlock_card,
                          title: S.of(context).common_title_app,
                          onSubmit: () async {
                        final hasEToken = await _bloc.hasEToken();
                        if (hasEToken && mounted) {
                          final value = await goPresent<String>(
                              context, const EtokenPinCode(isSubmit: true));
                          if (value?.isNotEmpty == true) {
                            final result =
                                await _bloc.setUnLockCard(otp: value);
                            if (result == true && mounted) {
                              // _bloc.getDetailCard(isDetail: true);
                              DialogUtil.showFlushBar(
                                  context,
                                  S
                                      .of(context)
                                      .card_info_snack_msg_unlock_success);
                            }
                          }
                        } else {
                          goToSetupToken(context).then((value) {
                            if (value == null || value == true) {
                              _bloc.setLockCardCancel();
                            }
                          });
                        }
                      }, onCancel: () {
                        _bloc.setLockCardCancel();
                      });
                    }
                  },
                  onTap: () {
                    _bloc.setLockCardCancel();
                    DialogUtil.alert(
                      context,
                      S.of(context).card_info_dialog_des_lost_card,
                      submit: S.of(context).common_agree,
                    );
                  },
                );
              },
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Divider(
                color: const Color(0xFF333333).withOpacity(0.2),
              ),
            ),
            Wrap(
              children: currentCard?.functions.map((item) {
                    return ActionItem(
                      icon: item.icon,
                      title: item.functionName,
                      isVirtualCard: currentCard.isNonePhysicCard,
                      show: item.functionCode == "FN001" ||
                              item.functionCode == "FN005" ||
                              item.functionCode == "FN009" ||
                              item.functionCode == "FN010"
                          ? true
                          : false,
                      onPressed: () => _handleClickItem(item, currentCard),
                    );
                  }).toList() ??
                  [],
            ),
            const SizedBox(height: 10.0),
            Divider(
              thickness: 6.0,
              height: 1.0,
              color: Theme.of(context).dividerColor,
            ),
            if (currentCard?.isPaid != null && !currentCard!.isPaid!)
              InkWell(
                onTap: () => go(
                  context,
                  PaymentCardPage(
                    card: currentCard,
                    listCard: _listCard,
                  ),
                ),
                child: Container(
                  height: 100.0,
                  margin: const EdgeInsets.all(16.0),
                  padding:
                      const EdgeInsets.only(top: 12.0, left: 8.0, right: 16.0),
                  decoration: const BoxDecoration(
                      color: Color(0xFFF4F4F7),
                      borderRadius: BorderRadius.all(Radius.circular(8.0))),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ImageAssets.svgAssets(ImagesResource.ic_payment_now),
                      const SizedBox(width: 10.0),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 10.0),
                            Text(
                              S.of(context).card_info_label_balance_pay,
                              style: StyleApp.descStyle(context,
                                  color: getTheme(context, true).primaryColor),
                            ),
                            const SizedBox(height: 12.0),
                            Text(
                              S.of(context).card_info_button_pay_now,
                              style: StyleApp.subtitle2(context, false)
                                  ?.copyWith(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .secondary),
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            if (currentCard != null) CardInfoWidget(card: currentCard),
            const SizedBox(height: kPaddingMedium * 12),
          ],
        );
      },
    );
  }

  Widget _titleCard(CardModel? currentCard) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Text(
            currentCard?.productName ?? '',
            style: StyleApp.titleStyle(context),
          ),
        ),
        const SizedBox(width: 30.0),
        Container(
          decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
              borderRadius: BorderRadius.circular(4.0)),
          padding: const EdgeInsets.symmetric(
            horizontal: 8.0,
            vertical: 3.0,
          ),
          child: Text(
            currentCard?.ownerType == "1"
                ? S.of(context).card_info_label_main_card
                : S.of(context).card_info_label_supplementary_card,
            style: StyleApp.titleStyle(
              context,
              fontSize: 12.0,
              color: Colors.white,
            ),
          ),
        )
      ],
    );
  }

  _activeCard(CardModel? card) {
    return LoadingArea<CardModel>(
      stream: _bloc.cardDetailStream,
      onTryClick: _bloc.getDetailCard,
      onEmpty: () => Container(),
      onSuccess: (_) {
        return card?.isNonePhysicCard == true
            ? VCActivateInfo(card: card)
            : Container(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 10.0),
                    _titleCard(card),
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16.0),
                      child: Text(
                        S.of(context).card_info_des_in_shipping,
                        style: StyleApp.titleStyle(context),
                      ),
                    ),
                    Text(
                      S.of(context).card_info_des_activate_card_receive,
                      style: StyleApp.descStyle(context,
                          color: const Color(0xFF333333)),
                    ),
                  ],
                ),
              );
      },
    );
  }

  _gotoActiveDone(CardModel? cardModel, CheckCardForActiveResponse card,
      String fourLastDigit) async {
    final hasEToken = await _bloc.hasEToken();
    if (hasEToken && mounted) {
      final value =
          await goPresent<String>(context, const EtokenPinCode(isSubmit: true));
      if (value?.isNotEmpty == true && mounted) {
        final result = cardModel?.isCreditCard == true
            ? await _bloc.activeCreditCard(card, otp: value)
            : await _bloc.activeDebitCard(card, otp: value);
        if (result != null && mounted) {
          go(
            context,
            ActiveCardDone(
              cardModel: cardModel,
              card: result,
              onReload: () {
                _bloc.currentCard?.hasDetail = false;
                _bloc.reload();
              },
            ),
          );
        }
      }
    } else if (mounted) {
      goToSetupToken(context);
    }
  }
}
