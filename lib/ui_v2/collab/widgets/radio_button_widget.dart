import 'package:common/ks_common.dart';
import 'package:flutter/material.dart';
import 'package:mobile_banking/assets/dimens.dart';

class RadioButtonWidget<T> extends StatelessWidget {
  final EdgeInsets? padding;
  final T groupValueRadio;
  final T valueRadio;
  final double radius;
  final Color? selectedBorderColor;
  final Color? unselectedBorderColor;
  final Color? selectedColor;
  final double borderSide;
  final Function(T) onChanged;
  final String? title;
  final TextStyle? titleStyle;
  final Widget? titleWidget;
  final String? value;
  final TextStyle? valueStyle;
  final Widget? valueWidget;
  final String? subTitle;
  final TextStyle? subTitleStyle;
  final Widget? subTitleWidget;

  const RadioButtonWidget(
      {Key? key,
      this.padding,
      required this.groupValueRadio,
      required this.valueRadio,
      this.radius = 8.0,
      this.selectedColor,
      this.unselectedBorderColor,
      this.selectedBorderColor,
      this.borderSide = 1.0,
      required this.onChanged,
      this.title,
      this.titleStyle,
      this.titleWidget,
      this.value,
      this.valueStyle,
      this.valueWidget,
      this.subTitle,
      this.subTitleStyle,
      this.subTitleWidget})
      : super(key: key);

  // Border _getBorder(BuildContext context) {
  // if (groupValueRadio == valueRadio) {
  //   return Border.all(
  //       color: selectedBorderColor ?? Theme.of(context).primaryColor,
  //       width: borderSide);
  // }
  // return Border.all(
  //     color: unselectedBorderColor ??
  //         DynamicTheme.of(context)!.customColor.border!,
  //     width: borderSide);
  // }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => onChanged(valueRadio),
      child: Container(
        padding: padding ?? const EdgeInsets.all(kInnerPadding),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.0),
          color: Colors.white, /* border: _getBorder(context)*/
        ),
        child: Row(
          children: [
            Expanded(
                child: Column(
              children: [
                Row(
                  children: [
                    SizedBox(
                      width: 24.0,
                      height: 24.0,
                      child: Radio<T>(
                        activeColor: Theme.of(context).primaryColor,
                        groupValue: groupValueRadio,
                        value: valueRadio,
                        onChanged: (newValue) {
                          if (newValue != null) {
                            onChanged.call(newValue);
                          }
                        },
                      ),
                    ),
                    const SizedBox(
                      width: kPaddingMedium,
                    ),
                    titleWidget ??
                        Text(
                          title ?? '',
                          style: titleStyle ??
                              StyleApp.bodyText2(context)
                                  ?.copyWith(color: const Color(0xFF212633)),
                        )
                  ],
                ),
                if (subTitleWidget != null || subTitle != null)
                  const SizedBox(
                    width: kInnerPadding,
                  ),
                if (subTitleWidget != null || subTitle != null)
                  subTitleWidget ??
                      Text(
                        subTitle ?? '',
                        style: subTitleStyle ??
                            StyleApp.bodyText2(context)
                                ?.copyWith(color: const Color(0xFF333333)),
                      )
              ],
            )),
            if (valueWidget != null || value != null)
              const SizedBox(
                width: kInnerPadding,
              ),
            if (valueWidget != null || value != null)
              valueWidget ??
                  Text(
                    value ?? '',
                    style: valueStyle ??
                        StyleApp.bodyText2(context)
                            ?.copyWith(color: const Color(0xFF333333)),
                  )
          ],
        ),
      ),
    );
  }
}
