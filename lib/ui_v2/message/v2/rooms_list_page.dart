import 'package:ksb_bloc/bloc/profile/profile_bloc.dart';
import 'package:ksb_bloc/environment.dart';
import 'package:common/global_callback.dart';
import 'package:common/navigator.dart';
import 'package:common/utils/model_util.dart';
import 'package:common/widgets/empty_widget.dart';
import 'package:common/widgets/loading/loading.dart';
import 'package:common/widgets/loading/loading_area.dart';
import 'package:flutter/material.dart';
import 'package:ks_chat/ks_chat.dart';
import 'package:mobile_banking/assets.dart';
import 'package:mobile_banking/assets/images.dart';
import 'package:mobile_banking/di/inject.dart';
import 'package:mobile_banking/generated/l10n.dart';
import 'package:mobile_banking/ui_v2/chat/v2/chat_page.dart';
import 'package:mobile_banking/ui_v2/message/v2/widgets/message_notification_widget.dart';
import 'package:mobile_banking/ui_v2/widget/slider_edit_remove.dart';

import 'custom/message_chat_item.dart';
import 'message_login_page.dart';

mixin RoomListPage<T extends StatefulWidget> on State<T> {
  final bloc = Injection.injector.get<RocketChatRoomsBloc>();
  final chatVisitorBloc = Injection.injector.get<RocketChatVisitorRoomsBloc>();

  final env = Injection.injector.get<Environment>();
  final profileBloc = Injection.injector.get<ProfileBloc>();

  @override
  void initState() {
    super.initState();
    _initVisitorChat();
  }

  _initVisitorChat() async {
    final visitorToken = await bloc.preferences.userId;
    chatVisitorBloc.getVisitorRooms(visitorToken);
  }

  Future onRefresh() async {
    bloc.getRooms();
  }

  List listRooms() {
    return [
      _getRoomChatModules(),
      _getCurrentLiveChat(),
      SliverToBoxAdapter(
        child: Container(
          height: 10,
          color: Theme.of(context).dividerColor,
        ),
      ),
      _getRoomsChat(RoomType.L, null, isLoading: true),
      _getRoomsChat(
        RoomType.C,
        SliverFillRemaining(
          child: Center(
            child: EmptyWidget(
              message: "Quý khách chưa có tin nhắn nào",
              icon: ImageAssets.svgAssets(ImagesResource.ic_empty_favorite),
            ),
          ),
        ),
      ),
      _getRoomsChat(RoomType.D, null),
      _getRoomsChat(RoomType.P, null),
    ];
  }

  Widget _getRoomsChat(RoomType roomType, Widget? onEmptyAll,
      {bool isLoading = false}) {
    return LoadingArea<List<RoomItemInfo>>(
      stream: bloc.myRoomsStream,
      loading: SliverToBoxAdapter(child: isLoading ? ring : Container()),
      onError: (msg) => SliverToBoxAdapter(
        child: Container(),
      ),
      onEmpty: () {
        return onEmptyAll ?? const SliverToBoxAdapter();
      },
      onSuccess: (_data) {
        List<BaseItem<RoomItemInfo>?> data;
        var name;
        switch (roomType) {
          case RoomType.D:
            data = bloc.chatDirect;
            name = S.of(context).chat_direct;
            break;
          case RoomType.L:
            data = bloc.chatInProgress;
            name = S.of(context).chat_in_progress;
            break;
          case RoomType.C:
            data = bloc.chatPublic;
            name = S.of(context).chat_public;
            break;
          case RoomType.P:
            data = bloc.chatPrivate;
            name = S.of(context).chat_private;
            break;
        }
        return SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              if (index == 0) {
                return Container(
                  padding: const EdgeInsets.only(
                      top: 10, left: 15, right: 15, bottom: 10),
                  child: Text(
                    name,
                    style: StyleApp.titleStyle(context),
                  ),
                );
              }
              final item = data[index - 1];
              final lastMessageTime = item?.data?.lastMessage?.ts?.date ?? 0;
              return SliderEditRemove(
                textRemove: S.of(context).delete,
                onRemove: () async {
                  if (roomType == RoomType.D) {
                    var authInfo = await bloc.getAuthInfo();
                    final currentUserName = authInfo?.data?.me?.username;
                    /*final friendUserName = item.data.usernames?.findOrFirst((item) => item != currentUserName) ??
                        null;

                    final user =
                        await profileBloc.getUserByLoginName(friendUserName);
                    final invited = await profileBloc.getUserInvited();

                    if (user?.referralCd == invited?.referralCd &&
                        invited?.referralCd?.isNotEmpty == true) {
                      DialogUtil.alert(context,
                          'Không thể xoá được người giới thiệu của bạn');
                      return;
                    }*/
                  }
                  if (item?.data != null) {
                    bloc.deleteRoom(item!.data!);
                  }
                },
                child: MessageChatItem(
                  title: item?.title,
                  subTitle: item?.subTitle,
                  avatar: item?.image,
                  unReadCount: toInt(item?.trailing),
                  time: lastMessageTime > 0
                      ? DateTime.fromMillisecondsSinceEpoch(lastMessageTime)
                      : null,
                  onPressed: () async {
                    go(
                      context,
                      ChatPage(
                        room: item,
                        roomType: roomType,
                        departmentId: item?.data?.departmentId,
                      ),
                    );
                  },
                ),
              );
            },
            childCount: data.isNotEmpty ? data.length + 1 : 0,
          ),
        );
      },
    );
  }

  _getRoomChatModules() {
    return SliverList(
      delegate: SliverChildListDelegate(
        [
          MessageNotificationWidget(
            title: S.of(context).chat_support_247,
            icon: ImageAssets.ic_logo,
            onPressed: () {
              GlobalCallback.instance.onChatOpen!(ChatModel(
                  openLiveChat: true, stage: StageCallLog.SUPPORT_USER));
            },
          ),
        ],
      ),
    );
  }

  Widget _getCurrentLiveChat() {
    return StreamBuilder<List<RoomItemInfo>>(
      stream: chatVisitorBloc.rooms,
      builder: (context, snapshot) {
        final data = snapshot.data;
        if (data == null || data.isEmpty) {
          return SliverToBoxAdapter(child: Container());
        }
        return SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              final item = data[index];
              final lastMessageTime = item.lastMessage?.ts?.date ?? 0;
              final department =
                  chatVisitorBloc.getLocalDepartment(item.departmentId ?? '');
              final roomName =
                  department?.name ?? item.servedBy?.username ?? '';
              return SliderEditRemove(
                textRemove: S.of(context).delete,
                onRemove: () {
                  chatVisitorBloc.closeRoomVisitor(item);
                },
                child: MessageChatItem(
                  title: roomName,
                  subTitle: item.lastMessage?.msg ?? '',
                  avatar: bloc.getUrlAvatar(item.servedBy?.username),
                  unReadCount: item.unRead,
                  time: lastMessageTime > 0
                      ? DateTime.fromMillisecondsSinceEpoch(lastMessageTime)
                      : null,
                  onPressed: () async {
                    chatVisitorBloc.refreshUnRead(item.id ?? '');
                    GlobalCallback.instance.onChatOpen!(
                      ChatModel(
                        openLiveChat: true,
                        department: item.departmentId,
                        roomId: item.id,
                        roomName: roomName,
                      ),
                    );
                  },
                ),
              );
            },
            childCount: data.isNotEmpty ? data.length : 0,
          ),
        );
      },
    );
  }

  Widget bodyRequireLogin() {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.lock,
            size: 80,
          ),
          const SizedBox(height: 10),
          Text(
            S.of(context).error_login_chat,
            style: StyleApp.descStyle(context),
          ),
          const SizedBox(height: 10),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              shape: const StadiumBorder(),
              padding: const EdgeInsets.symmetric(horizontal: 50),
            ),
            onPressed: _goToLoginPage,
            child: Text(S.of(context).sign_in),
          )
        ],
      ),
    );
  }

  _goToLoginPage() async {
    await popUpPage(MessageLoginPage(), context);
  }
}
