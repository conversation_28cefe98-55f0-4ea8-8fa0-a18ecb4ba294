import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:common/model/strapi/strapi_models.dart';
import 'package:common/navigator.dart';
import 'package:common/utils/dialog_util.dart';
import 'package:common/widgets/index.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flare_flutter/flare_actor.dart';
import 'package:flutter/material.dart';
import 'package:ks_chat/bloc/bloc/rocket_chat_group_bloc.dart';
import 'package:ks_chat/bloc/bloc/rocket_chat_rooms_bloc.dart';
import 'package:ks_chat/bloc/bloc/rocket_chat_visitor_rooms_bloc.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_bloc/bloc/ekyc/update_profile_ekyc_bloc.dart';
import 'package:ksb_bloc/bloc/model/home/<USER>';
import 'package:ksb_bloc/bloc/money_transaction/request_transaction_bloc.dart';
import 'package:ksb_bloc/bloc/open_card/open_card_bloc.dart';
import 'package:ksb_bloc/bloc/payment_qr/payment_qr_bloc.dart';
import 'package:ksb_bloc/bloc/viet_qr/viet_qr_bloc.dart';
import 'package:ksb_bloc/deep_link.dart';
import 'package:ksb_common/shared/widgets/background_image_app_bar.dart';
import 'package:mobile_banking/assets/assets.dart';
import 'package:mobile_banking/assets/dimens.dart';
import 'package:mobile_banking/assets/images.dart';
import 'package:mobile_banking/di/inject.dart';
import 'package:mobile_banking/generated/l10n.dart';
import 'package:mobile_banking/model/type_text.dart';
import 'package:mobile_banking/new_version.dart';
import 'package:mobile_banking/ui_v2/account_detail/account_summary.dart';
import 'package:mobile_banking/ui_v2/auth/notification_surplus/message_notification_screen.dart';
import 'package:mobile_banking/ui_v2/card_v2/withdrawal_atm.dart';
import 'package:mobile_banking/ui_v2/home/<USER>';
import 'package:mobile_banking/ui_v2/message/v2/message_page.dart';
import 'package:mobile_banking/ui_v2/open_card_qr/open_card_qr_page.dart';
import 'package:mobile_banking/ui_v2/pay/pay_miniapp/common_payment_page.dart';
import 'package:mobile_banking/ui_v2/profile_account/profile_page.dart';
import 'package:mobile_banking/ui_v2/profile_account/screen_customer_vnpost.dart';
import 'package:mobile_banking/ui_v2/profile_account/screen_email.dart';
import 'package:mobile_banking/ui_v2/profile_account/screen_lock_old_app.dart';
import 'package:mobile_banking/ui_v2/qr_scanner/qr_scanner.dart';
import 'package:mobile_banking/ui_v2/transfer/transfer_accept.dart';
import 'package:mobile_banking/ui_v2/transfer_stock/transfer_stock.dart';
import 'package:mobile_banking/utils/biometrics.dart';
import 'package:mobile_banking/utils/functions.dart';
import 'package:mobile_banking/utils/navigator.dart' show goModule;
import 'package:mobile_banking/utils/validator_qr.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:rxdart/rxdart.dart';
import 'package:umee_shop/repository/src/model/index.dart';
import 'package:umee_shop/shared/cubits/event/event_util.dart';
import 'package:umee_shop/ui/paybox/views/linking_paybox.dart';

import '../sunshine_app.dart';
import 'lucky_money/ui/receive/handle_service/lucky_money_handle_service.dart';

class HomeTabPage extends StatefulWidget {
  const HomeTabPage({Key? key}) : super(key: key);

  @override
  HomeTabPageState createState() => HomeTabPageState();
}

class HomeTabPageState extends State<HomeTabPage>
    with RouteAware, SingleTickerProviderStateMixin {
  final _session = Injection.injector.get<Session>();
  final _bloc = Injection.injector.get<ProfileBloc>();
  final _chatBloc = Injection.injector.get<RocketChatRoomsBloc>();
  final _chatVisitorBloc = Injection.injector.get<RocketChatVisitorRoomsBloc>();
  final _chatGroupBloc = Injection.injector.get<RocketChatGroupBloc>();
  final _homeBloc = Injection.injector.get<HomeTabBloc>();
  final _requestTransactionBloc =
      Injection.injector.get<RequestTransactionBloc>();
  final _tabBarBehavior = BehaviorSubject<int>.seeded(0);
  final _blocQr = Injection.injector.get<PaymentQrBloc>();
  final _blocVietQr = Injection.injector.get<VietQrBloc>();
  final _profileEKycBloc = Injection.injector.get<ProfileEKycBloc>();
  final _updateProfileEKycBloc =
      Injection.injector.get<UpdateProfileEKycBloc>();
  int currentIndex = 0;
  late TabController _tabController;
  final double _heightIcon = 24;
  StreamSubscription? _subLink;
  late Biometrics biometrics;
  Uri? streamDeepLink;
  late Preferences _prefs;
  bool errorBottomBar = true;
  late StreamSubscription _subUnread;
  final _totalUnread = BehaviorSubject<int>();
  late NewVersion newVersion;
  bool showAds = false;
  final _env = Injection.injector.get<Environment>();
  final _priorityBloc = Injection.injector.get<PriorityBloc>();

  @override
  void initState() {
    super.initState();
    RepeatCountdown.isLoginPage = false;
    _bloc.session.isSleep.add(false);
    _prefs = Injection.preferences;
    _initOnesignalOpenHandler();
    biometrics = Biometrics();
    _tabController = TabController(initialIndex: 0, length: 4, vsync: this);
    _tabController.addListener(() {
      //FocusScope.of(context).requestFocus(FocusNode());
      if (_tabController.index == 0 || _tabController.index == 1) {
        currentIndex = _tabController.index;
      } else if (_tabController.index == 2 || _tabController.index == 3) {
        currentIndex = _tabController.index + 1;
      }
      _tabBarBehavior.add(currentIndex);
    });
    _initAppQrCode();
    _getLoginAction();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initAppLink();
      _checkVersion();
    });
    _blocQr.streamSubs.add(_blocQr.errorStream.listen((event) {
      if (event?.isNotEmpty == true && mounted) {
        DialogUtil.alert(context, event);
      }
    }));

    _blocVietQr.streamSubs.add(_blocVietQr.errorStream.listen((event) {
      if (event?.isNotEmpty == true && mounted) {
        DialogUtil.alert(context, event);
      }
    }));
    _bloc.profileStream.flatMap((profile) {
      if ((profile.createdFrom == "MIGRATION" &&
              profile.lockedOldApp == false) ||
          (profile.verifyEmail == false) ||
          (profile.organization == "VNPOST" &&
              profile.customerGroupUserId.isNullOrEmpty &&
              profile.passEkyc == true)) {
        return _bloc.profileStream;
      } else {
        return _bloc.popupAdsStream;
      }
    }).listen((event) {
      if (event is ProfileInfo) {
        if (event.createdFrom == "MIGRATION" &&
            event.lockedOldApp == false &&
            mounted) {
          go(context, ScreenLockOldApp(bloc: _bloc));
        } else if (event.organization == "VNPOST" &&
            event.customerGroupUserId.isNullOrEmpty &&
            event.passEkyc == true &&
            mounted) {
          go(
            context,
            ScreenCustomerVNPost(info: _bloc.profileInfo),
          );
        } else if (event.verifyEmail == false && mounted) {
          go(context, ScreenEmail(info: _bloc.profileInfo));
        }
      } else if (event is StrapiBanner && mounted && showAds) {
        goBannerPopup(context, event);
      }
    });

    _subUnread = _chatBloc.totalUnreadStream.listen((event) {
      _totalUnread.add(event);
    });

    _subUnread = _chatVisitorBloc.visitorUnread.listen((event) {
      _totalUnread.add(event);
    });

    FirebaseMessaging.instance.getInitialMessage().then((message) {
      if (message != null) {
        //TODO go to chat
        logger.v(message);
      }
    });
    _initChat();
    _session.getCurrentEKycLevel();
    _profileEKycBloc.retrySendVideoEKyc();
    _updateProfileEKycBloc.retrySendVideoEKyc();
    _priorityBloc.getPriorityCustomer();
  }

  _initChat() async {
    try {
      var auth = await _chatBloc.getAuthInfo();
      auth ??= await _chatBloc.login().catchError((e) {
        logger.e(e);
        return null;
      });
      final userId = await _homeBloc.pref.userId;
      _chatVisitorBloc.getVisitorRooms(userId);

      final profile = await _homeBloc.pref.getProfileInfo();
      Future.delayed(const Duration(seconds: 5), () {
        _chatBloc.updateProfile(
          fullName: profile?.fullName,
          avatar: profile?.avatarUrl,
        );
      });
    } catch (e) {
      logger.e(e);
    }
  }

  _checkVersion() async {
    newVersion = NewVersion();
    VersionStatus? result = await newVersion.getVersionStatus();
    if (!mounted) return;
    if (result != null && result.canUpdate) {
      DialogUtil.confirm(
        context,
        Text(S.of(context).common_update_app_des),
        title: S.of(context).common_update_app_title,
        cancelText: S.of(context).common_later,
        submitText: S.of(context).common_update_now,
        onSubmit: () {
          newVersion.launchAppStore(result.appStoreLink);
        },
      );
    } else {
      _showSetupBiometrics();
    }
  }

  _showSetupBiometrics() async {
    final userSetting = _homeBloc.pref.userSetting;
    final bool? remindLater = userSetting.isRemindLaterBiometrics;
    final isBiometrics = userSetting.isBiometrics;
    final isAvailable = await biometrics.isBiometricAvailable();
    if (!mounted) return;
    if (remindLater == false && isBiometrics == false && isAvailable) {
      DialogUtil.confirm(
        context,
        Text(S.of(context).remind_biometrics),
        title: S.of(context).ask_remind_biometrics,
        cancelText: S.of(context).remind_later,
        onCancel: () {
          _bloc.setRemindLaterBiometricSetting(true);
        },
        submitText: S.of(context).agree,
        onSubmit: () async {
          final isBioAvailable = await biometrics.isBiometricAvailable();
          bool hasListBio = await biometrics.getListOfBiometricTypes();
          if (!mounted) return;
          if (isBioAvailable) {
            if (!hasListBio && Platform.isAndroid) {
              await DialogUtil.alert(
                context,
                S.of(context).error_setting_touch,
                submit: S.of(context).agree,
              );
            }
            await biometrics.authenticateUser(
              context,
              onHandler: () async {
                _bloc.setBiometricSetting(true);
              },
            );
          } else {
            DialogUtil.alert(context, S.of(context).error_device_no_support,
                submit: S.of(context).agree);
          }
        },
      );
    }
  }

  _initOnesignalOpenHandler() async {
    // Use for IOS
    await OneSignal.InAppMessages.paused(false);
    OneSignal.User.pushSubscription
        .addObserver((OSPushSubscriptionChangedState changes) {
      if (changes.current.optedIn) {
        _session.sendPlayerId(playerId: changes.current.id);
      }
    });

    OneSignal.Notifications.addClickListener(
        (OSNotificationClickEvent result) async {
      //_bloc.updateUserNotification(false, result.notification.payload.notificationId);
      if (result.notification.additionalData != null) {
        final additionalData = result.notification.additionalData;
        logger.v(additionalData);
        var threadId = additionalData?['threadId'];
        final lotteryId = additionalData?['lottery_id'];
        final bidId = additionalData?['bid_id'];
        final pushEvent = additionalData?['push-event'];
        final feedId = additionalData?['feed_id'];
        final moduleId = additionalData?['module_id'];
        final notifyId = additionalData?['noti_id'];
        final auctionId = additionalData?['auction_id'];
        final requestId = additionalData?[''];
        final receiveId = additionalData?['receive_id'];
        final type = additionalData?['type']; // promotion, system;
        final promotionId = additionalData?['id'];
        final code = additionalData?['code'];

        if (type == 'module' && additionalData!['module'] != null) {
          ModuleModel? module;
          try {
            final data = Injection.injector
                .get<KsbankApiSmartbank>()
                .serializers
                .fromJson<AppModuleDto>(AppModuleDto.serializer,
                    additionalData['module'].toString());
            module = ModuleModel.fromList(data);
          } finally {
            final userInfo = _bloc.profileInfo;
            if (module != null) {
              goModule(
                context,
                module.icon ?? "",
                url: module.link,
                webLink: module.webLink ?? '',
                title: module.name,
                typeModule: module.type,
                phoneNumber: userInfo?.phoneNumber,
              );
            }
          }
        } else if (type == 'promotion') {
          go(
              context,
              const MessageNotificationScreen(
                index: NotificationTabIndex.promotion,
                isOutside: false,
              ));
          // if (promotionId != null && code != null) {
          //   TODO go to detail
          // }
        } else if (type == 'system') {
          go(
              context,
              const MessageNotificationScreen(
                index: NotificationTabIndex.system,
                isOutside: false,
              ));
          // if (promotionId != null && code != null) {
          //   //TODO go to detail
          // }
        } else {
          go(
              context,
              const MessageNotificationScreen(
                isOutside: false,
              ));
        }
        if (threadId == null || (threadId is String && threadId.isEmpty)) {
          threadId = additionalData?['thread_id'];
        }
      } else {
        if (!mounted) return;
        go(context, const MessageNotificationScreen(isOutside: false));
      }
      _noticeToMyShopModule();
      vibrate();
    });

    OneSignal.Notifications.addForegroundWillDisplayListener((event) {
      event.preventDefault();
      if (event.notification.additionalData != null) {
        final additionalData = event.notification.additionalData;
        L().t(additionalData);
      }
      _env.appEventSink.add(AppEvent.NEW_NOTIFICATION);
      _noticeToMyShopModule();
      vibrate();
      event.notification.display();
    });

    final userId = OneSignal.User.pushSubscription.id;
    if (userId?.isNotEmpty == true) {
      await _session.sendPlayerId(playerId: userId);
    }
    await _updateExternalInfoToOneSignal();
  }

  String get getDeviceLanguageCode {
    final locale = Platform.localeName.split('_');
    if (locale.length > 1) {
      return locale.first;
    }
    return 'vi';
  }

  void _noticeToMyShopModule() {
    try {
      MyShopEventUtil.updateEvent(context);
    } catch (e) {
      logger.e(e);
    }
  }

  _updateExternalInfoToOneSignal() async {
    final profileInfo = await Injection.preferences.getProfileInfo();
    final email = profileInfo?.email;
    final verifyEmail = profileInfo?.verifyEmail;
    final userId = await _prefs.userId;
    if (email?.isNotEmpty == true) {
      OneSignal.User.addEmail(email!).whenComplete(() {
        logger.d('Successfully set email');
      }).catchError((e) {
        logger.e('Failed to set email with error: $e');
      });
      OneSignal.User.addTagWithKey(
          'is_verify_email', verifyEmail == true ? '1' : '0');
    }
    if (userId?.isNotEmpty == true) {
      await OneSignal.login(userId ?? '');
      OneSignal.User.setLanguage(getDeviceLanguageCode);
    }
  }

  _initAppQrCode() async {
    var uriQrCode = await _prefs.getQrDeepLink();
    var uriQrCodeParse = Uri.parse(uriQrCode ?? '');
    if (!uriQrCode.isNullOrEmpty && uriQrCodeParse.isAbsolute) {
      logger.t("Environment.serverUrlStm ${uriQrCodeParse.scheme}");
      var indexQr = ValidatorQr.getServerQr(uri: uriQrCodeParse);
      logger.t("indexQr $indexQr");
      switch (indexQr) {
        case 0:
          _handleOriginLinkApp(Uri.parse(uriQrCode ?? ''));
          break;
        case 1:
          _getLinkQrAtmInfo(Uri.parse(uriQrCode ?? ''));
          break;
        case 4:
          goToQrOpenCard(Uri.parse(uriQrCode ?? ''));
          break;
        case 5:
          Future.delayed(Duration.zero, () {
            _prefs.setQrDeepLink('');
            LuckyMoneyHandleService.handleAppQrScan(
              context,
              uriString: uriQrCode,
            );
          });

          break;
      }
    } else if (ValidatorQr.checkVietQr(qrCode: uriQrCode)) {
      _getVietQr(
        qrCode: uriQrCode,
      );
    } else if (uriQrCode?.contains('UMEE_PAIR') == true ||
        uriQrCode?.contains('PAYBOX_PAIR') == true) {
      final data = uriQrCode?.split('|') ?? [];
      if (data.first == "UMEE_PAIR" || data.first == "PAYBOX_PAIR") {
        _handleScanPaybox(data);
      }
    }
  }

  _getLoginAction() async {
    final moduleType = await _prefs.getModuleTypeFromLogin();
    if (moduleType?.isNotEmpty == true && mounted) {
      _prefs.setModuleTypeFromLogin('');
      goModule(context, moduleType!, webLink: '');
    }
    String? url = _prefs.getLuckyMoneyInfo();
    _prefs.setLuckyMoneyInfo('');
    if (!url.isNullOrEmpty && mounted) {
      goModule(
        context,
        TypeModule.LUCKYMONEY,
        webLink: '',
        url: url,
      );
    }
  }

  void _handleScanPaybox(List<String> values) {
    _prefs.setQrDeepLink('');
    // data scan từ PAYBOX QR có form: UMEE_PAIR|deviceId|pairingKey|name|model|serial
    if (values.length == 6) {
      go(
        context,
        LinkPayBoxPage(
          payBox: PayBox.fromScanQr(
            deviceId: values[1],
            pairingKey: values[2],
            name: values[3],
            model: values[4],
            serial: values[5],
          ),
        ),
      );
    } else {
      DialogUtil.alert(context, S.of(context).qr_code_error_message);
    }
  }

  Future _getLinkQrAtmInfo(Uri uri) async {
    _prefs.setQrDeepLink('');
    List<String>? params = uri.pathSegments;
    final result = await _blocQr.getInfoSTM(qrPaymentId: params.last);
    if (!mounted) return;
    if (result != null) {
      goToWithdrawalAtm(context, result);
    } else {
      DialogUtil.alert(context, S.of(context).error_validator_dialog_withdraw);
    }
    return Future.value('not found');
  }

  Future _getVietQr({String? qrCode, String? urlCallback}) async {
    _prefs.setQrDeepLink('');
    final decode = await _blocVietQr.decodeVietQr(vietQrCode: qrCode ?? '');
    if (!mounted) return;
    if (decode != null) {
      TransferModel model = TransferModel(
        targetAccountNumber: decode.accountNo,
        targetBankShortName: decode.bankShortName,
        targetBankName: decode.bankName,
        // targetCommonBankName: '',
        targetBankImage: decode.url,
        targetBankImageType: decode.bankImageType,
        targetBankCode: decode.bankIdNapas,
        targetBankCitad: decode.bankCitad,
        targetBankIdNapas: decode.bankIdNapas,
        targetBankCodeId: decode.bankCode,
        is247: true,
        isAccount: true,
        amount: decode.amount,
        note: decode.description,
        callbackUrl: urlCallback,
        channelCode: "2",
      );
      goToTransferAcceptV2(context, model);
    } else {
      DialogUtil.alert(context, S.of(context).qr_code_login);
    }
    return Future.value('not found');
  }

  Future _getPaymentTransfer({CommonPayment? payment}) async {
    final blocBank = Injection.injector.get<TransferBanksBloc>();
    final itemBank = await blocBank.getBankItem(bankCode: payment?.bankCode);
    blocBank.dispose();
    if (!mounted) return;
    logger.v(itemBank);
    if (payment != null && itemBank != null) {
      TransferModel model = TransferModel(
        targetAccountNumber: payment.toAccount ?? payment.toCard,
        targetBankShortName: itemBank.title,
        targetBankName: itemBank.subTitle,
        targetCommonBankName: itemBank.commonTitle,
        targetBankImage: itemBank.image,
        targetBankImageType: itemBank.imageType,
        targetBankCode: itemBank.bankIdNapas,
        targetBankCitad: itemBank.data,
        targetBankIdNapas: itemBank.bankIdNapas,
        targetBankCodeId: itemBank.id,
        is247: true,
        isAccount: payment.toAccount.isNullOrEmpty ? false : true,
        amount: payment.amount,
        note: payment.content,
        callbackUrl: payment.urlCallBack,
      );
      goToTransferAcceptV2(context, model);
    } else {
      DialogUtil.alert(context, S.of(context).error_retry_server);
    }
    return Future.value('not found');
  }

  _initAppLink() async {
    try {
      var uriQrCode = await _prefs.getQrDeepLink();
      final deepLink = DeepLink.instance;
      Uri? initialUri = deepLink.takeValue();
      _handleOriginLinkApp(initialUri);
      showAds = (initialUri == null) &&
          (uriQrCode == null || uriQrCode.isEmpty == true);
      if (showAds) {
        _bloc.getPopupAds();
      }
      _subLink =
          deepLink.stream.where((event) => event != null).listen((Uri? uri) {
        logger.v(uri);
        _handleOriginLinkApp(uri);
      });
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(
        e,
        StackTrace.current,
      );
    }
  }

  _handleOriginLinkApp(Uri? uri) async {
    if (uri != null && uri != streamDeepLink) {
      streamDeepLink = uri;
      try {
        final shareInfo = await _getLinkInfo(uri);
        _handlerLinkInfo(shareInfo);
      } catch (e) {
        FirebaseCrashlytics.instance.recordError(
          e,
          StackTrace.current,
        );
      } finally {
        streamDeepLink = null;
      }
    }
  }

  Future _getLinkInfo(Uri? uri) async {
    List<String>? params = uri?.pathSegments;
    _prefs.setQrDeepLink('');

    if (!params.isNullOrEmpty) {
      if (params?.first == "request-transfer") {
        final moneyRequest = await _requestTransactionBloc
            .getDetailMoneyTransfer(id: params?.last ?? '');
        if (!mounted) return;
        if (moneyRequest != null) {
          TransferModel model = TransferModel(
            targetBankName: moneyRequest.bankName,
            targetAccountNumber: moneyRequest.accountNo,
            targetAccountName: TiengViet.parse(moneyRequest.accountName ?? ''),
            amount: moneyRequest.amount,
            note: TiengViet.parse(moneyRequest.description ?? ''),
          );
          goToTransferAcceptV2(context, model);
        } else {
          DialogUtil.alert(
              context, S.of(context).error_validator_request_dialog);
        }
      } else if (params?.first == "makePayment") {
        final data = uri?.queryParameters;
        if (data != null) {
          final mapData = jsonDecode(data['data'] ?? '');
          CommonPayment paymentModel = CommonPayment.fromMap(mapData);
          final type = paymentModel.type?.toUpperCase() ?? '';
          if (type == CommonPayment.VIETQR) {
            _getVietQr(
              qrCode: paymentModel.deepLinkVietQrCode,
              urlCallback: paymentModel.urlCallBack,
            );
          } else if (type == CommonPayment.TRANSFER) {
            _getPaymentTransfer(payment: paymentModel);
          } else if (type == CommonPayment.SECURITY) {
            go(context, TransferSockV2(model: paymentModel));
          } else {
            go(
              context,
              CommonPaymentPage(data: data['data']),
            );
          }
        }
      }
    }

    await LuckyMoneyHandleService.handleDeepLink(context, uri: uri);

    return Future.value('not found');
  }

  Future goToQrOpenCard(Uri? uri) async {
    List<String>? params = uri?.pathSegments;
    _prefs.setQrDeepLink('');
    final openCardBloc = Injection.injector.get<OpenCardBloc>();
    final result =
        await openCardBloc.getStmInfo(transactionId: params?.last ?? '');
    openCardBloc.dispose();
    if (!mounted) return;
    if (result != null) {
      await go(context, OpenCardQrPage(transactionId: params?.last ?? ''));
    } else {
      DialogUtil.alert(context, S.of(context).error_qr_code_invalid);
    }
    return Future.value('not found');
  }

  //TODO check info share and open screen
  _handlerLinkInfo(shareInfo) async {}

  @override
  void dispose() {
    _tabController.dispose();
    _tabBarBehavior.close();
    _subLink?.cancel();
    _blocQr.dispose();
    _requestTransactionBloc.dispose();
    _chatBloc.dispose();
    _chatGroupBloc.dispose();
    _chatVisitorBloc.dispose();
    _blocVietQr.dispose();
    _subUnread.cancel();
    _profileEKycBloc.dispose();
    _updateProfileEKycBloc.dispose();
    _priorityBloc.dispose();
    routeObserver.unsubscribe(this);
    super.dispose();
  }

  @override
  void didPushNext() {
    showAds = false;
    super.didPushNext();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.subscribe(this, ModalRoute.of(context) as PageRoute);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      body: _buildBody(),
      bottomNavigationBar: errorBottomBar
          ? StreamBuilder<int>(
              stream: _tabBarBehavior.stream,
              builder: (context, snapshotLight) {
                return _buildBottomNavigationBar();
              })
          : null,
    );
  }

  _buildBody() {
    return TabBarView(
      controller: _tabController,
      physics: errorBottomBar ? null : const NeverScrollableScrollPhysics(),
      children: [
        HomePage(
          profileBloc: _bloc,
          priorityBloc: _priorityBloc,
          onError: (value) {
            setState(() {
              errorBottomBar = value;
            });
          },
        ),
        const AccountSummaryWidget(),
        const MessagePage(),
        ProfilePage(priorityBloc: _priorityBloc),
      ],
    );
  }

  _buildBottomNavigationBar() {
    final iconColor = ksbCommonProperties?.priorityProfileColor ??
        Theme.of(context).primaryColor;
    return BottomNavigationBar(
      items: [
        _buildBottomItem(
            icon: Padding(
              padding: const EdgeInsets.only(bottom: kPaddingSmall),
              child: ImageAssets.svgAssets(
                ImagesResource.ic_appbar_home,
                color: const Color(0xFF333333),
                width: _heightIcon,
                height: _heightIcon,
              ),
            ),
            title: S.of(context).common_home,
            activeIcon: Padding(
              padding: const EdgeInsets.only(bottom: kPaddingSmall),
              child: ImageAssets.svgAssets(
                ImagesResource.ic_appbar_home_active,
                color: iconColor,
                width: _heightIcon,
                height: _heightIcon,
              ),
            )),
        _buildBottomItem(
            icon: Padding(
              padding: const EdgeInsets.only(bottom: kPaddingSmall),
              child: ImageAssets.svgAssets(
                ImagesResource.ic_appbar_account,
                color: const Color(0xFF333333),
                width: _heightIcon,
                height: _heightIcon,
              ),
            ),
            title: S.of(context).profile_home_title_account,
            activeIcon: Padding(
              padding: const EdgeInsets.only(bottom: kPaddingSmall),
              child: ImageAssets.svgAssets(
                ImagesResource.ic_appbar_account_active,
                color: iconColor,
                width: _heightIcon,
                height: _heightIcon,
              ),
            )),
        _buildBottomItem(
          icon: Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
                color: iconColor, borderRadius: BorderRadius.circular(10)),
            child: const FittedBox(
              fit: BoxFit.scaleDown,
              child: SizedBox(
                width: 25,
                height: 25,
                child: FlareActor("assets/images/barcode.flr",
                    alignment: Alignment.center,
                    color: Colors.white,
                    animation: "default"),
              ),
            ),
          ),
        ),
        _buildBottomItem(
          badgeStream: _totalUnread.stream,
          icon: Padding(
            padding: const EdgeInsets.only(bottom: kPaddingSmall),
            child: ImageAssets.svgAssets(
              ImagesResource.ic_appbar_mailbox,
              color: const Color(0xFF333333),
              width: _heightIcon,
              height: _heightIcon,
            ),
          ),
          title: S.of(context).inbox,
          activeIcon: Padding(
            padding: const EdgeInsets.only(bottom: kPaddingSmall),
            child: ImageAssets.svgAssets(
              ImagesResource.ic_appbar_mailbox_active,
              color: iconColor,
              width: _heightIcon,
              height: _heightIcon,
            ),
          ),
        ),
        _buildBottomItem(
            icon: Padding(
              padding: const EdgeInsets.only(bottom: kPaddingSmall),
              child: ImageAssets.svgAssets(
                ImagesResource.ic_appbar_profile,
                color: const Color(0xFF333333),
                width: _heightIcon,
                height: _heightIcon,
              ),
            ),
            title: S.of(context).profile_my_qr_label_personal,
            activeIcon: Padding(
              padding: const EdgeInsets.only(bottom: kPaddingSmall),
              child: ImageAssets.svgAssets(
                ImagesResource.ic_appbar_profile_active,
                color: iconColor,
                width: _heightIcon,
                height: _heightIcon,
              ),
            )),
      ],
      backgroundColor: Theme.of(context).cardColor,
      unselectedLabelStyle:
          StyleApp.caption(context)?.copyWith(color: const Color(0xFF333333)),
      selectedFontSize: 10,
      selectedIconTheme: IconThemeData(color: iconColor),
      unselectedIconTheme: const IconThemeData(color: Color(0xFF333333)),
      selectedItemColor: iconColor,
      unselectedItemColor: const Color(0xFF333333),
      currentIndex: currentIndex,
      onTap: _onTabSelected,
      selectedLabelStyle: StyleApp.caption(context)?.copyWith(
          color: Theme.of(context).primaryColor, fontWeight: FontWeight.w500),
      type: BottomNavigationBarType.fixed,
    );
  }

  _getBadgeView({Stream<int>? stream}) {
    if (stream != null) {
      return StreamBuilder<int>(
          stream: stream,
          builder: (context, snapshot) {
            final data = snapshot.data ?? 0;
            if (data > 0) {
              return Positioned(
                right: 0,
                child: Container(
                  padding: const EdgeInsets.all(1),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 12,
                    minHeight: 12,
                  ),
                  // child: new Text(
                  //   '$data',
                  //   style: new TextStyle(
                  //     color: Colors.white,
                  //     fontSize: 8,
                  //   ),
                  //   textAlign: TextAlign.center,
                  // ),
                ),
              );
            }
            return const SizedBox();
          });
    }
    return const SizedBox();
  }

  _buildBottomItem(
      {Widget? icon,
      Widget? activeIcon,
      String? title,
      Stream<int>? badgeStream,
      bool isNotify = true}) {
    if (isNotify == false) {
      return BottomNavigationBarItem(
          backgroundColor: Theme.of(context).cardColor,
          icon: icon ?? Container(),
          label: title ?? "",
          activeIcon: activeIcon);
    }
    return BottomNavigationBarItem(
      backgroundColor: Theme.of(context).cardColor,
      icon: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 5),
            child: icon,
          ),
          _getBadgeView(stream: badgeStream)
        ],
      ),
      label: title ?? "",
      activeIcon: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 5),
            child: activeIcon,
          ),
          _getBadgeView(stream: badgeStream)
        ],
      ),
    );
  }

  _onTabSelected(int index) async {
    if (index == 2) {
      popUpPage(
          const QrScanner(
            typecall: 0,
          ),
          //await goToWithdrawalAtm(context, null),
          context);
    } else {
      if (index == 0 || index == 1) {
        _tabController.index = index;
      } else if (index == 3 || index == 4) {
        _tabController.index = index - 1;
      }
    }
    _tabBarBehavior.add(currentIndex);
  }
}
