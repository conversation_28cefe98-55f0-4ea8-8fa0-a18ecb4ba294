import 'dart:async';

import 'package:common/ks_common.dart';
import 'package:common/widgets/money_widget.dart';
import 'package:flutter/material.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_common/shared/assets.dart';
import 'package:ksb_common/shared/route_path.dart';
import 'package:ksb_common/shared/widgets/auth_transaction_mixin_common.dart';
import 'package:ksb_common/shared/widgets/background_app.dart';
import 'package:mobile_banking/di/inject.dart';
import 'package:mobile_banking/generated/l10n.dart';
import 'package:mobile_banking/ui_v2/lucky_money/shared/show_error_widget.dart';
import 'package:mobile_banking/ui_v2/lucky_money/ui/confirm_transfer/extension/lucky_money_extension.dart';
import 'package:mobile_banking/ui_v2/lucky_money/ui/confirm_transfer/extension/schedule_extension.dart';
import 'package:mobile_banking/ui_v2/lucky_money/ui/confirm_transfer/lucky_money_transfer_success.dart';
import 'package:passport_reader_plugin/UI/widgets/dialog_util.dart';

import '../../../widget/loading/loading_view.dart';
import '../../shared/custom_divider.dart';
import '../../shared/lucky_money_button.dart';
import '../../shared/row_content.dart';
import '../../shared/transfer_model_detail_widget.dart';
import 'widgets/lucky_money_schedule_widget.dart';

class LuckyMoneyConfirmTransfer extends StatefulWidget {
  const LuckyMoneyConfirmTransfer({
    super.key,
    required this.fillBloc,
    this.showFaceId,
    this.transactionNo,
    this.title,
  });

  final String? title;
  final LuckyMoneyFillBloc fillBloc;
  final bool? showFaceId;
  final String? transactionNo;

  @override
  State<LuckyMoneyConfirmTransfer> createState() =>
      _LuckyMoneyConfirmTransferState();
}

class _LuckyMoneyConfirmTransferState
    extends FlowTransactionState<LuckyMoneyConfirmTransfer>
    with AuthTransactionConfirmMixin<LuckyMoneyConfirmTransfer> {
  final ValueNotifier<String?> _errorText = ValueNotifier<String?>(null);
  final _scrollController = ScrollController();
  late String? _time;
  late StreamSubscription _streamSubscription;

  @override
  String get firstRoute => RoutePaths.lucky_money_fill;

  @override
  void initState() {
    super.initState();
    _errorText.addListener(() {
      if (!_errorText.value.isNullOrEmpty) {
        Future.delayed(const Duration(milliseconds: 300), () {
          _scrollController.animateTo(
              _scrollController.position.maxScrollExtent,
              duration: const Duration(milliseconds: 300),
              curve: Curves.fastOutSlowIn);
        });
      }
    });
    _time = (widget.fillBloc.schedule.valueOrNull != null
            ? widget.fillBloc.schedule.valueOrNull?.fromDate
            : DateTime.now())
        .timeFormatLuckyMoney;
    _streamSubscription = widget.fillBloc.schedule.listen((value) {
      _time = (widget.fillBloc.schedule.valueOrNull != null
              ? widget.fillBloc.schedule.valueOrNull?.fromDate
              : DateTime.now())
          .timeFormatLuckyMoney;
      setState(() {});
    });
  }

  @override
  void dispose() {
    _streamSubscription.cancel();
    widget.fillBloc.changeSchedule(null);
    _errorText.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  String? _checkErrorText(ScheduleModel? scheduleModel) {
    final currentDate = DateTime.now();
    final scheduleDay = scheduleModel?.fromDate;
    final dayDifference = scheduleDay?.difference(currentDate).inDays;
    final timeDifference = scheduleDay?.difference(currentDate).inMinutes;
    if (timeDifference != null && (timeDifference >= 0 && timeDifference < 4)) {
      return 'Vui lòng đặt sau thời gian hiện tại 5 phút';
    }
    if (dayDifference != null && dayDifference > 2) {
      return 'Vượt quá thời gian đặt lịch cho phép (3 ngày tính từ hôm nay)';
    }
    if (timeDifference != null && timeDifference < 0) {
      return 'Không được phép chọn thời gian trong quá khứ';
    }
    return null;
  }

  void onPressConfirm() async {
    if (!passAuthFaceID) {
      await authByFaceID(
        showFaceID: widget.showFaceId ?? false,
        transactionNo: widget.transactionNo,
      );
    }
    if (!passAuthFaceID) return;

    final pinEtoken = await Injection.preferences.pinEtoken ?? '';
    final result = await widget.fillBloc.createLuckyMoney(
      pinEtoken: pinEtoken,
      transactionNo: widget.transactionNo,
    );
    if (result != null && mounted) {
      replace(
        context,
        LuckyMoneyTransferSuccess(fillBloc: widget.fillBloc, response: result),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return ShowErrorView(
      errorStream: widget.fillBloc.errorStream,
      child: LoadingView(
        loadingStream: widget.fillBloc.progressVisible,
        child: BackgroundAppBarImage(
          useNetworkImageAppBar: false,
          image: ImageAssets.bg_app_bar_red,
          appBar: getAppBarDark(
            context,
            title: widget.title ?? S.of(context).confirm_tranfer,
            leading: IconButton(
              onPressed: () {
                popRoutes(context);
              },
              icon: const Icon(
                Icons.arrow_back_ios,
                color: Colors.white,
              ),
            ),
          ),
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  controller: _scrollController,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: MediaQuery.of(context).size.width,
                        color: const Color(0xfff4f4f7),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 24),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              S.of(context).transaction_amount,
                              style: StyleApp.subtitle1(context),
                            ),
                            const SizedBox(height: 8),
                            MoneyWidget(
                              amount: widget.fillBloc.amount.valueOrNull,
                              fontSize: 30,
                            ),
                          ],
                        ),
                      ),
                      TransferModelDetailWidget(
                        model: widget.fillBloc.transferModel.valueOrNull,
                        padding: const EdgeInsets.symmetric(
                            vertical: 24, horizontal: 16),
                      ),
                      const LuckyDividerCustomDivider(),
                      _buildContent(context),
                    ],
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: ValueListenableBuilder<String?>(
                  valueListenable: _errorText,
                  builder: (context, value, child) {
                    return LuckyMoneyButton(
                      title: S.of(context).confirm1,
                      enableButton: value == null,
                      onTapButton: () {
                        onPressConfirm();
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _getWarning() {
    DialogUtil.confirm(
      context,
      Text(
        'Tôi đồng ý với việc phong tỏa số tiền ${widget.fillBloc.type.valueOrNull.toInfoText(context).toLowerCase()} để thực hiện giao dịch',
      ),
      onSubmit: onPressConfirm,
    );
  }

  Widget _buildContent(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          LuckyMoneyRowContent(
            title: S.of(context).time,
            contentText: _time,
          ),
          LuckyMoneyRowContent(
            title: widget.fillBloc.type.valueOrNull == LuckyMoneyType.present
                ? S.of(context).number_receiver
                : S.of(context).number_money,
            contentText: widget.fillBloc.type.valueOrNull ==
                    LuckyMoneyType.present
                ? widget.fillBloc.listReceiver.valueOrNull?.length.toString()
                : widget.fillBloc.countEnvelope.valueOrNull.toString(),
          ),
          LuckyMoneyRowContent(
            title: S.of(context).share_money,
            contentText:
                widget.fillBloc.method.valueOrNull.toLocaleString(context),
          ),
          LuckyMoneyRowContent(
            title: S.of(context).share1,
            contentText: S.of(context).transfers,
          ),
          LuckyMoneyRowContent(
            title: S.of(context).validity_period,
            contentText: widget.fillBloc.effectiveTime.valueOrNull
                .toLocaleString(context),
          ),
          LuckyMoneyRowContent(
            title: S.of(context).transaction_fee,
            contentWidget: Text(
              S.of(context).free,
              style: StyleApp.subtitle1(context)
                  ?.copyWith(color: const Color(0xff00BC3C)),
            ),
          ),
          StreamBuilder<ScheduleModel?>(
            stream: widget.fillBloc.schedule,
            builder: (context, snapshot) {
              final schedule = snapshot.data;
              Future.delayed(Duration.zero,
                  () => _errorText.value = _checkErrorText(schedule));
              return Column(
                children: [
                  LuckyMoneyRowContent(
                    title: S.of(context).schedule,
                    titleStyle: StyleApp.subtitle1(context),
                    contentWidget: InkWell(
                      onTap: () async {
                        final result = await goPresent(
                          context,
                          LuckyMoneyScheduleWidget(
                            schedule: schedule,
                            hour: schedule?.fromDate?.hour,
                            minute: schedule?.fromDate?.minute,
                          ),
                        );
                        if (result != null && result is ScheduleModel) {
                          if (result.isNow) {
                            widget.fillBloc.changeSchedule(null);
                          } else {
                            widget.fillBloc.changeSchedule(result);
                          }
                        }
                      },
                      child: Row(
                        children: [
                          Text(
                            schedule == null
                                ? S.of(context).transfer_now
                                : S.of(context).change,
                            style: StyleApp.subtitle1(context)
                                ?.copyWith(color: const Color(0xff228BCC)),
                          ),
                          ImageAssets.svgAssets(ImageAssets.ic_arrow_forward,
                              color: Colors.black),
                        ],
                      ),
                    ),
                    hasDivider: false,
                  ),
                  if (schedule == null)
                    Container()
                  else
                    ValueListenableBuilder<String?>(
                      valueListenable: _errorText,
                      builder: (context, value, child) => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Expanded(
                                child: Text(
                                  schedule.getStringFormatLuckyMoney,
                                  style: const TextStyle(
                                      color: Colors.blueAccent, height: 1.5),
                                ),
                              ),
                              IconButton(
                                icon:
                                    const Icon(Icons.highlight_remove_outlined),
                                onPressed: () {
                                  widget.fillBloc.changeSchedule(null);
                                },
                              )
                            ],
                          ),
                          if (value != null)
                            Text(
                              value,
                              style: StyleApp.subtitle2(context)?.copyWith(
                                  color: const Color(0xffED0000),
                                  fontWeight: FontWeight.w400),
                            )
                        ],
                      ),
                    ),
                ],
              );
            },
          )
        ],
      ),
    );
  }
}
