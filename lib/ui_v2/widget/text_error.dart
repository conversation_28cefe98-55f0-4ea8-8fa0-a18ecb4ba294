import 'package:flutter/material.dart';

class TextError extends StatelessWidget {
  final Stream<String?>? dataStream;
  final double padding;
  final Alignment alignment;

  const TextError(
      {Key? key,
      this.dataStream,
      this.padding = 10,
      this.alignment = Alignment.center})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<String?>(
      stream: dataStream,
      builder: (context, snapshot) {
        if (snapshot.hasData && snapshot.data?.isNotEmpty == true) {
          return Container(
            alignment: alignment,
            padding: EdgeInsets.all(padding),
            child: Text(
              snapshot.data ?? '',
              style: TextStyle(color: Theme.of(context).colorScheme.error),
            ),
          );
        }
        return Container();
      },
    );
  }
}
