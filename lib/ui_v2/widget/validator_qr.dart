import 'package:common/navigator.dart';
import 'package:common/qr_utils/index.dart';
import 'package:common/utils/dialog_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_bloc/bloc/money_transaction/request_transaction_bloc.dart';
import 'package:ksb_bloc/bloc/open_card/open_card_bloc.dart';
import 'package:ksb_bloc/bloc/payment_qr/payment_qr_bloc.dart';
import 'package:ksb_bloc/bloc/viet_qr/viet_qr_bloc.dart';
import 'package:mobile_banking/di/inject.dart';
import 'package:mobile_banking/generated/l10n.dart';
import 'package:mobile_banking/ui_v2/card_v2/withdrawal_atm.dart';
import 'package:mobile_banking/ui_v2/transfer/transfer_accept.dart';
import 'package:umee_shop/repository/umee_shop_repository.dart';
import 'package:umee_shop/ui/paybox/views/linking_paybox.dart';

mixin QrValidator<T extends StatefulWidget> on State<T> {
  final String REQUEST_TRANSFER = "request-transfer";
  final String QR_PAYMENT = "qr-payment";
  final String GLOBAL_UNIQUE_ID = "A000000727";
  final String REFERRAL = "referral";
  final String OPEN_CARD = "open-card";
  final _blocVietQr = Injection.injector.get<VietQrBloc>();
  final _openCardBloc = Injection.injector.get<OpenCardBloc>();
  final _requestTransactionBloc =
      Injection.injector.get<RequestTransactionBloc>();
  final _blocQr = Injection.injector.get<PaymentQrBloc>();

  @override
  void initState() {
    super.initState();
    _blocVietQr.streamSubs.add(_blocVietQr.errorStream.listen((event) {
      if (mounted) {
        DialogUtil.alert(context, event);
      }
    }));
    _openCardBloc.streamSubs.add(_openCardBloc.errorStream.listen((event) {
      if (mounted) {
        DialogUtil.alert(context, event);
      }
    }));

    _blocQr.streamSubs.add(_openCardBloc.errorStream.listen((event) {
      if (mounted) {
        DialogUtil.alert(context, event);
      }
    }));

    _requestTransactionBloc.streamSubs
        .add(_requestTransactionBloc.errorStream.listen((event) {
      if (mounted) {
        DialogUtil.alert(context, event);
      }
    }));
  }

  @override
  void dispose() {
    super.dispose();
    _blocVietQr.dispose();
    _openCardBloc.dispose();
    _blocQr.dispose();
    _requestTransactionBloc.dispose();
  }

  Future parseQrCode(String? value) async {
    if (value == null || value.isEmpty) {
      return;
    }
    final data = value.split('|');
    if (data.first == "UMEE_PAIR" || data.first == "PAYBOX_PAIR") {
      handleScanPaybox(data);
      return;
    }
    final List<String> acceptDomain =
        Environment.serversQr.map((e) => Uri.parse(e).host).toList();
    try {
      onStartDetection();
      final uri = Uri.parse(value);
      if (uri.isAbsolute && acceptDomain.contains(uri.host)) {
        var pathSegments = uri.pathSegments;
        if (pathSegments.contains(REQUEST_TRANSFER)) {
          return onParsedRequestTransfer(pathSegments);
        } else if (pathSegments.contains(REFERRAL)) {
          return onParsedReferral(pathSegments);
        } else if (pathSegments.contains(QR_PAYMENT)) {
          return onParsedQRATM(pathSegments);
        } else if (pathSegments.contains(OPEN_CARD)) {
          return onParseOpenCard(pathSegments);
        } else if (pathSegments.contains(GLOBAL_UNIQUE_ID)) {
          return onParseGlobalUniqueId(value);
        }
      } else if (value.contains(GLOBAL_UNIQUE_ID)) {
        onParseGlobalUniqueId(value);
      } else {
        onParsedUnknown(value);
      }
    } catch (e) {
      logger.e(e);
      onParsedUnknown(value);
    }
  }

  onStartDetection() {}

  onParsedLink(String url) {
    openUrl(url: url);
  }

  onParsedReferral(List<String> values) {}

  onParsedUnknown(String rawCode) {
    DialogUtil.alert(context, S.of(context).qr_code_error_message);
  }

  onParseGlobalUniqueId(String qrCode) async {
    final decode = await _blocVietQr.decodeVietQr(vietQrCode: qrCode);
    TransferModel model = TransferModel(
      targetAccountNumber: decode?.accountNo,
      targetBankCode: decode?.bankIdNapas,
      targetBankShortName: decode?.bankShortName,
      targetCommonBankName: decode?.bankShortName,
      targetBankName: decode?.bankName,
      targetBankImage: decode?.url,
      targetBankImageType: decode?.bankImageType,
      targetBankCitad: decode?.bankCitad,
      targetBankIdNapas: decode?.bankIdNapas,
      targetBankCodeId: decode?.bankCode,
      is247: true,
      amount: decode?.amount,
      note: decode?.description,
      channelCode: "2",
    );
    if (!mounted) return;
    goToTransferAcceptV2(context, model);
  }

  onParsedPdf(PdfPayload pdfFile) {
    openPDF(
      context,
      viewUrl: pdfFile.url!,
      downloadUrl: pdfFile.url!,
      title: pdfFile.name ?? '',
    );
  }

  onParsedRoom(RoomPayload room) {
    logger.i(room.roomCd, error: room.roomCode);
  }

  onParsedContractConfirm(ContractPayload contract) {
    DialogUtil.alert(context, S.of(context).qr_code_error_message);
  }

  onParseProfile(UserPayload profile) {
    DialogUtil.alert(context, S.of(context).qr_code_error_message);
  }

  onParseSalerGroup(SalerGroupPayload group) async {
    // final result = await NetworkGroupJoinScreen(
    //   group: group,
    //   showMessage: false,
    // ).join();
    // if (result == true) {
    //   goToPage(
    //     context,
    //     $residence.KSResidenceApp(
    //       onExit: () {
    //         Navigator.of(context).pop();
    //       },
    //       data: $residence.KSHomesActionsEnum.salerProfileJoin,
    //     ),
    //   );
    // }
  }

  onParseOpenCard(List<String> values) async {
    // final result = await _openCardBloc.getStmInfo(transactionId: values.last);
    // if (result != null) {
    //   return await goWithRoute(
    //     context,
    //     OpenNewCardQrPage(
    //       transactionId: values.last,
    //       cards: result.cards!,
    //     ),
    //     RoutePaths.open_card_qr_page,
    //   );
    // }
  }

  onParsedQRATM(List<String> values) async {
    final result = await _blocQr.getInfoSTM(qrPaymentId: values.last);
    if (result != null) {
      if (!mounted) return;
      goToWithdrawalAtm(context, result);
    } else {
      if (!mounted) return;
      DialogUtil.alert(context, S.of(context).expire_qr_stm, onSubmit: () {
        Injection.preferences.setQrDeepLink('');
      });
    }
  }

  onParsedRequestTransfer(List<String> values) async {
    final moneyRequest =
        await _requestTransactionBloc.getDetailMoneyTransfer(id: values.last);
    if (moneyRequest != null) {
      TransferModel model = TransferModel(
        targetBankName: moneyRequest.bankName,
        targetAccountNumber: moneyRequest.accountNo,
        targetAccountName: TiengViet.parse(moneyRequest.accountName ?? ""),
        amount: moneyRequest.amount,
        note: moneyRequest.description ?? '',
      );
      if (!mounted) return;
      goToTransferAcceptV2(context, model);
    } else {
      if (!mounted) return;
      DialogUtil.alert(context, S.of(context).error_validator_request_dialog,
          onSubmit: () {
        Injection.preferences.setQrDeepLink('');
      });
    }
  }

  void handleScanPaybox(List<String> values) {
    // data scan từ PAYBOX QR có form: UMEE_PAIR|deviceId|pairingKey|name|model|serial
    // data scan từ PAYBOX QR có form: PAYBOX_PAIR|deviceId|pairingKey|name|model|serial
    if (values.length == 6) {
      go(
          context,
          LinkPayBoxPage(
            payBox: PayBox.fromScanQr(
              deviceId: values[1],
              pairingKey: values[2],
              name: values[3],
              model: values[4],
              serial: values[5],
            ),
          ));
    } else {
      onParsedUnknown("");
    }
  }
}
