import 'package:cached_network_image/cached_network_image.dart';
import 'package:common/widgets/loading/loading.dart';
import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';

class ImageViewFull extends StatefulWidget {
  final List<String> images;
  final int? index;
  final Map<String, String>? headers;

  ImageViewFull({required this.images, this.index, this.headers});

  @override
  _ScreenState createState() => _ScreenState();
}

class _ScreenState extends State<ImageViewFull> {
  late PageController _pageController;
  String tag = '';

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: widget.index ?? 0);
    tag = '${(widget.index ?? 0) + 1}/${widget.images.length}';
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            Container(
              color: Colors.black,
              alignment: Alignment.center,
              child: PhotoViewGallery.builder(
                itemCount: widget.images.length,
                builder: (context, index) {
                  return PhotoViewGalleryPageOptions(
                    imageProvider: CachedNetworkImageProvider(
                        widget.images[index],
                        headers: widget.headers),
                    initialScale: PhotoViewComputedScale.contained,
                  );
                },
                loadingBuilder: (context, event) => Container(
                  alignment: Alignment.center,
                  child: ring,
                ),
                onPageChanged: (index) {
                  setState(() {
                    tag = '${index + 1}/${widget.images.length}';
                  });
                },
                pageController: _pageController,
              ),
            ),
            Positioned(
              bottom: 35,
              right: 15,
              child: Text(
                tag,
                style: Theme.of(context)
                    .textTheme
                    .bodyMedium
                    ?.copyWith(color: Colors.white),
              ),
            ),
            Positioned(
              top: 0,
              left: 0,
              child: InkWell(
                onTap: () {
                  Navigator.of(context).pop();
                },
                child: Container(
                  padding: const EdgeInsets.all(15),
                  child: const Icon(
                    Icons.close_rounded,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
