import 'package:common/widgets/calendar/cupertino/date_picker.dart' as common;
import 'package:common/widgets/style_app.dart';
import 'package:flutter/material.dart';
import 'package:ksb_common/shared/assets.dart' as klbCommon;
import 'package:ksb_common/shared/widgets/background_app.dart';

import '../../assets/assets.dart';

class Test extends StatelessWidget {
  Test({Key? key}) : super(key: key);
  final TextEditingController _pinPutController = TextEditingController();
  final FocusNode _pinPutFocusNode = FocusNode();

  BoxDecoration get _pinPutDecoration {
    return BoxDecoration(
      color: Colors.black,
      borderRadius: BorderRadius.circular(100.0),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BackgroundAppBarImage(
      image: ImageAssets.svgAssets(
        klbCommon.ImageAssets.img_appbar,
        fit: BoxFit.fill,
        width: MediaQuery.of(context).size.width,
      ),
      appBar: AppBarCustom(
        backgroundColor: Colors.transparent,
        title: "Test",
        brightness: Brightness.dark,
        style: StyleApp.titleStyle(context, color: Colors.white),
      ),
      child: Container(
        color: Theme.of(context).cardColor,
        child: Column(
          children: [
            TextButton(
              child: Text("Show dialog"),
              onPressed: () async {},
            ),
            SizedBox(
              height: 300,
              width: 300,
              child: common.CupertinoDatePicker(
                onDateTimeChanged: (DateTime value) {
                  // setDate('${value.month}/${value.year}', setDateFunction,
                  //     section, arrayPos);
                },
                initialDateTime: DateTime.now(),
                mode: common.CupertinoDatePickerMode.date,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
