import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_bloc/bloc/profile/profile_bloc.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:mobile_banking/assets.dart';
import 'package:mobile_banking/di/inject.dart';
import 'package:mobile_banking/generated/l10n.dart';
import 'package:mobile_banking/ui_v2/widget/image/image_from_type.dart';
import 'package:mobile_banking/ui_v2/widget/loading/loading_view.dart';
import 'package:common/widgets/bottom_sheet.dart';
import 'package:mobile_banking/utils/screen_size.dart';

class LoginMiniAppPage extends StatefulWidget {
  final String? icon;
  final String? name;
  final String? scopes;

  const LoginMiniAppPage({super.key, this.icon, this.name, this.scopes});

  @override
  State<LoginMiniAppPage> createState() => _LoginMiniAppPageState();
}

class _LoginMiniAppPageState extends State<LoginMiniAppPage> {
  final _bloc = Injection.injector.get<ProfileBloc>();
  final _accountListBloc = Injection.injector.get<ListAccountsBloc>();

  @override
  void initState() {
    super.initState();
    _init();
  }

  @override
  void dispose() {
    _bloc.dispose();
    _accountListBloc.dispose();
    super.dispose();
  }

  _init() async {
    await _accountListBloc.getAccountList();
    final isAcceptTerm = await _bloc.isAcceptTerm(widget.name ?? '');
    if (isAcceptTerm == true) {
      _confirmLogin();
    }
  }

  _confirmLogin() async {
    var info = await _bloc.getProfileWithBankInfo();
    List<AccountModel> listData =
        _accountListBloc.items.valueOrNull ?? [];
    await _accountListBloc.items
        .where((element) => element != null && element.length > 0)
        .first;
    var _bankInfo = _accountListBloc.selectedItem;
    if (_bankInfo != null) {
      info['acc_no'] = _bankInfo.accountNumber;
      info['acc_name'] = _bankInfo.accountName;
      info['acc_bankname'] = BaseBloc.KLB.subTitle;
      info['accBranch'] = _bankInfo.branch;
      info['acc_customer_name'] = _bankInfo.customerName;
    }
    if (listData != null && listData.isNotEmpty) {
      info["accounts"] = List.from(listData.map((bankInfo) => <String, dynamic>{
        "accNo": bankInfo.accountNumber,
        'accName': bankInfo.accountName,
        'accBankName': BaseBloc.KLB.subTitle,
        'accBranch': bankInfo.branch,
        'accCustomerName': bankInfo.customerName,
      }));
    }
    await _bloc.setAcceptTerm(widget.name ?? '');
    return Navigator.of(context).pop(info);
  }

  @override
  Widget build(BuildContext context) {
    return LoadingView(
      loadingStream: _accountListBloc.progressVisible,
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Container(
          margin: const EdgeInsets.only(top: 50),
          alignment: Alignment.bottomCenter,
          decoration: const BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.all(Radius.circular(8.0)),
          ),
          child: FutureBuilder<bool>(
              future: _bloc.isAcceptTerm(widget.name ?? ''),
              builder: (context, snapshot) {
                if (snapshot.data == true) return Container();
                return Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: (ScreenMode.isLight == true
                        ? Theme.of(context).cardColor
                        : Theme.of(context).colorScheme.background),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                    ),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const BottomSheetHeader(
                        hasCloseButton: false,
                        hasDivider: false,
                      ),
                      getImageWidgetFromType(widget.icon, ImageType.LOCAL_SVG,
                          width: 70),
                      Text(
                        widget.name ?? '',
                        style: StyleApp.settingStyle(context),
                      ),
                      const Divider(),
                      Text(S.of(context).confirm_use_account_ksbank_for_login),
                      const SizedBox(height: 20),
                      SizedBox(
                        width: MediaQuery.of(context).size.width,
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 15),
                            backgroundColor: Theme.of(context).primaryColor,
                          ),
                          onPressed: _confirmLogin,
                          child: Text(S.of(context).sign_in),
                        ),
                      ),
                      const SizedBox(height: 10),
                      SizedBox(
                        width: MediaQuery.of(context).size.width,
                        child: OutlinedButton(
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 15),
                          ),
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          child: Text(
                            S.of(context).common_cancel,
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.error,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 30)
                    ],
                  ),
                );
              }),
        ),
      ),
    );
  }
}
