import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:common/utils/dialog_util.dart';
import 'package:ksb_common/shared/widgets/background_app.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:mobile_banking/utils/extension/color.dart';
import 'package:open_file/open_file.dart';
import 'package:path/path.dart' as path;
import 'package:common/global_callback.dart';
import 'package:common/ks_common.dart'
    show
        QRDetection,
        UserPayload,
        ContractPayload,
        PdfPayload,
        RoomPayload,
        GlobalCallback;
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mobile_banking/assets.dart';
import 'package:mobile_banking/di/inject.dart';
import 'package:mobile_banking/generated/l10n.dart';
import 'package:mobile_banking/model/file_model.dart';
import 'package:mobile_banking/navigator.dart';
import 'package:mobile_banking/ui_v2/invest_more/widgets/invest_appbar_button_widget.dart';
import 'package:mobile_banking/ui_v2/widget/picker/image_picker_handler.dart';
import 'package:mobile_banking/ui_v2/pay/pay_miniapp/common_payment_page.dart';
import 'package:mobile_banking/utils/navigator.dart';
import 'package:path_provider/path_provider.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_android/webview_flutter_android.dart';
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';

import 'login_mini_app_page.dart';

const fileExtensions = [
  '.pdf',
  '.doc',
  '.docx',
  '.ppt',
  '.pptx',
  '.xls',
  '.xlsx'
];

class WebViewPage extends StatefulWidget {
  final String? url;
  final String? title;
  final String? icon;
  final SystemUiOverlayStyle? overlayStyle;
  final Color? backgroundColor;
  final bool? showAppBar;
  final String? initialQuery;
  final bool? handleFirstLoad;

  const WebViewPage({
    super.key,
    this.url,
    this.title,
    this.icon,
    this.overlayStyle,
    this.backgroundColor,
    this.showAppBar,
    this.initialQuery,
    this.handleFirstLoad,
  });

  @override
  State<WebViewPage> createState() => _WebViewPageState();
}

class _WebViewPageState extends State<WebViewPage>
    with
        RouteAware,
        TickerProviderStateMixin,
        ImagePickerListener,
        QRDetection {
  final String trickJs = '123;';
  late WebViewController _webController;
  final _loadingStream = StreamController<bool>()..add(false);
  StreamSubscription<ActionStatus>? _updateTokenStream;
  bool _showAppBar = true;
  Color? _backgroundColor;
  SystemUiOverlayStyle? _overlayStyle;
  var _firstLoad = true;
  var clearCache = false;
  late AnimationController _controller;
  late ImagePickerHandler imagePicker;
  late PlatformWebViewControllerCreationParams params;

  @override
  void initState() {
    super.initState();
    if (WebViewPlatform.instance is WebKitWebViewPlatform) {
      ///IOS
      params = WebKitWebViewControllerCreationParams(
        allowsInlineMediaPlayback: true,
        mediaTypesRequiringUserAction: const <PlaybackMediaTypes>{},
      );
    } else {
      ///Android
      params = const PlatformWebViewControllerCreationParams();
    }

    _webController = WebViewController.fromPlatformCreationParams(params)
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..addJavaScriptChannel("Sunshine", onMessageReceived: _handlerWebMessage)
      ..setNavigationDelegate(NavigationDelegate(
        onPageStarted: (url) {
          _loadingStream.add(true);
        },
        onPageFinished: (url) {
          if (!_loadingStream.isClosed) {
            _loadingStream.add(false);
          }
          if (widget.handleFirstLoad != true) _updateFirstLoad();
        },
        onNavigationRequest: (request) async {
          final url = request.url;
          if (Platform.isAndroid && _checkFileExtension(url)) {
            launchWithUrl(url);
            return NavigationDecision.prevent;
          } else if (_isInstallApp(url)) {
            launchWithUrl(url);
            return NavigationDecision.prevent;
          }
          return NavigationDecision.navigate;
        },
      ));
    if (!widget.url.isNullOrEmpty) {
      _webController.loadRequest(Uri.parse(widget.url ?? ""));
    }

    if (_webController.platform is AndroidWebViewController) {
      AndroidWebViewController.enableDebugging(true);
      (_webController.platform as AndroidWebViewController)
          .setMediaPlaybackRequiresUserGesture(false);
    }

    // Exception on clear cached android
    // if (Platform.isAndroid) WebViewWidget.platform = SurfaceAndroidWebView();

    _overlayStyle = widget.overlayStyle;
    _backgroundColor = widget.backgroundColor;
    _showAppBar = widget.showAppBar ?? true;
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    imagePicker = ImagePickerHandler(this, _controller,
        isCrop: true, isMultiple: true, isPickFile: true);
    imagePicker.init();
    if (widget.initialQuery != null) parseQrCode(widget.initialQuery);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    super.dispose();
    _updateTokenStream?.cancel();
    _loadingStream.close();
    if (clearCache == true) {
      _webController.clearCache();
    }
  }

  @override
  void didPopNext() {
    super.didPopNext();
    _webController.runJavaScript(
        '''SunshineWebSdk.onResume();$trickJs''').catchError((e) {
      logger.t(e);
    });
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      ///test lại. oke thì xóa cmt này
      onPopInvoked: (didPop) => _back(),
      child: AnnotatedRegion<SystemUiOverlayStyle>(
        value: _overlayStyle ?? SystemUiOverlayStyle.dark,
        child: Scaffold(
          appBar: _getAppBar(),
          body: widget.url.isNullOrEmpty
              ? Container()
              : Stack(
                  children: [
                    _getBody(),
                    if (_firstLoad) _loadingPage(),
                  ],
                ),
        ),
      ),
    );
  }

  _getAppBar() {
    return _showAppBar == true
        ? AppBarCustom(
            leading: _getPreviousButton(),
            title: widget.title ?? S.of(context).kienlongbank,
            loadingStream: _loadingStream.stream,
            //titleSpacing: 0,
            actions: const [InvestAppbarButtonWidget()],
          )
        : null;
  }

  _getPreviousButton() {
    return TextButton(
      child: Container(
        alignment: Alignment.center,
        child: Icon(
          Icons.arrow_back_ios,
          color: Theme.of(context).appBarTheme.actionsIconTheme?.color,
        ),
      ),
      onPressed: () async {
        final onWillPop = await _back();
        if (onWillPop == true && mounted) {
          Navigator.of(context).pop();
        }
      },
    );
  }

  Future<bool> _back() async {
    final canBack = await _webController.canGoBack() == true;
    if (canBack == true) {
      _webController.goBack();
      return Future.value(false);
    }
    return Future.value(true);
  }

  _handlerWebMessage(JavaScriptMessage msg) {
    final message = msg.message;
    logger.t(message);

    final components = message.split(":");
    final command = components[0];
    final value = message.replaceFirst('$command:', '');
    if (command == 'hideAppBar') {
      if (_showAppBar != false) {
        setState(() {
          _showAppBar = false;
        });
      }
    } else if (command == 'exitMiniApp') {
      clearCache = toBool(value);
      Navigator.of(context).pop();
    } else if (command == 'login') {
      showLoginConfirm();
    } else if (command == 'setBackgroundColor') {
      Color? color;
      if (components.length > 1) {
        final value = components[1];
        if (value.contains("#") || !value.contains(",")) {
          color = HexColor.fromHex(value);
        } else {
          final rgb = value.split(",");
          color =
              Color.fromARGB(255, toInt(rgb[0]), toInt(rgb[1]), toInt(rgb[2]));
        }
        if (components.length > 2) {
          final isLight = toBool(components[2]);
          _overlayStyle =
              isLight ? SystemUiOverlayStyle.dark : SystemUiOverlayStyle.light;
        }
      }
      setState(() {
        _backgroundColor = color ?? Theme.of(context).colorScheme.surface;
      });
    } else if (command == 'openUrl') {
      if (value.length > 1) {
        launchWithUrl(value);
      }
    } else if (command == 'makePayment') {
      if (components.length > 1) {
        showCommonPayment(value);
      }
    } else if (command == 'openChat') {
      if (components.length > 1) {
        showChatMiniApp(value);
      }
    } else if (command == 'debugPrint') {
      logger.t(value);
    } else if (command == 'getToken') {
      getToken();
    } else if (command == 'pickFile') {
      pickFile(context);
    } else if (command == 'openBase64Pdf') {
      openBase64Pdf(value);
    } else if (command == 'scanQr') {
      scanQr();
    }
  }

  scanQr() async {
    final qrCode = await GlobalCallback.instance.onOpenScanQR?.call();
    if (qrCode != null) parseQrCode(qrCode);
  }

  pickFile(BuildContext context) =>
      uploadDocuments(context, imagePicker, onPickedFile: (file) {
        final extension = path.extension(file.path);
        if (checkImage(extension) || checkPdf(extension)) {
          sendFileToWeb([file]);
          return;
        }
        DialogUtil.alert(context, S.of(context).media_not_supported);
      });

  openBase64Pdf(String base64String) async {
    Uint8List bytes = base64.decode(base64String);
    String? dir = (Platform.isIOS
            ? await getApplicationDocumentsDirectory()
            : await getExternalStorageDirectory())
        ?.path;
    File file = File("$dir/${DateTime.now().millisecondsSinceEpoch}.pdf");
    await file.writeAsBytes(bytes);
    await OpenFile.open(file.path);
  }

  getToken() async {
    final token = await Injection.injector.get<Session>().getToken();
    final environment = Injection.injector.get<Environment>();
    final query =
        '''SunshineWebSdk.getTokenResponse('accepted', '${environment.toJson()}', '${jsonEncode(token)}');$trickJs''';
    logger.t('FROM APP - Query: $query');
    _webController.runJavaScript(query);
  }

  showCommonPayment(value) async {
    go(context, CommonPaymentPage(data: value));
  }

  showChatMiniApp(value) async {
    GlobalCallback.instance.onChatOpen?.call(
      ChatModel(openLiveChat: true),
    );
  }

  showLoginConfirm() async {
    final userInfo = await goPresent<Map<String, dynamic>>(
      context,
      LoginMiniAppPage(
        name: widget.title,
        icon: widget.icon,
      ),
    );
    if (userInfo != null) {
      logger.t(jsonEncode(userInfo));
      final query =
          '''SunshineWebSdk.loginResponse('accepted', '${jsonEncode(userInfo)}');$trickJs''';
      logger.t(query);
      _webController.runJavaScript(query);
    } else {
      _webController.runJavaScript(
          '''SunshineWebSdk.loginResponse('canceled', '');$trickJs''');
    }
  }

  _loadingPage() {
    return Container(
      color: _backgroundColor,
      alignment: Alignment.center,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.max,
        children: [
          Text(
            S.of(context).loading_data_please_wait,
            style: StyleApp.descStyle(context).copyWith(
                color: _overlayStyle == SystemUiOverlayStyle.light
                    ? Colors.white
                    : Colors.black),
          ),
          const SizedBox(height: 10),
          const CircularProgressIndicator(),
        ],
      ),
    );
  }

  _updateFirstLoad() {
    Future.delayed(const Duration(seconds: 2), () {
      if (_firstLoad && mounted) {
        setState(() {
          _firstLoad = false;
        });
        if (widget.initialQuery != null) parseQrCode(widget.initialQuery);
      }
    });
  }

  bool _isInstallApp(String? url) {
    if (url == null || url.isEmpty) return false;
    return url.contains(RegExp(r'itms-appss://apps.apple.com|play.google.com'));
  }

  Widget _getBody() {
    // not use
    // final environment = Injection.injector.get<Environment>();
    return Container(
      color: _backgroundColor ?? Theme.of(context).colorScheme.surface,
      child: SafeArea(
        child: WebViewWidget(
          controller: _webController,
          //prevent white screen when using swipe to back on IOS
          gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>{
            Factory<VerticalDragGestureRecognizer>(
              () => VerticalDragGestureRecognizer(),
            ),
            Factory<HorizontalDragGestureRecognizer>(
              () => HorizontalDragGestureRecognizer(),
            )
          },
        ),
      ),
    );
  }

  bool _checkFileExtension(String url) {
    for (var extension in fileExtensions) {
      if (url.contains(extension)) {
        return true;
      }
    }
    return false;
  }

  linearProgressStream(Stream<bool>? loadingStream, {double height = 1}) =>
      loadingStream != null
          ? StreamBuilder<bool>(
              stream: loadingStream,
              initialData: false,
              builder: (context, snapshot) => snapshot.data == true
                  ? SizedBox(
                      height: height, child: const LinearProgressIndicator())
                  : Container(),
            )
          : Container();

  @override
  onPicked(List<File> images) {
    if (!images.isNullOrEmpty) sendFileToWeb(images);
  }

  sendFileToWeb(List<File> files) async {
    var files0 = files.map((x) => FileModel(file: x)).toList();
    final query =
        '''SunshineWebSdk.fileResponse('accepted', '${jsonEncode(files0)}');$trickJs''';
    logger.t('FROM APP - query - $query');
    _webController.runJavaScript(query);
  }

  @override
  onError(String message) {}

  @override
  onProcessing() {}

  @override
  onParseProfile(UserPayload profile) {}

  @override
  onParsedContractConfirm(ContractPayload contract) {}

  @override
  onParsedLink(String url) {}

  @override
  onParsedPdf(PdfPayload pdfFile) {}

  @override
  onParsedRoom(RoomPayload room) {
    if (room.roomCd != null) {
      final query =
          '''SunshineWebSdk.onParsedRoom('accepted', '${room.roomCode}');$trickJs''';
      logger.t(query);
      _webController.runJavaScript(query);
    }
  }

  @override
  onParsedUnknown(String rawCode) {
    final query =
        '''SunshineWebSdk.onParsedUnknown('accepted', '$rawCode');$trickJs''';
    logger.t(query);
    _webController.runJavaScript(query);
  }
}
