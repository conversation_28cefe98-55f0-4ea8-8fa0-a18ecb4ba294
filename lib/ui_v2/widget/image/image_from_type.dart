import 'package:flutter_svg/flutter_svg.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:common/widgets/load_image_url.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:mobile_banking/assets/assets.dart';

Widget getImageWidgetFromType(
  String? url,
  ImageType? type, {
  Widget? emptyUrl,
  double? width,
  double? height,
  BoxFit? fit,
}) {
  if (url == null || url.isEmpty) {
    return emptyUrl ?? Container();
  }
  final isNetwork = url.startsWith('http');
  final isSvg = url.endsWith('svg');
  if ((type == ImageType.LOCAL_SVG || isSvg) && !isNetwork) {
    return ImageAssets.svgAssets(
      url,
      width: width,
      height: height,
      fit: fit ?? BoxFit.contain,
    );
  } else if ((type == ImageType.LOCAL_IMAGE || !isSvg) && !isNetwork) {
    return Image.asset(
      url,
      width: width,
      height: height,
      fit: fit ?? BoxFit.cover,
      errorBuilder: (_, __, ___) {
        return emptyUrl ?? Container();
      },
    );
  } else if ((type == ImageType.NETWORK_SVG) || (isNetwork && isSvg)) {
    return ImageAssets.svgUrl(
      url,
      width: width,
      height: height,
      padding: const EdgeInsets.all(0),
      fit: fit ?? BoxFit.contain,
      placeholderWidget: emptyUrl ?? Container(),
    );
  }
  return LoadImageUrl(
    url: url,
    width: width,
    height: height,
    fit: fit,
    viewUrlEmpty: () {
      return emptyUrl ?? Container();
    },
  );
}
