import 'package:common/model/loading_event.dart';
import 'package:common/utils/dialog_util.dart';
import 'package:flutter/material.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_common/shared/widgets/background_app.dart';
import 'package:ksb_common/shared/assets.dart' as ksb;
import 'package:mobile_banking/assets.dart';
import 'package:mobile_banking/assets/dimens.dart';
import 'package:mobile_banking/di/inject.dart';
import 'package:mobile_banking/generated/l10n.dart';
import 'package:mobile_banking/ui_v2/saving/tabs/accumulation_tab.dart';
import 'package:mobile_banking/ui_v2/saving/tabs/saving_tab.dart';
import 'package:mobile_banking/ui_v2/widget/loading/loading.dart';
import 'package:mobile_banking/ui_v2/widget/tab_bar_custom.dart';

class SavingAndAccumulatePage extends StatefulWidget {
  const SavingAndAccumulatePage({super.key});

  @override
  State<SavingAndAccumulatePage> createState() =>
      _SavingAndAccumulatePageState();
}

class _SavingAndAccumulatePageState extends State<SavingAndAccumulatePage>
    with SingleTickerProviderStateMixin {
  final _bloc = Injection.injector.get<SavingBloc>();
  final _env = Injection.injector.get<Environment>();
  late TabController tabController;

  @override
  void initState() {
    super.initState();

    _bloc.getSavingAccounts();

    tabController = TabController(
      length: 2,
      vsync: this,
      initialIndex: 0,
      animationDuration: const Duration(seconds: 1),
    );

    _env.appEventStream.listen((event) {
      if (event == AppEvent.SAVING_ACCOUNT_CHANGE) {
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) {
            _bloc.getSavingAccounts();
          }
        });
      }
    });

    _bloc.streamSubs.add(_bloc.errorStream.listen((event) {
      if (mounted) {
        DialogUtil.alert(context, event);
      }
    }));
  }

  @override
  Widget build(BuildContext context) {
    return BackgroundAppBarImage(
      appBar: getAppBarDark(
        context,
        title: S.of(context).saving_and_accumulate,
      ),
      image: ImageAssets.svgAssets(
        ksb.ImageAssets.img_appbar,
        fit: BoxFit.fill,
        width: MediaQuery.of(context).size.width,
      ),
      child: DefaultTabController(
        initialIndex: 0,
        length: 2,
        child: Column(
          children: [
            Container(
              alignment: Alignment.centerLeft,
              padding: const EdgeInsets.only(
                left: kPaddingSmall,
                right: kPaddingPage,
                top: kPaddingSmall,
              ),
              child: TabBarCustom(
                tabs: [
                  Tab(
                    icon: Text(S.of(context).saving),
                  ),
                  Tab(
                    icon: Text(S.of(context).accumulate),
                  ),
                ],
              ),
            ),
            Divider(
              thickness: 1,
              height: 1,
              color: Theme.of(context).dividerColor,
            ),
            Expanded(
              child: StreamBuilder<LoadingWidgetModel>(
                stream: _bloc.savingAccountStream,
                builder: (context, snapshot) {
                  final data = snapshot.data;
                  if (data == null) return loadingData();
                  final accounts = data.data;
                  if (accounts is SavingAccounts) {
                    return TabBarView(
                      children: <Widget>[
                        SavingTab(
                          onRefresh: () => _bloc.getSavingAccounts(),
                          listData:
                              accounts.onlineAndOfflineSavingAccounts ?? [],
                        ),
                        AccumulationTab(
                          onRefresh: () => _bloc.getSavingAccounts(),
                          data: accounts,
                        ),
                      ],
                    );
                  } else {
                    return loadingData();
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget headerTabs() {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Column(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: kPaddingMedium),
              child: Row(
                children: [
                  TextButton(
                    onPressed: () {
                      //Tap1
                    },
                    child: Text(
                      "Tiết kiệm",
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w400,
                          ),
                    ),
                  ),
                  const SizedBox(width: kPaddingMedium),
                  TextButton(
                    onPressed: () {
                      //Tap2
                    },
                    child: Text(
                      "Tích lũy",
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w400,
                          ),
                    ),
                  ),
                ],
              ),
            ),
            const Divider(
              thickness: 1,
              height: 1,
              indent: 1,
              color: Color(0xFFebebeb),
            ),
          ],
        ),
        Positioned(
          top: 47,
          left: 17,
          child: Container(
            height: 3,
            width: 63,
            decoration: const BoxDecoration(
              color: Color(0xFF2b275e),
              borderRadius: BorderRadius.all(Radius.circular(3)),
            ),
          ),
        )
      ],
    );
  }

  Widget loadingData() {
    return TabBarView(
      children: <Widget>[
        ring,
        ring,
      ],
    );
  }
}
