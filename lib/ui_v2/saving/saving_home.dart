import 'package:common/utils/dialog_util.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:common/model/loading_event.dart';
import 'package:common/widgets/bottom_button.dart';
import 'package:common/widgets/loading/loading.dart';
import 'package:common/widgets/style_app.dart';
import 'package:flutter/material.dart';
import 'package:mobile_banking/assets/assets.dart';
import 'package:mobile_banking/assets/images.dart';
import 'package:mobile_banking/di/inject.dart';
import 'package:mobile_banking/generated/l10n.dart';
import 'package:mobile_banking/ui_v2/saving/new_saving_page.dart';
import 'package:mobile_banking/ui_v2/saving/saving_online/open_saving_page.dart';
import 'package:mobile_banking/ui_v2/saving/savings_offline.dart';
import 'package:mobile_banking/ui_v2/saving/savings_online.dart';
import 'package:mobile_banking/ui_v2/saving/savings_vnp.dart';
import 'package:ksb_common/shared/widgets/background_app.dart';
import 'package:mobile_banking/utils/navigator.dart';
import 'package:ksb_common/shared/assets.dart' as ksb;

import 'saving_vnp/opening_saving_vnp.dart';

class SavingHome extends StatefulWidget {
  static const TYPE_OFFLINE = 0;
  static const TYPE_ONLINE = 1;
  static const TYPE_TARGET = 2;
  static const TYPE_VNP = 3;
  final int? type;
  final String? title;
  final bool? isCreateNew;

  const SavingHome({
    Key? key,
    this.type,
    this.title,
    this.isCreateNew,
  }) : super(key: key);

  @override
  State<SavingHome> createState() => _SavingHomeState();
}

class _SavingHomeState extends State<SavingHome> {
  final _bloc = Injection.injector.get<SavingBloc>();
  final _env = Injection.injector.get<Environment>();
  late bool isSavingVNP;

  @override
  void initState() {
    super.initState();
    isSavingVNP = widget.type == SavingHome.TYPE_VNP;
    isSavingVNP ? _bloc.getSavingVNP() : _bloc.getSavingAccounts();

    _env.appEventStream.listen((event) {
      if (event == AppEvent.SAVING_ACCOUNT_CHANGE) {
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) {
            _bloc.getSavingAccounts();
          }
        });
      } else if (event == AppEvent.SAVING_VNP_ACCOUNT_CHANGE) {
        if (mounted) {
          _bloc.getSavingVNP();
        }
      }
    });

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      if (widget.isCreateNew == true) {
        openNewSaving();
      }
    });

    _bloc.streamSubs.add(_bloc.errorStream.listen((event) {
      if (mounted) {
        DialogUtil.alert(context, event);
      }
    }));
  }

  @override
  Widget build(BuildContext context) {
    return BackgroundAppBarImage(
      appBar: _getAppbar(),
      child: isSavingVNP ? _listSavingVNP() : _listSaving(),
      image: ImageAssets.svgAssets(
        ksb.ImageAssets.img_appbar,
        fit: BoxFit.fill,
        width: MediaQuery.of(context).size.width,
      ),
    );
  }

  _getAppbar() {
    return getAppBarDark(
      context,
      title: widget.title ?? S.of(context).saving_home_title_saving,
    );
  }

  _listSaving() {
    return StreamBuilder<LoadingWidgetModel>(
      stream: _bloc.savingAccountStream,
      builder: (context, snapshot) {
        final data = snapshot.data;
        if (data == null) return ring;
        final accounts = data.data;
        if (accounts is SavingAccounts) {
          return Column(
            children: [
              Expanded(
                child: RefreshIndicator(
                  onRefresh: () async {
                    _bloc.getSavingAccounts();
                  },
                  child: getListsItems(accounts),
                ),
              ),
              getBottomButton(accounts),
            ],
          );
        } else {
          return _emptyWidget();
        }
      },
    );
  }

  _listSavingVNP() {
    return StreamBuilder<LoadingWidgetModel>(
      stream: _bloc.savingVNPStream,
      builder: (context, snapshot) {
        final dataSnapshot = snapshot.data?.data;
        if (dataSnapshot == null) return ring;
        final savingVNPs = dataSnapshot.data;
        return Column(
          children: [
            Expanded(
              child: RefreshIndicator(
                onRefresh: () async => _bloc.getSavingVNP(),
                child: (savingVNPs == null || savingVNPs.isEmpty)
                    ? _emptyWidget()
                    : SavingVNPCollectionWidget(dataVNPs: savingVNPs),
              ),
            ),
            BottomButton(
              title: S.of(context).common_open_new,
              onTap: openNewSaving,
            ),
          ],
        );
      },
    );
  }

  Widget getListsItems(SavingAccounts? accounts) {
    if (accounts == null || isEmpty(accounts)) return _emptyWidget();
    // if (widget.type == SavingHome.TYPE_TARGET) {
    //   return SavingsTargetWidget(
    //     accounts: accounts.targetSavingAccounts ?? [],
    //     onReload: () => _bloc.getSavingAccounts(),
    //   );
    // }
    if (widget.type == SavingHome.TYPE_ONLINE) {
      return SavingsOnlineWidget(accounts: accounts.onlineSavingAccounts ?? []);
    }
    if (widget.type == SavingHome.TYPE_OFFLINE) {
      return SavingsOfflineWidget(
          accounts: accounts.offlineSavingAccounts ?? []);
    }
    return Container();
  }

  bool isEmpty(SavingAccounts? accounts) {
    if (accounts == null) return true;
    // if (widget.type == SavingHome.TYPE_TARGET) {
    //   if ((accounts.targetSavingAccounts?.length ?? 0) <= 0) {
    //     return true;
    //   }
    // }
    if (widget.type == SavingHome.TYPE_ONLINE) {
      if ((accounts.onlineSavingAccounts?.length ?? 0) <= 0) {
        return true;
      }
    }
    if (widget.type == SavingHome.TYPE_OFFLINE) {
      if ((accounts.offlineSavingAccounts?.length ?? 0) <= 0) {
        return true;
      }
    }
    return false;
  }

  Widget getBottomButton(SavingAccounts accounts) {
    if (widget.type == SavingHome.TYPE_OFFLINE || isEmpty(accounts)) {
      return Container();
    }
    return BottomButton(
      title: S.of(context).common_open_new,
      onTap: openNewSaving,
    );
  }

  openNewSaving() {
    if (widget.type == SavingHome.TYPE_ONLINE) {
      go(context, const OpenSavingPage());
    } else
    //   if (widget.type == SavingHome.TYPE_TARGET) {
    //   go(context, const TargetAccumulationPage());
    // } else
      if (widget.type == SavingHome.TYPE_VNP) {
      go(context, const OpeningSavingVNPPage());
    } else {
      go(context, const NewSavingPage());
    }
  }

  Widget _emptyWidget() {
    return SizedBox(
      width: MediaQuery.of(context).size.width,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ImageAssets.svgAssets(ImagesResource.ic_saving_empty),
          const SizedBox(height: 16.0),
          Text(
            S.of(context).saving_history_empty_title_no_saving,
            style: StyleApp.subtitle1(context, true),
          ),
          Padding(
            padding: const EdgeInsets.only(
              left: 16.0,
              right: 16.0,
              top: 8,
              bottom: 16.0,
            ),
            child: Text(
              S.of(context).saving_history_empty_des_no_saving,
              textAlign: TextAlign.center,
              style: StyleApp.bodyStyle(context),
            ),
          ),
          if (widget.type != SavingHome.TYPE_OFFLINE &&
              widget.type != SavingHome.TYPE_VNP)
            TextButton.icon(
              onPressed: openNewSaving,
              icon: const Padding(
                padding: EdgeInsets.only(left: 16.0),
                child: Icon(
                  Icons.add,
                  color: Colors.white,
                ),
              ),
              label: Padding(
                padding: const EdgeInsets.only(right: 20.0),
                child: Text(
                  S.of(context).common_open_new,
                  style: const TextStyle(color: Colors.white),
                ),
              ),
              style: ButtonStyle(
                backgroundColor: WidgetStateProperty.all<Color>(
                    Theme.of(context).primaryColor),
              ),
            )
        ],
      ),
    );
  }
}
