import 'dart:async';
import 'dart:io';
import 'dart:typed_data';

import 'package:ksb_bloc/bloc.dart';
import 'package:common/utils/dialog_util.dart';
import 'package:common/widgets/bottom_button.dart';
import 'package:common/widgets/empty_widget.dart';
import 'package:flutter/material.dart';
import 'package:mobile_banking/di/inject.dart';
import 'package:mobile_banking/generated/l10n.dart';
import 'package:mobile_banking/ui_v2/statement/widgets/statement_pdf_widget.dart';
import 'package:mobile_banking/ui_v2/widget/loading/loading.dart';
import 'package:mobile_banking/ui_v2/widget/permission/permission.dart';
import 'package:common/widgets/bottom_sheet.dart';
import 'package:path_provider/path_provider.dart';
import 'package:screenshot/screenshot.dart';
import 'package:share/share.dart';

class SavingCertificatePage extends StatefulWidget {
  final String? accountNumber;
  final SavingAccountCertificate? info;
  final bool? isOnline;

  SavingCertificatePage({this.accountNumber, this.info, this.isOnline = true});

  @override
  _ScreenState createState() => _ScreenState();
}

class _ScreenState extends State<SavingCertificatePage>
    with WidgetsBindingObserver {
  final _savingBloc = Injection.injector.get<SavingBloc>();
  ScreenshotController screenshotController = ScreenshotController();
  Uint8List? _pdfBytes;
  String? _pathFile;
  UniqueKey? _pdfKey;

  @override
  void initState() {
    super.initState();
    _getPdfFile();

    WidgetsBinding.instance?.addObserver(this);
  }

  @override
  void dispose() {
    _savingBloc.dispose();
    WidgetsBinding.instance?.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    logger.e(state);
    if (state == AppLifecycleState.resumed) {
      Future.delayed(const Duration(milliseconds: 500), () {
        _pdfKey = UniqueKey();
        _savingBloc.refreshView();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: _getBody(),
    );
  }

  _getBody() {
    double hAppbar = MediaQuery.of(context).padding.top + kToolbarHeight;
    return Container(
      margin: EdgeInsets.only(top: hAppbar),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: const BorderRadius.all(Radius.circular(8.0)),
      ),
      child: StreamBuilder<String?>(
          stream: _savingBloc.pdfPathStream,
          builder: (context, snapshot) {
            final data = snapshot.data;
            if (data == null) {
              return Center(child: ring);
            }

            if (!data.isNullOrEmpty) {
              return _buildCertificate(_pathFile ?? '');
            }

            return Container(
              padding: const EdgeInsets.all(15),
              child: Center(
                  child: EmptyWidget(
                title: S.of(context).error_no_data,
                icon: Container(),
              )),
            );
          }),
    );
  }

  Widget _buildCertificate(String data) {
    return SizedBox(
      height: double.infinity,
      child: Column(
        children: [
          BottomSheetHeader(title: S.of(context).deposit_confirmation),
          Expanded(
            child: StatementPdfWidget(
              filePath: data,
              key: _pdfKey ?? UniqueKey(),
            ),
          ),
          BottomButton(
            title: S.of(context).download,
            onTap: () async {
              if (Platform.isIOS) {
                _onSharePdf(_pathFile ?? '');
              } else {
                _saveFileToDownload();
              }
            },
          )
        ],
      ),
    );
  }

  _onSharePdf(String path) {
    Share.shareFiles(
      [path],
      subject: S.of(context).account_pay_detail_title_statement,
    );
  }

  _saveFileToDownload() async {
    /* PermissionStatus permission = await PermissionHandler()
        .checkPermissionStatus(PermissionGroup.storage);
    if (permission != PermissionStatus.granted) {
      final request = await PermissionHandler()
          .requestPermissions([PermissionGroup.storage]);
      if (request != null) {
        permission = request[PermissionGroup.storage];
      }
    }*/
    _savingBloc.showLoading();
    checkPermission(await permissionStorage(), onAllowed: () async {
      try {
        var dir = await getExternalStorageDirectories(
            type: StorageDirectory.downloads);
        String? fileName = _pathFile?.split('/').last;
        String path = "/sdcard/Download/";
        final file = File("$path/$fileName");
        if (_pdfBytes != null) {
          await file.writeAsBytes(_pdfBytes!);
        }
        _alertSaveSuccess(file.path);
      } catch (ex) {
        DialogUtil.alert(
          context,
          S.of(context).error_download_state_fdf_fail,
          submit: S.of(context).close,
        );
      }
    }, onNeverAllowed: () {
      DialogUtil.alert(
        context,
        S.of(context).download_state_not_access,
        submit: S.of(context).close,
      );
    });
    _savingBloc.completeLoading();
  }

  _alertSaveSuccess(String path) {
    DialogUtil.confirm(
      context,
      Text(S.of(context).download_successfully),
      cancelText: S.of(context).close,
      submitText: S.of(context).common_share,
      onSubmit: () {
        _onSharePdf(path);
      },
    );
  }

  Future<String?> createFileOfPdfUrl() async {
    try {
      String filename = "${widget.accountNumber}_KienlongBank";
      final dir = await getTemporaryDirectory();
      File file = File("${dir.path}/$filename.pdf");
      final isExists = await file.exists();
      if (isExists) {
        file.deleteSync(recursive: true);
      }
      await file.create(recursive: true);
      _pdfBytes =
          await _savingBloc.getPdf(accountNumber: widget.accountNumber ?? '');
      if (_pdfBytes != null) {
        await file.writeAsBytes(_pdfBytes!, flush: true);
        _pathFile = file.path;
      } else {
        _pathFile = "";
      }
      logger.t("file $file");
    } catch (e) {
      logger.e(e);
      _pathFile = "";
    }

    return _pathFile;
  }

  _getPdfFile() {
    _savingBloc.showLoading();
    createFileOfPdfUrl().then((f) {
      _savingBloc.completeLoading();
      if (!_savingBloc.isClose) {
        _savingBloc.pdfPathSink.add(f);
      }
    });
  }
}
