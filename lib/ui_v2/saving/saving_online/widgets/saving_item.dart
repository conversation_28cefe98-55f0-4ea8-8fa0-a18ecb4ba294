import 'package:common/ks_common.dart';
import 'package:common/widgets/load_image_url.dart';
import 'package:flutter/material.dart';
import 'package:ksb_common/shared/constant.dart';
import 'package:ksb_common/shared/theme/custom_theme.dart';
import 'package:mobile_banking/assets/assets.dart';
import 'package:mobile_banking/assets/dimens.dart';
import 'package:mobile_banking/assets/images.dart';
import 'package:mobile_banking/generated/l10n.dart';
import 'package:mobile_banking/model/base_model.dart';
import 'package:mobile_banking/ui_v2/saving/saving_home.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';

class SavingItem extends StatefulWidget {
  final BaseModel model;
  final VoidCallback onPressed;

  const SavingItem({Key? key, required this.model, required this.onPressed})
      : super(key: key);

  @override
  _ScreenState createState() => _ScreenState();
}

class _ScreenState extends State<SavingItem> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final model = widget.model;
    final hasPercent = model.percent != null && model.percent! >= 0;
    return InkWell(
      onTap: () => widget.onPressed(),
      child: Container(
        margin: const EdgeInsets.only(left: 16.0, right: 16.0, top: 8.0),
        decoration: BoxDecoration(
          border: Border.all(
            color: Theme.of(context).dividerColor,
            width: 0.6,
          ),
          borderRadius: BorderRadius.circular(4.0),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              color: const Color(0xFF191250).withOpacity(0.05),
              child: _getPhotoView(),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const SizedBox(height: 12),
                    Text(
                      model.title ?? '',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: StyleApp.subtitle1(context, true),
                    ),
                    if (model.type != SavingHome.TYPE_TARGET)
                      Padding(
                        padding: const EdgeInsets.only(top: 5.0),
                        child: Text(
                          model.caption ?? '',
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: StyleApp.subtitle1(context, true)
                              ?.copyWith(color: Theme.of(context).primaryColor),
                        ),
                      ),
                    if (model.caption1?.isNotEmpty == true)
                      Padding(
                        padding: const EdgeInsets.only(top: 10),
                        child: Text(
                          model.caption1 ?? '',
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: StyleApp.bodyStyle(context),
                        ),
                      ),
                    if (model.type == SavingHome.TYPE_TARGET)
                      Padding(
                        padding: const EdgeInsets.only(top: 5),
                        child: Text(
                          model.caption ?? '',
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: StyleApp.bodyStyle(context),
                        ),
                      ),
                    if (model.caption3?.isNotEmpty == true)
                      Padding(
                        padding: const EdgeInsets.only(top: 16),
                        child: Row(
                          children: [
                            Text(
                              model.caption3 ?? '',
                              style: TextStyle(
                                color: Theme.of(context).primaryColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            if (hasPercent)
                              Expanded(
                                child: Text(
                                  model.percent?.dividedPercentFormat ?? '',
                                  maxLines: 1,
                                  textAlign: TextAlign.right,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(
                                      color: Theme.of(context).primaryColor),
                                ),
                              ),
                            const SizedBox(width: 5),
                          ],
                        ),
                      ),
                    if (hasPercent)
                      LinearPercentIndicator(
                        lineHeight: 8,
                        padding: const EdgeInsets.symmetric(horizontal: 5),
                        percent: model.percent != null
                            ? toDouble(model.percent) > 1
                                ? 1
                                : toDouble(model.percent)
                            : 0,
                        barRadius: const Radius.circular(kPaddingSmall),
                        progressColor: Theme.of(context).primaryColor,
                        backgroundColor:
                            DynamicTheme.of(context)?.customColor.lightBlue20,
                      ),
                    SizedBox(height: kSmallPadding),
                    if (model.type == SavingHome.TYPE_TARGET)
                      RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text:
                                  '${S.of(context).savings_book_opening_date}: ',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall
                                  ?.copyWith(
                                    fontWeight: FontWeight.w400,
                                    color: DynamicTheme.of(context)
                                        ?.customColor
                                        .unSelectedColorTabBar,
                                  ),
                            ),
                            TextSpan(
                              text: model.time.formatDMY,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall
                                  ?.copyWith(
                                    fontWeight: FontWeight.w500,
                                    color: DynamicTheme.of(context)
                                        ?.customColor
                                        .black,
                                  ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  _getPhotoView() {
    final model = widget.model;
    if (model.icon?.isNotEmpty ?? false) {
      return Center(
        child: ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(2),
            bottomLeft: Radius.circular(2),
          ),
          child: model.icon?.contains('http') == true
              ? LoadImageUrl(
                  url: model.icon,
                  width: toSp(120),
                  height: toSp(120),
                  viewUrlEmpty: () => ImageAssets.svgAssets(
                    ImagesResource.ic_target_saving,
                    fit: BoxFit.cover,
                    width: toSp(120),
                    height: toSp(120),
                  ),
                )
              : ImageAssets.svgAssets(
                  model.icon ?? '',
                  fit: BoxFit.cover,
                  width: toSp(120),
                  height: toSp(120),
                ),
        ),
      );
    }
    return Container(color: Colors.grey.shade400);
  }
}
