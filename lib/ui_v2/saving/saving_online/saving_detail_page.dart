import 'package:common/navigator.dart';
import 'package:common/utils/dialog_util.dart';
import 'package:common/widgets/bottom_button.dart';
import 'package:common/widgets/bottom_sheet_widget.dart';
import 'package:flutter/material.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_bloc/bloc/model/global_model.dart';
import 'package:ksb_common/shared/assets.dart' as ksb;
import 'package:ksb_common/shared/widgets/auth_transaction_mixin_common.dart';
import 'package:ksb_common/shared/theme/custom_theme.dart';
import 'package:ksb_common/shared/widgets/background_app.dart';
import 'package:mobile_banking/di/inject.dart';
import 'package:mobile_banking/generated/l10n.dart';
import 'package:mobile_banking/model/base_model.dart';
import 'package:mobile_banking/sunshine_app.dart' as sunshine_app;
import 'package:mobile_banking/ui_v2/saving/saving_online/saving_certificate_page.dart';
import 'package:mobile_banking/ui_v2/saving/saving_online/saving_settlement_page.dart';
import 'package:mobile_banking/ui_v2/saving/saving_online/widgets/account_source_widget.dart';
import 'package:mobile_banking/ui_v2/saving/saving_online/widgets/saving_info_widget.dart';
import 'package:mobile_banking/ui_v2/widget/loading/loading_area.dart';
import 'package:mobile_banking/ui_v2/widget/loading/loading_view.dart';
import 'widgets/select_settlement_method.dart';

class SavingDetailPage extends StatefulWidget {
  final String accountNumber;
  final bool isOnline;
  final String? routeNameStartPage;

  const SavingDetailPage({
    Key? key,
    required this.accountNumber,
    this.isOnline = true,
    this.routeNameStartPage,
  }) : super(key: key);

  @override
  State<SavingDetailPage> createState() => _ScreenState();
}

class _ScreenState extends FlowTransactionState<SavingDetailPage>
    with AuthTransactionMixin<SavingDetailPage> {
  final SavingBloc _savingBloc = Injection.injector.get<SavingBloc>();
  final _bloc = Injection.injector.get<ListAccountsBloc>();

  @override
  void initState() {
    widget.isOnline
        ? _savingBloc.getSavingAccountDetail(widget.accountNumber)
        : _savingBloc.getOfflineSavingAccountDetail(widget.accountNumber);
    _savingBloc.streamSubs.add(
      _savingBloc.statusStream.listen((event) {
        if (event != null) {
          listenError(event.convertModelToCommon());
        }
      }),
    );
    _savingBloc.streamSubs
        .add(_savingBloc.savingDetailStream.listen((event) async {
      if (event.data != null && event.data is NormalSavingAccountInfo) {
        final NormalSavingAccountInfo? info = event.data;
        if (info != null &&
            info.finalAccountNumber.isNullOrEmpty &&
            info.isOnline) {
          await _bloc.getAccountList();
          final defaultAccount = _bloc.selectedItem;
          info.finalAccountNumber = defaultAccount?.accountNumber;
          info.finalAccountName = defaultAccount?.accountName;
        }
        await _savingBloc.getAccountDetail(info?.finalAccountNumber ?? '');
        if (mounted) setState(() {});
      }
    }));
    super.initState();
  }

  @override
  void dispose() {
    _savingBloc.dispose();
    _bloc.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LoadingView(
      loadingStream: _savingBloc.progressVisible,
      child: BackgroundAppBarImage(
        appBar: getAppBarDark(context,
            title: S.of(context).saving_detail_title_save_detail),
        child: _getBody(),
        image: ksb.ImageAssets.bg_app_bar,
      ),
    );
  }

  _getBody() {
    return LoadingArea(
      stream: _savingBloc.savingDetailStream,
      onSuccess: (data) {
        final info = data as NormalSavingAccountInfo;

        return Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    AccountSourceWidget(
                      title: S.of(context).saving_detail_label_original_account,
                      name: info.finalAccountName ?? info.customerName,
                      accountNo:
                          info.sourceAccountNo ?? info.finalAccountNumber,
                      padding: const EdgeInsets.only(top: 8.0),
                      isSaving: true,
                      accountNo2: info.accountNumber,
                      name2: info.accountName,
                      isOnline: widget.isOnline,
                    ),
                    Divider(
                      height: 8,
                      thickness: 8,
                      color: Theme.of(context).dividerColor,
                    ),
                    SavingInfoWidget(
                      data: [
                        BaseModel(
                          title: S
                              .of(context)
                              .saving_withdraw_confirm_label_send_money,
                          right: GlobalModel.formatMoney(
                            info.balance?.toDouble(),
                            currencyStr: info.currency,
                          ),
                        ),
                        BaseModel(
                            title: S.of(context).period, right: info.termName),
                        BaseModel(
                            title: S.of(context).saving_label_interest,
                            right:
                                "${info.rate.toString().replaceAll('.', ',')}%/${S.of(context).common_year_short}"),
                        BaseModel(
                            title:
                                S.of(context).saving_label_interest_accumulate,
                            right: GlobalModel.formatMoney(
                              info.interestAmount?.toDouble(),
                              currencyStr: info.currency,
                            ),
                            isPrice: true),
                        BaseModel(
                            title:
                                S.of(context).saving_open_online_title_pay_form,
                            right: info.finalTypeName),
                        BaseModel(
                            title: S.of(context).saving_label_active_date,
                            right: info.contractDate.formatDMY),
                        BaseModel(
                            title: S
                                .of(context)
                                .deposit_certificate_label_due_date,
                            right: info.dueDate.formatDMY),
                        if (widget.isOnline)
                          BaseModel(
                              title: S
                                  .of(context)
                                  .profile_withdraw_commission_label_receiving_account,
                              right:
                                  "${info.finalAccountNumber}\n ${info.finalAccountName ?? info.customerName}"),
                        BaseModel(
                            title: S.of(context).saving_label_lockdown_state,
                            rightColor: info.blocked == true
                                ? Colors.red
                                : Colors.green,
                            right: info.blocked == true
                                ? S.of(context).common_yes
                                : S.of(context).common_no),
                      ],
                    ),
                    const SizedBox(height: 4),
                    if (widget.isOnline)
                      Divider(
                        height: 8,
                        thickness: 8,
                        color: Theme.of(context).dividerColor,
                      ),
                    if (widget.isOnline)
                      ListTile(
                        title: Text(
                          S.of(context).online_deposit_confirmation,
                          style:
                              Theme.of(context).textTheme.bodyLarge?.copyWith(
                                    color: const Color(0xFF333333),
                                    fontWeight: FontWeight.normal,
                                  ),
                        ),
                        trailing: const Icon(
                          Icons.arrow_forward_ios,
                          size: 14.0,
                          color: Color(0xFF333333),
                        ),
                        onTap: () {
                          goPresent(
                            context,
                            SavingCertificatePage(
                              accountNumber: info.accountNumber,
                            ),
                          );
                        },
                      )
                  ],
                ),
              ),
            ),
            if (widget.isOnline) buildSubmitButton(info),
          ],
        );
      },
    );
  }

  Widget _buildButtonVnPost(NormalSavingAccountInfo info) {
    return StreamBuilder<AccountModel>(
      stream: _savingBloc.accountInfoStream,
      builder: (context, snapshot) {
        return BottomButton(
          title: S.of(context).saving_button_settle,
          isDisable: !snapshot.hasData,
          onTap: () async {
            final result = await _savingBloc.checkVpnRegister();
            if (result != null &&
                result.vnpregister == true &&
                result.vnpregister == true &&
                (snapshot.data?.accountBalance ?? 0) > 0 &&
                info.blocked == false &&
                mounted) {
              goPresent(
                context,
                BottomSheetWidget(
                  isIntrinsicHeight: true,
                  title: S.of(context).choose_settlement_method,
                  child: SelectSettlementMethod(
                    onClick: (value) {
                      if (value == SettlementType.normal) {
                        Navigator.of(context).pop();
                        _settlementOnline(info: info, isVnPost: false);
                      } else {
                        Navigator.of(context).pop();
                        _settlementOnline(info: info, isVnPost: true);
                      }
                    },
                  ),
                ),
              );
            } else {
              _settlementOnline(info: info, isVnPost: false);
            }
          },
        );
      },
    );
  }

  Widget buildSubmitButton(NormalSavingAccountInfo info) {
    if (sunshine_app.moduleConfig?.enableSettlementSavingVnpost == true) {
      return _buildButtonVnPost(info);
    }
    return _buildButtonNormal(info);
  }

  Widget _buildButtonNormal(NormalSavingAccountInfo info) {
    return BottomButton(
      title: S.of(context).saving_button_settle,
      onTap: () async {
        _settlementOnline(info: info, isVnPost: false);
      },
    );
  }

  _settlementOnline(
      {required NormalSavingAccountInfo info, required bool isVnPost}) async {
    if (!widget.isOnline && mounted) {
      DialogUtil.alert(context, S.of(context).error_bill_sorry_not_support);
      return;
    } else if ((info.blocked ?? false) && mounted) {
      DialogUtil.alert(context, S.of(context).error_bill_sorry_blocked_text);
      return;
    } else if (info.finalAccountNumber.isNullOrEmpty && mounted) {
      DialogUtil.alert(context, S.of(context).error_bill_sorry_no_account);
      return;
    }
    final NormalSavingAccountInfo? result =
        await _savingBloc.reviewCloseSavingAccount(info);
    bool? isConfirm;
    if (result != null && mounted) {
      await DialogUtil.confirm(
          context,
          Text(
            S.of(context).settlement_saving_confirm(
                result.interestAmount?.currencyFormatNotDecimal ?? '',
                result.currency ?? ''),
          ),
          title: S.of(context).pre_payment,
          cancelText: S.of(context).dialog_confirm_no,
          onCancel: () => isConfirm = false,
          submitText: S.of(context).dialog_confirm_yes,
          onSubmit: () => isConfirm = true);
      if (mounted && (isConfirm ?? false)) {
        onGoToConfirm(
          nextToConfirmPage: () => go(
            context,
            SavingSettlementPage(
              info: result,
              blocked: info.blocked,
              isVnPost: isVnPost,
              routeNameStartPage: widget.routeNameStartPage,
              showFaceID: _savingBloc.showFaceID,
            ),
          ),
          nextStep: _savingBloc.step,
          message: _savingBloc.messageDialog,
        );
      }
    }
  }
}
