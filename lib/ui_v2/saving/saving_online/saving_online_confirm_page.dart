import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_bloc/bloc/model/global_model.dart';
import 'package:common/ks_common.dart';
import 'package:common/utils/dialog_util.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:ksb_common/shared/assets.dart' as ksb;
import 'package:ksb_common/shared/widgets/auth_transaction_mixin_common.dart';
import 'package:ksb_common/shared/theme/custom_theme.dart';
import 'package:mobile_banking/di/inject.dart';
import 'package:mobile_banking/generated/l10n.dart';
import 'package:mobile_banking/model/base_model.dart';
import 'package:mobile_banking/ui_v2/home/<USER>';
import 'package:mobile_banking/ui_v2/widget/loading/loading_view.dart';
import 'package:mobile_banking/ui_v2/profile_account/etoken/etoken_setup.dart';
import 'package:mobile_banking/ui_v2/saving/saving_online/saving_online_done.dart';
import 'package:mobile_banking/ui_v2/saving/saving_online/widgets/account_source_widget.dart';
import 'package:mobile_banking/ui_v2/saving/saving_online/widgets/saving_info_widget.dart';
import 'package:ksb_common/shared/widgets/background_app.dart';
import 'package:mobile_banking/utils/auth_fatsh.dart';
import 'package:url_launcher/url_launcher.dart';

class SavingOnlineConfirmPage extends StatefulWidget {
  final NormalSavingAccountInfo info;
  final String? routeNameStartPage;
  final bool showFaceID;

  const SavingOnlineConfirmPage({
    Key? key,
    required this.info,
    this.routeNameStartPage,
    this.showFaceID = false,
  }) : super(key: key);

  @override
  State<SavingOnlineConfirmPage> createState() =>
      _SavingOnlineConfirmPageState();
}

class _SavingOnlineConfirmPageState
    extends FlowTransactionState<SavingOnlineConfirmPage>
    with AuthTransactionConfirmMixin<SavingOnlineConfirmPage> {
  final _savingBloc = Injection.injector.get<SavingBloc>();
  final _bloc = Injection.injector.get<TransferBloc>();
  final _blocAuth = Injection.injector.get<AuthenTransactionBloc>();
  bool isFastAuth = false;

  @override
  Widget build(BuildContext context) {
    return LoadingView(
      loadingStream: _savingBloc.progressVisible,
      child: BackgroundAppBarImage(
        appBar: getAppBarDark(context,
            title: S.of(context).saving_withdraw_confirm_title_confirm),
        child: _getBody(),
        image: ksb.ImageAssets.bg_app_bar,
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    _savingBloc.errorStream.listen((event) {
      if (event != null && event.isNotEmpty) {
        DialogUtil.alert(context, event);
      }
    });

    _blocAuth.streamSubs.add(_blocAuth.isFastAuthStream.listen((event) {
      if (event) {
        setState(() {
          isFastAuth = event;
        });
      }
    }));
  }

  @override
  void dispose() {
    super.dispose();
    _savingBloc.dispose();
    _blocAuth.dispose();
    _bloc.dispose();
  }

  _getBody() {
    return Container(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: MediaQuery.of(context).size.width,
                    color: DynamicTheme.of(context)
                        ?.customColor
                        .buttonTransparentColor,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: EdgeInsets.only(
                            top: toSp(24.0),
                            left: 16.0,
                            bottom: 4.0,
                          ),
                          child: Text(
                            S
                                .of(context)
                                .saving_withdraw_confirm_label_send_money,
                            style: StyleApp.subtitle1(context),
                          ),
                        ),
                        Padding(
                          padding:
                              const EdgeInsets.only(left: 16.0, bottom: 16.0),
                          child: Text(
                            GlobalModel.formatMoney(widget.info.amount,
                                currencyStr: widget.info.currency),
                            style: TextStyle(
                              fontSize: Theme.of(context)
                                  .textTheme
                                  .headlineMedium
                                  ?.fontSize,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  AccountSourceWidget(
                    name: widget.info.accountName,
                    accountNo: widget.info.accountNumber,
                    bank: S.of(context).bank_kienlong_name,
                  ),
                  Container(height: 8, color: Theme.of(context).dividerColor),
                  SavingInfoWidget(
                    data: [
                      BaseModel(
                          title: S.of(context).saving_label_due_date,
                          right: widget.info.termName),
                      BaseModel(
                          title: S.of(context).saving_label_interest,
                          right:
                              "${widget.info.rate}%/${S.of(context).common_year_short}"),
                      BaseModel(
                        title: S
                            .of(context)
                            .saving_open_success_label_interest_last_term,
                        right: GlobalModel.formatMoney(
                            widget.info.interestAmount?.toDouble(),
                            currencyStr: widget.info.currency),
                      ),
                      BaseModel(
                          title:
                              S.of(context).saving_open_online_title_pay_form,
                          right: widget.info.finalTypeName),
                      BaseModel(
                          title: S.of(context).saving_label_active_date,
                          right: widget.info.contractDate.formatDMY),
                      BaseModel(
                          title: S.of(context).date_due,
                          right: widget.info.dueDate.formatDMY),
                      BaseModel(
                        title: S
                            .of(context)
                            .profile_withdraw_commission_label_receiving_account,
                        right:
                            '${widget.info.finalAccountNumber}\n${(widget.info.finalAccountName ?? "")}',
                      ),
                    ],
                  )
                ],
              ),
            ),
          ),
          Column(
            children: [
              const Divider(height: 1),
              Container(
                padding:
                    const EdgeInsets.only(left: 15.0, right: 15.0, top: 10.0),
                color: Colors.white,
                child: RichText(
                  text: TextSpan(
                    text: S.of(context).saving_confirm_info_des_term1,
                    style: StyleApp.bodyStyle(context),
                    children: [
                      TextSpan(
                          text: S.of(context).profile_sms_setting_des_term_link,
                          style: StyleApp.bodyStyle(context,
                              color: Theme.of(context).colorScheme.secondary),
                          recognizer: TapGestureRecognizer()
                            ..onTap = _getTermsAndConditions),
                      TextSpan(
                        text: S.of(context).ruler_of_my,
                        style: StyleApp.bodyStyle(context),
                      )
                    ],
                  ),
                ),
              ),
              BottomButton(
                title: S.of(context).common_continue,
                onTap: () => _goInputSoftOtp(),
                isDivider: false,
              ),
            ],
          )
        ],
      ),
    );
  }

  _lauchUrl() async {
    const url =
        "https://kienlongbank.com/dieu-khoan-dieu-kien-su-dung-dich-vu-ngan-hang-dien-tu";
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    } else {
      throw 'not found $url';
    }
  }

  _getTermsAndConditions() async {
    final getData = await _savingBloc.getTermsAndConditions();
    if (getData != null) {
      if (getData.content != null && getData.content?.isNotEmpty == true) {
        go(
            context,
            HomeBannerContent(
              modelTerms: getData,
            ));
      } else {
        _lauchUrl();
      }
    } else {
      _lauchUrl();
    }
  }

  _goInputSoftOtp() async {
    if (!passAuthFaceID) {
      await authByFaceID(
        showFaceID: widget.showFaceID,
        transactionNo: widget.info.transactionNo,
      );
    }
    if (!passAuthFaceID || !mounted) return;

    final hasEToken = await _bloc.hasEToken();
    if (hasEToken) {
      final data = await _blocAuth.getetoken();
      final value =
          await FastAuth().checkAuth(context, widget.info.amount ?? 0, data);
      if (value?.isNotEmpty == true) {
        final result =
            await _savingBloc.openSavingAccount(widget.info, otp: value);
        if (result != null) {
          result.termName = widget.info.termName;
          go(
            context,
            SavingOnlineDone(
              info: result,
              sourceAccountNo: widget.info.accountNumber ?? '',
              routeNameStartPage: widget.routeNameStartPage,
            ),
          );
        }
      }
    } else {
      goToSetupToken(context);
    }
  }
}
