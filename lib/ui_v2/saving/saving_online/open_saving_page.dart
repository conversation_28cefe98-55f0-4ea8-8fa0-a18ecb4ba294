import 'package:common/ks_common.dart';
import 'package:common/utils/dialog_util.dart';
import 'package:common/utils/global.dart';
import 'package:common/widgets/bottom_sheet_widget.dart';
import 'package:flutter/material.dart';
import 'package:keyboard_attachable/keyboard_attachable.dart';
import 'package:ksb_bloc/bloc/form/keyboard_bloc.dart';
import 'package:ksb_bloc/bloc/model/global_model.dart';
import 'package:ksb_bloc/bloc/model/saving/online_saving_account_info.dart';
import 'package:ksb_bloc/bloc/model/saving/saving_interest_money.dart';
import 'package:ksb_bloc/bloc/model/saving/saving_period.dart';
import 'package:ksb_bloc/bloc/saving/saving_bloc.dart';
import 'package:ksb_common/model/model.dart';
import 'package:ksb_common/shared/assets.dart' as ksb;
import 'package:ksb_common/shared/theme/custom_theme.dart';
import 'package:ksb_common/shared/widgets/background_app.dart';
import 'package:mobile_banking/di/inject.dart';
import 'package:mobile_banking/generated/l10n.dart';
import 'package:mobile_banking/theme/constans.dart';
import 'package:mobile_banking/ui_v2/saving/saving_online/saving_form_payment_page.dart';
import 'package:mobile_banking/ui_v2/transfer/row_choose_account.dart';
import 'package:mobile_banking/ui_v2/transfer/widgets/common_textfield_money.dart';
import 'package:mobile_banking/ui_v2/transfer/widgets/transfer_confirm_content_widget.dart';
import 'package:mobile_banking/ui_v2/widget/view/keyboard_header_widget.dart';
import 'package:rxdart/rxdart.dart';

class OpenSavingPage extends StatefulWidget {
  final String? routeNameStartPage;

  const OpenSavingPage({Key? key, this.routeNameStartPage}) : super(key: key);

  @override
  State<OpenSavingPage> createState() => _OpenSavingPageState();
}

class _OpenSavingPageState extends State<OpenSavingPage> {
  final _minAmount = 1000000.0;
  final _savingBloc = Injection.injector.get<SavingBloc>();
  final _keyboardBloc = Injection.injector.get<KeyboardBloc>();
  final keyMoney = GlobalKey<CommonTextFieldMoneyState>();
  final NormalSavingAccountInfo info = NormalSavingAccountInfo();

  final _selectedAccount = BehaviorSubject<AccountModel>();

  late TextEditingController _depositsController;
  late FocusNode _moneyFocus;
  double _amount = 0;

  @override
  void initState() {
    super.initState();
    info.amount = _minAmount;
    _amount = _minAmount;
    _depositsController =
        TextEditingController(text: _minAmount.currencyFormat);

    _savingBloc.getSavingPeriods();
    _savingBloc.streamSubs.add(_savingBloc.errorStream.listen((value) {
      if (value?.isNotEmpty == true) {
        DialogUtil.alert(context, value, title: S.of(context).common_title_app);
      }
    }));
    _moneyFocus = FocusNode();

    _savingBloc.streamSubs.add(_savingBloc.savingPeriodStream.listen((event) {
      if ((info.termId == null || info.termId?.isEmpty == true) &&
          (event.isNotEmpty == true)) {
        _onSelectTerm(event.first);
      }
    }));

    _keyboardBloc.streamSubs.add(
      _keyboardBloc.moneyChangeStream.listen((event) {
        if (event != null && event.isNotEmpty) {
          keyMoney.currentState?.setMoneyText(event);
          _amount = Global.convertTextToMoney(event);
          _keyboardBloc.setMoney(Global.convertTextToMoney(event));
          info.amount = _amount;
          setState(() {});
        }
      }),
    );
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _savingBloc.streamSubs.add(_selectedAccount.listen((value) {
        setState(() {});
      }));
    });
  }

  @override
  void dispose() {
    super.dispose();
    _savingBloc.dispose();
    _selectedAccount.close();
    _keyboardBloc.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BackgroundAppBarImage(
      stream: _savingBloc.loadingScreenStream,
      appBar: getAppBarDark(context,
          title: S.of(context).saving_list_save_label_open_save_book),
      image: ksb.ImageAssets.bg_app_bar,
      child: FooterLayout(
        footer: KeyboardHeaderWidget(
          bloc: _keyboardBloc,
        ),
        child: _getBody(),
      ),
    );
  }

  _getBody() {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: kOuterPadding),
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: kOuterPadding),
                    child: Text(
                      S
                          .of(context)
                          .deposit_certificate_open_account_label_send_account,
                      style: StyleApp.subtitle1(context, true),
                    ),
                  ),
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(vertical: kOuterPadding),
                    child: StreamBuilder<AccountModel>(
                      stream: _selectedAccount,
                      builder: (context, snapshot) {
                        return ChooseAccountCard(
                          accountNumberSelected: snapshot.data?.accountNumber,
                          onSelectAccount: (account) {
                            logger.t(account);
                            _selectedAccount.add(account);
                            info.accountNumber = account.accountNumber;
                            info.accountName = account.displayName;
                          },
                        );
                      },
                    ),
                  ),
                  Divider(
                    height: 8,
                    thickness: 8,
                    color: Theme.of(context).dividerColor,
                  ),
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text(
                      S.of(context).saving_open_online_label_save_info,
                      style: StyleApp.subtitle1(context, true),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Column(
                      children: [
                        CommonTextFieldMoneyWidget(
                          key: keyMoney,
                          focusNode: _moneyFocus,
                          maxLength: 18,
                          textController: _depositsController,
                          labelText: S.of(context).amount_saving,
                          hintText: S.of(context).saving_amount_min,
                          textInputType: TextInputType.number,
                          onBlur: (value) {
                            if (Global.convertTextToMoney(value) > 500000.0) {
                              getProfit();
                            }
                          },
                          valueText: _amount > 0 ? _amount.currencyFormat : '',
                          suffixEnabled: true,
                          suffixAlignment: Alignment.bottomRight,
                          suffixPadding: const EdgeInsets.only(bottom: 9),
                          suffix: InkWell(
                            onTap: _amount > 0
                                ? () {
                                    _depositsController.clear();
                                    info.amount = 0;
                                  }
                                : null,
                            child: _amount > 0
                                ? Icon(
                                    Icons.cancel_rounded,
                                    color: StyleApp.captionColor(context),
                                  )
                                : null,
                          ),
                          onChanged: (value) {
                            info.amount = value;
                            setState(() {});
                            _keyboardBloc.setMoney(value);
                            _amount = value;
                          },
                          onInputTap: (value) {
                            _keyboardBloc.setMoney(_amount);
                          },
                        ),
                        const SizedBox(height: 8),
                        StreamBuilder<List<SavingPeriod>>(
                          stream: _savingBloc.savingPeriodStream,
                          builder: (context, snapshot) {
                            return CommonDropdown(
                              title: S
                                  .of(context)
                                  .saving_open_online_label_due_date,
                              value: info.termName,
                              onTap: () {
                                _moneyFocus.unfocus();
                                if (snapshot.data?.isNotEmpty == true &&
                                    info.termId != null) {
                                  _openSelectSendingTerm(
                                    snapshot.data!,
                                    info.termId!,
                                  );
                                }
                              },
                            );
                          },
                        ),
                        const SizedBox(height: 8),
                        StreamBuilder<SavingInterestMoney?>(
                            stream: _savingBloc.savingInterestMoneyStream,
                            builder: (context, snapshot) {
                              return CommonDropdown(
                                title: S.of(context).interest_rate,
                                titleStyle: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontSize: 12,
                                    color: const Color(0xFF333333)
                                        .withOpacity(0.4)),
                                value: "${info.rate}%/năm",
                                leadingIcon: null,
                                backgroundColor: DynamicTheme.of(context)
                                    ?.customColor
                                    .bgTextFieldGrey,
                                borderColor: Colors.transparent,
                              );
                            }),
                        StreamBuilder<SavingInterestMoney?>(
                          stream: _savingBloc.savingInterestMoneyStream,
                          builder: (context, snapshot) {
                            if (!snapshot.hasData) {
                              info.interestAmount = 0.0;
                              info.dueDate = null;
                              return Container();
                            } else {
                              info.interestAmount =
                                  snapshot.data?.interestMoney;
                              info.dueDate = snapshot.data?.dueDate;
                            }
                            return Column(
                              children: [
                                TransferConfirmContentWidget(
                                  title: S
                                      .of(context)
                                      .saving_open_online_label_interest_expect,
                                  spaceLeft: 0.0,
                                  spaceRight: 0.0,
                                  spaceTop: 20,
                                  content:
                                      "${snapshot.data?.interestMoney?.currencyFormat} ${GlobalModel.VND}",
                                ),
                                const Divider(),
                                TransferConfirmContentWidget(
                                  title: S.of(context).date_aturity,
                                  spaceLeft: 0.0,
                                  spaceRight: 0.0,
                                  content:
                                      "${snapshot.data?.dueDate.formatDMY}",
                                ),
                              ],
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          BottomButton(
            title: S.of(context).common_continue,
            isDisable: _isDisableButton(),
            onTap: () async {
              final availableBalance =
                  _selectedAccount.valueOrNull?.availableBalance ?? 0;
              final amount = toDouble(info.amount);
              if (amount > availableBalance) {
                DialogUtil.alert(
                    context, S.of(context).error_account_not_surplus_mount);
              } else if (amount < 500000.0) {
                DialogUtil.alert(
                    context, S.of(context).amount_minium_saving("500.000 VND"),
                    title: S.of(context).common_title_app);
              } else if (info.firstScreenPass) {
                final result = await _savingBloc.getInterestMoney(
                  info.termId ?? '',
                  _amount,
                );
                if (result != null && mounted) {
                  info.interestAmount = result.interestMoney;
                  info.dueDate = result.dueDate;
                  go(
                    context,
                    SavingFormPaymentPage(
                      info: info,
                      routeNameStartPage: widget.routeNameStartPage,
                    ),
                  );
                }
              } else {
                DialogUtil.alert(context, S.of(context).please_complete,
                    title: S.of(context).common_title_app);
              }
            },
          )
        ],
      ),
    );
  }

  _openSelectSendingTerm(List<SavingPeriod> data, String selectedTermId) {
    goPresent(
      context,
      BottomSheetWidget(
          title: S.of(context).saving_open_online_bottomsheet_title_choose_due,
          isIntrinsicHeight: true,
          child: SingleChildScrollView(
            child: Column(
              children: [
                for (var i = 0; i < data.length; i++) ...[
                  termItem(data[i], selectedTermId),
                  i < data.length - 1
                      ? const Divider(indent: 16, height: 1)
                      : const Divider(color: Colors.transparent, height: 1),
                ],
              ],
            ),
          )),
    );
  }

  String getNameTerm(String name, rate) {
    if (name.isNotEmpty == true) {
      return "$name - $rate%/năm";
    }
    return '';
  }

  _onSelectTerm(SavingPeriod item) async {
    info.termId = item.id;
    info.termName = item.name;
    info.rate = item.rate;
    info.contractDate = DateTime.now();
    await getProfit();
    if (mounted) setState(() {});
  }

  Widget termItem(SavingPeriod item, String selectedTermId) {
    final isSelected = item.id == selectedTermId;
    return Container(
      color: isSelected
          ? DynamicTheme.of(context)?.customColor.buttonTransparentColor
          : null,
      child: ListTile(
        title: Text(
          getNameTerm(item.name ?? '', item.rate),
          style: StyleApp.bodyText2(context),
        ),
        selectedTileColor: Theme.of(context).primaryColor,
        trailing: isSelected
            ? Icon(
                Icons.check_rounded,
                color: Theme.of(context).primaryColor,
              )
            : null,
        onTap: () async {
          if (!mounted) return;
          Navigator.of(context).pop();
          _onSelectTerm(item);
        },
      ),
    );
  }

  getProfit() async {
    await _savingBloc.getInterestMoney(
      info.termId ?? '',
      _amount,
    );
  }

  _isDisableButton() {
    return _selectedAccount.valueOrNull == null ||
        info.amount == null ||
        info.amount == 0 ||
        info.termId == null ||
        info.termId!.isEmpty;
  }
}
