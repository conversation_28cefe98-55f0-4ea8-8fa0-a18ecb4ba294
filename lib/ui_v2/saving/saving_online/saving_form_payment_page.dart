import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_common/shared/assets.dart' as ksb;
import 'package:common/navigator.dart';
import 'package:common/widgets/bottom_button.dart';
import 'package:common/widgets/style_app.dart';
import 'package:flutter/material.dart';
import 'package:ksb_common/shared/widgets/auth_transaction_mixin_common.dart';
import 'package:mobile_banking/di/inject.dart';
import 'package:mobile_banking/generated/l10n.dart';
import 'package:mobile_banking/ui_v2/widget/loading/loading_area.dart';
import 'package:mobile_banking/ui_v2/saving/saving_online/saving_online_confirm_page.dart';
import 'package:mobile_banking/ui_v2/saving/saving_online/widgets/radio_item.dart';
import 'package:mobile_banking/ui_v2/transfer/row_choose_account.dart';
import 'package:ksb_common/shared/widgets/background_app.dart';
import 'package:mobile_banking/ui_v2/widget/loading/loading_view.dart';
import 'package:rxdart/rxdart.dart';

class SavingFormPaymentPage extends StatefulWidget {
  final NormalSavingAccountInfo? info;
  final String? routeNameStartPage;

  const SavingFormPaymentPage({
    Key? key,
    this.info,
    this.routeNameStartPage,
  }) : super(key: key);

  @override
  State<SavingFormPaymentPage> createState() => _SavingFormPaymentPageState();
}

class _SavingFormPaymentPageState
    extends FlowTransactionState<SavingFormPaymentPage>
    with AuthTransactionMixin<SavingFormPaymentPage> {
  final _savingBloc = Injection.injector.get<SavingBloc>();
  List<SavingInterestReceive> receives = [];
  String accountNumber = '';
  bool isSelected = false;
  int indexItem = 2;
  final _selectedAccount = BehaviorSubject<AccountModel>();

  @override
  void initState() {
    super.initState();
    _savingBloc.getInterestReceives();

    _savingBloc.streamSubs.add(
      _savingBloc.statusStream.listen((event) {
        if (event != null) {
          listenError(event.convertModelToCommon());
        }
      }),
    );

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _savingBloc.streamSubs.add(_selectedAccount.listen((value) {
        setState(() {});
      }));
    });
  }

  @override
  void dispose() {
    super.dispose();
    _selectedAccount.close();
    _savingBloc.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BackgroundAppBarImage(
      appBar: getAppBarDark(context,
          title: S.of(context).saving_list_save_label_open_save_book),
      child: _getBody(),
      image: ksb.ImageAssets.bg_app_bar,
    );
  }

  bool _isDisableButton() {
    return (_selectedAccount.valueOrNull == null) || (widget.info == null);
  }

  _getBody() {
    return LoadingView(
      loadingStream: _savingBloc.progressVisible,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16.0,
                    vertical: 16.0,
                  ),
                  child: Text(
                    S.of(context).saving_open_online_title_pay_form,
                    style: StyleApp.subtitle1(context, true),
                  ),
                ),
                LoadingArea<List<SavingInterestReceive>>(
                  stream: _savingBloc.savingInterestReceiveStream,
                  loading: null,
                  onSuccess: (data) {
                    receives = data;
                    return ListView.separated(
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      primary: false,
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemBuilder: (BuildContext context, int index) {
                        SavingInterestReceive currentItem;
                        currentItem = data.firstWhere((element) =>
                            element.name == data[indexItem].name);
                        widget.info?.finalTypeId = currentItem.id;
                        widget.info?.finalTypeName = currentItem.name;
                        return RadioItem(
                          title: data[index].name,
                          value: index,
                          groupValue: indexItem,
                          onChanged: (value) {
                            if (value is int) {
                              indexItem = value;
                              currentItem = data[value];
                              widget.info?.finalTypeId = currentItem.id;
                              widget.info?.finalTypeName = currentItem.name;
                            }

                            setState(() {});
                          },
                        );
                      },
                      separatorBuilder: (BuildContext context, int index) {
                        return const Divider(indent: 8);
                      },
                      itemCount: data.length,
                    );
                  },
                ),
                Container(
                  margin: const EdgeInsets.only(top: 10),
                  height: 10,
                  color: Theme.of(context).dividerColor,
                ),
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    S
                        .of(context)
                        .profile_withdraw_commission_label_receiving_account,
                    style: StyleApp.subtitle1(context, true),
                  ),
                ),
                StreamBuilder<AccountModel>(
                  stream: _selectedAccount,
                  builder: (context, snapshot) {
                    return ChooseAccountCard(
                      accountNumberSelected: snapshot.data?.accountNumber,
                      onSelectAccount: (account) {
                        _selectedAccount.add(account);
                        widget.info?.finalAccountNumber = account.accountNumber;
                        widget.info?.finalAccountName = account.displayName;
                      },
                    );
                  },
                ),
              ],
            ),
          ),
          BottomButton(
            title: S.of(context).common_continue,
            isDisable: _isDisableButton(),
            onTap: () async {
              if (widget.info != null) {
                final NormalSavingAccountInfo? result =
                    await _savingBloc.reviewOpenSavingAccount(widget.info);
                if (result != null && mounted) {
                  result.interestAmount = widget.info?.interestAmount;
                  result.accountName = widget.info?.accountName;
                  result.finalAccountName = widget.info?.finalAccountName ?? '';
                  onGoToConfirm(
                    nextToConfirmPage: () {
                      go(
                        context,
                        SavingOnlineConfirmPage(
                          info: result,
                          routeNameStartPage: widget.routeNameStartPage,
                          showFaceID: _savingBloc.showFaceID,
                        ),
                      );
                    },
                    nextStep: _savingBloc.step,
                    message: _savingBloc.messageDialog,
                  );
                }
              }
            },
          )
        ],
      ),
    );
  }
}
