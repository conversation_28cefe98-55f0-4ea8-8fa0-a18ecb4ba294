import 'package:common/model/loading_event.dart';
import 'package:common/utils/dialog_util.dart';
import 'package:common/widgets/bottom_button.dart';
import 'package:flutter/material.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_bloc/bloc/model/global_model.dart';
import 'package:ksb_common/shared/assets.dart' as ksb;
import 'package:ksb_common/shared/widgets/auth_transaction_mixin_common.dart';
import 'package:ksb_common/shared/widgets/background_app.dart';
import 'package:mobile_banking/assets.dart';
import 'package:mobile_banking/assets/images.dart';
import 'package:mobile_banking/di/inject.dart';
import 'package:mobile_banking/generated/l10n.dart';
import 'package:mobile_banking/model/base_model.dart';
import 'package:mobile_banking/ui_v2/profile_account/etoken/etoken_setup.dart';
import 'package:mobile_banking/ui_v2/saving/saving_online/saving_settlement_done.dart';
import 'package:mobile_banking/ui_v2/saving/saving_online/widgets/saving_info_widget.dart';
import 'package:mobile_banking/ui_v2/transfer/row_choose_account.dart';
import 'package:mobile_banking/ui_v2/widget/loading/loading_view.dart';
import 'package:mobile_banking/utils/auth_fatsh.dart';
import 'package:mobile_banking/utils/navigator.dart';
import 'package:rxdart/rxdart.dart';

import 'info_receive_money_vnpost_page.dart';

class SavingSettlementPage extends StatefulWidget {
  final NormalSavingAccountInfo? info;
  final bool? blocked;
  final bool? isVnPost;
  final String? routeNameStartPage;
  final bool showFaceID;

  const SavingSettlementPage({
    Key? key,
    this.info,
    this.blocked,
    this.isVnPost = false,
    this.routeNameStartPage,
    this.showFaceID = false,
  }) : super(key: key);

  @override
  State<SavingSettlementPage> createState() => _ScreenState();
}

class _ProfileAccounts {
  final ProfileInfo? info;
  final List<AccountModel?>? accounts;

  _ProfileAccounts(this.info, this.accounts);
}

class _ScreenState extends FlowTransactionState<SavingSettlementPage>
    with AuthTransactionConfirmMixin<SavingSettlementPage> {
  // NormalSavingAccountInfo? info;
  final _selectedAccount = BehaviorSubject<AccountModel>();
  final _savingBloc = Injection.injector.get<SavingBloc>();
  SavingAccountCertificate? _certificate;
  final _profileBloc = Injection.injector.get<ProfileBloc>();
  final _blocAuth = Injection.injector.get<AuthenTransactionBloc>();
  final accList = BehaviorSubject<List<AccountModel?>>();
  late Stream<_ProfileAccounts> profileStream;

  @override
  void initState() {
    _profileBloc.getProfileInfo();
    _selectedAccount.add(AccountModel(
      accountNumber: widget.info?.finalAccountNumber,
      accountName: widget.info?.finalAccountName,
    ));
    _savingBloc.errorStream.listen((error) {
      if (!error.isNullOrEmpty) DialogUtil.alert(context, error);
    });
    _savingBloc.getCertificate(widget.info?.accountNumber ?? '');
    _savingBloc.savingAccountCertificateStream.listen((event) {
      if (event.status == LoadingStatus.success) {
        _certificate = event.data;
      }
    });
    profileStream = Rx.combineLatest2(
      _profileBloc.profileStream,
      accList.stream,
      (a, b) => _ProfileAccounts(a, b),
    );
    super.initState();
  }

  @override
  void dispose() {
    _savingBloc.dispose();
    _selectedAccount.close();
    _blocAuth.dispose();
    accList.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LoadingView(
      loadingStream: _savingBloc.progressVisible,
      child: BackgroundAppBarImage(
        appBar: _getAppBar(),
        child: _getBody(),
        image: ksb.ImageAssets.bg_app_bar,
      ),
    );
  }

  _getAppBar() {
    return getAppBarDark(
      context,
      title: S.of(context).saving_confirm_info_title_confirm,
    );
  }

  _getBody() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.white,
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  if (widget.isVnPost == false)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16.0, vertical: 16),
                          child: Text(
                            S
                                .of(context)
                                .profile_withdraw_commission_label_receiving_account,
                            style: StyleApp.subtitle1(context, true),
                          ),
                        ),
                        StreamBuilder<AccountModel>(
                          stream: _selectedAccount,
                          builder: (context, snapshot) {
                            return ChooseAccountCard(
                              accountNumberSelected:
                                  snapshot.data?.accountNumber,
                              onSelectAccount: _selectedAccount.add,
                              onGetAccounts: (res) {
                                accList.add(res);
                              },
                            );
                          },
                        ),
                      ],
                    ),
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16.0,
                      vertical: 16,
                    ),
                    child: Text(
                      S.of(context).saving_detail,
                      style: StyleApp.subtitle1(context, true),
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Theme.of(context).dividerColor),
                      borderRadius: BorderRadius.circular(4.0),
                    ),
                    margin: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Row(
                      children: [
                        ImageAssets.svgAssets(
                          ImagesResource.ic_pig_coin,
                          width: 76.0,
                          height: 76.0,
                        ),
                        const SizedBox(width: 8.0),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.info?.accountName ?? '',
                              style: StyleApp.subtitle1(context, true),
                            ),
                            const SizedBox(height: 5),
                            Text(
                              widget.info?.accountNumber ?? '',
                              style: StyleApp.bodyStyle(context),
                            ),
                          ],
                        )
                      ],
                    ),
                  ),
                  const SizedBox(height: 10),
                  _buildItems(),
                  const SizedBox(height: 30)
                ],
              ),
            ),
          ),
          StreamBuilder<_ProfileAccounts>(
            stream: profileStream,
            builder: (context, snapshot) {
              final data = snapshot.data;
              return BottomButton(
                isDisable:
                    !(data?.accounts?.isNotEmpty == true && data?.info != null),
                title: widget.isVnPost == true
                    ? S.of(context).next
                    : S.of(context).saving_withdraw_confirm_button_settle,
                onTap: () {
                  if (widget.isVnPost == true) {
                    if (snapshot.hasData && widget.info != null) {
                      go(
                        context,
                        InfoReceiveMoneyVNPost(
                          info: widget.info!,
                          profileInfo: data?.info,
                        ),
                      );
                    }
                  } else {
                    _goInputSoftOtp();
                  }
                },
              );
            },
          )
        ],
      ),
    );
  }

  _buildItems() {
    return SavingInfoWidget(
      data: [
        BaseModel(
          title: S.of(context).saving_withdraw_confirm_label_send_money,
          right: GlobalModel.formatMoney(
            widget.info?.balance?.toDouble(),
            currencyStr: widget.info?.currency,
          ),
        ),
        BaseModel(
          title:
              S.of(context).saving_withdraw_confirm_label_interest_before_due,
          right: GlobalModel.formatMoney(
            widget.info?.interestAmount?.toDouble(),
            currencyStr: widget.info?.currency,
          ),
        ),
        BaseModel(
          title: S.of(context).saving_withdraw_confirm_label_receive_total,
          right: GlobalModel.formatMoney(
            widget.info?.finalAmount?.toDouble(),
            currencyStr: widget.info?.currency,
          ),
        ),
        BaseModel(
          title: S.of(context).saving_label_active_date,
          right: widget.info?.contractDate.formatDMY,
        ),
        BaseModel(
          title: S.of(context).saving_settle_success_label_date,
          right: widget.info?.transactionDate.formatDMY,
        ),
        BaseModel(
          title: S.of(context).blockade_status,
          right: widget.blocked == true
              ? S.of(context).dialog_confirm_yes
              : S.of(context).dialog_confirm_no,
          rightColor: widget.blocked == true ? Colors.red : Colors.green,
        ),
      ],
    );
  }

  _goInputSoftOtp() async {
    if (!passAuthFaceID) {
      await authByFaceID(
        showFaceID: widget.showFaceID,
        transactionNo: widget.info?.transactionNo,
      );
    }
    if (!passAuthFaceID) return;

    final hasEToken = await _savingBloc.hasEToken();
    final data = await _blocAuth.getetoken();
    if (hasEToken && mounted) {
      final value = await FastAuth()
          .checkAuth(context, widget.info?.finalAmount ?? 0, data);
      if (value?.isNotEmpty == true && widget.info != null) {
        final selectedAccount = _selectedAccount.valueOrNull;
        widget.info?.finalAccountName = selectedAccount?.displayName;
        widget.info?.finalAccountNumber = selectedAccount?.accountNumber;
        final NormalSavingAccountInfo? result =
            await _savingBloc.closeSavingAccount(widget.info!, otp: value);
        if (result != null && _certificate != null && mounted) {
          go(
            context,
            SavingSettlementDone(
              info: widget.info!,
              certificate: _certificate!,
              routeNameStartPage: widget.routeNameStartPage,
            ),
          );
        }
      }
    } else if (mounted) {
      goToSetupToken(context);
    }
  }
}
