import 'package:common/navigator.dart';
import 'package:common/utils/dialog_util.dart';
import 'package:common/widgets/bottom_2_button.dart';
import 'package:common/widgets/common_size.dart';
import 'package:common/widgets/empty_widget.dart';
import 'package:common/widgets/ink_well_button.dart';
import 'package:common/widgets/style_app.dart';
import 'package:flutter/material.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_bloc/bloc/model/global_model.dart';
import 'package:ksb_common/shared/assets.dart' as klbCommon;
import 'package:ksb_common/shared/theme/custom_theme.dart';
import 'package:ksb_common/shared/widgets/auth_transaction_mixin_common.dart';
import 'package:ksb_common/shared/widgets/background_app.dart';
import 'package:mobile_banking/assets/assets.dart';
import 'package:mobile_banking/assets/images.dart';
import 'package:mobile_banking/di/inject.dart';
import 'package:mobile_banking/generated/l10n.dart';
import 'package:mobile_banking/ui_v2/account/account_source_widget.dart';
import 'package:mobile_banking/ui_v2/profile_account/etoken/etoken_setup.dart';
import 'package:mobile_banking/ui_v2/saving/target_accumulation/edit_info_target_accumulation.dart';
import 'package:mobile_banking/ui_v2/saving/target_accumulation/target_settlement_page.dart';
import 'package:mobile_banking/ui_v2/saving/target_accumulation/widget/target_accumulation_bottom_sheet.dart';
import 'package:mobile_banking/ui_v2/transfer/widgets/transfer_confirm_content_widget.dart';
import 'package:mobile_banking/ui_v2/widget/loading/loading_area.dart';
import 'package:mobile_banking/ui_v2/widget/loading/loading_view.dart';
import 'package:mobile_banking/utils/auth_fatsh.dart';
import 'package:mobile_banking/utils/otp_utils.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';

class TargetAccumulationDetail extends StatefulWidget {
  final String accountNo;
  final TargetSavingAccountInfo? info;
  final VoidCallback? onBack;
  final String? routeNameStartPage;

  const TargetAccumulationDetail({
    Key? key,
    required this.accountNo,
    this.info,
    this.onBack,
    this.routeNameStartPage,
  }) : super(key: key);

  @override
  State<TargetAccumulationDetail> createState() =>
      _TargetAccumulationDetailState();
}

class _TargetAccumulationDetailState
    extends FlowTransactionState<TargetAccumulationDetail>
    with
        AuthTransactionMixin<TargetAccumulationDetail>,
        AuthTransactionConfirmMixin<TargetAccumulationDetail> {
  final _targetSavingBloc = Injection.injector.get<TargetSavingBloc>();
  final _bloc = Injection.injector.get<TransferBloc>();
  final _blocAuth = Injection.injector.get<AuthenTransactionBloc>();
  bool isFastAuth = false;

  @override
  void initState() {
    _targetSavingBloc.streamSubs.add(
      _targetSavingBloc.statusStream.listen((event) {
        if (event != null) {
          listenError(event.convertModelToCommon());
        }
      }),
    );
    _targetSavingBloc.getTargetSavingAccountDetail(widget.accountNo);

    _blocAuth.streamSubs.add(_blocAuth.isFastAuthStream.listen((event) {
      if (event) {
        setState(() {
          isFastAuth = event;
        });
      }
    }));

    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
    _targetSavingBloc.dispose();
    _blocAuth.dispose();
    _bloc.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BackgroundAppBarImage(child: _buildDetailWithLoadingView());
  }

  _buildDetailWithLoadingView() {
    return LoadingView(
      loadingStream: _targetSavingBloc.progressVisible,
      child: LoadingArea<TargetSavingAccountInfo>(
        stream: _targetSavingBloc.accountDetailStream,
        onTryClick: () =>
            _targetSavingBloc.getTargetSavingAccountDetail(widget.accountNo),
        loading: null,
        onError: (msg) => BackgroundAppBarImage(
          appBar: AppBarCustom(
            title: S.of(context).detail_goals,
            backPage: () {
              if (widget.onBack != null) {
                widget.onBack!();
              }
            },
          ),
          child: Center(
            child: EmptyWidget(
              icon: ImageAssets.svgAssets(
                  ImagesResource.ic_empty_card_transaction),
              message: msg,
              titleButton: S.of(context).back_page,
              onPressedAction: () => Navigator.pop(context),
            ),
          ),
        ),
        onSuccess: (data) {
          return BackgroundAppBarImage(
            appBar: _getAppBar(data),
            image: ImageAssets.svgAssets(
              klbCommon.ImageAssets.img_appbar,
              fit: BoxFit.fill,
              width: MediaQuery.of(context).size.width,
            ),
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: toSp(200.0),
                          width: double.infinity,
                          child: ImageAssets.headImage(
                            imageUrl: data.imageUrl ??
                                ImagesResource.bg_target_saving_update,
                            isDetail: true,
                          ),
                        ),
                        const SizedBox(height: 20),
                        BankAccountWidget(
                          accountNumber: data.accountNo,
                          accountName: data.accountName,
                          aliasName: data.customerName,
                          accountingBalance: toDouble(data.currentAmount),
                          availableBalances: toDouble(data.availableBalance),
                          currency: data.currency,
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16.0, vertical: 16.0),
                          child: InkWellButton(
                            title: S.of(context).common_withdraw,
                            textColor: getTheme(context, true).primaryColor,
                            buttonColor: const Color(0xFFF4F4F7),
                            onTap: () => _withdrawMoney(data),
                          ),
                        ),
                        Container(
                          height: 10,
                          color: Theme.of(context).dividerColor,
                        ),
                        ..._getContent(data),
                        const SizedBox(height: 10),
                        _buildSavingProgress(data),
                        const SizedBox(height: 30),
                      ],
                    ),
                  ),
                ),
                Bottom2Button(
                  title1: S.of(context).saving_withdraw_confirm_button_settle,
                  title2: S.of(context).saving_add_money_title_add,
                  colorButton1:
                      Theme.of(context).primaryColor.withOpacity(0.05),
                  colorTextButton1: Theme.of(context).primaryColor,
                  onTapTitle1: () => _completeSaving(data),
                  onTapTitle2: () => _depositMoney(data),
                )
              ],
            ),
          );
        },
      ),
    );
  }

  _getAppBar(TargetSavingAccountInfo data) {
    return getAppBarDark(
      context,
      title: S.of(context).detail_goals,
      actions: [
        InkWell(
          onTap: () => go(
            context,
            EditInfoTargetAccumulation(
              info: data.copyWith(),
              onReloadData: () {
                Navigator.of(context).pop();
                _targetSavingBloc
                    .getTargetSavingAccountDetail(widget.accountNo);
              },
            ),
          ),
          child: Center(
            child: Padding(
              padding: const EdgeInsets.only(right: 16.0),
              child: Text(
                S.of(context).common_fix,
                style: StyleApp.bodyStyle(context, color: Colors.white),
              ),
            ),
          ),
        )
      ],
    );
  }

  Widget _buildSavingProgress(TargetSavingAccountInfo data) {
    double percent = toDouble(data.amountRateCompletion);
    final radius = MediaQuery.of(context).size.width / 4;
    final primaryColor = Theme.of(context).primaryColor;
    return Center(
      child: CircularPercentIndicator(
        radius: radius + toSp(25),
        lineWidth: toSp(20.0),
        animation: true,
        percent: percent > 1 ? 1 : percent,
        center: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              S.of(context).saving_target_label_pass,
              style: StyleApp.subtitle1(context)?.copyWith(color: primaryColor),
            ),
            Padding(
              padding: const EdgeInsets.only(top: 5),
              child: Text(
                percent.dividedPercentFormat,
                style: StyleApp.headline4(context, FontWeight.w500)
                    ?.copyWith(color: primaryColor),
              ),
            )
          ],
        ),
        circularStrokeCap: CircularStrokeCap.round,
        progressColor: primaryColor,
        backgroundColor: primaryColor.withOpacity(0.1),
      ),
    );
  }

  _getDivider([double? height]) {
    return Divider(
      height: height,
      indent: 15,
      endIndent: 15,
    );
  }

  List<Widget> _getContent(TargetSavingAccountInfo list) {
    return [
      TransferConfirmContentWidget(
        spaceTop: 10,
        title: S.of(context).saving_target_label_name,
        content: list.accountName,
      ),
      _getDivider(),
      TransferConfirmContentWidget(
        spaceTop: 10,
        title: S.of(context).saving_target_label_amount,
        content:
            "${list.targetAmount?.currencyFormatNotDecimal} ${GlobalModel.VND}",
      ),
      _getDivider(),
      TransferConfirmContentWidget(
        spaceTop: 10,
        title: S.of(context).saving_target_label_date,
        content: list.contractDate.formatDMY,
      ),
      _getDivider(),
      TransferConfirmContentWidget(
        spaceTop: 10,
        title: S.of(context).saving_target_label_automatic,
        content: list.isAddFromTransaction == true
            // ? list.coefficiency.currencyFormat + MoneyWidget.unitDefault
            ? S.of(context).common_yes
            : S.of(context).common_no,
      ),
    ];
  }

  _withdrawMoney(TargetSavingAccountInfo data) {
    goPresent(
      context,
      TargetAccumulationBottomSheet(
        bloc: _targetSavingBloc,
        title: S.of(context).common_withdraw,
        accTypeTitle: S.of(context).account_get_money,
        onSubmit: (sourceAccount, amount) async {
          if (amount == 0) {
            DialogUtil.alert(context, S.of(context).please_input_money);
            return;
          }

          /// reset lại thuộc tính _passAuthFaceID về false.
          /// do sau mỗi lần xác thực tại 1 màn hình thì giá trị này sẽ nhân giá trị true,
          /// khi thực hiện xác thực lãi tại chính màn hình đó 1 lần nữa thì sẽ không show xác thực khuôn mặt, pass luôn. => lỗi
          setPassAuthFaceID(false);

          _targetSavingBloc.setAmountAndTransactionType(
            amount: amount,
            transactionType: TransactionCategoriesType.SAVINGS,
          );

          final result = await _targetSavingBloc.reviewTransaction();

          if (result == true) {
            onGoToConfirm(
              nextToConfirmPage: () => nextToConfirmWithdrawPage(
                amount: amount,
                sourceAccountNo: sourceAccount.accountNumber,
                targetAccountNo: data.accountNo,
              ),
              nextStep: _targetSavingBloc.step,
              message: _targetSavingBloc.messageDialog,
            );
          }
        },
      ),
    );
  }

  void nextToConfirmWithdrawPage({
    required double amount,
    String? sourceAccountNo,
    String? targetAccountNo,
  }) async {
    if (!passAuthFaceID) {
      await authByFaceID(
        showFaceID: _targetSavingBloc.showFaceID,
        transactionNo: _targetSavingBloc.transactionNumber,
      );
    }
    if (!passAuthFaceID) return;

    var value = await OtpUtils.goInputSoftOtp(context);
    if (value != null) {
      if (!mounted) return;
      Navigator.of(context).pop();
      var result = await _targetSavingBloc.transferMoney(
        isDeposit: false,
        amount: amount,
        sourceAccountNo: sourceAccountNo,
        targetAccountNo: targetAccountNo,
        transactionNo: _targetSavingBloc.transactionNumber,
        otp: value,
      );
      if (result != null) {
        if (!mounted) return;
        DialogUtil.showFlushBar(context, S.of(context).withdrawal_success);
        _targetSavingBloc.getTargetSavingAccountDetail(widget.accountNo);
      }
    }
  }

  _depositMoney(TargetSavingAccountInfo data) {
    return goPresent(
      context,
      TargetAccumulationBottomSheet(
        bloc: _targetSavingBloc,
        title: S.of(context).saving_add_money_title_add,
        accTypeTitle: S.of(context).deducting_account,
        onSubmit: (sourceAccount, amount) async {
          if (amount == 0) {
            DialogUtil.alert(context, S.of(context).please_input_money);
            return;
          }

          if (toDouble(sourceAccount.availableBalance) < amount) {
            DialogUtil.alert(
                context, S.of(context).error_account_not_surplus_mount);
            return;
          }

          /// reset lại thuộc tính _passAuthFaceID về false.
          /// do sau mỗi lần xác thực tại 1 màn hình thì giá trị này sẽ nhân giá trị true,
          /// khi thực hiện xác thực lãi tại chính màn hình đó 1 lần nữa thì sẽ không show xác thực khuôn mặt, pass luôn. => lỗi
          setPassAuthFaceID(false);

          _targetSavingBloc.setAmountAndTransactionType(
            amount: amount,
            transactionType: TransactionCategoriesType.SAVINGS,
          );

          final result = await _targetSavingBloc.reviewTransaction();

          if (result == true) {
            onGoToConfirm(
              nextToConfirmPage: () => _goInputSoftOtp(
                amount: amount,
                sourceAccount: sourceAccount,
                accountNo: data.accountNo,
              ),
              nextStep: _targetSavingBloc.step,
              message: _targetSavingBloc.messageDialog,
            );
          }
        },
      ),
    );
  }

  _completeSaving(TargetSavingAccountInfo data) async {
    final result = await _targetSavingBloc.reviewCloseTargetSavingAccount(
        data.accountNo ?? '', data.sourceAccountNo ?? '');
    bool? isConfirm;
    if (result != null) {
      if (!mounted) return;
      await DialogUtil.confirm(
          context,
          Text(
            S.of(context).settlement_target_accumulation_confirm(
                result.availableBalance?.currencyFormatNotDecimal ?? '',
                result.currency ?? ''),
            style: StyleApp.bodyStyle(context),
          ),
          title: S.of(context).pre_payment,
          cancelText: S.of(context).common_no,
          onCancel: () => isConfirm = false,
          submitText: S.of(context).common_yes,
          onSubmit: () => isConfirm = true);
      if (isConfirm ?? false) {
        if (!mounted) return;
        go(
          context,
          TargetSettlementPage(
            info: result,
            isShowFaceId: _targetSavingBloc.showFaceID,
            routeNameStartPage: widget.routeNameStartPage,
            transactionNo: _targetSavingBloc.transactionNumber,
          ),
        );
      }
    }
  }

  _goInputSoftOtp({
    AccountModel? sourceAccount,
    double? amount,
    String? accountNo,
  }) async {
    if (!passAuthFaceID) {
      await authByFaceID(
        showFaceID: _targetSavingBloc.showFaceID,
        transactionNo: _targetSavingBloc.transactionNumber,
      );
    }
    if (!passAuthFaceID) return;

    final hasEToken = await _bloc.hasEToken();
    if (hasEToken) {
      final data = await _blocAuth.getetoken();
      if (!mounted) return;
      Navigator.of(context).pop();
      final value = await FastAuth().checkAuth(context, amount ?? 0, data);
      if (value?.isNotEmpty == true) {
        if (!mounted) return;
        var result = await _targetSavingBloc.transferMoney(
          amount: amount,
          sourceAccountNo: sourceAccount?.accountNumber ?? '',
          targetAccountNo: accountNo,
          transactionNo: _targetSavingBloc.transactionNumber,
          otp: value,
        );
        if (result != null) {
          if (!mounted) return;
          DialogUtil.showFlushBar(
              context, S.of(context).deposit_to_target_saving_success);
          await _targetSavingBloc
              .getTargetSavingAccountDetail(widget.accountNo);
        }
      }
    } else {
      if (!mounted) return;
      goToSetupToken(context);
    }
  }
}
