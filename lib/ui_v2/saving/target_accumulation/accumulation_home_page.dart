import 'package:common/utils/dialog_util.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:common/model/loading_event.dart';
import 'package:common/widgets/bottom_button.dart';
import 'package:common/widgets/loading/loading.dart';
import 'package:common/widgets/style_app.dart';
import 'package:flutter/material.dart';
import 'package:ksb_bloc/bloc/model/global_model.dart';
import 'package:ksb_common/shared/constant.dart';
import 'package:ksb_common/shared/theme/custom_theme.dart';
import 'package:mobile_banking/assets/assets.dart';
import 'package:mobile_banking/assets/images.dart';
import 'package:mobile_banking/di/inject.dart';
import 'package:mobile_banking/generated/l10n.dart';
import 'package:mobile_banking/ui_v2/saving/savings_target.dart';
import 'package:ksb_common/shared/widgets/background_app.dart';
import 'package:mobile_banking/utils/navigator.dart';
import 'package:ksb_common/shared/assets.dart' as ksb;

import 'target_accumulation_page.dart';

class AccumulationHomePage extends StatefulWidget {
  final String? title;
  final bool? isCreateNew;

  const AccumulationHomePage({
    Key? key,
    this.title,
    this.isCreateNew,
  }) : super(key: key);

  @override
  State<AccumulationHomePage> createState() => _AccumulationHomePageState();
}

class _AccumulationHomePageState extends State<AccumulationHomePage> {
  final _bloc = Injection.injector.get<SavingBloc>();
  final _env = Injection.injector.get<Environment>();

  @override
  void initState() {
    super.initState();
    _bloc.getSavingAccounts();

    _env.appEventStream.listen((event) {
      if (event == AppEvent.SAVING_ACCOUNT_CHANGE) {
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) {
            _bloc.getSavingAccounts();
          }
        });
      }
    });

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      if (widget.isCreateNew == true) {
        openNewSaving();
      }
    });

    _bloc.streamSubs.add(_bloc.errorStream.listen((event) {
      if (mounted) {
        DialogUtil.alert(context, event);
      }
    }));
  }

  @override
  Widget build(BuildContext context) {
    return BackgroundAppBarImage(
      appBar: _getAppbar(),
      child: _listSaving(),
      image: ImageAssets.svgAssets(
        ksb.ImageAssets.img_appbar,
        fit: BoxFit.fill,
        width: MediaQuery.of(context).size.width,
      ),
    );
  }

  _getAppbar() {
    return getAppBarDark(
      context,
      title: widget.title ?? S.of(context).saving_home_title_saving,
    );
  }

  _listSaving() {
    return StreamBuilder<LoadingWidgetModel>(
      stream: _bloc.savingAccountStream,
      builder: (context, snapshot) {
        final data = snapshot.data;
        if (data == null) return ring;
        final accounts = data.data;
        return Column(
          children: [
            Expanded(
              child: (accounts is SavingAccounts)
                  ? Column(
                      children: [
                        isEmpty(accounts)
                            ? const SizedBox()
                            : totalAmount(accounts),
                        Expanded(
                          child: RefreshIndicator(
                            onRefresh: () async {
                              _bloc.getSavingAccounts();
                            },
                            child: getListsItems(accounts),
                          ),
                        ),
                      ],
                    )
                  : _emptyWidget(),
            ),
            getBottomButton(),
          ],
        );
      },
    );
  }

  Widget totalAmount(SavingAccounts? accounts) {
    return Container(
      padding: kPaddingStandard,
      alignment: Alignment.centerLeft,
      color: DynamicTheme.of(context)?.customColor.buttonTransparentColor,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            S.of(context).total_amount,
            style: Theme.of(context)
                .textTheme
                .bodyLarge
                ?.copyWith(fontWeight: FontWeight.w400),
          ),
          const SizedBox(height: 10),
          Text(
            (accounts?.totalCurrentAmountTargetSaving ?? 0).currencyFormat +
                GlobalModel.VND,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w500,
                  fontSize: 30,
                ),
          ),
          const SizedBox(height: 10),
          RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: '${S.of(context).total_accumulated_interest}: ',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.w400,
                        color: const Color(0xFF333333).withOpacity(0.8),
                      ),
                ),
                TextSpan(
                  text: (accounts?.totalAnticipatedInterestTargetSaving ?? 0)
                          .currencyFormat +
                      GlobalModel.VND,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.w500,
                        color: const Color(0xFF333333),
                      ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget getListsItems(SavingAccounts? accounts) {
    if (accounts == null || isEmpty(accounts)) return _emptyWidget();
    return SavingsTargetWidget(
      accounts: accounts.targetSavingAccounts ?? [],
      onReload: () => _bloc.getSavingAccounts(),
      padding: EdgeInsets.only(top: kSmallPadding),
    );
  }

  bool isEmpty(SavingAccounts? accounts) {
    if (accounts == null) return true;
    if ((accounts.targetSavingAccounts?.length ?? 0) <= 0) {
      return true;
    }
    return false;
  }

  Widget getBottomButton() {
    return BottomButton(
      title: S.of(context).common_open_new,
      onTap: openNewSaving,
    );
  }

  openNewSaving() {
    go(context, const TargetAccumulationPage());
  }

  Widget _emptyWidget() {
    return Center(
      child: SizedBox(
        width: MediaQuery.of(context).size.width,
        child: SingleChildScrollView(
          padding: kPaddingStandard,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ImageAssets.svgAssets(ImagesResource.ic_accumulation_empty),
              SizedBox(height: kOuterPadding),
              Text(
                S.of(context).saving_accumulation_target,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Theme.of(context).primaryColor,
                    fontWeight: FontWeight.w700),
              ),
              SizedBox(height: kTinyPadding),
              Text(
                S.of(context).accumulation_target_empty_des,
                textAlign: TextAlign.center,
                style: StyleApp.bodyStyle(context).copyWith(
                  height: 20 / 14,
                  color: DynamicTheme.of(context)
                      ?.customColor
                      .black
                      ?.withOpacity(0.8),
                ),
              ),
              SizedBox(height: kPadding24),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: _item(
                      S.of(context).accumulation_target_intro_1,
                      ImagesResource.ic_accum_empty_1,
                    ),
                  ),
                  Expanded(
                    child: _item(
                      S.of(context).accumulation_target_intro_2,
                      ImagesResource.ic_accum_empty_2,
                    ),
                  ),
                  Expanded(
                    child: _item(
                      S.of(context).accumulation_target_intro_3,
                      ImagesResource.ic_accum_empty_3,
                    ),
                  ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _item(String label, String icon) {
    return Column(
      children: [
        ImageAssets.svgAssets(icon, height: 36, width: 36),
        SizedBox(height: kOuterPadding),
        Text(
          label,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: DynamicTheme.of(context)?.customColor.black,
                fontWeight: FontWeight.w500,
              ),
        )
      ],
    );
  }
}
