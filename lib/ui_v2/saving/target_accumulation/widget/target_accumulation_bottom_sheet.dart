import 'package:common/utils/global.dart';
import 'package:common/widgets/bottom_button.dart';
import 'package:common/widgets/common_textfield.dart';
import 'package:common/widgets/style_app.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:mobile_banking/assets/dimens.dart';
import 'package:mobile_banking/generated/l10n.dart';
import 'package:ksb_common/shared/widgets/background_app.dart';
import 'package:mobile_banking/ui_v2/transfer/row_choose_account.dart';
import 'package:mobile_banking/ui_v2/widget/loading/loading_view.dart';
import 'package:mobile_banking/ui_v2/widget/view/currency_format_input.dart';
import 'package:rxdart/rxdart.dart';

class TargetAccumulationBottomSheet extends StatefulWidget {
  final BaseBloc bloc;
  final String title;
  final String? errorMoney;
  final String? accTypeTitle;
  final Function(AccountModel account, double amount)? onSubmit;

  const TargetAccumulationBottomSheet({
    Key? key,
    required this.bloc,
    required this.title,
    this.errorMoney,
    this.accTypeTitle,
    this.onSubmit,
  }) : super(key: key);

  @override
  State<TargetAccumulationBottomSheet> createState() =>
      _TargetAccumulationBottomSheetState();
}

class _TargetAccumulationBottomSheetState
    extends State<TargetAccumulationBottomSheet> {
  late FocusNode _moneyFocus;
  bool _showClearText = false;
  final _selectedAccount = BehaviorSubject<AccountModel>();
  final TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    super.initState();
    _moneyFocus = FocusNode();
    _moneyFocus.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    super.dispose();
    _controller.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LoadingView(
      loadingStream: widget.bloc.progressVisible,
      child: BackgroundAppBar(
        backgroundColor: StyleApp.captionColor(context)?.withOpacity(0),
        child: _getContainer(),
      ),
    );
  }

  _getContainer() {
    return Column(
      children: [
        const Spacer(),
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8.0), topRight: Radius.circular(8.0)),
          ),
          height: MediaQuery.of(context).size.height * 0.4,
          child: Column(
            children: [
              _buildHeader(),
              const Divider(height: 1),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      widget.accTypeTitle != null
                          ? Padding(
                              padding: const EdgeInsets.all(16),
                              child: Text(
                                widget.accTypeTitle ?? '',
                                style: StyleApp.subtitle1(context, true),
                              ),
                            )
                          : const SizedBox(height: 10),
                      StreamBuilder<AccountModel>(
                          stream: _selectedAccount,
                          builder: (context, snapshot) {
                            return ChooseAccountCard(
                              accountNumberSelected:
                                  snapshot.data?.accountNumber,
                              onSelectAccount: _selectedAccount.add,
                            );
                          }),
                      const SizedBox(height: 6.0),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0),
                        child: CommonTextField(
                          autofocus: true,
                          focusNode: _moneyFocus,
                          textController: _controller,
                          maxLength: 18,
                          labelText:
                              S.of(context).saving_add_money_label_amount,
                          labelStyle: StyleApp.bodyStyle(context,
                              color: const Color(0xFF333333).withOpacity(0.4)),
                          hintText: S
                              .of(context)
                              .saving_withdraw_hint_text_enter_amount,
                          textInputType: TextInputType.number,
                          suffixIconConstraints: const BoxConstraints.tightFor(
                              width: 40.0, height: 40.0),
                          suffixAlwaysOn: _showClearText,
                          suffixEnabled: true,
                          checkNull: (_) {
                            return S.of(context).please_input_money;
                          },
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly,
                            CurrencyInputFormatter(),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _showClearText = value.isNotEmpty ? true : false;
                            });
                          },
                          suffix: _showClearText
                              ? Align(
                                  alignment: Alignment.bottomCenter,
                                  child: Text(
                                    "VND",
                                    style: StyleApp.descStyle(context,
                                        color: const Color(0xFF333333)),
                                  ),
                                )
                              : null,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              BottomButton(
                title: widget.title,
                onTap: () {
                  if (widget.onSubmit != null &&
                      _selectedAccount.valueOrNull != null) {
                    _moneyFocus.unfocus();
                    widget.onSubmit!(
                      _selectedAccount.valueOrNull!,
                      Global.convertTextToMoney(_controller.text),
                    );
                  }
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  _buildHeader() {
    return Padding(
      padding: const EdgeInsets.only(
          top: kPaddingLarge,
          left: kPaddingLarge,
          right: kPaddingLarge,
          bottom: kPaddingSmall),
      child: Row(
        children: [
          InkWell(
              child: const Icon(
                Icons.close,
                color: Color(0XFF707070),
              ),
              onTap: () {
                widget.bloc.addError("");
                Navigator.pop(context);
              }),
          Expanded(
              child: Text(
            widget.title,
            style: StyleApp.buttonStyle(context),
            textAlign: TextAlign.center,
          )),
          const Icon(
            Icons.close,
            color: Colors.transparent,
          ),
        ],
      ),
    );
  }
}
