import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_bloc/bloc/base_authenticate_bloc.dart';
import 'package:ksb_bloc/bloc/model/global_model.dart';
import 'package:common/navigator.dart';
import 'package:common/widgets/bottom_button.dart';
import 'package:common/widgets/style_app.dart';
import 'package:flutter/material.dart';
import 'package:ksb_common/shared/constant.dart';
import 'package:ksb_common/shared/widgets/auth_transaction_mixin_common.dart';
import 'package:mobile_banking/assets/assets.dart';
import 'package:mobile_banking/assets/images.dart';
import 'package:mobile_banking/di/inject.dart';
import 'package:mobile_banking/generated/l10n.dart';
import 'package:mobile_banking/model/base_model.dart';
import 'package:mobile_banking/ui_v2/widget/loading/loading_view.dart';
import 'package:mobile_banking/ui_v2/profile_account/etoken/etoken_setup.dart';
import 'package:mobile_banking/ui_v2/saving/saving_online/widgets/saving_info_widget.dart';
import 'package:mobile_banking/ui_v2/saving/target_accumulation/target_settlement_done.dart';
import 'package:mobile_banking/ui_v2/transfer/row_choose_account.dart';
import 'package:ksb_common/shared/widgets/background_app.dart';
import 'package:mobile_banking/utils/auth_fatsh.dart';
import 'package:rxdart/rxdart.dart';
import 'package:ksb_common/shared/assets.dart' as ksb;

class TargetSettlementPage extends StatefulWidget {
  final TargetSavingAccountInfo info;
  final String? routeNameStartPage;
  final bool isShowFaceId;
  final String? transactionNo;

  const TargetSettlementPage({
    Key? key,
    required this.info,
    this.routeNameStartPage,
    this.isShowFaceId = false,
    this.transactionNo,
  }) : super(key: key);

  @override
  _TargetSettlementPageState createState() => _TargetSettlementPageState();
}

class _TargetSettlementPageState
    extends FlowTransactionState<TargetSettlementPage>
    with AuthTransactionConfirmMixin<TargetSettlementPage> {
  final _targetSavingBloc = Injection.injector.get<TargetSavingBloc>();
  final _selectedAccount = BehaviorSubject<AccountModel>();
  final _bloc = Injection.injector.get<TransferBloc>();
  final _blocAuth = Injection.injector.get<AuthenTransactionBloc>();
  bool isFastAuth = false;

  @override
  void initState() {
    super.initState();
    _blocAuth.streamSubs.add(_blocAuth.isFastAuthStream.listen((event) {
      if (event) {
        setState(() {
          isFastAuth = event;
        });
      }
    }));
  }

  @override
  void dispose() {
    super.dispose();
    _targetSavingBloc.dispose();
    _selectedAccount.close();
    _blocAuth.dispose();
    _bloc.dispose();
  }

  void onPressConfirm() async {
    if (!passAuthFaceID) {
      await authByFaceID(
        showFaceID: widget.isShowFaceId,
        transactionNo: widget.transactionNo,
      );
    }
    if (!passAuthFaceID) return;

    final targetAccount = _selectedAccount.valueOrNull;
    widget.info.sourceAccountNo = targetAccount?.accountNumber;
    widget.info.sourceAccountName = targetAccount?.displayName;
    await _goInputSoftOtp();
  }

  @override
  Widget build(BuildContext context) {
    return LoadingView(
      loadingStream: _targetSavingBloc.progressVisible,
      child: BackgroundAppBarImage(
        appBar: getAppBarDark(context,
            title: S.of(context).saving_withdraw_confirm_title_confirm),
        child: _getBody(),
        image: ksb.ImageAssets.bg_app_bar,
      ),
    );
  }

  _getBody() {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16.0, vertical: 16),
                    child: Text(
                      S
                          .of(context)
                          .profile_withdraw_commission_label_receiving_account,
                      style: StyleApp.subtitle1(context, true),
                    ),
                  ),
                  StreamBuilder<AccountModel>(
                    stream: _selectedAccount,
                    builder: (context, snapshot) {
                      return ChooseAccountCard(
                        accountNumberSelected: snapshot.data?.accountNumber,
                        onSelectAccount: _selectedAccount.add,
                      );
                    },
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16.0,
                      vertical: 16,
                    ),
                    child: Text(
                      S.of(context).saving_detail,
                      style: StyleApp.subtitle1(context, true),
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Theme.of(context).dividerColor),
                      borderRadius: BorderRadius.circular(4.0),
                    ),
                    margin: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Row(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(kTinyPadding),
                            bottomLeft: Radius.circular(kTinyPadding),
                          ),
                          child: ImageAssets.svgAssets(
                            ImagesResource.ic_target_accumulation_update,
                          ),
                        ),
                        SizedBox(width: kInnerPadding),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.info.accountName ?? '',
                                style: StyleApp.subtitle1(context, true),
                              ),
                              const SizedBox(height: 5),
                              Text(
                                widget.info.accountNo ?? '',
                                style: StyleApp.bodyStyle(context),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                  const SizedBox(height: 10),
                  SavingInfoWidget(
                    data: [
                      BaseModel(
                          title: S
                              .of(context)
                              .saving_withdraw_confirm_label_send_money,
                          right: GlobalModel.formatMoney(
                            toDouble(widget.info.availableBalance),
                            currencyStr: widget.info.currency,
                          )),
                      BaseModel(
                          title: S
                              .of(context)
                              .saving_withdraw_confirm_label_interest_before_due,
                          right: GlobalModel.formatMoney(
                            toDouble(widget.info.finalAmount) -
                                toDouble(widget.info.availableBalance),
                            currencyStr: widget.info.currency,
                          )),
                      BaseModel(
                        title: S
                            .of(context)
                            .saving_withdraw_confirm_label_receive_total,
                        right: GlobalModel.formatMoney(
                          toDouble(widget.info.finalAmount),
                          currencyStr: widget.info.currency,
                        ),
                        isPrice: true,
                      ),
                      BaseModel(
                          title: S.of(context).saving_label_active_date,
                          right: widget.info.contractDate.formatDMY),
                      BaseModel(
                          title: S.of(context).saving_settle_success_label_date,
                          right: widget.info.transactionDate.formatDMY),
                    ],
                  )
                ],
              ),
            ),
          ),
          BottomButton(
            title: S.of(context).saving_button_settle,
            onTap: onPressConfirm,
          )
        ],
      ),
    );
  }

  _goInputSoftOtp() async {
    final hasEToken = await _bloc.hasEToken();
    if (hasEToken) {
      final data = await _blocAuth.getetoken();
      final value = await FastAuth()
          .checkAuth(context, widget.info.finalAmount?.toDouble() ?? 0, data);
      if (value?.isNotEmpty == true) {
        final result = await _targetSavingBloc.closeAccount(widget.info,
            otp: value, transactionNo: widget.transactionNo);
        if (result != null) {
          go(
            context,
            TargetSettlementDone(
              info: widget.info,
              routeNameStartPage: widget.routeNameStartPage,
            ),
          );
        }
      }
    } else {
      goToSetupToken(context);
    }
  }
}
