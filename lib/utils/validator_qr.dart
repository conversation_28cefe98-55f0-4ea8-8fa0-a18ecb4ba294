import 'dart:convert';

import 'package:ksb_bloc/environment.dart';
import 'package:ksb_bloc/repository/loyalty/model/current_user_ref_model.dart';
import 'package:ksb_bloc/repository/preferences/preferences.dart';
import 'package:mobile_banking/di/inject.dart';
import 'package:mobile_banking/utils/ui-ultils.dart';

class ValidatorQr {
  static int? getServerQr({Uri? uri}) {
    int indexQr = -1;
    for (int i = 0; i < Environment.serversQr.length; i++) {
      Uri uriQr = Uri.parse(Environment.serversQr[i]);
      if (uriQr.isAbsolute) {
        if (uriQr.host == uri?.host && uriQr.isScheme(uri?.scheme ?? '')) {
          if (uriQr.host.contains('lixi')) {
            return 5;
          }
          return TypeQr.getPathQr(uri?.pathSegments[0]);
        }
        if (indexQr == 2 && TypeQr.SMART_BANK == uri?.pathSegments[0]) {
          return TypeQr.getPathQr(uri?.pathSegments[1]);
        }
        if (indexQr == 4 && TypeQr.OPEN_CARD == uri?.pathSegments[0]) {
          return TypeQr.getPathQr(uri?.pathSegments[1]);
        }
      }
    }
    return -1;
  }

  static bool checkVietQr({String? qrCode}) {
    if ((qrCode ?? '').contains(TypeQr.GLOBAL_UNIQUE_ID)) return true;
    return false;
  }

  static bool checkMyQrCode({String? qrCode}) {
    final valueMyQrCode = getValueMyQrCode(qrCode: qrCode);
    return valueMyQrCode != null;
  }

  static Future<bool> checkMyQrCodeV2(
      {String? qrCode, Function(String code, String id)? scanQRSuccess}) async {
    Preferences preferences = Injection.preferences;
    Environment? currentEnv = await preferences.getEnvironment();
    try {
      Uri uriQr = Uri.parse(qrCode ?? '');
      Uri uriCurrentEnv = Uri.parse(currentEnv?.endPoint ?? '');
      if (uriQr.isAbsolute) {
        if (uriQr.host == uriCurrentEnv.host &&
            uriQr.isScheme(uriCurrentEnv.scheme)) {
          if (TypeQr.REFERRAL == uriQr.pathSegments[1]) {
            scanQRSuccess?.call(uriQr.pathSegments[2], uriQr.pathSegments[3]);
            return true;
          }
        }
      }
    } on FormatException {
      return false;
    }
    return false;
  }

  static CurrentUserRefModel? getValueMyQrCode({String? qrCode}) {
    try {
      Map<String, dynamic> valueMap = jsonDecode(qrCode ?? '');
      final valueQrCode = CurrentUserRefModel.fromResponseQrCode(valueMap);
      if (valueQrCode.accountNo != null ||
          valueQrCode.accountName != null ||
          valueQrCode.bankFullName != null ||
          valueQrCode.commonBankName != null ||
          valueQrCode.bankName != null ||
          valueQrCode.code != null ||
          valueQrCode.referralPhone != null ||
          valueQrCode.description != null) return valueQrCode;
      return null;
    } on FormatException {
      return null;
    }
  }
}
