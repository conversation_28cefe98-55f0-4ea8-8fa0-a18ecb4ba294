import 'dart:math';

import 'package:ksb_bloc/bloc/model/ekyc/card_type.dart';
import 'package:ksb_bloc/environment.dart';
import 'package:ksb_bloc/repository/preferences/preferences.dart';
import 'package:ksb_bloc/utils/log.dart';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:image/image.dart' as imglib;
import 'package:mobile_banking/di/inject.dart';
import 'package:mobile_banking/generated/l10n.dart';

String getCardTypeName(BuildContext context, CardIdentificationType type) {
  switch (type) {
    case CardIdentificationType.CCCD:
      return S.of(context).citizen_identity;
    case CardIdentificationType.CMND:
      return S.of(context).people_id;
    case CardIdentificationType.PASSPORT:
      return S.of(context).passport;
    default:
      return type.toString();
  }
}

String getLanguage(String language) {
  switch (language) {
    case 'vi':
      return 'VN';
    case 'en':
      return 'EN';
    default:
      return 'VN';
  }
}

class TypeQr {
  static const String SMART_BANK = "smartbank";
  static const String REQUEST_TRANSFER = "request-transfer";
  static const String QR_PAYMENT = "qr-payment";
  static const String GLOBAL_UNIQUE_ID = "A000000727";
  static const String REFERRAL = "referral";
  static const String OPEN_CARD = "open-card";

  static int? getPathQr(String? path) {
    switch (path) {
      case REQUEST_TRANSFER:
        return 0;
      case QR_PAYMENT:
        return 1;
      case SMART_BANK:
        return 2;
      case REFERRAL:
        return 3;
      case OPEN_CARD:
        return 4;
      default:
        return null;
    }
  }
}

// * "Kết quả Face Verification
// * 0 : Không cùng 1 người
// * 1: Có thê thuộc cùng 1 người
// * 2: Các ảnh gửi lên thuộc cùng 1 người"

Color getFaceVerifyColor(BuildContext context, int verifyResult) {
  switch (verifyResult) {
    case 0:
      return Colors.redAccent;
    case 1:
      return Colors.orangeAccent;
    case 2:
      return Colors.blueAccent;
    default:
      return Colors.black;
  }
}

String getFaceVerifyText(BuildContext context, int verifyResult) {
  switch (verifyResult) {
    case 0:
      return 'Không cùng 1 người';
    case 1:
      return 'Có thê thuộc cùng 1 người';
    case 2:
      return 'Cùng 1 người';
    default:
      return '';
  }
}

Future<List<int>?> convertImagetoPng(CameraImage image) async {
  try {
    imglib.Image? img;
    if (image.format.group == ImageFormatGroup.yuv420) {
      img = _convertYUV420(image);
    } else if (image.format.group == ImageFormatGroup.bgra8888) {
      img = _convertBGRA8888(image);
    }

    imglib.PngEncoder pngEncoder = imglib.PngEncoder();

    if (img != null) {
      // Convert to png
      List<int> png = pngEncoder.encodeImage(img);
      return png;
    }
  } catch (e) {
    logger.e(">>>>>>>>>>>> ERROR:" + e.toString());
  }
  return null;
}

// CameraImage BGRA8888 -> PNG
// Color
imglib.Image _convertBGRA8888(CameraImage image) {
  return imglib.Image.fromBytes(
    image.width,
    image.height,
    image.planes[0].bytes,
    format: imglib.Format.bgra,
  );
}

// CameraImage YUV420_888 -> PNG -> Image (compresion:0, filter: none)
// Black
imglib.Image _convertYUV420(CameraImage image) {
  var img = imglib.Image(image.width, image.height); // Create Image buffer

  Plane plane = image.planes[0];
  const int shift = (0xFF << 24);

  // Fill image buffer with plane[0] from YUV420_888
  for (int x = 0; x < image.width; x++) {
    for (int planeOffset = 0;
        planeOffset < image.height * image.width;
        planeOffset += image.width) {
      final pixelColor = plane.bytes[planeOffset + x];
      // color: 0x FF  FF  FF  FF
      //           A   B   G   R
      // Calculate pixel color
      var newVal = shift | (pixelColor << 16) | (pixelColor << 8) | pixelColor;

      img.data[planeOffset + x] = newVal;
    }
  }
  return img;
}

urlValidate(String urlString, ValueChanged<String> onValue) async {
  Uri uriQrCodeParse = Uri.parse(urlString);
  Preferences preferences = Injection.preferences;
  Environment? currentEnv = await preferences.getEnvironment();
  if (uriQrCodeParse.isAbsolute) {
    onValue.call(uriQrCodeParse.toString());
  } else {
    onValue.call('${currentEnv?.endPoint}${uriQrCodeParse.toString()}');
  }
}

String randomId() {
  String requestId = "";
  for (int i = 0; requestId.length < 13; i++) {
    requestId += "${Random().nextInt(100) + DateTime.now().second}";
  }
  return requestId;
}
