import 'package:ksb_bloc/utils/log.dart';
import 'package:flutter/cupertino.dart';
import 'package:intl/intl.dart';
import 'package:jiffy/jiffy.dart';

class ScheduleDates {
  static DateTime startDate = DateTime.now().add(Duration(days: 1));
  static List<int> _listDay = [
    1,
    2,
    3,
    4,
    5,
    6,
    7,
    8,
    9,
    10,
    11,
    12,
    13,
    14,
    15,
    16,
    17,
    18,
    19,
    20,
    21,
    22,
    23,
    24,
    25,
    26,
    27,
    28,
    29,
    30,
    31,
  ];

  static int selectDayOfMonth({String? dateOfMonth}) {
    final selected = (dateOfMonth ?? '')
        .split(',')
        .map((e) => (int.tryParse(e) ?? 1) - 1)
        .where((element) => element != null)
        .toList();
    if (selected.length > 0) {
      return selected[0];
    }
    return 0;
  }

  static int getDateCount(
      {DateTime? startDate, DateTime? endDate, required int selectedDate}) {
    int countMonth = 0;
    int yearCount = 0;
    if (startDate == null || endDate == null) {
      return 0;
    }

    var startMoth = Jiffy.parseFromDateTime(startDate);
    final year = startDate.year;
    var endMonth = Jiffy.parseFromDateTime(endDate);
    String resultMoth = startMoth.from(endMonth);
    final startYear = startDate.year;
    final endYear = endDate.year;
    if (endYear - startYear > 0) {
      for (int i = startYear; endYear >= i; i++) {
        final tempEndMonth = 12;
        final tempStartMonth = 1;
        final startCount = coincideInMoth(
            start: startDate.month,
            end: tempEndMonth,
            year: startYear,
            startDay: startDate.day,
            endDay: endDate.day,
            date: selectedDate);
        final endCount = coincideInMoth(
          start: tempStartMonth,
          end: endDate.month,
          year: endYear,
          date: selectedDate,
          startDay: 1,
          endDay: endDate.day,
        );
        if ((endYear - startYear) > 1) {
          yearCount = 12;
        }
        countMonth = startCount + endCount + yearCount;
      }
    } else {
      countMonth = coincideInMoth(
          start: startDate.month,
          end: endDate.month,
          year: year,
          startDay: startDate.day,
          endDay: endDate.day,
          date: selectedDate);
    }
    return countMonth;
  }

  static int coincideInMoth(
      {required int start,
        required int end,
        required int year,
        required int date,
        required int endDay,
        required int startDay}) {
    int countMonth = 0;
    int day = startDate.day;
    int month = startDate.month;
    var result = [];
    for (var index = start; index <= end; index++) {
      if (index > start && index < end) {
        final tempMonth = DateTime(year, index);
        result = new List<int>.generate(daysInMonth(tempMonth), (i) => i + 1);
      } else if (index == start) {
        final tempMonth = DateTime(year, start);
        for (int i = startDay; i <= daysInMonth(tempMonth) ; i++) {
          result.add(i);
        }
      } else {
        final tempMonth = DateTime(year, end);
        for (var i = 1; i <= endDay; i++) {
          result.add(i);
        }
      }
      result = result;
      result.forEach((item) {
        if (item == date) {
          countMonth++;
        }
      });
      result = [];
    }

    // for (int i = start; end >= i; i++) {
    //   final tempMonth = DateTime(year, i);
    //   final result =
    //       new List<int>.generate(daysInMonth(tempMonth), (i) => i + 1);

    //   result.forEach((item) {
    //     if (item == date) {
    //       countMonth++;
    //     }
    //   });
    // }
    // if (start == month && _listDay[date] < day) countMonth--;

    return countMonth;
  }

  static int daysInMonth(DateTime date) {
    var firstDayThisMonth = DateTime(date.year, date.month, date.day);
    var firstDayNextMonth = DateTime(firstDayThisMonth.year,
        firstDayThisMonth.month + 1, firstDayThisMonth.day);
    int days = firstDayNextMonth.difference(firstDayThisMonth).inDays;
    return days;
  }

  static String dayInWeeks({
    required int date,
    required int month,
    required int year,
  }) {
    DateTime day = DateTime(year, month, date);
    String days = DateFormat("EEEE").format(day);
    return days;
  }

  static int coincideInDays({
    required DateTime startMonth,
    required DateTime endMonth,
    required int year,
    required String selectDays,
  }) {
    int countMonth = 0;
    for (int i = startMonth.month; endMonth.month >= i; i++) {
      final tempMonth = DateTime(year, i);
      var result = new List<int>.generate(daysInMonth(tempMonth), (i) => i + 1);
      debugPrint('result: ${result.length}');
      int start = startMonth.day - 1;
      int end = result.length;
      if (startMonth.month != i) {
        start = 0;
      }

      if (endMonth.month == i) {
        end = endMonth.day;
      }

      for (start; end > start; start++) {
        var tempDate = result[start];
        String date = ScheduleDates.dayInWeeks(
            date: tempDate, month: tempMonth.month, year: year);
        if (selectDays != null && selectDays.contains(date)) {
          debugPrint(
              'thứ : $date <--------> ngày : $tempDate <--------> tháng : ${tempMonth.month} <--------> năm : ${tempMonth.year}');
          countMonth++;
        }
      }
      debugPrint('Tổng ngày $countMonth');
    }
    return countMonth;
  }

  static int getDateInWeekCount(
      {DateTime? startDate, DateTime? endDate, required String selectDays}) {
    int countMonth = 0;
    if (startDate == null || endDate == null) {
      return 0;
    }
    var startMoth = Jiffy.parseFromDateTime(startDate);
    final year = startDate.year;
    var endMonth = Jiffy.parseFromDateTime(endDate);
    String resultMoth = startMoth.from(endMonth);
    final startYear = startDate.year;
    final endYear = endDate.year;
    debugPrint('resultMoth $resultMoth ');
    if (endYear - startYear > 0) {
      if (startYear != endYear) {
        for (int i = startYear; endYear >= i; i++) {
          DateTime tempEndDate = DateTime(startYear, 12, 31);
          DateTime tempStartDate = DateTime(endYear, 1, 1);
          final startCount = coincideInDays(
              startMonth: startDate,
              endMonth: tempEndDate,
              year: startYear,
              selectDays: selectDays);
          final endCount = coincideInDays(
              startMonth: tempStartDate,
              endMonth: endDate,
              year: endYear,
              selectDays: selectDays);

          countMonth = startCount + endCount;
        }
      } else {
        countMonth = coincideInDays(
            startMonth: startDate,
            endMonth: endDate,
            year: year,
            selectDays: selectDays);
      }
    } else {
      countMonth = coincideInDays(
          startMonth: startDate,
          endMonth: endDate,
          year: year,
          selectDays: selectDays);
    }
    return countMonth;
  }

  static int getMonthFilter({required int typeFilter}) {
    DateTime endDate = DateTime.now();
    int tempMonth = endDate.month - typeFilter;
    int countMonth = 0;
    late DateTime startDate;
    if (typeFilter == 1) {
      if (tempMonth == 0)
        startDate = DateTime(endDate.year - 1, 12);
      else
        startDate = DateTime(endDate.year, tempMonth);
    } else if (typeFilter == 2) {
      if (tempMonth == 0)
        startDate = DateTime(endDate.year - 1, tempMonth + 8);
      else
        startDate = DateTime(endDate.year, tempMonth);
    } else if (typeFilter == 3 || typeFilter == 4) {
      if (tempMonth < 0)
        startDate = DateTime(endDate.year - 1, tempMonth + 12);
      else
        startDate = DateTime(endDate.year, tempMonth);
    }

    final startYear = startDate.year;
    final endYear = endDate.year;

    logger.v("startYear = $startYear");
    logger.v("endYear = $endYear");
    if (startYear < endYear) {
      for (int i = startYear; endYear >= i; i++) {
        final tempEndMonth = DateTime(i, 12);
        final tempStartMonth = DateTime(i, 1);
        final startCount =
        monthFilter(startDate: startDate, endDate: tempEndMonth);
        final endCount =
        monthFilter(startDate: tempStartMonth, endDate: endDate);

        countMonth = startCount + endCount;
      }
    } else {
      countMonth = monthFilter(startDate: startDate, endDate: endDate);
    }
    return countMonth;
  }

  static int monthFilter({DateTime? startDate, DateTime? endDate}) {
    int countMonth = 0;
    if (startDate == null || endDate == null) {
      return 0;
    }

    logger.v("endDate = $endDate");
    logger.v("startDate = $startDate");

    for (int i = startDate.month; endDate.month >= i; i++) {
      final tempMonths = DateTime(startDate.year, i);
      final result =
      new List<int>.generate(daysInMonth(tempMonths), (i) => i + 1);
      result.forEach((item) {
        if (tempMonths.month == startDate.month) if (item > endDate.day) {
          countMonth++;
        }
        if (tempMonths.month == endDate.month) if (item <= endDate.day) {
          countMonth++;
        }
        if (tempMonths.month != endDate.month &&
            tempMonths.month != startDate.month) {
          countMonth++;
        }
      });
    }

    logger.v("result = $countMonth");
    return countMonth;
  }
}
