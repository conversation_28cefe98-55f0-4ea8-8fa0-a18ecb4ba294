import 'dart:io';

import 'package:app_settings/app_settings.dart';
import 'package:common/utils/dialog_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_jailbreak_detection/flutter_jailbreak_detection.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:local_auth_darwin/local_auth_darwin.dart';
import 'package:local_auth_android/local_auth_android.dart';
import 'package:local_auth/local_auth.dart';
import 'package:mobile_banking/generated/l10n.dart';

class Biometrics {
  late LocalAuthentication auth;

  Biometrics() {
    auth = LocalAuthentication();
  }

  var iosStrings = IOSAuthMessages(
    cancelButton: S.current.cancel,
    goToSettingsButton: S.current.setting,
    goToSettingsDescription: S.current.error_ios_setting_biometrics_des,
    lockOut: S.current.error_ios_lockout_biometrics_msg,
  );

  var androidStrings = AndroidAuthMessages(
    biometricHint: "",
    biometricRequiredTitle: S.current.authentication_required,
    signInTitle: S.current.authentication_required,
    cancelButton: S.current.cancel,
    goToSettingsButton: S.current.setting,
    goToSettingsDescription: S.current.error_android_setting_biometrics_des,
    biometricNotRecognized: S.current.error_android_biometrics_not_recognized,
    biometricSuccess: S.current.success,
    deviceCredentialsRequiredTitle:
    S.current.error_android_required_biometrics_title,
    deviceCredentialsSetupDescription:
    S.current.error_android_required_biometrics_des,
  );

  Future<bool> isBiometricAvailable() async {
    bool isAvailable = false;
    try {
      isAvailable = await auth.canCheckBiometrics;
    } on PlatformException catch (e) {
      logger.e(e);
    }
    isAvailable
        ? debugPrint('Biometric is available!')
        : debugPrint('Biometric is unavailable.');
    // if (!isAvailable && Platform.isIOS) {
    //   onSettingsTap();
    // }
    return isAvailable;
  }

  Future<bool> isDeviceSupported() async {
    bool isAvailable = false;
    try {
      isAvailable = await auth.isDeviceSupported();
    } on PlatformException catch (e) {
      logger.e(e);
    }
    isAvailable
        ? debugPrint('Biometric supported!')
        : debugPrint('Biometric is unsupported.');

    return isAvailable;
  }

  Future<List<BiometricType>?> getBiometricTypeList() async {
    List<BiometricType>? listOfBiometrics;
    try {
      listOfBiometrics = await auth.getAvailableBiometrics();
    } on PlatformException catch (e) {
      logger.e(e);
    }
    return listOfBiometrics;
  }

  Future<bool> getListOfBiometricTypes() async {
    List<BiometricType> listOfBiometrics;
    try {
      listOfBiometrics = await auth.getAvailableBiometrics();
      return listOfBiometrics.isNotEmpty;
      // if (listOfBiometrics.isEmpty) {
      //   return false;
      // } else {
      //   if (listOfBiometrics.contains(BiometricType.face)) {
      //     // Face ID.
      //     return true;
      //   } else if (listOfBiometrics.contains(BiometricType.fingerprint)) {
      //     // Touch ID.
      //     return false;
      //   }
      // }
    } on PlatformException catch (e) {
      logger.e(e);
      return false;
    }
    // return false;
  }

  _showAlertJailbreak(BuildContext context) {
    DialogUtil.alert(
      context,
      S.of(context).error_device_can_not_used_msg,
    );
  }

  Future<bool> authenticateUser(
      BuildContext context, {
        VoidCallback? onHandler,
        VoidCallback? onError,
        Function? onCancel,
      }) async {
    final isDeviceJailbreak = await FlutterJailbreakDetection.jailbroken;
    if (isDeviceJailbreak == true) {
      if (context.mounted) _showAlertJailbreak(context);
      return Future.value(false);
    }

    bool isAuthenticated = false;
    try {
      isAuthenticated = await auth.authenticate(
        localizedReason: S.current.error_please_verify_biometrics_msg,
        authMessages: [iosStrings, androidStrings],
        options: const AuthenticationOptions(
          useErrorDialogs: true,
          stickyAuth: true,
          biometricOnly: true,
        ),
      );
    } on PlatformException catch (e) {
      //android is not enrolled, ios is not available
      final shouldOpenSettings =
      ["NotEnrolled", "NotAvailable"].contains(e.code);
      //ios is not available, contains [canceled, not available, failure]
      final isFailed = ["Authentication canceled.", "Authentication failure."]
          .contains(e.message);
      //ios is not supported
      final notSupported = e.code == "NotSupported";

      if (shouldOpenSettings) {
        if (Platform.isIOS && isFailed) {
          isAuthenticated = false;
          onError?.call();
        } else {
          DialogUtil.confirm(
            context,
            Text(Platform.isIOS
                ? S.of(context).request_permission_biometrics
                : S.of(context).fast_auth_alert),
            cancelText: S.of(context).common_cancel,
            submitText: S.of(context).profile_home_label_setting,
            onSubmit: () {
              AppSettings.openAppSettings();
            },
            onCancel: onCancel,
          );
        }
      } else if (notSupported) {
        DialogUtil.alert(
          context,
          S.of(context).error_device_no_support,
          submit: S.of(context).agree,
        );
      }
      logger.e(e);
      return false;
    }

    isAuthenticated
        ? debugPrint('User is authenticated!')
        : debugPrint('User is not authenticated.');

    if (isAuthenticated) {
      onHandler?.call();
    } else {
      onError?.call();
    }
    return isAuthenticated;
  }
}
