import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:mobile_banking/ui_v2/card/card_home_page.dart';

class RoutePaths {
  static const String card = "/card";
  static const String invest = "invest";
  static const String securities = "securities";
  static const String home = "/";
  static const String bond = "bond";
  static const String house = "house";
  static const String credit = "credit";
  static const String bill = "bill";
  static const String statement = "statement";
  static const String wallet = "wallet";
  static const String loyalty = "loyalty";
  static const String account_tabbar = "account_tabbar";
  static const String schedule = "schedule";
  static const String mobile = "mobile";
  static const String smart_invest = "smart_invest";
  static const String micro_finance = "micro_finance";
  static const String donation = "donation";
  static const String gold = "gold";
  static const String etoken = "etoken";
  static const String authen_transaction = "authen_transaction";
  static const String transferHome = "transfer-home";
  static const String qrAtm = "qrAtm-home";
  static const String request_transfer = "/request-transfer/";
  static const String cash_withdrawal = "cash_withdrawal";
  static const String contact = "contact";
  static const String transfer_shedule = "transfer_shedule";
  static const String saving = "saving";
  static const String saving_categories = "saving_categories";
  static const String loan_list = "loan_list";
  static const String nick_name = "nick_name";
  static const String type_loan = "type_loan";
  static const String savings_book_mortgage_loan_info =
      "savings_book_mortgage_loan_info";
  static const String collab_loan_list = "collab_loan_list";

  static const String verify_repayment_page = '/verify_repayment_page';
  static const String consulting_schedule = '/consulting_schedule';
  static const String ebanking = "ebanking";

  static const String initital_profile_page = "initital_profile_page";
  static const String collab_loan_widget = "collab_loan_widget";
  static const String microfinance_page = "microfinance_page";
  static const String collab_tab_page = "collab_tab_page";
  static const String collab_customer_tab = 'collab_customer_tab';
  static const String collab_customer_detail = 'collab_customer_detail';
  static const String collab_add_todo_page = 'collab_add_todo_page';

  static const String loan_active_page = 'loan_active_page';
  static const String saving_and_accumulation_page = 'saving_and_accumulation_page';
  static const String select_card_page = 'select_card_page';
  static const String vc_card_confirm_page = 'vc_card_confirm_page';
}

class RouteGenerate {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    logger.v("----- ROUTE----: ${settings.name} ------\n"
        "arguments: ${settings.arguments.toString()}");
    switch (settings.name) {
      case RoutePaths.card:
        return CupertinoPageRoute(builder: (_) => const CardHomePage());
      default:
        return _defaultRoute('No route defined for ${settings.name}');
    }
  }

  static _defaultRoute(text) => CupertinoPageRoute(
      builder: (_) => Scaffold(body: Center(child: Text(text))));
}
