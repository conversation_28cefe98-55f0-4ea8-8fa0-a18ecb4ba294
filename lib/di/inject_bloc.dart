import 'package:ks_chat/repository/repository_chat.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:ksb_bloc/bloc/account/account_new_bloc.dart';
import 'package:ksb_bloc/bloc/account/account_update_alias_bloc.dart';
import 'package:ksb_bloc/bloc/account/detail_static_account_bloc.dart';
import 'package:ksb_bloc/bloc/auth/register/input_branch_bloc.dart';
import 'package:ksb_bloc/bloc/card/card_pin_setting_bloc.dart';
import 'package:ksb_bloc/bloc/card/virtual_card_bloc.dart';
import 'package:ksb_bloc/bloc/ekyc/ekyc_bloc.dart';
import 'package:ksb_bloc/bloc/ekyc/update_profile_ekyc_bloc.dart';
import 'package:ksb_bloc/bloc/ekyc/update_verify_ekyc_bloc.dart';
import 'package:ksb_bloc/bloc/log_call/video_call_bloc.dart';
import 'package:ksb_bloc/bloc/lucky_money/index.dart';
import 'package:ksb_bloc/bloc/lucky_money/lucky_money_2345_bloc.dart';
import 'package:ksb_bloc/bloc/money_transaction/request_transaction_bloc.dart';
import 'package:ksb_bloc/bloc/mortgage_loan/create_loan_bloc.dart';
import 'package:ksb_bloc/bloc/mortgage_loan/loan_settlement_bloc.dart';
import 'package:ksb_bloc/bloc/mortgage_loan/mortgage_loan_bloc.dart';
import 'package:ksb_bloc/bloc/open_card/open_card_bloc.dart';
import 'package:ksb_bloc/bloc/payment_qr/payment_qr_bloc.dart';
import 'package:ksb_bloc/bloc/sharebill/share_bill_bloc.dart';
import 'package:ksb_bloc/bloc/stapifaqs/stapi_faqs_bloc.dart';
import 'package:ksb_bloc/bloc/terms_and_conditions/terms_and_conditions_bloc.dart';
import 'package:ksb_bloc/bloc/theme_bloc.dart';
import 'package:ksb_bloc/bloc/tip/tip_bloc.dart';
import 'package:ksb_bloc/bloc/transfer/transfer_progress_bloc.dart';
import 'package:ksb_bloc/bloc/transfer/transfer_stock_bloc.dart';
import 'package:ksb_bloc/bloc/viet_qr/viet_qr_bloc.dart';
import 'package:ksb_bloc/bloc/virtual_card/vc_list_card_bloc.dart';
import 'package:ksb_bloc/bloc/virtual_card/virtual_card_map_bloc.dart';
import 'package:ksb_bloc/bloc/vnpost/customer_vnpost_bloc.dart';

import 'inject.dart';

void injectBloc(Repository _repository, Preferences preferences,
    Session _session, RepositoryChat repositoryChat) {
  Injection.injector.map<KeyboardBloc>(
      (injector) => KeyboardBloc(_repository, preferences, _session));
  Injection.injector.map<EkycBloc>(
      (injector) => EkycBloc(_repository, preferences, _session));
  Injection.injector.map<EKycBlocV2>(
      (injector) => EKycBlocV2(_repository, preferences, _session));
  Injection.injector.map<ProfileEKycBloc>(
      (injector) => ProfileEKycBloc(_repository, preferences, _session));
  Injection.injector.map<UpdateProfileEKycBloc>(
      (injector) => UpdateProfileEKycBloc(_repository, preferences, _session));
  Injection.injector.map<AuthTransactionEKycBloc>((injector) =>
      AuthTransactionEKycBloc(_repository, preferences, _session));
  Injection.injector.map<LoginBloc>((injector) =>
      LoginBloc(_repository, preferences, _session, repositoryChat));
  Injection.injector.map<HomeBloc>(
      (injector) => HomeBloc(_repository, preferences, _session));
  Injection.injector.map<AccountCreateBloc>(
      (injector) => AccountCreateBloc(_repository, preferences, _session));
  Injection.injector.map<InputPhoneBloc>(
      (injector) => InputPhoneBloc(_repository, preferences, _session));
  Injection.injector.map<DetailAccountBloc>(
      (injector) => DetailAccountBloc(_repository, preferences, _session));
  Injection.injector.map<ProfileBloc>((injector) =>
      ProfileBloc(_repository, preferences, _session, repositoryChat));
  Injection.injector.map<ChangePasswordBloc>(
      (injector) => ChangePasswordBloc(_repository, preferences, _session));
  Injection.injector.map<ProfileUpdateBloc>(
      (injector) => ProfileUpdateBloc(_repository, preferences, _session));
  Injection.injector.map<OtpVerifyBloc>(
      (injector) => OtpVerifyBloc(_repository, preferences, _session));
  Injection.injector.map<OtpTokenBloc>(
      (injector) => OtpTokenBloc(_repository, preferences, _session));
  Injection.injector.map<EkycVerifyBloc>(
      (injector) => EkycVerifyBloc(_repository, preferences, _session));
  Injection.injector.map<UpdateEKYCVerifyBloc>(
      (injector) => UpdateEKYCVerifyBloc(_repository, preferences, _session));
  Injection.injector.map<TransactionAccountBloc>(
      (injector) => TransactionAccountBloc(_repository, preferences, _session));
  Injection.injector.map<TransactionDetailBloc>(
      (injector) => TransactionDetailBloc(_repository, preferences, _session));
  Injection.injector.map<EtokenBloc>(
      (injector) => EtokenBloc(_repository, preferences, _session));
  Injection.injector.map<DevicesManagerBloc>(
      (injector) => DevicesManagerBloc(_repository, preferences, _session));
  Injection.injector.map<CardBloc>(
      (injector) => CardBloc(_repository, preferences, _session));
  Injection.injector.map<CardCreditBloc>(
      (injector) => CardCreditBloc(_repository, preferences, _session));
  Injection.injector.map<StatisticAccountBloc>(
      (injector) => StatisticAccountBloc(_repository, preferences, _session));
  Injection.injector.map<AccountListBloc>(
      (injector) => AccountListBloc(_repository, preferences, _session));
  Injection.injector.map<AccountSummaryBloc>(
      (injector) => AccountSummaryBloc(_repository, preferences, _session));
  Injection.injector.map<SavingBloc>(
      (injector) => SavingBloc(_repository, preferences, _session));
  Injection.injector.map<TargetSavingBloc>(
      (injector) => TargetSavingBloc(_repository, preferences, _session));
  Injection.injector.map<BillBloc>(
      (injector) => BillBloc(_repository, preferences, _session));
  Injection.injector.map<BillBlocV2>(
      (injector) => BillBlocV2(_repository, preferences, _session));
  Injection.injector.map<AuthenTransactionBloc>(
      (injector) => AuthenTransactionBloc(_repository, preferences, _session));
  Injection.injector.map<ContactListBloc>(
      (injector) => ContactListBloc(_repository, preferences, _session));
  Injection.injector.map<ForgotPasswordBloc>(
      (injector) => ForgotPasswordBloc(_repository, preferences, _session));
  Injection.injector.map<MobileTopupBloc>(
      (injector) => MobileTopupBloc(_repository, preferences, _session));
  Injection.injector.map<MobileTopUpBlocV2>(
      (injector) => MobileTopUpBlocV2(_repository, preferences, _session));
  Injection.injector.map<MyQrBloc>(
      (injector) => MyQrBloc(_repository, preferences, _session));
  Injection.injector.map<MobileConfirmBloc>(
      (injector) => MobileConfirmBloc(_repository, preferences, _session));
  Injection.injector.map<StatementPdfBloc>(
      (injector) => StatementPdfBloc(_repository, preferences, _session));
  Injection.injector.map<CardDebitBloc>(
      (injector) => CardDebitBloc(_repository, preferences, _session));
  Injection.injector.map<StatementListBloc>(
      (injector) => StatementListBloc(_repository, preferences, _session));
  Injection.injector.map<NicknameBloc>(
      (injector) => NicknameBloc(_repository, preferences, _session));
  Injection.injector.map<RequestTransactionBloc>(
      (injector) => RequestTransactionBloc(_repository, preferences, _session));
  Injection.injector.map<AccountNewBloc>(
      (injector) => AccountNewBloc(_repository, preferences, _session));
  Injection.injector.map<AnnouncementBloc>(
      (injector) => AnnouncementBloc(_repository, preferences, _session));
  Injection.injector.map<PaymentQrBloc>(
      (injector) => PaymentQrBloc(_repository, preferences, _session));
  Injection.injector.map<MapAtmBloc>(
      (injector) => MapAtmBloc(_repository, preferences, _session));
  Injection.injector.map<ProfileConsultantBloc>(
      (injector) => ProfileConsultantBloc(_repository, preferences, _session));
  Injection.injector.map<StapiFaqsBloc>(
      (injector) => StapiFaqsBloc(_repository, preferences, _session));
  Injection.injector.map<CardControlBloc>(
      (injector) => CardControlBloc(_repository, preferences, _session));
  Injection.injector.map<AccountUpdateAliasBloc>(
      (injector) => AccountUpdateAliasBloc(_repository, preferences, _session));
  Injection.injector.map<InputBranchBloc>(
      (injector) => InputBranchBloc(_repository, preferences, _session));
  Injection.injector.map<VietQrBloc>(
      (injector) => VietQrBloc(_repository, preferences, _session));
  Injection.injector.map<ShareBillBloc>(
      (injector) => ShareBillBloc(_repository, preferences, _session));
  Injection.injector.map<TransferVNPostBloc>(
      (injector) => TransferVNPostBloc(_repository, preferences, _session));
  Injection.injector.map<PromotionDetailBloc>(
      (injector) => PromotionDetailBloc(_repository, preferences, _session));
  Injection.injector.map<NewsDetailBloc>(
      (injector) => NewsDetailBloc(_repository, preferences, _session));
  Injection.injector.map<NotificationBloc>(
      (injector) => NotificationBloc(_repository, preferences, _session));
  Injection.injector.map<EBankBloc>(
      (injector) => EBankBloc(_repository, preferences, _session));
  Injection.injector.map<SmsBankingBloc>(
      (injector) => SmsBankingBloc(_repository, preferences, _session));
  Injection.injector.map<ReferralCodeBloc>(
      (injector) => ReferralCodeBloc(_repository, preferences, _session));
  Injection.injector.map<HomeTabBloc>((injector) =>
      HomeTabBloc(_repository, preferences, _session, repositoryChat));
  Injection.injector.map<OpenCardBloc>(
      (injector) => OpenCardBloc(_repository, preferences, _session));
  Injection.injector.map<NetworkBloc>(
      (injector) => NetworkBloc(_repository, preferences, _session));
  Injection.injector.map<NetworkDetailBloc>(
      (injector) => NetworkDetailBloc(_repository, preferences, _session));
  Injection.injector.map<NetworkConsultantBloc>(
      (injector) => NetworkConsultantBloc(_repository, preferences, _session));
  Injection.injector.map<NetworkScheduleBloc>(
      (injector) => NetworkScheduleBloc(_repository, preferences, _session));
  Injection.injector.map<NetworkReasonBloc>(
      (injector) => NetworkReasonBloc(_repository, preferences, _session));
  Injection.injector.map<NetworkCounselingBloc>(
      (injector) => NetworkCounselingBloc(_repository, preferences, _session));
  Injection.injector.map<NetworkTransactionRoomBloc>(
      (i) => NetworkTransactionRoomBloc(_repository, preferences, _session));
  Injection.injector.map<NetworkServiceBloc>(
      (injector) => NetworkServiceBloc(_repository, preferences, _session));
  Injection.injector.map<NetworkCounselingDetailBloc>(
      (i) => NetworkCounselingDetailBloc(_repository, preferences, _session));
  Injection.injector.map<DetailStaticAccountBloc>(
      (i) => DetailStaticAccountBloc(_repository, preferences, _session));
  Injection.injector.map<NetworkMapBloc>(
      (injector) => NetworkMapBloc(_repository, preferences, _session));
  Injection.injector.map<NetworkVoteBloc>(
      (injector) => NetworkVoteBloc(_repository, preferences, _session));
  Injection.injector.map<TransferStockBloc>(
      (injector) => TransferStockBloc(_repository, preferences, _session));
  Injection.injector.map<TermsAndConditionsBloc>(
      (injector) => TermsAndConditionsBloc(_repository, preferences, _session));
  Injection.injector.map<NetworkFilterBloc>(
      (injector) => NetworkFilterBloc(_repository, preferences, _session));
  Injection.injector.map<NetworkPhotoBloc>(
      (injector) => NetworkPhotoBloc(_repository, preferences, _session));
  Injection.injector.map<ThemeBloc>(
      (injector) => ThemeBloc(_repository, preferences, _session));
  Injection.injector.map<MortgageLoanBloc>(
      (injector) => MortgageLoanBloc(_repository, preferences, _session));
  Injection.injector.map<CreateLoanBloc>(
      (injector) => CreateLoanBloc(_repository, preferences, _session));
  Injection.injector.map<LoanSettlementBloc>(
      (injector) => LoanSettlementBloc(_repository, preferences, _session));
  //collab
  Injection.injector.map<RegisterCollabBloc>(
      (injector) => RegisterCollabBloc(_repository, preferences, _session));
  Injection.injector.map<RepaymentBloc>(
      (injector) => RepaymentBloc(_repository, preferences, _session));
  Injection.injector.map<ClientBloc>(
      (injector) => ClientBloc(_repository, preferences, _session),
      isSingleton: true);
  Injection.injector.map<RegisterLoanBloc>(
      (injector) => RegisterLoanBloc(_repository, preferences, _session),
      isSingleton: true);
  Injection.injector.map<DebtCollectionBloc>(
      (injector) => DebtCollectionBloc(_repository, preferences, _session));
  Injection.injector.map<LoanCollabBloc>(
      (injector) => LoanCollabBloc(_repository, preferences, _session));
  Injection.injector.map<LoanProfileBloc>(
      (injector) => LoanProfileBloc(_repository, preferences, _session));
  Injection.injector.map<DocumentSaleBloc>(
      (injector) => DocumentSaleBloc(_repository, preferences, _session));
  Injection.injector.map<CollabTodoBloc>(
      (injector) => CollabTodoBloc(_repository, preferences, _session));
  Injection.injector.map<CollabSaleBloc>(
      (injector) => CollabSaleBloc(_repository, preferences, _session));
  Injection.injector.map<CollabUtilsBloc>(
      (injector) => CollabUtilsBloc(_repository, preferences, _session));
  Injection.injector.map<CollabNotifyBloc>(
      (injector) => CollabNotifyBloc(_repository, preferences, _session));
  Injection.injector.map<VideoCallBloc>(
      (injector) => VideoCallBloc(_repository, preferences, _session));
  Injection.injector
      .map<TipBloc>((injector) => TipBloc(_repository, preferences, _session));
  Injection.injector.map<CustomerVNPostBloc>(
      (injector) => CustomerVNPostBloc(_repository, preferences, _session));
  Injection.injector.map<BannerBloc>(
      (injector) => BannerBloc(_repository, preferences, _session));
  Injection.injector.map<TransferProgressBloc>(
      (injector) => TransferProgressBloc(_repository, preferences, _session));
  Injection.injector.map<VideoEKycBloc>(
      (injector) => VideoEKycBloc(_repository, preferences, _session));
  Injection.injector.map<KlbPayQrBloc>(
      (injector) => KlbPayQrBloc(_repository, preferences, _session));

  Injection.injector.map<ReferralBloc>(
      (injector) => ReferralBloc(_repository, preferences, _session));
  Injection.injector.map<LuckyMoneyFillBloc>(
      (injector) => LuckyMoneyFillBloc(_repository, preferences, _session));
  Injection.injector.map<LuckyMoney2345Bloc>(
      (injector) => LuckyMoney2345Bloc(_repository, preferences, _session));
  Injection.injector.map<LuckyMoneyHistoryBloc>(
      (injector) => LuckyMoneyHistoryBloc(_repository, preferences, _session));
  (injector) => KlbPayQrBloc(_repository, preferences, _session);
  Injection.injector.map<CardPinSettingBloc>(
      (injector) => CardPinSettingBloc(_repository, preferences, _session));

  Injection.injector.map<LuckyMoneyDetailHistoryBloc>((injector) =>
      LuckyMoneyDetailHistoryBloc(_repository, preferences, _session));
  Injection.injector.map<LuckyMoneyNotificationBloc>((injector) =>
      LuckyMoneyNotificationBloc(_repository, preferences, _session));
  Injection.injector.map<OpenGiftBloc>(
      (injector) => OpenGiftBloc(_repository, preferences, _session));
  Injection.injector.map<PriorityBloc>(
      (injector) => PriorityBloc(_repository, preferences, _session));

  /// blocs for 2345
  Injection.injector.map<UpdateBiology2345Bloc>(
      (injector) => UpdateBiology2345Bloc(_repository, preferences, _session));

  // virtual card
  Injection.injector.map<VirtualCardMapBloc>(
      (injector) => VirtualCardMapBloc(_repository, preferences, _session));
  Injection.injector.map<VCListCardBloc>(
      (injector) => VCListCardBloc(_repository, preferences, _session));

  Injection.injector.map<VirtualCardBloc>(
      (injector) => VirtualCardBloc(_repository, preferences, _session));

  ///noble
  Injection.injector.map<NobleBloc>(
      (injector) => NobleBloc(_repository, preferences, _session));

  //review_transaction
  Injection.injector.map<ReviewTransactionBloc>(
      (injector) => ReviewTransactionBloc(_repository, preferences, _session));
}
