import 'package:common/environment.dart' as $common;
import 'package:common/repository/strapi/index.dart';
import 'package:flutter_simple_dependency_injection/injector.dart';
import 'package:ks_chat/di/inject.dart' as injectionChat;
import 'package:ks_chat/ks_chat.dart';
import 'package:ksb_bloc/bloc.dart';
import 'package:mobile_banking/bloc_impl/repository/app_interceptor.dart';
import 'package:mobile_banking/bloc_impl/repository/http_utils.dart';
import 'package:mobile_banking/bloc_impl/repository/repository_impl.dart';
import 'package:mobile_banking/bloc_impl/session/session_chat_impl.dart';
import 'package:mobile_banking/bloc_impl/session/session_impl.dart';
import 'package:mobile_banking/di/inject_bloc.dart';
import 'package:mobile_banking/di/inject_load_bloc.dart';
import 'package:mobile_banking/di/inject_transfer_bloc.dart';
import 'package:open_api_umee/open_api_collab.dart';
import 'package:open_api_umee/open_api_credit.dart';
import 'package:open_api_umee/open_api_pay.dart';
import 'package:video_ekyc_api/video_ekyc_api.dart';

class Injection {
  static late Injector injector;
  static late Preferences preferences;

  static initInjection(Environment environment, Preferences preferences) async {
    Injection.preferences = preferences;
    injector = Injector();
    injector.map<Environment>(
      (i) => environment,
      isSingleton: true,
    );

    //Repository
    final repository = _injectRepository(environment);
    //Session
    final session = _injectSession(preferences, repository, environment);
    await session.initializationDone;
    KsChatPreferences ksChatPreferences = KsChatPreferences();
    ksChatPreferences.initPreferences();

    final sessionChat = SessionChatImpl(preferences);
    injector.map<SessionChatImpl>((injector) => sessionChat);

    final envChat = $common.Environment.staging();
    envChat.setChatServerUrl(environment.chatServerUrl.toString());
    envChat.setChatSocketUrl(environment.chatSocketServer);
    final repositoryChat = injectionChat.Injection.injectChat(
      injector,
      ksChatPreferences,
      sessionChat,
      envChat,
      repository.strApi!,
    );
    //Bloc
    _injectBloc(repository, preferences, session, repositoryChat);
  }

  static Repository _injectRepository(Environment environment) {
    final defaultInterceptors = [AppInterceptors()];
    final noTokenInterceptors = [AppInterceptors(isHasToken: false)];

    final profileApi = KsbankApiProfile(
      dio: initDio("${environment.endPoint}/profile", proxy: environment.proxy),
      interceptors: noTokenInterceptors,
      serializers: ksbankProfileSerializers,
    );
    final bankApi = KsbankApiSmartbank(
      dio: initDio(
        "${environment.endPoint}/smartbank",
        proxy: environment.proxy,
        connectTimeout: 1000 * 60 * 2,
        receiveTimeout: 1000 * 60 * 2,
      ),
      interceptors: defaultInterceptors,
      serializers: ksbankSmartBankSerializers,
    );
    final mediaApi = KsbankApiMedia(
      dio: initDio("${environment.endPoint}/media", proxy: environment.proxy),
      interceptors: noTokenInterceptors,
      serializers: ksbankMediaSerializers,
    );
    final notificationApi = KsbankApiNotification(
      dio: initDio(
        "${environment.endPoint}/notification",
        proxy: environment.proxy,
      ),
      interceptors: defaultInterceptors,
      serializers: ksbankNotificationSerializers,
    );
    final stocksApi = KsbankApiStocks(
      dio: initDio(
        "${environment.endPoint}/stocks",
        proxy: environment.proxy,
        connectTimeout: 1000 * 60 * 5,
        receiveTimeout: 1000 * 60 * 5,
      ),
      interceptors: defaultInterceptors,
      serializers: ksbankStocksSerializers,
    );
    final stmApi = KsbankApiStm(
      dio: initDio("${environment.endPoint}/stm", proxy: environment.proxy),
      interceptors: defaultInterceptors,
      serializers: ksbankStmSerializers,
    );
    final strApi = StrApi(
      initDio(environment.strApiUrl, proxy: environment.proxy),
      interceptors: [AppInterceptors(isHasToken: false, isStrapiToken: true)],
    );
    final crmApi = CRMApi(
      initDio("${environment.endPoint}/crm", proxy: environment.proxy),
      interceptors: defaultInterceptors,
    );
    final loyaltyApi = KsbankApiLoyalty(
      dio: initDio("${environment.endPoint}/loyalty", proxy: environment.proxy),
      interceptors: defaultInterceptors,
      serializers: ksbankLoyaltySerializers,
    );
    final maintenanceApi = KsbankApiMaintenance(
      dio: initDio("${environment.endPoint}/maintenance",
          proxy: environment.proxy),
      interceptors: defaultInterceptors,
      serializers: ksbankMaintenanceSerializers,
    );
    final networkApi = NetworkApi(
      initDio("https://api-staging.kienlongbank.co/crm/bridge",
          proxy: environment.proxy),
      interceptors: defaultInterceptors,
    );
    final collabApi = UmeeApiCollab(
      dio: initDio("${environment.endPoint}/collab", proxy: environment.proxy),
      interceptors: defaultInterceptors,
    );
    final creditApi = UmeeApiCredit(
      dio: initDio("${environment.endPoint}/credit", proxy: environment.proxy),
      interceptors: defaultInterceptors,
    );
    final nicknameApi = KsbankApiNickname(
      dio:
          initDio("${environment.endPoint}/nickname", proxy: environment.proxy),
      interceptors: defaultInterceptors,
    );
    final videoEKycApi = VideoEkycApi(
      dio: initDio("${environment.endPoint}/video-ekyc",
          proxy: environment.proxy),
      interceptors: defaultInterceptors,
      serializers: videoEkycSerializers,
    );
    final savingVnpApi = SavingVnpApi(
      dio: initDio(
        "${environment.endPoint}/vnpost",
        proxy: environment.proxy,
      ),
      interceptors: defaultInterceptors,
      serializers: ksbankSavingVnpSerializers,
    );
    final klbPayApi = UmeeApiPay(
      dio: initDio("${environment.endPoint}/pay-middleware",
          proxy: environment.proxy),
      interceptors: defaultInterceptors,
    );

    final luckyMoneyApi = LuckyMoneyApi(
      dio: initDio("${environment.endPoint}/lucky-money",
          proxy: environment.proxy),
      interceptors: defaultInterceptors,
      serializers: ksbankLuckyMoneySerializers,
    );

    injector.map<KsbankApiProfile>(
      (i) => profileApi,
      isSingleton: true,
    );

    injector.map<KsbankApiSmartbank>(
      (i) => bankApi,
      isSingleton: true,
    );

    injector.map<KsbankApiMedia>(
      (i) => mediaApi,
      isSingleton: true,
    );

    injector.map<KsbankApiNotification>(
      (i) => notificationApi,
      isSingleton: true,
    );

    injector.map<KsbankApiStocks>(
      (i) => stocksApi,
      isSingleton: true,
    );

    injector.map<KsbankApiStm>(
      (i) => stmApi,
      isSingleton: true,
    );

    injector.map<StrApi>(
      (i) => strApi,
      isSingleton: true,
    );

    injector.map<CRMApi>(
      (i) => crmApi,
      isSingleton: true,
    );

    injector.map<KsbankApiLoyalty>(
      (i) => loyaltyApi,
      isSingleton: true,
    );

    injector.map<SavingVnpApi>(
      (i) => savingVnpApi,
      isSingleton: true,
    );

    injector.map<KsbankApiMaintenance>(
      (i) => maintenanceApi,
      isSingleton: true,
    );

    injector.map<NetworkApi>(
      (i) => networkApi,
      isSingleton: true,
    );

    injector.map<UmeeApiCollab>(
      (i) => collabApi,
      isSingleton: true,
    );

    injector.map<UmeeApiCredit>(
      (i) => creditApi,
      isSingleton: true,
    );

    injector.map<KsbankApiNickname>(
      (i) => nicknameApi,
      isSingleton: true,
    );

    injector.map<VideoEkycApi>(
      (i) => videoEKycApi,
      isSingleton: true,
    );

    injector.map<UmeeApiPay>(
      (i) => klbPayApi,
      isSingleton: true,
    );

    injector.map<LuckyMoneyApi>(
      (i) => luckyMoneyApi,
      isSingleton: true,
    );

    var repositoryImpl = RepositoryImpl(
      profileApi: profileApi,
      bankApi: bankApi,
      mediaApi: mediaApi,
      notificationApi: notificationApi,
      stmApi: stmApi,
      stocksApi: stocksApi,
      strApi: strApi,
      crmApi: crmApi,
      maintenanceApi: maintenanceApi,
      loyaltyApi: loyaltyApi,
      networkApi: networkApi,
      umeeApiCollab: collabApi,
      umeeApiCredit: creditApi,
      nicknameApi: nicknameApi,
      videoEKycApi: videoEKycApi,
      savingVnpApi: savingVnpApi,
      klbPayApi: klbPayApi,
      luckyMoneyApi: luckyMoneyApi,
    );
    injector.map<Repository>(
      (i) => repositoryImpl,
      isSingleton: true,
    );
    return repositoryImpl;
  }

  static Session _injectSession(
    Preferences preferences,
    Repository repository,
    Environment environment,
  ) {
    final session = SessionImpl(preferences, repository, environment);
    injector.map<Session>(
      (i) => session,
      isSingleton: true,
    );
    return session;
  }

  static void _injectBloc(Repository repository, Preferences preferences,
      Session session, RepositoryChat repositoryChat) {
    injectBloc(repository, preferences, session, repositoryChat);
    injectTransferBloc(repository, preferences, session);
    injectLoadBloc(repository, preferences, session);
  }
}
