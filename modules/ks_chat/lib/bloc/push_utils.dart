import 'dart:convert';

import 'package:common/utils/log.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:ks_chat/bloc/model/rocket_chat_ejson.dart';
import 'package:vibration/vibration.dart';

/// Initialize the [FlutterLocalNotificationsPlugin] package.
final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
FlutterLocalNotificationsPlugin();

/// Create a [AndroidNotificationChannel] for heads up notifications
const AndroidNotificationChannel androidChannel = AndroidNotificationChannel(
  'high_importance_channel', // id
  'High Importance Notifications', // title
  importance: Importance.defaultImportance,
);

class PushUtils {
  static init() async {
    /// Create an Android Notification Channel.
    ///
    /// We use this channel in the `AndroidManifest.xml` file to override the
    /// default FCM channel to enable heads up notifications.
    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
        AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(androidChannel);
  }

  static sendPushChatNotification(RemoteMessage message) {
    logger.t(message);
    final data = message.data;
    if (data != null) {
      final ejson = data['ejson'] ?? '';
      final title = data['title'];
      final message = data['message'];

      //is RocketChat message
      if (ejson is String && ejson.length > 0) {
        RocketChatEJson ejsonModel = RocketChatEJson.fromJson(ejson);
        sendPushChat(
          messageId: ejsonModel.messageId,
          roomId: ejsonModel.rid,
          message: message,
          title: title,
        );
      }
    }
  }

  static sendPushChat({
    String? messageId,
    String? roomId,
    String? title,
    String? message,
    String? roomType,
  }) async {
    logger.t(title, error: message);
    if (message == null || message.isEmpty) return;
    flutterLocalNotificationsPlugin.show(
      roomId?.hashCode ?? 10,
      title ?? "",
      message,
      NotificationDetails(
        android: AndroidNotificationDetails(
          androidChannel.id,
          androidChannel.name,
          icon: 'ic_stat_onesignal_default',
        ),
      ),
      payload: jsonEncode(
        {
          'messageId': messageId,
          'roomId': roomId,
          't' : roomType,
          'fname' : title
        },
      ),
    );
    final hasVibration = await Vibration.hasVibrator();
    if (hasVibration == true) {
      Vibration.vibrate();
    }
  }
}
