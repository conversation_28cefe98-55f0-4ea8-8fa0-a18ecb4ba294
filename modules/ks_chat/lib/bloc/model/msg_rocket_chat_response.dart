import 'dart:convert';

import 'package:common/utils/log.dart';
import 'package:rocket_chat_flutter_connector/web_socket/notification_type.dart';

typedef MapResultResponse<T> = T Function(Map resultMap);

class MsgRocketChatResponse<T> {
  NotificationType? msg;
  String? id;
  String? collection;

  T? result;
  dynamic rawResult;
  dynamic rawResponse;

  String? serverId;
  List<String>? subs;
  List<String>? methods;
  NotificationFields<T>? fields;

  MsgRocketChatResponse.fromMap(Map response,
      [MapResultResponse<T>? mapperResult]) {
    rawResponse = response;
    msg = notificationTypeFromString(response['msg']);
    collection = response['collection'];
    id = response['id'];
    fields = NotificationFields.fromMap(response['fields']);
    rawResult = response['result'];
    result = (rawResult != null && mapperResult != null)
        ? mapperResult(rawResult)
        : null;
  }

  T? updateResult(MapResultResponse<T>? mapperResult) {
    logger.t(rawResult);
    result = (rawResult != null && mapperResult != null && rawResult is Map)
        ? mapperResult(rawResult)
        : null;
    return result;
  }

  @override
  String toString() {
    return 'MsgRocketChatResponse{msg: $msg, id: $id, result: $result, rawResult: $rawResult, rawResponse: $rawResponse}';
  }
}

class NotificationFields<T> {
  String? eventName;
  dynamic args;
  dynamic rawArgs;

  NotificationFields({
    this.eventName,
    this.args,
  });

  NotificationFields.fromMap(Map<String, dynamic>? json,
      [MapResultResponse<T>? mapperResult]) {
    if (json != null) {
      eventName = json['eventName'];
      if (json['args'] != null) {
        rawArgs = json['args'];
        args = (rawArgs != null && mapperResult != null)
            ? mapperResult(rawArgs)
            : null;
      }
    }
  }

  @override
  String toString() {
    return 'WebSocketMessageFields{eventName: $eventName, args: $args}';
  }
}

class RocketChatSocketToken {
  RocketChatSocketToken({
    this.id,
    this.token,
    this.tokenExpires,
    this.type,
  });

  String? id;
  String? token;
  TokenExpires? tokenExpires;
  String? type;

  factory RocketChatSocketToken.fromJson(String str) =>
      RocketChatSocketToken.fromMap(json.decode(str));

  String toJson() => json.encode(toMap());

  factory RocketChatSocketToken.fromMap(Map<dynamic, dynamic>? json) =>
      RocketChatSocketToken(
        id: json?["id"],
        token: json?["token"],
        tokenExpires: TokenExpires.fromMap(json?["tokenExpires"]),
        type: json?["type"],
      );

  Map<String, dynamic> toMap() => {
        "id": id,
        "token": token,
        "tokenExpires": tokenExpires?.toMap(),
        "type": type,
      };
}

class TokenExpires {
  TokenExpires({
    this.date,
  });

  int? date;

  factory TokenExpires.fromJson(String str) =>
      TokenExpires.fromMap(json.decode(str));

  String toJson() => json.encode(toMap());

  factory TokenExpires.fromMap(Map<String, dynamic> json) => TokenExpires(
        date: json["\u0024date"],
      );

  Map<String, dynamic> toMap() => {
        "\u0024date": date,
      };
}
