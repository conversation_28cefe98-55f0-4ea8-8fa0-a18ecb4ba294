import 'dart:io';

import 'package:common/ks_common.dart';
import 'package:common/model/token.dart';
import 'package:ks_chat/bloc/model/msg_rocket_chat_response.dart';
import 'package:ks_chat/repository/chat/rocket_chat_authentication.dart';
import 'package:ks_chat/repository/chat/rocket_chat_error.dart';
import 'package:ks_chat/repository/chat/rocket_chat_models.dart';
import 'package:ks_chat/repository/preferences_impl.dart';
import 'package:ks_chat/repository/repository_chat.dart';
import 'package:ks_chat/repository/session_chat.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:package_info/package_info.dart';
import 'package:path/path.dart' as $path;
import 'package:rocket_chat_flutter_connector/models/authentication.dart';
import 'package:rxdart/rxdart.dart';

import 'chat_session.dart';

abstract class ChatBaseBloc extends CommonBaseBloc {
  KsChatPreferences preferences;
  RepositoryChat repositoryChat;
  SessionChat session;
  ChatSession chatSession;
  static bool inChat = false;
  static String OAUTH_SERVICE_NAME = 'identityserver4';

  String get chatUrl => repositoryChat.apiUri.toString();
  String? userId;
  String? authToken;

  final _hasTyping = PublishSubject<bool>();
  bool _isLogin = false;

  Stream<bool> get hasTypingSteam => _hasTyping.stream;

  Stream<ChatLoginStatus> get authApiChange =>
      repositoryChat.authApi.authApiChange;

  final _isTyping = PublishSubject<bool>();

  Stream<bool> get isTypingStream => _isTyping.distinct();
  late Future<String> _jitsiPrefixStr;
  late Future<String> _jitsiUniqueIdStr;
  late Future<String> _jitsiSuffixStr;
  late Future<String> _callServer;
  final firstLoadComplete = BehaviorSubject<bool?>();

  ChatBaseBloc(
    this.session,
    this.preferences,
    this.repositoryChat,
    this.chatSession,
  ) {
    init();
  }

  Future<String> getVideoCallRoomId(String roomChatId) async {
    return '${await _jitsiPrefixStr}${await _jitsiUniqueIdStr}$roomChatId${await _jitsiSuffixStr}';
  }

  Future<String> getCallServer() {
    return _callServer;
  }

  @override
  init() async {
    super.init();

    _jitsiPrefixStr = preferences.getJitsiPrefix();
    _jitsiUniqueIdStr = preferences.getJitsiUniqueID();
    _jitsiSuffixStr = preferences.getJitsiSuffix();
    _callServer = preferences.getJitsiDomain();

    streamSubs.add(
      _isTyping
          .transform(SwitchMapStreamTransformer((i) =>
              Stream.fromFuture(Future.delayed(Duration(seconds: 3), () => i))))
          .listen(
        (event) {
          if (event == true) {
            stopTyping();
          }
        },
      ),
    );

    streamSubs.add(chatSession.authChange.listen((event) {
      if (event == ChatLoginStatus.LOGGED) {
        reload();
      }
    }));
  }

  String? getUrlAvatar(String? name) {
    if (name != null && name.length > 0) {
      String url = $path.join(repositoryChat.apiUri.toString(), 'avatar',
          Uri.encodeComponent(name));
      url += '?format=jpg';
      // logger.v(url);
      return url;
    }
    return name;
  }

  logout() {
    repositoryChat.authApi.logout();
    repositoryChat.socket.reset();
  }

  @override
  dispose() {
    super.dispose();
    _hasTyping.close();
    _isTyping.close();
    firstLoadComplete.close();
  }

  void stopTyping() {
    _isTyping.add(false);
  }

  void startTyping() {
    _isTyping.add(true);
  }

  handlerTyping(MsgRocketChatResponse event) {
    final args = event.fields?.rawArgs ?? [];
    if (args is List && args.length > 1) {
      _hasTyping.add(toBool(args[1]));
    }
  }

  Future<Authentication?> login([String? serviceName]) async {
    if (_isLogin == true) return getAuthInfo();
    _isLogin = true;
    showLoading();
    try {
      final token = await session.getToken();
      return await _loginWithToken(token, serviceName);
    } catch (e) {
      handlerApiError(e);
      _isLogin = false;
      throw e;
    } finally {
      completeLoading();
      _isLogin = false;
    }
  }

  Future<Authentication> _loginWithToken(Token token,
      [String? serviceName]) async {
    final authInfo =
        await repositoryChat.authApi.loginWithToken(token, serviceName);
    _loginSocket();
    getSettingJitsi();
    return authInfo;
  }

  Future<Authentication> loginWithInfo(String username, String password) async {
    showLoading();
    try {
      final authInfo = await repositoryChat.authApi.login(username, password);
      await _loginSocket();
      return authInfo;
    } catch (e) {
      handlerApiError(e);
      throw e;
    } finally {
      completeLoading();
    }
  }

  _loginSocket() async {
    final authInfo = await getAuthInfo();
    final socket = repositoryChat.socket;
    if (authInfo != null) {
      await socket.loginWithAuthToken(authInfo);
      _subCommonSocketEvent(authInfo);
      await _sendPushToken();
    }
  }

  _sendPushToken() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    var token = OneSignal.User.pushSubscription.token;
    return sendPushToken(token, packageInfo.packageName);
  }

  _subCommonSocketEvent(Authentication authInfo) {
    final socket = repositoryChat.socket;
    final userId = authInfo.data?.userId;
    socket.streamNotifyUserSubscribe(authInfo.data!.me!);
    if (userId != null) {
      socket.streamRoomsChangeSubscribe(userId);
      socket.streamSubscriptionChange(userId);
    }
  }

  Future sendPushToken(String? token, String packageInfo) async {
    logger.t(token, error: packageInfo);
    var authInfo = await getAuthInfo();
    if (authInfo == null || token == null || token.isEmpty) return;
    try {
      return repositoryChat.roomApi.pushToken(
          Platform.isIOS ? 'apn' : 'gcm', token, packageInfo, authInfo);
    } catch (e) {
      handlerApiError(e);
    } finally {
      completeLoading();
    }
  }

  Future<Authentication?> getAuthInfo() async {
    Authentication? authInfo = await repositoryChat.authApi.getAuthInfo();
    authToken = authInfo?.data?.authToken ?? "";
    userId = authInfo?.data?.userId ?? "";
    return authInfo;
  }

  Future<String?> getCurrentUserId() async {
    var authInfo = await getAuthInfo();
    return authInfo?.data?.me?.id;
  }

  Future updateProfile({String? fullName, String? avatar}) async {
    final authInfo = await getAuthInfo();
    await repositoryChat.userApi.updateProfile(fullName, authInfo);
    await repositoryChat.userApi.setAvatar(avatar, authInfo);
  }

  Future<RocketChatUser?> getUserInfo(String loginName) async {
    showLoading();
    try {
      final authInfo = await getAuthInfo();
      if (authInfo == null) {
        throw 'Lỗi xác thực tài khoản';
      }
      final userInfo =
          await repositoryChat.userApi.getUserInfo(loginName, authInfo);

      return userInfo;
    } catch (e) {
      handlerApiError(e);
    } finally {
      completeLoading();
    }
    return Future.value(null);
  }

  @override
  void handlerApiError(error, {BehaviorSubject? behavior}) {
    super.handlerApiError(error, behavior: behavior);
    if (error is RocketChatError) {
      if (error.response!.statusCode == 401) {
        logout();
      } else {
        final int? statusCode = error.response?.statusCode;
        final errMessage =
            "Dịch vụ tạm thời bị gián đoạn, Quý khách vui lòng thực hiện lại sau${statusCode != null ? ' ($statusCode)' : ''}";
        this.error.add(errMessage);
        if (behavior != null && statusCode is num) {
          LoadingResult.error(
              behavior: behavior, error: errMessage, errorCode: statusCode);
        }
      }
    }
  }

  getSettingJitsi() async {
    try {
      var authInfo = await repositoryChat.authApi.getAuthInfo();
      if (authInfo == null) return;
      final settings = await repositoryChat.roomApi.getSettings(authInfo);
      final jitsiPrefix = settings
          .firstWhere((element) => element.sId == 'Jitsi_URL_Room_Prefix');

      final jitsiSuffix = settings
          .firstWhere((element) => element.sId == 'Jitsi_URL_Room_Suffix');

      final jitsiUniqueId =
          settings.firstWhere((element) => element.sId == 'uniqueID');
      final jitsiDomain =
          settings.firstWhere((element) => element.sId == 'Jitsi_Domain');
      //Jitsi_Enabled //true
      //Jitsi_SSL //true
      //Jitsi_URL_Room_Hash //true
      if (jitsiPrefix.value != null) {
        await preferences.setJitsiPrefix(jitsiPrefix.value!);
      }
      if (jitsiSuffix.value != null) {
        await preferences.setJitsiSuffix(jitsiSuffix.value!);
      }
      if (jitsiUniqueId.value != null) {
        await preferences.setJitsiUniqueID(jitsiUniqueId.value!);
      }
      if (jitsiDomain.value != null) {
        await preferences.setJitsiDomain(jitsiDomain.value!);
      }
    } catch (e) {
      logger.e(e);
    }
  }
}
