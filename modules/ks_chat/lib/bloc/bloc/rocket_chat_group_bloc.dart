import 'package:common/ks_common.dart';
import 'package:ks_chat/bloc/bloc/chat_base_bloc.dart';
import 'package:ks_chat/ks_chat.dart';
import 'package:ks_chat/repository/preferences_impl.dart';
import 'package:ks_chat/repository/repository_chat.dart';
import 'package:ks_chat/repository/session_chat.dart';
import 'package:rocket_chat_flutter_connector/models/authentication.dart';
import 'package:rxdart/rxdart.dart';

class RocketChatGroupBloc extends ChatBaseBloc {
  RocketChatGroupBloc(
    SessionChat session,
    KsChatPreferences preferences,
    RepositoryChat repositoryChat,
    ChatSession chatSession,
  ) : super(session, preferences, repositoryChat, chatSession);
  final _members = BehaviorSubject<List<Member>>();

  Stream<List<Member>> get membersStream => _members.stream;
  String? roomId;
  String? authToken;
  String? userId;

  @override
  init() {
    super.init();
  }

  @override
  void dispose() {
    _members.close();
    super.dispose();
  }

  getUsersOfRoom({String? room}) async {
    roomId = room;
    Authentication? authentication = await getAuthInfo();
    if (roomId == null && authentication == null) return;
    repositoryChat.roomApi.getUsersOfRoom(roomId!, authentication!).then((res) {
      if (!res.member.isNullOrEmpty) _members.add(res.member!);
    }).catchError((e) {
      handlerApiError(e);
    }).whenComplete(completeLoading);
  }

  Future<RocketChatUser?> findUser(String phone, String loginName) async {
    showLoading();
    try {
      final authInfo = await getAuthInfo();
      if (authInfo == null) {
        throw 'Lỗi xác thực tài khoản';
      }
      var userFound = await getUserInfo(phone);
      if (userFound == null) {
        await Future.delayed(Duration(seconds: 2), () {});
        userFound = await getUserInfo(loginName);
      }
      return userFound;
    } catch (e) {
      handlerApiError(e);
    } finally {
      completeLoading();
    }
    return Future.value(null);
  }

  Future<RoomItemInfo?> createDirectRoom(String username) async {
    showLoading();
    try {
      final authInfo = await getAuthInfo();
      if (authInfo == null) {
        throw 'Lỗi xác thực tài khoản';
      }
      final roomInfo =
          await repositoryChat.roomApi.getDirectRoom(username, authInfo);
      return roomInfo;
    } catch (e) {
      handlerApiError(e);
    } finally {
      completeLoading();
    }
    return Future.value(null);
  }
}
