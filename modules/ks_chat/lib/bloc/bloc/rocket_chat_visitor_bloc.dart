import 'dart:async';
import 'dart:convert';

import 'package:common/ks_common.dart';
import 'package:common/model/feeds/index.dart';
import 'package:common/repository/strapi/index.dart';
import 'package:ks_chat/bloc/bloc/chat_base_bloc.dart';
import 'package:ks_chat/bloc/model/base_item.dart';
import 'package:ks_chat/bloc/model/chat_model.dart';
import 'package:ks_chat/bloc/model/message_model.dart';
import 'package:ks_chat/bloc/model/msg_rocket_chat_response.dart';
import 'package:ks_chat/repository/chat/rocket_chat_error.dart';
import 'package:ks_chat/repository/chat/rocket_chat_models.dart';
import 'package:ks_chat/repository/preferences_impl.dart';
import 'package:ks_chat/repository/repository_chat.dart';
import 'package:ks_chat/repository/session_chat.dart';
import 'package:rocket_chat_flutter_connector/models/response/message_new_response.dart';
import 'package:rocket_chat_flutter_connector/web_socket/notification_type.dart';
import 'package:rxdart/rxdart.dart';

import 'chat_session.dart';

class RocketChatVisitorBloc extends ChatBaseBloc {
  RocketChatVisitorBloc(
    KsChatPreferences preferences,
    RepositoryChat repositoryChat,
    SessionChat session,
    ChatSession chatSession,
  ) : super(session, preferences, repositoryChat, chatSession);

  //#region VARIABLES
  final _isOffline = BehaviorSubject<bool>.seeded(false);

  Stream<bool> get isOfflineStream => _isOffline.stream;

  String wellComeMessage =
      "Kính chào quý khách, Quý khách vui lòng để lại tin nhắn để đội ngũ chuyên viên tư vấn chi tiết";

  final _messages = BehaviorSubject<LoadingWidgetModel>();

  final _room = BehaviorSubject<BaseItem>();

  Stream<BaseItem> get roomStream => _room.stream;

  BaseItem? get currentRoom => _room.valueOrNull;

  final _chatBotElements = BehaviorSubject<List<ElementButton>?>();

  List<ElementButton>? get chatBotElements => _chatBotElements.valueOrNull;

  Stream<List<ElementButton>?> get chatBotElementStream =>
      _chatBotElements.stream;

  Stream<LoadingWidgetModel> get messageStream => _messages.stream;

  final _agentInfo = BehaviorSubject<AgentModel>();

  AgentModel? get agentInfo => _agentInfo.valueOrNull;

  Stream<AgentModel> get agentInfoStream => _agentInfo.stream;

  String? _roomId;
  static const collection_stream_room_messages = 'stream-room-messages';

  Visitor? visitor;
  String? visitorId;
  String? visitorToken;
  String? _email;
  String? _name;

  @override
  void init() async {
    super.init();
    _listenMessageSocket();

    streamSubs.add(
      isTypingStream.listen(
        (value) {
          if ((_roomId?.length ?? 0) > 0 &&
              visitor != null &&
              visitorToken != null) {
            repositoryChat.socket.liveChatSendTyping(
              _roomId,
              visitor?.username,
              visitorToken,
              value,
            );
          }
        },
      ),
    );
  }

  //{"msg":"changed","collection":"stream-notify-room","id":"id","fields":{"eventName":"2ndkF6QMLp5PXHMb4/typing","args":["taint",true]}}
  _listenMessageSocket() {
    final streamSub = repositoryChat.socket.messageStream.listen((event) async {
      final eventName = event.fields?.eventName ?? '';
      final isChangeType = event.msg == NotificationType.CHANGED;
      final isChangeRoom = event.collection == collection_stream_room_messages;
      final isCurrentRoom = eventName == _roomId;
      if (isChangeType && isChangeRoom && isCurrentRoom) {
        _handlerMessageRoomEvent(event);
      } else if (eventName == '$_roomId/typing') {
        handlerTyping(event);
      }
    });
    streamSubs.add(streamSub);
  }

  _handlerMessageRoomEvent(MsgRocketChatResponse event) async {
    final model = _getCurrentMessages();
    final newMessages = List<MessageModel>.from(
      event.fields!.rawArgs
          .map(
            (x) => VisitorChatMessage.fromJson(x),
          )
          .map((y) => MessageModel.fromVisitorMessage(
              y, visitorId, getUrlAvatar, chatUrl)),
    );
    model.insertAll(0, newMessages);
    LoadingResult.success(data: model, behavior: _messages);
  }

  subCommonSocketEvent(bool sub) {
    final socket = repositoryChat.socket;
    if (visitorToken != null && visitorToken!.length > 0) {
      socket.liveChatSetupConnection(visitorToken);
      if (_roomId != null && _roomId!.length > 0) {
        socket.liveChatListenRoom(_roomId, visitorToken, sub);
        socket.liveChatListenMessages(_roomId, visitorToken, sub);
        socket.liveChatListenDeleteMessage(_roomId, visitorToken, sub);
        socket.liveChatListenTyping(_roomId, visitorToken, sub);
      }
    }
  }

  @override
  void reload() {
    super.reload();
    if (visitorToken != null) {
      getChatHistory();
    }
    subCommonSocketEvent(true);
  }

  @override
  void dispose() {
    stopTyping();
    _messages.close();
    _room.close();
    _isOffline.close();
    _chatBotElements.close();
    _agentInfo.close();
    super.dispose();
  }

  uploadFile(MessageModel? messageModel) async {
    if (messageModel == null || messageModel.attachments.isNullOrEmpty) return;
    final message = MessageModel(
      status: DeliveryStatus.local,
      type: MessageType.text,
      isMySide: true,
      time: DateTime.now(),
      attachments: messageModel.attachments,
    );
    final model = _getCurrentMessages();
    try {
      bool response = await repositoryChat.messageApi.uploadFileLiveChat(
          messageModel.attachments!.first.titleLink!,
          _roomId!,
          visitorToken ?? "");
      logger.t("Message send success : $response");

      message.status = DeliveryStatus.sent;
      LoadingResult.success(data: model, behavior: _messages);
    } catch (e) {
      handlerApiError(e);
    }
  }

  Future<bool?> sendMessage(String? roomId, String? text,
      {String? userId}) async {
    if (text.isNullOrEmpty) return null;
    dynamic env = await preferences.getEnvironment();
    if (_isOffline.valueOrNull != null && _isOffline.valueOrNull!) {
      return repositoryChat.messageApi
          .sendOfflineMessage(
            _name,
            _email,
            text,
            host: env?.chatServerUrl.toString(),
          )
          .then((value) => value)
          .catchError((e) {
        handlerApiError(e);
        return false;
      });
    } else if (roomId != null && roomId.isNotEmpty) {
      // final message = MessageModel(
      //   status: DeliveryStatus.local,
      //   type: MessageType.text,
      //   payload: text,
      //   isMySide: true,
      //   time: DateTime.now(),
      // );
      // final model = _getCurrentMessages();
      // model.insert(0, message);
      // LoadingResult.success(data: model, behavior: _messages);
      try {
        MessageNewResponse response = await repositoryChat.messageApi
            .sendVisitorMessage(userId, roomId, text!);
        logger.t("Message send success : ${response.success}");
      } catch (e) {
        handlerApiError(e);
      }
      // message.status = DeliveryStatus.sent;
      // LoadingResult.success(data: model, behavior: _messages);
    }
    return false;
  }

  Future<VideoCallModel?> callOmniChannel() async {
    showScreenLoading(maxApi: 1, isSubmit: true);
    //String token = await preferences.userId;
    //String roomId = await preferences.getRoomChatId();

    return repositoryChat.roomApi
        .videoCallMessage(visitorToken, roomId: _roomId)
        .catchError((e) {
      handlerApiError(e);
      return null;
    }).whenComplete(() => completeScreenLoading(isSubmit: true));
  }

  Future<String?> chatMiniApp(
    String fullName,
    String email,
    String token,
    String phone,
    String department,
    String? roomId,
  ) async {
    _email = email;
    _name = fullName;
    visitorToken = token;
    try {
      final roomApi = repositoryChat.roomApi;
      visitor = await roomApi.createVisitor(
        fullName,
        email,
        token,
        phone,
        department,
      );
      visitorId = visitor?.sId;
      final response = await roomApi.createRoom(token, roomId: roomId);
      roomId = response.sId;
      await getAgentInfo(token, roomId);

      await preferences.setRoomChatId(
        department,
        RoomChatModel(
          id: roomId,
          roomName: agentInfo?.name ?? '',
          time: response.t,
        ),
      );
      BaseItem item = BaseItem<Room>(
        title: agentInfo?.name ?? '',
        subTitle: agentInfo?.username ?? '',
        id: roomId,
        data: response,
        image: getUrlAvatar(agentInfo?.username ?? ''),
        type: response.t,
      );
      safeAddData(_room, item);
      _roomId = roomId;
      safeAddData(firstLoadComplete, true);
      firstLoadComplete.close();
      return roomId;
    } catch (e) {
      handlerApiError(e, behavior: _messages);
      return null;
    } finally {
      completeLoading();
    }
  }

  getAgentInfo(String token, String? roomId) async {
    try {
      final roomApi = repositoryChat.roomApi;
      final agentInfo = await roomApi.getAgentInfo(token, roomId: roomId);
      _agentInfo.add(agentInfo);
    } catch (e) {
      logger.e(e);
    }
  }

  Future getChatHistory() async {
    LoadingResult.loading(behavior: _messages);
    try {
      final messages = await repositoryChat.messageApi.loadLiveChatHistory(
        _roomId,
        visitorToken ?? '',
      );

      final model = messages
          .map(
            (e) => MessageModel.fromVisitorMessage(
              e,
              visitorId,
              getUrlAvatar,
              chatUrl,
            ),
          )
          .toList();
      if (model.length == 2) {
        model.insert(
          0,
          MessageModel(
            type: MessageType.text,
            time: DateTime.now(),
            isMySide: false,
            payload: wellComeMessage,
          ),
        );
      }
      LoadingResult.success(data: model, behavior: _messages);
    } catch (e) {
      handlerApiError(e, behavior: _messages);
    } finally {
      completeLoading();
    }
  }

  @override
  handlerApiError(e, {BehaviorSubject? behavior}) {
    super.handlerApiError(e);
    if (e is RocketChatError) {
      if (e.response != null && e.response?.body.isNotEmpty == true) {
        String message = "";
        RocketChatErrorResponse response =
            RocketChatErrorResponse.fromJson(jsonDecode(e.response!.body));
        if (response.error?.contains("no-agent-online") == true) {
          message = response.error!;
          safeAddData(_room, BaseItem(title: "Offline"));
          safeAddData(_isOffline, true);
        } else {
          message = response.reason ?? response.message ?? response.error ?? '';
        }
        if (!_room.isClosed) {
          error.add(message);
        }
        if (behavior != null) {
          LoadingResult.error(behavior: behavior, error: message);
        }
      }
    }
  }

  sendStatus(String currentStatus) {
    if (visitorToken != null && visitorToken!.length > 0) {
      return repositoryChat.messageApi
          .sendVisitorStatus(visitorToken!, currentStatus)
          .catchError((e) {
        handlerApiError(e);
        return false;
      });
    }
  }

  Future<bool> closeRoomVisitor() async {
    showLoading();
    try {
      final success = await repositoryChat.messageApi
          .closeRoomVisitor(_roomId, visitorToken);
      return success;
    } catch (e) {
      handlerApiError(e);
      return false;
    } finally {
      completeLoading();
    }
  }

  Future<ChatBotMessage?> getChatBotMessage(
      {String? payload, bool? isAddToList}) async {
    final response =
        await repositoryChat.strApi.getChatBotMessages(payload: payload);
    final firstItem = response.firstOrNull;
    _chatBotElements.add(firstItem?.elements ?? []);

    if (isAddToList == true) {
      final message = MessageModel(
        status: DeliveryStatus.sent,
        type: MessageType.chatBot,
        payload: firstItem?.content,
        isMySide: false,
        time: DateTime.now(),
        attachments: firstItem?.attachments
                ?.map(
                  (e) => AttachmentModel(
                      type: e.type,
                      videoUrl: e.url,
                      imageUrl: e.thumbnail?.getImage(),
                      imageUrlLarge:
                          e.thumbnail?.getImage(imageSize: ImageSize.large)),
                )
                .toList() ??
            [],
      );
      final model = _getCurrentMessages();
      model.insert(0, message);
      LoadingResult.success(data: model, behavior: _messages);
    }
    return firstItem;
  }

  List<MessageModel> _getCurrentMessages() {
    return (_messages.valueOrNull?.data ?? <MessageModel>[])
        as List<MessageModel>;
  }
}
