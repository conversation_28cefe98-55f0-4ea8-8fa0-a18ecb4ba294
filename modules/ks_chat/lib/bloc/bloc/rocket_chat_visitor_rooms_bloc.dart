import 'package:collection/collection.dart';
import 'package:common/ks_common.dart';
import 'package:ks_chat/bloc/bloc/chat_base_bloc.dart';
import 'package:ks_chat/bloc/model/message_model.dart';
import 'package:ks_chat/bloc/model/msg_rocket_chat_response.dart';
import 'package:ks_chat/repository/chat/rocket_chat_models.dart';
import 'package:ks_chat/repository/preferences_impl.dart';
import 'package:ks_chat/repository/repository_chat.dart';
import 'package:ks_chat/repository/session_chat.dart';
import 'package:rocket_chat_flutter_connector/web_socket/notification_type.dart';
import 'package:rxdart/rxdart.dart';

import 'chat_session.dart';

class RocketChatVisitorRoomsBloc extends ChatBaseBloc {
  RocketChatVisitorRoomsBloc(
      KsChatPreferences preferences,
      RepositoryChat repositoryChat,
      SessionChat session,
      ChatSession chatSession)
      : super(session, preferences, repositoryChat, chatSession);

  final rooms = BehaviorSubject<List<RoomItemInfo>>.seeded([]);
  final visitorUnread = BehaviorSubject<int>();

  static const collection_stream_room_messages = 'stream-room-messages';
  late String jitsiPrefixStr;
  late String jitsiUniqueIdStr;

  String? visitorToken;

  final onNewLiveMessages = PublishSubject<List<MessageModel>>();

  final livechatDepartments = BehaviorSubject<List<Department?>>();

  int _totalUnread = 0;

  @override
  void init() async {
    super.init();
    _listenMessageSocket();
    jitsiPrefixStr = await preferences.getJitsiPrefix();
    jitsiUniqueIdStr = await preferences.getJitsiUniqueID();
    getLiveDepartments();
  }

  getVisitorRooms(String? visitorToken) async {
    if (visitorToken == null || visitorToken.isEmpty) return;
    final authInfo = await getAuthInfo();
    this.visitorToken = visitorToken;
    final newRooms =
        await repositoryChat.roomApi.getVisitorRooms(authInfo, visitorToken);
    _totalUnread = 0;
    for (var i = 0; i < newRooms.length; i++) {
      int unRead = await preferences.getUnReadVisitor(newRooms[i].id!);
      _totalUnread += unRead;
      final room = newRooms[i]..unRead = unRead;
      subCommonSocketEvent(room.id, visitorToken, true);
    }
    logger.t(newRooms.length);
    visitorUnread.add(_totalUnread);
    rooms.add(newRooms);
  }

  _listenMessageSocket() {
    final streamSub = repositoryChat.socket.messageStream.listen((event) async {
      final isChangeType = event.msg == NotificationType.CHANGED;
      final isChangeRoom = event.collection == collection_stream_room_messages;
      if (isChangeType && isChangeRoom) {
        _handlerMessageRoomEvent(event);
      }
    });
    streamSubs.add(streamSub);
  }

  RoomItemInfo? getRoomForDepartment(String department) {
    final currentRooms = rooms.valueOrNull ?? <RoomItemInfo>[];
    try {
      RoomItemInfo room = currentRooms
          .firstWhere((element) => element.departmentId == department);
      return room;
    } catch (e) {
      return null;
    }
  }

  RoomItemInfo? getLocalRoom(String? roomId) {
    final currentRooms = rooms.valueOrNull ?? <RoomItemInfo>[];
    try {
      return currentRooms.firstWhere((element) => element.id == roomId);
    } catch (e) {
      return null;
    }
  }

  refreshRooms() {
    final currentRooms = rooms.valueOrNull ?? <RoomItemInfo>[];
    visitorUnread.add(_totalUnread);
    rooms.add(currentRooms);
  }

  _handlerMessageRoomEvent(
    MsgRocketChatResponse event,
  ) async {
    final newMessages = List<MessageModel>.from(
      event.fields!.rawArgs
          .map(
            (x) => VisitorChatMessage.fromJson(x),
          )
          .where((item) => item.t != "command" && item.t != "livechat-close")
          .map(
            (y) => MessageModel.fromVisitorMessage(
                y, visitorToken, getUrlAvatar, chatUrl),
          ),
    );
    onNewLiveMessages.add(newMessages);
    for (var element in newMessages) {
      final room = getLocalRoom(element.roomId);
      if (room != null) {
        room.lastMessage?.msg = element.payload;
        if (!ChatBaseBloc.inChat) {
          int unRead = await preferences.getUnReadVisitor(room.id!);
          unRead += 1;
          _totalUnread += 1;
          room.unRead = unRead;
          preferences.setUnReadVisitor(unRead.toString(), room.id!);
        }
      }
    }
    refreshRooms();
  }

  subCommonSocketEvent(String? roomId, String? visitorToken, bool sub) {
    final socket = repositoryChat.socket;
    if (visitorToken != null && visitorToken.length > 0) {
      socket.liveChatSetupConnection(visitorToken);
      if (roomId != null && roomId.length > 0) {
        socket.liveChatListenRoom(roomId, visitorToken, sub);
        socket.liveChatListenMessages(roomId, visitorToken, sub);
        socket.liveChatListenDeleteMessage(roomId, visitorToken, sub);
      }
    }
  }

  Department? getLocalDepartment(String departmentId) {
    final department = (livechatDepartments.valueOrNull ?? []).firstWhereOrNull(
      (element) => element?.sId == departmentId,
    );
    return department;
  }

  getLiveDepartments() async {
    String? token = visitorToken?.isNotEmpty == true
        ? visitorToken
        : await preferences.userId;
    final departments = await repositoryChat.roomApi.getDepartments(token);
    livechatDepartments.add(departments);
  }

  Future<bool> closeRoomVisitor(RoomItemInfo room) async {
    showLoading();
    try {
      final success = await repositoryChat.messageApi.closeRoomVisitor(
        room.id,
        room.v?.token,
      );
      getVisitorRooms(room.v?.token);
      return success;
    } catch (e) {
      handlerApiError(e);
      return false;
    } finally {
      completeLoading();
    }
  }

  void refreshUnRead(String id) async {
    int oldUnRead = await preferences.getUnReadVisitor(id);
    _totalUnread -= oldUnRead;
    preferences.setUnReadVisitor('0', id);
    final unRead = 0;
    final room = getLocalRoom(id);
    room?.unRead = unRead;
    preferences.setUnReadVisitor(unRead.toString(), id);
    refreshRooms();
  }
}
