import 'package:common/ks_common.dart';
import 'package:ks_chat/repository/chat/rocket_chat_authentication.dart';
import 'package:ks_chat/repository/entity/socket_status.dart';
import 'package:ks_chat/repository/repository_chat.dart';
import 'package:rxdart/rxdart.dart';

class ChatSession extends CommonBaseBloc {
  RepositoryChat repositoryChat;

  ChatSession(this.repositoryChat) {
    init();
  }

  BehaviorSubject<ChatLoginStatus> _authInfo =
      BehaviorSubject<ChatLoginStatus>();

  Stream<ChatLoginStatus> get authChange => _authInfo.stream.distinct();
  ChatLoginStatus? get authStatus => _authInfo.valueOrNull;

  @override
  void init() async {
    super.init();

    streamSubs
        .add(Rx.combineLatest2<SocketEvent, ChatLoginStatus, ChatLoginStatus>(
      repositoryChat.socket.eventStream,
      repositoryChat.authApi.authApiChange,
      (a, b) {
        if (a.status == SocketStatus.authenticated &&
            b == ChatLoginStatus.LOGGED) {
          return b;
        }
        return ChatLoginStatus.LOGOUT;
      },
    ).listen((event) {
      _authInfo.add(event);
    }));
  }

  @override
  dispose() {
    super.dispose();
    _authInfo.close();
  }
}
