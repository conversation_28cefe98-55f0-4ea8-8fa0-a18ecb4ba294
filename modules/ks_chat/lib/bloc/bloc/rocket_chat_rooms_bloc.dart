import 'package:collection/collection.dart';
import 'package:common/ks_common.dart';
import 'package:ks_chat/bloc/bloc/chat_base_bloc.dart';
import 'package:ks_chat/bloc/model/base_item.dart';
import 'package:ks_chat/bloc/model/msg_rocket_chat_response.dart';
import 'package:ks_chat/bloc/push_utils.dart';
import 'package:ks_chat/repository/chat/rocket_chat_models.dart';
import 'package:ks_chat/repository/preferences_impl.dart';
import 'package:ks_chat/repository/repository_chat.dart';
import 'package:ks_chat/repository/session_chat.dart';
import 'package:rocket_chat_flutter_connector/web_socket/notification_args.dart';
import 'package:rxdart/rxdart.dart';
import 'package:flutter/material.dart';

import 'chat_session.dart';

class RocketChatRoomsBloc extends ChatBaseBloc {
  RocketChatRoomsBloc(
    KsChatPreferences preferences,
    RepositoryChat repositoryChat,
    SessionChat session,
    ChatSession chatSession,
  ) : super(session, preferences, repositoryChat, chatSession);

  //#region VARIABLES
  final _myRooms = BehaviorSubject<LoadingWidgetModel>();
  final _reConnect = BehaviorSubject<LoadingWidgetModel>();
  final _dropConnect = BehaviorSubject<bool>.seeded(false);

  Stream<bool> get dropConnect => _dropConnect.stream;

  Sink<bool> get setDropConnect => _dropConnect.sink;

  Stream<LoadingWidgetModel> get myRoomsStream => _myRooms.stream;

  Stream<LoadingWidgetModel> get reConnectStream => _reConnect.stream;

  final _totalUnread = BehaviorSubject<int>();

  Stream<int> get totalUnreadStream => _totalUnread.stream;

  int get totalUnread => _totalUnread.valueOrNull ?? 0;

  final List<BaseItem<RoomItemInfo>?> chatInProgress = [];
  final List<BaseItem<RoomItemInfo>?> chatPublic = [];
  final List<BaseItem<RoomItemInfo>?> chatDirect = [];
  final List<BaseItem<RoomItemInfo>?> chatPrivate = [];

  static const collection_stream_notify_user = 'stream-notify-user';

  bool hasRooms() {
    final rooms = _myRooms.valueOrNull?.data ?? [];
    return rooms != null && rooms.length > 0;
  }

  @override
  void init() async {
    super.init();
    _listenMessageSocket();
  }

  @override
  reload() {
    super.reload();
    setDropConnect.add(false);
    getRooms();
  }

  _listenMessageSocket() {
    final streamSub = repositoryChat.socket.messageStream.listen((event) async {
      final eventName = event.fields?.eventName ?? '';
      logger.t(eventName);
      if (eventName.endsWith('rooms-changed')) {
        final args = event.fields?.rawArgs ?? [];

        if (args is Iterable && args.length > 1) {
          final roomChangeType = args.elementAt(0);
          final roomChangeValue = args.elementAt(1);
          final room = RoomItemInfo.fromMap(roomChangeValue);
          var authInfo = await getAuthInfo();
          final currentUserName = authInfo?.data?.me?.username;
          final model = _getRoomModel(room, currentUserName: currentUserName);
          if (room.t == null) return;
          final currentRoom = _findRoom(
            room.t,
            room.id,
            autoAdd: true,
            model: model,
          );

          List<RoomItemInfo> data = _myRooms.valueOrNull?.data ?? [];
          if (currentRoom == null || currentRoom.id == null) {
            data.add(room);
          }
          if (currentRoom != null) {
            currentRoom.title = currentRoom.title ?? model?.title ?? '';
            currentRoom.subTitle = model?.subTitle;
            currentRoom.id = model?.id;
            currentRoom.data = model?.data;
            currentRoom.image = model?.image;
          }
          _sortRooms();
          LoadingResult.success(data: data, behavior: _myRooms);
        }
      }
      //FSnihYCLJqtvMFMxx/notification
      else if (eventName.endsWith('/notification')) {
        _handlerStreamNotifyUser(event);
      } else if (eventName.endsWith('/subscriptions-changed')) {
        final args = event.fields?.rawArgs ?? [];
        if (args is Iterable && args.length > 1) {
          final roomChangeType = args.elementAt(0);
          final roomChangeValue = args.elementAt(1);
          final room = RoomItemSubscription.fromMap(roomChangeValue);
          if (room.t == null) return;
          BaseItem<RoomItemInfo>? currentRoom = _findRoom(room.t, room.rid);
          if (currentRoom != null) {
            List<RoomItemInfo> data = _myRooms.valueOrNull?.data ?? [];
            currentRoom.trailing = (room.unread ?? 0).toString();
            _sortRooms();
            LoadingResult.success(data: data, behavior: _myRooms);
          }
        }
      }
    });
    streamSubs.add(streamSub);
  }

  _sortRooms() {
    chatInProgress.sort(_compare);
    chatPublic.sort(_compare);
    chatDirect.sort(_compare);
    chatPrivate.sort(_compare);
    _updateUnRead();
  }

  _updateUnRead() {
    int total = 0;
    chatInProgress.forEach((element) {
      total += toInt(element?.trailing);
    });
    chatPublic.forEach((element) {
      total += toInt(element?.trailing);
    });
    chatDirect.forEach((element) {
      total += toInt(element?.trailing);
    });
    chatPrivate.forEach((element) {
      total += toInt(element?.trailing);
    });
    _totalUnread.add(total);
  }

  int _compare(BaseItem<RoomItemInfo>? a, BaseItem<RoomItemInfo>? b) {
    var dateA = a?.data?.lm?.date ?? a?.data?.updatedAt?.date ?? 0;
    var dateB = b?.data?.lm?.date ?? b?.data?.updatedAt?.date ?? 0;
    return dateB - dateA;
  }

  BaseItem<RoomItemInfo>? _findRoom(
    RoomType? t,
    String? id, {
    bool? autoAdd,
    BaseItem<RoomItemInfo>? model,
  }) {
    BaseItem<RoomItemInfo>? currentRoom;
    if (t == null) return null;
    switch (t) {
      case RoomType.D:
        currentRoom = chatDirect.firstWhere(
          (element) => element?.id == id,
          orElse: () => BaseItem(),
        );
        if (currentRoom?.id == null && autoAdd == true) chatDirect.add(model);
        break;
      case RoomType.L:
        currentRoom = chatInProgress.firstWhere(
          (element) => element?.id == id,
          orElse: () => BaseItem(),
        );
        if (currentRoom?.id == null && autoAdd == true)
          chatInProgress.add(model);
        break;
      case RoomType.C:
        currentRoom = chatPublic.firstWhere(
          (element) => element?.id == id,
          orElse: () => BaseItem(),
        );
        if (currentRoom?.id == null && autoAdd == true) chatPublic.add(model);
        break;
      case RoomType.P:
        currentRoom = chatPrivate.firstWhere(
          (element) => element?.id == id,
          orElse: () => BaseItem(),
        );
        if (currentRoom?.id == null && autoAdd == true) chatPrivate.add(model);
        break;
    }
    return currentRoom;
  }

  _handlerStreamNotifyUser(MsgRocketChatResponse event) async {
    final args = event.fields!.rawArgs;
    if (args is Iterable && args.length > 0) {
      final newMessages = List<NotificationArgs>.from(
        args.map((x) => NotificationArgs.fromMap(x)),
      );
      if (!ChatBaseBloc.inChat) {
        newMessages.forEach((element) {
          PushUtils.sendPushChat(
              message: element.text,
              title: element.title,
              roomId: element.payload?.rid,
              messageId: element.payload?.id,
              roomType: element.payload?.type);
        });
      }
    }
  }

  //#endregion
  Future getRooms() async {
    LoadingResult.loading(behavior: _myRooms);
    try {
      var authInfo = await getAuthInfo();
      if (authInfo == null) return;
      final rooms = await repositoryChat.roomApi.getRooms(authInfo);
      final roomsSubs = await repositoryChat.roomApi
          .getRoomsSubscriptions(authInfo)
          .catchError((e) {
        logger.e(e);
      });
      await _mapRooms(rooms, roomsSubs);
      _sortRooms();
      LoadingResult.success(data: rooms, behavior: _myRooms);
    } catch (e) {
      handlerApiError(e, behavior: _myRooms);
    } finally {
      completeLoading();
    }
  }

  BaseItem<RoomItemInfo>? _getRoomModel(
    RoomItemInfo room, {
    String? currentUserName,
    RoomItemSubscription? roomSub,
  }) {
    final lastMessage = room.lastMessage?.msg ?? '';
    final roomId = room.id;
    if (room.t == null) return null;
    final title = roomSub?.fname;
    final unreadCount = roomSub?.unread?.toString() ?? '';
    switch (room.t!) {
      case RoomType.D:
        String? friendUserName = room.usernames?.firstWhereOrNull(
            (element) => element != null && element != currentUserName);

        return BaseItem<RoomItemInfo>(
          title: title ?? friendUserName ?? '',
          subTitle: lastMessage,
          id: roomId,
          data: room,
          image: getUrlAvatar(friendUserName),
          trailing: unreadCount,
        );
      case RoomType.L:
        return BaseItem<RoomItemInfo>(
          title: title ?? room.fname,
          subTitle: lastMessage,
          id: roomId,
          data: room,
          image: getUrlAvatar(room.fname ?? ""),
          trailing: unreadCount,
        );
      case RoomType.C:
        return BaseItem<RoomItemInfo>(
          title: title ?? room.name,
          subTitle: lastMessage,
          id: roomId,
          data: room,
          image: getUrlAvatar(room.name ?? ""),
          trailing: unreadCount,
        );
      case RoomType.P:
        return BaseItem<RoomItemInfo>(
          title: title ?? room.name,
          subTitle: lastMessage,
          id: roomId,
          data: room,
          image: getUrlAvatar(room.name ?? ""),
          trailing: unreadCount,
        );
      default:
        return null;
    }
  }

  _mapRooms(List<RoomItemInfo>? rooms,
      Map<String, RoomItemSubscription>? roomsSubs) async {
    chatInProgress.clear();
    chatPublic.clear();
    chatDirect.clear();
    chatPrivate.clear();
    if (rooms == null) {
      return;
    }
    var authInfo = await getAuthInfo();
    final currentUserName = authInfo?.data?.me?.username;
    for (var room in rooms) {
      final model = _getRoomModel(
        room,
        currentUserName: currentUserName,
        roomSub: roomsSubs?[room.id],
      );

      if (model == null) continue;
      switch (room.t!) {
        case RoomType.D:
          chatDirect.add(model);
          break;
        case RoomType.L:
          chatInProgress.add(model);
          break;
        case RoomType.C:
          chatPublic.add(model);
          break;
        case RoomType.P:
          chatPrivate.add(model);
          break;
      }
    }
  }

  void removeRoomInList({String? roomId}) {
    List<RoomItemInfo> data = _myRooms.valueOrNull?.data ?? [];
    for (var room in data) {
      if (room.t == null) continue;
      switch (room.t!) {
        case RoomType.D:
          chatDirect.removeWhere((element) => element?.id == roomId);
          break;
        case RoomType.L:
          chatInProgress.removeWhere((element) => element?.id == roomId);
          break;
        case RoomType.C:
          chatPublic.removeWhere((element) => element?.id == roomId);
          break;
        case RoomType.P:
          chatPrivate.removeWhere((element) => element?.id == roomId);
          break;
      }
    }
    data.removeWhere((element) => element.id == roomId);
    LoadingResult.success(data: data, behavior: _myRooms);
  }

  Future<void> deleteRoom(RoomItemInfo data) async {
    final authInfo = await getAuthInfo();
    if (data.id?.isNotEmpty == true && authInfo != null) {
      repositoryChat.roomApi.deleteRoom(authInfo, data.id!);
      removeRoomInList(roomId: data.id);
    }
  }

  reconnectSocket(VoidCallback getVisitorMethod) {
    LoadingResult.loading(behavior: _reConnect);
    Future.delayed(const Duration(seconds: 1), () async {
      await login(ChatBaseBloc.OAUTH_SERVICE_NAME).then((value) {
        getVisitorMethod();
      }).catchError((e) {
        logger.e(e);
      });
      LoadingResult.success(behavior: _reConnect);
    });
  }
}
