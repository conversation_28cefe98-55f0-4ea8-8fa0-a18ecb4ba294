import 'package:common/ks_common.dart';
import 'package:ks_chat/bloc/bloc/chat_base_bloc.dart';
import 'package:ks_chat/bloc/model/base_item.dart';
import 'package:ks_chat/bloc/model/message_model.dart';
import 'package:ks_chat/bloc/model/msg_rocket_chat_response.dart';
import 'package:ks_chat/bloc/push_utils.dart';
import 'package:ks_chat/repository/chat/rocket_chat_message_models.dart';
import 'package:ks_chat/repository/chat/rocket_chat_models.dart';
import 'package:ks_chat/repository/preferences_impl.dart';
import 'package:ks_chat/repository/repository_chat.dart';
import 'package:ks_chat/repository/session_chat.dart';
import 'package:rocket_chat_flutter_connector/models/message_attachment.dart';
import 'package:rocket_chat_flutter_connector/models/new/message_new.dart';
import 'package:rocket_chat_flutter_connector/models/response/message_new_response.dart';
import 'package:rocket_chat_flutter_connector/web_socket/notification_args.dart';
import 'package:rocket_chat_flutter_connector/web_socket/notification_type.dart';
import 'package:rxdart/rxdart.dart';

import 'chat_session.dart';

class RocketChatBloc extends ChatBaseBloc {
  RocketChatBloc(
    KsChatPreferences preferences,
    RepositoryChat repositoryChat,
    SessionChat session,
    ChatSession chatSession,
  ) : super(session, preferences, repositoryChat, chatSession);

  final _messages = BehaviorSubject<LoadingWidgetModel>();

  final _room = BehaviorSubject<BaseItem>();

  Stream<BaseItem> get roomStream => _room.stream;

  Sink<BaseItem> get roomSink => _room.sink;

  Stream<LoadingWidgetModel> get messageStream => _messages.stream;
  String? _roomId;
  static const collection_stream_room_messages = 'stream-room-messages';

  @override
  void init() async {
    super.init();
    _listenMessageSocket();
    //userId = await preferences.userId;

    streamSubs.add(
      isTypingStream.listen(
        (value) async {
          final auth = await getAuthInfo();
          if ((_roomId?.length ?? 0) > 0 && auth != null) {
            repositoryChat.socket.sendTyping(
              _roomId,
              auth.data?.me?.name ?? userId,
              value,
            );
          }
        },
      ),
    );
  }

  setRoomId(String roomId) {
    _roomId = roomId;
    reload();
  }

  _listenMessageSocket() {
    final streamSub = repositoryChat.socket.messageStream.listen((event) async {
      final eventName = event.fields?.eventName ?? '';
      final isChangeType = event.msg == NotificationType.CHANGED;
      final isChangeRoom = event.collection == collection_stream_room_messages;
      final isCurrentRoom = eventName == _roomId;
      if (isChangeType && isChangeRoom && isCurrentRoom) {
        _handlerMessageRoomEvent(event);
      } else if (eventName.endsWith('rooms-changed')) {
      }
      //FSnihYCLJqtvMFMxx/notification
      else if (eventName.endsWith('/notification')) {
        _handlerStreamNotifyUser(event);
      } else if (eventName == '$_roomId/typing') {
        handlerTyping(event);
      }
    });
    streamSubs.add(streamSub);
  }

  _handlerStreamNotifyUser(MsgRocketChatResponse event) async {
    final args = event.fields!.rawArgs;
    if (args is Iterable && args.length > 0) {
      final newMessages = List<NotificationArgs>.from(
        args.map((x) => NotificationArgs.fromMap(x)),
      );
      if (!ChatBaseBloc.inChat) {
        newMessages.forEach((element) {
          PushUtils.sendPushChat(
              message: element.text,
              title: element.title,
              roomId: element.payload?.rid,
              messageId: element.payload?.id,
              roomType: element.payload?.type);
        });
      }
    }
  }

  _handlerMessageRoomEvent(MsgRocketChatResponse event) async {
    var userId = await getCurrentUserId();
    var model = _messages.valueOrNull?.data ?? <MessageModel>[];
    final newMessages = List<MessageModel>.from(
      event.fields!.rawArgs
          .map(
            (x) => RocketChatMessage.fromMap(x),
          )
          .map((y) =>
              MessageModel.fromRocketMessage(y, userId, getUrlAvatar, chatUrl)),
    );
    model.insertAll(0, newMessages);
    LoadingResult.success(data: model, behavior: _messages);
    setReadMessages();
  }

  @override
  void dispose() {
    super.dispose();
    _messages.close();
    _room.close();
    unListenMessageOnRom(_roomId);
  }

  @override
  reload() {
    super.reload();
    getChatHistory();
    listenMessageOnRom();
    setReadMessages();
  }

  setReadMessages() async {
    final authentication = await getAuthInfo();
    if (authentication != null && _roomId != null) {
      repositoryChat.roomApi.markAsReadDirect(_roomId!, authentication);
    }
  }

  //#endregion

  getChatHistory() async {
    if (_roomId == null) return;
    LoadingResult.loading(behavior: _messages);
    try {
      var authInfo = await getAuthInfo();
      final messages =
          await repositoryChat.messageApi.loadHistory(_roomId!, authInfo);
      final model = messages
          .map((e) => MessageModel.fromRocketMessage(
              e, authInfo?.data?.me?.id, getUrlAvatar, chatUrl))
          .toList();

      LoadingResult.success(data: model, behavior: _messages);
      safeAddData(firstLoadComplete, true);
      firstLoadComplete.close();
    } catch (e) {
      handlerApiError(e, behavior: _messages);
    } finally {
      completeLoading();
    }
  }

  uploadFile(MessageModel? messageModel) async {
    if (messageModel == null || messageModel.attachments.isNullOrEmpty) return;

    final message = MessageModel(
      status: DeliveryStatus.local,
      type: MessageType.text,
      isMySide: true,
      time: DateTime.now(),
      attachments: messageModel.attachments,
    );
    final model = _messages.valueOrNull?.data as List<MessageModel>;
    var authInfo = await getAuthInfo();
    try {
      MessageNewResponse response = await repositoryChat.messageApi.uploadFile(
          messageModel.attachments!.first.titleLink!, _roomId!, authInfo!);
      logger.t("Message send success : ${response.success}");

      message.status = DeliveryStatus.sent;
      LoadingResult.success(data: model, behavior: _messages);
    } catch (e) {
      handlerApiError(e);
    }
  }

  sendMessage(String roomId, {String? text, MessageModel? messageModel}) async {
    if (text != null && text.trim().isEmpty) return;
    final message = MessageModel(
      status: DeliveryStatus.local,
      type: MessageType.text,
      payload: text,
      isMySide: true,
      time: DateTime.now(),
      attachments: messageModel?.attachments,
    );
    final model = _messages.valueOrNull?.data as List<MessageModel>?;
    // model.insert(0, message);
    LoadingResult.success(data: model, behavior: _messages);
    MessageNew messageNew = MessageNew(
        roomId: roomId,
        text: text,
        attachments: messageModel?.attachments
            ?.map((e) => MessageAttachment.fromMap(e.toJson()))
            .toList());
    var authInfo = await getAuthInfo();
    if (authInfo == null) return;
    try {
      MessageNewResponse response =
          await repositoryChat.messageApi.postMessage(messageNew, authInfo);
      logger.t("Message send success : ${response.success}");

      message.status = DeliveryStatus.sent;
      LoadingResult.success(data: model, behavior: _messages);
    } catch (e) {
      handlerApiError(e);
    }
  }

  listenMessageOnRom() {
    if (_roomId != null && _roomId!.length > 0) {
      repositoryChat.socket.streamRoomMessagesSubscribe(_roomId!);
      repositoryChat.socket.listenTyping(_roomId, true);
    }
  }

  unListenMessageOnRom(String? roomId) {
    if (roomId != null && roomId.length > 0) {
      repositoryChat.socket.streamRoomMessagesUnsubscribe(roomId);
      repositoryChat.socket.listenTyping(roomId, false);
    }
  }

  Future jitsiUpdateTimeOut() async {
    final authInfo = await getAuthInfo();
    if (_roomId == null) return null;
    repositoryChat.messageApi
        .jitsiUpdateTimeout(_roomId!, authInfo)
        .catchError((e) {
      handlerApiError(e);
    });
  }

  void getModuleName(String departmentId) async {
    final authInfo = await getAuthInfo();
    repositoryChat.messageApi
        .getDepartmentInfo(departmentId, authInfo)
        .then((department) {
      BaseItem? currentRoom = _room.valueOrNull;
      if (currentRoom != null) {
        BaseItem newData = currentRoom;
        newData.departmentId = department.name;
        _room.add(newData);
      }
    }).catchError((e) {
      handlerApiError(e);
    });
  }

  Future<bool> closeRoom({RoomType? roomType}) async {
    showLoading();
    try {
      final authInfo = await getAuthInfo();
      if (userId == null || _roomId == null) return false;
      final success = await repositoryChat.messageApi
          .closeRoomAgent(userId!, _roomId!, authInfo);
      return success;
    } catch (e) {
      handlerApiError(e);
      return false;
    } finally {
      completeLoading();
    }
  }
}
