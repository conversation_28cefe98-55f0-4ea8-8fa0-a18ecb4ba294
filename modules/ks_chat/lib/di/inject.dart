import 'package:common/ks_common.dart';
import 'package:common/repository/strapi/index.dart';
import 'package:flutter_simple_dependency_injection/injector.dart';
import 'package:ks_chat/bloc/bloc/rocket_chat_group_bloc.dart';
import 'package:ks_chat/ks_chat.dart';
import 'package:ks_chat/repository/session_chat.dart';
import 'package:rocket_chat_flutter_connector/services/channel_service.dart';
import 'package:rocket_chat_flutter_connector/services/subscription_service.dart';

class Injection {
  static RepositoryChat injectChat(
    Injector injector,
    KsChatPreferences preferences,
    SessionChat _session,
    Environment env,
    StrApi strApi,
  ) {
    final httpService = RocketChatHttpService(
      env.chatServerUrl,
      Uri.parse(env.cloudApiUrl),
    );
    final authenticationService =
        RocketChatAuthentication(httpService, preferences);
    final channelService = ChannelService(httpService);
    final messageService = RocketChatMessageApi(httpService);
    final subscriptionService = SubscriptionService(httpService);
    final roomService = RocketChatRoomApi(httpService);
    final userService = RocketChatUserApi(httpService);

    final rocketChatSocket = RocketChatSocket(env.chatSocketServer);
    final chatRepository = RepositoryChatImpl(
      authApi: authenticationService,
      channelApi: channelService,
      messageApi: messageService,
      subscriptionApi: subscriptionService,
      roomApi: roomService,
      userApi: userService,
      socket: rocketChatSocket,
      httpService: httpService,
      strApi: strApi,
    );
    final chatSession = ChatSession(chatRepository);
    injector.map<ChatSession>(
      (injector) => chatSession,
      isSingleton: true,
    );

    injector.map<RepositoryChat>(
      (i) => chatRepository,
      isSingleton: true,
    );

    injector.map<RocketChatBloc>(
      (injector) =>
          RocketChatBloc(preferences, chatRepository, _session, chatSession),
    );

    injector.map<RocketChatVisitorBloc>(
      (injector) => RocketChatVisitorBloc(
          preferences, chatRepository, _session, chatSession),
    );

    injector.map<RocketChatVisitorRoomsBloc>(
      (injector) => RocketChatVisitorRoomsBloc(
          preferences, chatRepository, _session, chatSession),
      isSingleton: true,
    );

    injector.map<RocketChatGroupBloc>(
      (injector) => RocketChatGroupBloc(
          _session, preferences, chatRepository, chatSession),
    );

    injector.map<RocketChatRoomsBloc>(
      (injector) => RocketChatRoomsBloc(
          preferences, chatRepository, _session, chatSession),
      isSingleton: true,
    );
    return chatRepository;
  }
}
