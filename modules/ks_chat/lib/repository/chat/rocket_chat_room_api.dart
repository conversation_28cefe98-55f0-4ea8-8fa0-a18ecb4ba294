import 'dart:convert';

import 'package:common/ks_common.dart';
import 'package:common/utils/log.dart';
import 'package:http/http.dart' as http;
import 'package:ks_chat/repository/chat/rocket_chat_http_service.dart';
import 'package:ks_chat/repository/chat/rocket_chat_models.dart';
import 'package:rocket_chat_flutter_connector/exceptions/exception.dart';
import 'package:rocket_chat_flutter_connector/models/authentication.dart';
import 'package:rocket_chat_flutter_connector/models/response/response.dart';
import 'package:rocket_chat_flutter_connector/services/http_service.dart';
import 'package:rocket_chat_flutter_connector/services/room_service.dart';
import 'package:uuid/uuid.dart';

class RocketChatRoomApi extends RoomService {
  late RocketChatHttpService _httpService;

  RocketChatRoomApi(RocketChatHttpService httpService)
      : super(httpService as HttpService) {
    this._httpService = httpService;
  }

  String getID() {
    return Uuid().v1();
  }

  Future<List<RoomItemInfo>> getRooms(
    Authentication? authentication,
  ) async {
    http.Response response = await _httpService.methodPost(
      '/api/v1/method.call/rooms:get',
      r'''{"message":"{\"msg\":\"method\",\"method\":\"rooms/get\",\"params\":[],\"id\":\"10\"}"}''',
      authentication: authentication,
    );
    if (response.body.isNotEmpty == true) {
      final baseResponse =
          RocketChatBaseResponse.fromMap(jsonDecode(response.body));
      if (baseResponse.success != null && baseResponse.success!) {
        final data = jsonDecode(utf8.decode(baseResponse.message!.codeUnits));
        final model = RocketChatResultResponse.fromMap(
          data,
          mapper: (data) {
            return List<RoomItemInfo>.from(data.map((x) {
              return RoomItemInfo.fromMap(x);
            }));
          },
        );
        logger.t(model);
        return model.result!;
      } else {
        throw RocketChatException(baseResponse.error!);
      }
    }
    throw RocketChatException(response.body);
  }

  Future<Map<String, RoomItemSubscription>?> getRoomsSubscriptions(
    Authentication? authentication,
  ) async {
    http.Response response = await _httpService.methodPost(
      '/api/v1/method.call/subscriptions:get',
      r'''{"message":"{\"msg\":\"method\",\"method\":\"subscriptions/get\",\"params\":[],\"id\":\"10\"}"}''',
      authentication: authentication,
    );
    if (response.body.isNotEmpty == true) {
      final baseResponse =
          RocketChatBaseResponse.fromMap(jsonDecode(response.body));
      if (baseResponse.success != null && baseResponse.success!) {
        final data = jsonDecode(utf8.decode(baseResponse.message!.codeUnits));
        final model = RocketChatResultResponse.fromMap(
          data,
          mapper: (data) {
            Map<String, RoomItemSubscription> result =
                Map<String, RoomItemSubscription>();
            if (data != null && data is Iterable) {
              data.forEach((element) {
                final item = RoomItemSubscription.fromMap(element);
                result[item.rid ?? ''] = item;
              });
            }
            return result;
            // return List<RoomItemSubscription>.from(data.map((x) {
            //   return RoomItemSubscription.fromMap(x);
            // }));
          },
        );
        logger.t(model);

        return model.result!;
      } else {
        throw RocketChatException(baseResponse.error!);
      }
    }
    throw RocketChatException(response.body);
  }

  Future<List<RoomItemInfo>> getVisitorRooms(
    Authentication? authentication,
    String visitorToken,
  ) async {
    http.Response response = await _httpService.methodPost(
      _httpService.cloudUri.toString() + '/chat/getVisitorRooms',
      jsonEncode({
        "visitorToken": visitorToken,
        "endPoint": _httpService.apiUri.toString(),
      }),
      authentication: authentication,
    );
    if (response.body.isNotEmpty == true) {
      final baseResponse = jsonDecode(response.body);
      if (baseResponse['success'] == true) {
        final data = baseResponse['rooms'] ?? [];
        final model = List<RoomItemInfo>.from(data.map((x) {
          try {
            return RoomItemInfo.fromMap(x);
          } catch (e) {
            logger.e(e);
            throw e;
          }
        }));
        return model;
      } else {
        throw RocketChatException(baseResponse.error!);
      }
    }
    throw RocketChatException(response.body);
  }

  // {
  // "message": "{\"msg\":\"result\",\"id\":\"18\",\"error\":{\"isClientSafe\":true,\"error\":\"error-invalid-room\",\"reason\":\"Invalid room\",\"details\":{\"method\":\"eraseRoom\"},\"message\":\"Invalid room [error-invalid-room]\",\"errorType\":\"Meteor.Error\"}}",
  // "success": true
  // }
  // {"message":"{\"msg\":\"result\",\"id\":\"18\",\"result\":1}","success":true}
  Future<bool> deleteRoom(
    Authentication? authentication,
    String roomId,
  ) async {
    http.Response response = await _httpService.methodPost(
      _httpService.cloudUri.toString() + '/chat/eraseRoom',
      jsonEncode({
        "roomId": roomId,
        "endPoint": _httpService.apiUri.toString(),
      }),
      authentication: authentication,
    );
    if (response.body.isNotEmpty == true) {
      final baseResponse = jsonDecode(response.body);
      if (baseResponse['success'] == true) {
        return true;
      } else {
        throw RocketChatException(baseResponse.error!);
      }
    }
    throw RocketChatException(response.body);
  }

  Future<List<SettingModel>> getSettings(
    Authentication authentication,
  ) async {
    http.Response response = await _httpService.methodGet(
      '/api/v1/settings.public?query={"_id":{"\$in":["Jitsi_URL_Room_Suffix", "Jitsi_Enabled", "Jitsi_URL_Room_Prefix", "Jitsi_URL_Room_Hash", "Jitsi_Domain", "Jitsi_SSL", "uniqueID"]}}',
    );
    if (response.body.isNotEmpty == true) {
      final baseResponse = SettingsResponse.fromJson(
          jsonDecode(utf8.decode(response.body.codeUnits)));
      if (baseResponse.success!) {
        return baseResponse.settings!;
      } else {
        throw RocketChatException(baseResponse.toString());
      }
    }
    throw RocketChatException(response.body);
  }

  Future<bool> pushToken(
    String type,
    String token,
    String packageName,
    Authentication authentication,
  ) async {
    http.Response response = await _httpService.methodPost(
      '/api/v1/push.token',
      r'{"type":"' +
          type +
          r'","value":"' +
          token +
          r'","appName":"' +
          packageName +
          r'"}',
      authentication: authentication,
    );
    if (response.body.isNotEmpty == true) {
      final baseResponse =
          PushToken.fromJson(jsonDecode(utf8.decode(response.body.codeUnits)));
      if (baseResponse.success!) {
        return baseResponse.success!;
      } else {
        throw RocketChatException(baseResponse.toString());
      }
    }
    throw RocketChatException(response.body);
  }

  Future<bool> deletePush(String token, Authentication authentication) async {
    http.Response response = await _httpService.methodDelete(
      '/api/v1/push.token',
      r'{"token": "' + token + '"}',
      authentication,
    );
    if (response.body.isNotEmpty == true) {
      final baseResponse =
          RocketChatBaseResponse.fromMap(jsonDecode(response.body));
      if (baseResponse.success!) {
        return baseResponse.success!;
      } else {
        throw RocketChatException(baseResponse.toString());
      }
    }
    throw RocketChatException(response.body);
  }

  Future<Visitor> createVisitor(String name, String email, String token,
      String phone, String department) async {
    var data = VisitorRequest(
            name: name,
            email: email,
            token: token,
            phone: phone,
            department: department)
        .toJson();
    http.Response response = await _httpService.methodPost(
        '/api/v1/livechat/visitor', r'{"visitor": ' + jsonEncode(data) + '}');
    if (response.body.isNotEmpty == true) {
      final baseResponse = VisitorResponse.fromJson(
          jsonDecode(utf8.decode(response.body.codeUnits)));
      if (baseResponse.success!) {
        return baseResponse.visitor!;
      } else {
        throw RocketChatException(baseResponse.toString());
      }
    }
    throw RocketChatException(response.body);
  }

  Future<Room> createRoom(String token, {String? roomId}) async {
    if (roomId == null || roomId == 'null') {
      roomId = '';
    }
    var url = '/api/v1/livechat/room?token=$token&rid=$roomId';
    http.Response response = await _httpService.methodGet(url);
    if (response.body.isNotEmpty == true) {
      final baseResponse = RoomResponse.fromJson(
          jsonDecode(utf8.decode(response.body.codeUnits)));
      if (baseResponse.success!) {
        logger.t(baseResponse);
        Room room = Room.fromJson(baseResponse.room);
        return room;
      } else {
        throw RocketChatException(baseResponse.toString());
      }
    }
    throw RocketChatException(response.body);
  }

  Future<VideoCallModel?> videoCallMessage(String? token,
      {String? roomId}) async {
    var url = '/api/v1/livechat/video.call/$token?rid=$roomId';
    http.Response response = await _httpService.methodGet(url);
    if (response.body.isNotEmpty == true) {
      final baseResponse =
          VideoCallBaseResponse.fromJson(jsonDecode(response.body));
      if (baseResponse.success!) {
        return baseResponse.videoCall!;
      } else {
        throw RocketChatException(baseResponse.toString());
      }
    }
    throw RocketChatException(response.body);
  }

  Future<AgentModel> getAgentInfo(String token, {String? roomId}) async {
    var url = '/api/v1/livechat/agent.info/$roomId/$token';
    http.Response response = await _httpService.methodGet(url);
    if (response.body.isNotEmpty == true) {
      final baseResponse = AgentResponse.fromJson(
          jsonDecode(utf8.decode(response.body.codeUnits)));
      if (baseResponse.success!) {
        return baseResponse.agent!;
      } else {
        throw RocketChatException(baseResponse.toString());
      }
    }
    throw RocketChatException(response.body);
  }

  Future<MemberResult> getUsersOfRoom(
      String roomId, Authentication authentication) async {
    http.Response response = await _httpService.methodPost(
      '/api/v1/method.call/getUsersOfRoom',
      r'{"message":"{\"msg\":\"method\",\"method\":\"getUsersOfRoom\",\"params\":[\"' +
          roomId +
          r'\",true,{\"limit\":25,\"skip\":0},\"\"],\"id\":\"20\"}"}',
      authentication: authentication,
    );
    if (response.body.isNotEmpty == true) {
      final baseResponse =
          RocketChatBaseResponse.fromMap(jsonDecode(response.body));
      if (baseResponse.success != null && baseResponse.success!) {
        final data = jsonDecode(utf8.decode(baseResponse.message!.codeUnits));
        final model = MemberResponse.fromJson(data);
        logger.t(model.toJson());

        if (model.result != null) {
          return model.result!;
        } else {
          throw RocketChatException(baseResponse.toString());
        }
      } else {
        throw RocketChatException(baseResponse.error!);
      }
    }
    throw RocketChatException(response.body);
  }

  Future<RoomItemInfo> getDirectRoom(
    String username,
    Authentication authentication,
  ) async {
    http.Response response = await _httpService.methodPost(
      '/api/v1/im.create',
      jsonEncode({"username": username}),
      authentication: authentication,
    );
    if (response.body.isNotEmpty == true) {
      final baseResponse = jsonDecode(response.body);
      final roomData = baseResponse['room'];
      if (baseResponse['success'] == true && roomData != null) {
        final model = RoomItemInfo.fromMap(roomData);
        return model;
      } else {
        throw RocketChatException(baseResponse.error!);
      }
    }
    throw RocketChatException(response.body);
  }

  Future<LiveChatConfig?> getLiveChatConfig() async {
    var url = '/api/v1/livechat/config';
    http.Response response = await _httpService.methodGet(url);
    if (response.body.isNotEmpty == true) {
      final responseData = LiveChatConfig.fromMap(jsonDecode(response.body));
      return responseData;
    }
    throw RocketChatException(response.body);
  }

  Future<List<Department>> getDepartments(String? visitorToken) async {
    http.Response response = await _httpService.methodPost(
      _httpService.cloudUri.toString() + '/chat/getDepartments',
      jsonEncode({
        "endPoint": _httpService.apiUri.toString(),
        if (visitorToken != null) "visitorToken": visitorToken,
      }),
    );
    if (response.body.isNotEmpty == true) {
      final mapData = jsonDecode(response.body);
      logger.t(mapData);
      if (mapData['departments'] != null) {
        return List<Department>.from(
            mapData["departments"].map((x) => Department.fromMap(x)));
      }
    }
    throw RocketChatException(response.body);
  }

  Future<bool> markAsReadDirect(
      String roomId, Authentication authentication) async {
    Map<String, String?> body = {"rid": roomId};

    http.Response response = await _httpService.methodPost(
      '/api/v1/subscriptions.read',
      jsonEncode(body),
      authentication: authentication,
    );

    if (response.statusCode == 200) {
      if (response.body.isNotEmpty == true) {
        return Response.fromMap(jsonDecode(response.body)).success == true;
      } else {
        return false;
      }
    }
    throw RocketChatException(response.body);
  }
}
