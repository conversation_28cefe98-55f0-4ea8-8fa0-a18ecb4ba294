import 'dart:convert';

import 'package:http/http.dart' as http;
import 'package:ks_chat/ks_chat.dart';
import 'package:rocket_chat_flutter_connector/exceptions/exception.dart';
import 'package:rocket_chat_flutter_connector/models/authentication.dart';
import 'package:rocket_chat_flutter_connector/services/user_service.dart';

class RocketChatUser<PERSON><PERSON> extends UserService {
  late RocketChatHttpService _httpService;

  RocketChatUserApi(httpService) : super(httpService) {
    this._httpService = httpService;
  }

  Future<RocketChatUser> getUserInfo(
    String username,
    Authentication? authentication,
  ) async {
    http.Response response = await _httpService.methodGet(
      '/api/v1/users.info?username=$username',
      authentication: authentication,
    );
    if (response.statusCode == 200) {
      if (response.body.isNotEmpty == true) {
        final responseData = jsonDecode(response.body);
        final baseResponse = RocketChatBaseResponse.fromMap(responseData);
        if (baseResponse.success == true && responseData['user'] != null) {
          final user = RocketChatUser.fromMap(responseData['user']);
          return user;
        } else {
          throw RocketChatException(baseResponse.error!);
        }
      }
    }
    throw RocketChatException(response.body);
  }

  Future<bool> setAvatar(
    String? url,
    Authentication? authentication,
  ) async {
    if (url?.isNotEmpty != true || authentication == null) {
      return Future.value(false);
    }
    http.Response response = await _httpService.methodPost(
      '/api/v1/users.setAvatar',
      jsonEncode({
        "avatarUrl": url,
      }),
      authentication: authentication,
    );
    if (response.statusCode == 200) {
      if (response.body.isNotEmpty == true) {
        final responseData = jsonDecode(response.body);
        final baseResponse = RocketChatBaseResponse.fromMap(responseData);
        if (baseResponse.success == true) {
          return true;
        } else {
          throw RocketChatException(baseResponse.error!);
        }
      }
    }
    throw RocketChatException(response.body);
  }

  /*
   *
   *
    {
      "msg": "method",
      "method": "saveUserProfile",
      "params": [
        {
          "realname": "Nguyễn Văn Test 5",
          "newPassword": "",
          "username": "test5",
          "statusText": "",
          "statusType": "online",
          "nickname": "",
          "bio": ""
        },
        {}
      ],
      "id": "13"
     }
   *
   *
   */
  Future<bool> updateProfile(
    String? fullName,
    Authentication? authentication,
  ) async {
    if (fullName?.isNotEmpty != true || authentication == null) {
      return Future.value(false);
    }

    final data = {
      "msg": "method",
      "method": "saveUserProfile",
      "params": [
        {"realname": fullName}
      ],
      "id": "13"
    };

    http.Response response = await _httpService.methodPost(
      '/api/v1/method.call/saveUserProfile',
      jsonEncode({"message": jsonEncode(data)}),
      authentication: authentication,
    );
    if (response.body.isNotEmpty == true) {
      final baseResponse =
          RocketChatBaseResponse.fromMap(jsonDecode(response.body));
      if (baseResponse.success == true) {
        return true;
      } else {
        throw RocketChatException(baseResponse.error!);
      }
    }
    throw RocketChatException(response.body);
  }
}
