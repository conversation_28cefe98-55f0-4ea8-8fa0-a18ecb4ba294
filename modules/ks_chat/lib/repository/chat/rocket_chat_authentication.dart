import 'dart:convert';

import 'package:common/model/token.dart';
import 'package:common/utils/log.dart';
import 'package:common/utils/model_util.dart';
import 'package:ks_chat/repository/chat/rocket_chat_http_service.dart';
import 'package:ks_chat/repository/preferences_impl.dart';
import 'package:rocket_chat_flutter_connector/exceptions/exception.dart';
import 'package:rocket_chat_flutter_connector/models/authentication.dart';
import 'package:rocket_chat_flutter_connector/services/authentication_service.dart';
import 'package:rxdart/rxdart.dart';

enum ChatLoginStatus { LOGGED, LOGOUT }

class RocketChatAuthentication extends AuthenticationService {
  late RocketChatHttpService _httpService;
  Authentication? _authentication;
  late KsChatPreferences _preferences;
  BehaviorSubject<ChatLoginStatus> _authInfo =
      BehaviorSubject<ChatLoginStatus>();

  Stream<ChatLoginStatus> get authApiChange => _authInfo.stream;

  RocketChatAuthentication(RocketChatHttpService httpService, this._preferences)
      : super(httpService) {
    this._httpService = httpService;
  }

  close() {
    _authInfo.close();
  }

  Future<Authentication?> getAuthInfo() async {
    if (_authentication == null) {
      var savedToken = await _preferences.getTokenChat();
      _authentication = savedToken != null && savedToken.length > 0
          ? Authentication.fromMap(jsonDecode(savedToken))
          : null;
    }
    return Future.value(_authentication);
  }

  _setAuthInfo(Authentication auth) async {
    this._authentication = auth;
    _authInfo.add(ChatLoginStatus.LOGGED);
    await _preferences.setTokenChat(jsonEncode(auth.toMap()));
  }

  logout() {
    logger.t('logout');
    _authentication = null;
    _authInfo.add(ChatLoginStatus.LOGOUT);
    _preferences.setTokenChat('');
  }

  @override
  Future<Authentication> login(String user, String password) {
    return loginWithUserName(user, password).then((value) {
      _setAuthInfo(value);
      return value;
    });
  }

  Future<Authentication> loginWithUserName(String user, String password) async {
    Map<String, String> body = {'user': user, 'password': password};
    final response = await _httpService.methodPost(
      '/api/v1/login',
      jsonEncode(body),
    );

    if (response.statusCode == 200 && response.body.isNotEmpty == true) {
      return Authentication.fromMap(jsonDecode(response.body));
    }
    throw RocketChatException(response.body);
  }

  Future<Authentication> loginWithToken(Token token,
      [String? serviceName = 'keycloak']) async {
    Map<String, dynamic> body = {
      'serviceName': serviceName ?? 'keycloak',
      'accessToken': token.accessToken,
      'expiresIn': toInt(token.expiresIn),
    };
    logger.t(body);
    final response =
        await _httpService.methodPost('/api/v1/login', jsonEncode(body));

    if (response != null &&
        response.statusCode == 200 &&
        response.body.isNotEmpty == true) {
      var authentication = Authentication.fromMap(jsonDecode(response.body));
      await _setAuthInfo(authentication);
      return authentication;
    }
    throw RocketChatException(response.body);
  }
}
