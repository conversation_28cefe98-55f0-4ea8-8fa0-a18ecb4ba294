import 'package:common/utils/log.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:ks_chat/repository/chat/rocket_chat_error.dart';
import 'package:mime_type/mime_type.dart';
import 'package:rocket_chat_flutter_connector/models/authentication.dart';
import 'package:rocket_chat_flutter_connector/models/filters/filter.dart';
import 'package:rocket_chat_flutter_connector/services/http_service.dart';

class RocketChatHttpService extends HttpService {
  late Uri _apiUri;
  late Uri _cloudApiUri;

  RocketChatHttpService(Uri apiUrl, Uri cloudUrl) : super(apiUrl) {
    _apiUri = apiUrl;
    _cloudApiUri = cloudUrl;
    logger.t(_apiUri);
  }

  setApiUri(Uri apiUri) {
    this._apiUri = apiUri;
  }

  Uri get apiUri => _apiUri;

  Uri get cloudUri => _cloudApiUri;

  @override
  Future<http.Response> getWithFilter(
      String uri, Filter filter, Authentication authentication) async {
    logger.t(filter, error: uri);
    return http.get(
        Uri.parse(_apiUri.toString() + uri + '?' + _urlEncode(filter.toMap())),
        headers: _getHeaders(authentication));
  }

  Future<http.Response> methodGet(String uri,
      {Authentication? authentication}) async {
    logRequest(uri);
    final response =
        await http.get(getEndpoint(uri), headers: _getHeaders(authentication));
    logger.t(response.body, error: '$uri => ${response.statusCode}');
    return handlerResponse(response);
  }

  handlerResponse(http.Response response) {
    final successCode = [200, 201, 204, 2000000, 2000001, 2000004];
    if (successCode.indexOf(response.statusCode) >= 0) {
      return response;
    }

    throw RocketChatError(response.body, response: response);
  }

  Uri getEndpoint(String uri) {
    Uri endPoint;
    if (uri.contains(_apiUri.host) || uri.contains(_cloudApiUri.host)) {
      endPoint = Uri.parse(uri);
    } else {
      endPoint = Uri.parse(_apiUri.toString() + uri);
    }
    return endPoint;
  }

  Future<http.Response> methodPost(String uri, String body,
      {Authentication? authentication}) async {
    logRequest(uri, body);

    final response = await http.post(getEndpoint(uri),
        headers: _getHeaders(authentication), body: body);
    logger.t(response.body, error: '$uri => ${response.statusCode}');
    return handlerResponse(response);
  }

  Future<http.Response> put(
      String uri, String body, Authentication authentication) async {
    logRequest(uri, body);
    final response = await http.put(Uri.parse(_apiUri.toString() + uri),
        headers: _getHeaders(authentication), body: body);
    logger.t(response.body);
    return response;
  }

  Future<http.Response> methodDelete(
      String uri, String body, Authentication authentication) async {
    logRequest(uri, body);
    final response = await http.delete(Uri.parse(_apiUri.toString() + uri),
        body: body, headers: _getHeaders(authentication));
    logger.t(response.body, error: '$uri => ${response.statusCode}');
    return handlerResponse(response);
  }

  Future<http.Response> methodUploadFile(String uri, String filePath,
      {Authentication? authentication, String? visitorToken}) async {
    logRequest(uri, filePath);
    var request =
        http.MultipartRequest('POST', Uri.parse(_apiUri.toString() + uri));
    request.files.add(await http.MultipartFile.fromPath('file', filePath,
        contentType: MediaType.parse(mime(filePath)!)));
    if (authentication != null) {
      request.headers.addAll(_getHeaders(authentication));
    } else if (visitorToken != null) {
      Map<String, String> header = {};
      header['x-visitor-token'] = visitorToken;
      request.headers.addAll(header);
    }
    http.StreamedResponse response = await request.send();
    return http.Response.fromStream(response);
  }

  Map<String, String> _getHeaders(Authentication? authentication) {
    Map<String, String> header = {
      'Content-type': 'application/json',
    };

    if (authentication?.status == "success") {
      header['X-Auth-Token'] = authentication?.data?.authToken ?? "";
      header['X-User-Id'] = authentication?.data?.userId ?? "";
    }

    return header;
  }

  logRequest(uri, [body = '']) {
    logger.t(getEndpoint(uri), error: body);
  }
}

String _urlEncode(Map object) {
  int index = 0;
  String url = object.keys.map((key) {
    if (object[key]?.toString().isNotEmpty == true) {
      String value = "";
      if (index != 0) {
        value = "&";
      }
      index++;
      return "$value${Uri.encodeComponent(key)}=${Uri.encodeComponent(object[key].toString())}";
    }
    return "";
  }).join();
  return url;
}
