import 'dart:convert';

import 'package:common/utils/log.dart';
import 'package:http/http.dart' as http;
import 'package:ks_chat/repository/chat/rocket_chat_error.dart';
import 'package:ks_chat/repository/chat/rocket_chat_http_service.dart';
import 'package:ks_chat/repository/chat/rocket_chat_message_models.dart';
import 'package:ks_chat/repository/chat/rocket_chat_models.dart';
import 'package:rocket_chat_flutter_connector/exceptions/exception.dart';
import 'package:rocket_chat_flutter_connector/models/authentication.dart';
import 'package:rocket_chat_flutter_connector/models/new/message_new.dart';
import 'package:rocket_chat_flutter_connector/models/response/message_new_response.dart';
import 'package:rocket_chat_flutter_connector/services/http_service.dart';
import 'package:rocket_chat_flutter_connector/services/message_service.dart';
import 'package:uuid/uuid.dart';

class RocketChatMessageApi extends MessageService {
  late RocketChatHttpService _httpService;

  RocketChatMessageApi(RocketChatHttpService httpService)
      : super(httpService as HttpService) {
    this._httpService = httpService;
  }

  String getID() {
    return Uuid().v1();
  }

  @override
  Future<MessageNewResponse> postMessage(
      MessageNew message, Authentication authentication) async {
    http.Response response = await _httpService.methodPost(
      '/api/v1/chat.postMessage',
      jsonEncode(message.toMap()),
      authentication: authentication,
    );

    if (response.statusCode == 200) {
      if (response.body.isNotEmpty == true) {
        return MessageNewResponse.fromMap(jsonDecode(response.body));
      } else {
        return MessageNewResponse();
      }
    }
    throw RocketChatError('', response: response);
  }

  Future<List<RocketChatMessage>> loadHistory(
    String room,
    Authentication? authentication,
  ) async {
    if (room.isEmpty) return Future.value(<RocketChatMessage>[]);

    final time = DateTime.now().second.toString();

    http.Response response = await _httpService.methodPost(
      '/api/v1/method.call/loadHistory',
      r'{"message":"{\"msg\":\"method\",\"method\":\"loadHistory\",\"params\":[\"' +
          room +
          r'\",null,50,{\"$date\":' +
          time +
          r'}],\"id\":\"25\"}"}',
      authentication: authentication,
    );
    if (response.body.isNotEmpty == true) {
      final baseResponse = RocketChatBaseResponse.fromMap(
          jsonDecode(utf8.decode(response.body.codeUnits)));
      if (baseResponse.success!) {
        final data = jsonDecode(baseResponse.message!);
        final model = RocketChatResultResponse.fromMap(
          data,
          mapper: (data) {
            final messages = data['messages'];
            return List<RocketChatMessage>.from(messages.map((x) {
              return RocketChatMessage.fromMap(x);
            }));
          },
        );
        logger.v(model);
        return model.result!;
      } else {
        throw RocketChatException(baseResponse.error!);
      }
    }
    throw RocketChatException(response.body);
  }

  Future<bool> jitsiUpdateTimeout(
    String roomId,
    Authentication? authentication,
  ) async {
    if (roomId.isEmpty) return Future.value(false);
    http.Response response = await _httpService.methodPost(
      '/api/v1/method.call/jitsi:updateTimeout',
      r'{"message":"{\"msg\":\"method\",\"method\":\"jitsi:updateTimeout\",\"params\":[\"' +
          roomId +
          r'\"],\"id\":\"20\"}"}',
      authentication: authentication,
    );
    if (response.body.isNotEmpty == true) {
      final baseResponse =
          RocketChatBaseResponse.fromMap(jsonDecode(response.body));
      if (baseResponse.success!) {
        return baseResponse.success!;
      } else {
        throw RocketChatException(baseResponse.error!);
      }
    }
    throw RocketChatException(response.body);
  }

  Future<List<VisitorChatMessage>> loadLiveChatHistory(
      String? roomId, String vToken) async {
    if (roomId == null || roomId.isEmpty)
      return Future.value(<VisitorChatMessage>[]);
    http.Response response = await _httpService.methodGet(
        '/api/v1/livechat/messages.history/$roomId?token=$vToken&limit=500');
    if (response.body.isNotEmpty == true) {
      final baseResponse = RoomMessages.fromMap(
          jsonDecode(utf8.decode(response.body.codeUnits)));
      if (baseResponse.success!) {
        return baseResponse.messages!;
      } else {
        throw RocketChatException('Error');
      }
    }
    throw RocketChatException(response.body);
  }

  Future<MessageNewResponse> sendVisitorMessage(
    String? token,
    String roomId,
    String msg,
  ) async {
    final data = {"token": token, "rid": roomId, "msg": msg};
    http.Response response = await _httpService.methodPost(
      '/api/v1/livechat/message',
      jsonEncode(data),
    );
    if (response.statusCode == 200) {
      if (response.body.isNotEmpty == true) {
        return MessageNewResponse.fromMap(jsonDecode(response.body));
      } else {
        return MessageNewResponse();
      }
    }
    throw RocketChatError('', response: response);
  }

  /////////////
  //away
  //online
  ////////////
  Future<bool> sendVisitorStatus(String vToken, String status) async {
    final data = {"token": vToken, "status": status};
    http.Response response = await _httpService.methodPost(
      '/api/v1/livechat/visitor.status',
      jsonEncode(data),
    );
    if (response.statusCode == 200) {
      return true;
    }
    throw RocketChatException(response.body);
  }

  Future<bool> closeRoomAgent(
      String userId, String roomId, Authentication? authentication) async {
    http.Response response = await _httpService.methodPost(
      '/api/v1/method.call/livechat:closeRoom',
      r'{"message":"{\"msg\":\"method\",\"method\":\"livechat:closeRoom\",\"params\":[\"' +
          roomId +
          r'\",\"test comment\",{\"clientAction\":true,\"tags\":[\"demo tag\"]}],\"id\":\"42\"}"}',
      authentication: authentication,
    );
    if (response.body.isNotEmpty == true) {
      final baseResponse =
          RocketChatBaseResponse.fromMap(jsonDecode(response.body));
      if (baseResponse.success!) {
        return baseResponse.success!;
      } else {
        throw RocketChatException(baseResponse.error!);
      }
    }
    throw RocketChatException(response.body);
  }

  Future<bool> closeRoomVisitor(String? roomId, String? visitorToken) async {
    http.Response response = await _httpService.methodPost(
      '/api/v1/livechat/room.close',
      '{"rid": "$roomId", "token": "$visitorToken"}',
    );
    if (response.body.isNotEmpty == true) {
      final baseResponse =
          RocketChatBaseResponse.fromMap(jsonDecode(response.body));
      if (baseResponse.success!) {
        return baseResponse.success!;
      } else {
        throw RocketChatException(baseResponse.error!);
      }
    }
    throw RocketChatException(response.body);
  }

  Future<Department> getDepartmentInfo(
      String departmentId, Authentication? authInfo) async {
    http.Response response = await _httpService.methodGet(
        '/api/v1/livechat/department/$departmentId',
        authentication: authInfo);
    if (response.body.isNotEmpty == true) {
      final baseResponse = DepartmentResponse.fromJson(
          jsonDecode(utf8.decode(response.body.codeUnits)));
      if (baseResponse.success!) {
        return baseResponse.department!;
      } else {
        throw Department();
      }
    }
    throw RocketChatException(response.body);
  }

  Future<bool> getRoomInfo(String roomId, Authentication authInfo) async {
    http.Response response = await _httpService.methodGet(
        '/api/v1/rooms.info?roomId=$roomId',
        authentication: authInfo);
    if (response.body.isNotEmpty == true) {
      final baseResponse =
          RocketChatBaseResponse.fromMap(jsonDecode(response.body));
      if (baseResponse.success!) {
        return baseResponse.success!;
      } else {
        throw RocketChatException(baseResponse.error!);
      }
    }
    throw RocketChatException(response.body);
  }

  Future<bool> sendOfflineMessage(String? name, String? email, String? message,
      {String? host}) async {
    Map<String, dynamic> data = {
      "name": name ?? "",
      "email": email ?? "",
      "message": message ?? "",
      "host": host ?? ""
    };
    http.Response response = await _httpService.methodPost(
      '/api/v1/livechat/offline.message',
      jsonEncode(data),
    );
    if (response.body.isNotEmpty == true) {
      final baseResponse =
          RocketChatBaseResponse.fromMap(jsonDecode(response.body));
      if (baseResponse.success!) {
        return baseResponse.success!;
      } else {
        throw RocketChatException(baseResponse.error!);
      }
    }
    throw RocketChatException(response.body);
  }

  Future<MessageNewResponse> uploadFile(
      String file, String roomId, Authentication authentication) async {
    http.Response response = await _httpService.methodUploadFile(
        '/api/v1/rooms.upload/$roomId', file,
        authentication: authentication);
    if (response.statusCode != 200) {
      throw RocketChatError('', response: response);
    }
    if (response.body.isNotEmpty == true) {
      final baseResponse =
          MessageNewResponse.fromMap(jsonDecode(response.body));
      if (baseResponse.success!) {
        return baseResponse;
      } else {
        throw RocketChatException("");
      }
    }
    throw RocketChatException(response.body);
  }

  Future<bool> uploadFileLiveChat(
      String file, String roomId, String visitorToken) async {
    http.Response response = await _httpService.methodUploadFile(
        '/api/v1/livechat/upload/$roomId', file,
        visitorToken: visitorToken);
    if (response.statusCode != 200) {
      throw RocketChatError('', response: response);
    }
    if (response.body.isNotEmpty == true) {
      final baseResponse =
          MessageNewResponse.fromMap(jsonDecode(response.body));
      if (baseResponse.success!) {
        return baseResponse.success!;
      } else {
        throw RocketChatException("");
      }
    }
    throw RocketChatException(response.body);
  }
}
