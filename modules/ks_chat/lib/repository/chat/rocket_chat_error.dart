import 'package:http/http.dart';
import 'package:rocket_chat_flutter_connector/exceptions/exception.dart';

class RocketChatError extends RocketChatException {
  Response? response;
  RocketChatError(
      String message, {
        this.response,
      }) : super(message);

  String toString() {
    final parentString = super.toString();
    if (response != null) {
      return "${response?.statusCode.toString()} -" + parentString;
    }
    return parentString;
  }
}
