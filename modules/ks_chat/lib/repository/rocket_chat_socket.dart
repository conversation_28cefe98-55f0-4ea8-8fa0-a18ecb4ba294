import 'dart:async';
import 'dart:convert';

import 'package:common/utils/log.dart';
import 'package:connectivity/connectivity.dart';
import 'package:crypto/crypto.dart';
import 'package:ks_chat/bloc/model/msg_rocket_chat_response.dart';
import 'package:ks_chat/repository/entity/socket_status.dart';
import 'package:rocket_chat_flutter_connector/models/authentication.dart';
import 'package:rocket_chat_flutter_connector/models/channel.dart';
import 'package:rocket_chat_flutter_connector/models/user.dart';
import 'package:rocket_chat_flutter_connector/web_socket/notification_type.dart';
import 'package:rxdart/rxdart.dart';

import 'chat/rocket_chat_models.dart';
import 'socketio.dart';

//["{\"msg\":\"sub\",\"id\":\"fXZecbDeJAM3ojmXr\",\"name\":\"stream-room-messages\",\"params\":[\"8fGLGJnfJGudX3nLxDrieZNyea2RLYZpui\",{\"useCollection\":false,\"args\":[]}]}"]
class RocketChatSocket extends SocketIO {
  late PublishSubject<MsgRocketChatResponse> _socketMessage;
  late PublishSubject<SocketEvent> _socketEvent;

  late BehaviorSubject<ConnectivityResult> _networkStatus;

  Stream<ConnectivityResult> get networkStatusStream => _networkStatus.stream;

  Stream<MsgRocketChatResponse> get messageStream => _socketMessage.stream;

  Stream<SocketEvent> get eventStream => _socketEvent.stream;

  StreamSubscription? _networkSub;

  RocketChatSocketToken? _authentication;

  RocketChatSocket(String endPoint) : super(endPoint) {
    _socketMessage = PublishSubject<MsgRocketChatResponse>();
    _socketEvent = PublishSubject<SocketEvent>();
    _networkStatus = BehaviorSubject<ConnectivityResult>();
    getNetworkStatus();
    _handlerNetWorkChange();
    initCommunication();
  }

  Future<ConnectivityResult> getNetworkStatus() async {
    var connectivityResult = await (Connectivity().checkConnectivity());
    if (!_networkStatus.isClosed) {
      _networkStatus.add(connectivityResult);
    }
    return connectivityResult;
  }

  _handlerNetWorkChange() {
    _networkSub = Connectivity()
        .onConnectivityChanged
        .listen((ConnectivityResult result) {
      logger.i('onConnectivityChanged $result');
      _networkStatus.add(result);

      if (result != ConnectivityResult.none && !isConnected) {
        initCommunication();
      }
    });
  }

  @override
  send(message, [bool showLog = true]) {
    final networkStatus = _networkStatus.valueOrNull;
    if (networkStatus != null && networkStatus != ConnectivityResult.none) {
      super.send(jsonEncode(message), showLog);
    } else {
      _authentication = null;
      _socketEvent.add(SocketEvent(
          SocketStatus.disconnected, SocketStatus.disconnected.toString()));
      logger.e('Network is off');
    }
  }

  close() {
    _socketMessage.close();
    _socketEvent.close();
    _networkStatus.close();
    _networkSub?.cancel();
  }

  @override
  void onReceptionOfMessageFromServer(message) {
    var mainMessage = jsonDecode(message);
    var mainMessageModel = MsgRocketChatResponse.fromMap(mainMessage);
    if (mainMessageModel.msg == NotificationType.PING) {
      send({'msg': 'pong'}, false);
    } else {
      logger.i(message);
      _socketMessage.add(mainMessageModel);
    }
  }

  @override
  void onConnectSocket() {
    logger.i('onConnectSocket');
    _authentication = null;
    _sendConnectRequest();
    _socketEvent.add(
        SocketEvent(SocketStatus.connected, SocketStatus.connected.toString()));
  }

  @override
  void onSocketClose() {
    logger.i('onSocketClose');
    _authentication = null;
    _socketEvent.add(SocketEvent(
        SocketStatus.disconnected, SocketStatus.disconnected.toString()));
  }

  @override
  void onSocketError(error, stackTrace) {
    _authentication = null;
    _socketEvent.add(SocketEvent(SocketStatus.error, error?.toString() ?? ""));
  }

  void _sendConnectRequest() {
    Map msg = {
      "msg": "connect",
      "version": "1",
      "support": ["1", "pre2", "pre1"]
    };
    send(msg);
  }

  Future<T?> _toFuture<T>(String requestId, [MapResultResponse<T>? mapper]) {
    return _socketMessage
        .firstWhere((element) => element.id == requestId)
        .timeout(Duration(seconds: 20))
        .then<T?>((value) => value.updateResult(mapper));
  }

  Future<RocketChatSocketToken?> loginWithAuthToken(
      Authentication authentication) async {
    final String id = getID();
    Map msg = {
      "msg": "method",
      "method": "login",
      "id": id,
      "params": [
        {"resume": authentication.data?.authToken}
      ]
    };
    send(msg);
    _authentication = await _toFuture<RocketChatSocketToken>(id, (result) {
      return RocketChatSocketToken.fromMap(result);
    });
    _socketEvent.add(SocketEvent(
        SocketStatus.authenticated, SocketStatus.authenticated.toString()));
    return _authentication;
  }

  Future logout() async {
    _authentication = null;
    final String id = getID();
    Map msg = {"msg": "method", "method": "logout", "id": id, "params": []};
    send(msg);
    await _toFuture(id);

    _socketEvent.add(
        SocketEvent(SocketStatus.connected, SocketStatus.connected.toString()));
  }

  // {
  //   "id" : "9c78d650-a1cb-11eb-a6b0-cf99b3937ea8",
  //   "msg" : "result",
  //   "result" : {
  //      "token" : "bK_scjfAaOm01WgJsG9ItRt5bLhQyFQIsrnYMxxStvO",
  //      "type" : "password",
  //      "tokenExpires" : {
  //        "$date" : 1626695927313
  //      },
  //      "id" : "DrieZNyea2RLYZpui"
  //   }
  // }
  //
  Future loginWithUserName({
    required String userName,
    required String password,
  }) async {
    var key = utf8.encode(password);
    var value = sha256.convert(key).toString();
    final id = getID();
    Map msg = {
      "msg": "method",
      "method": "login",
      "id": id,
      "params": [
        {
          "user": {"username": userName},
          "password": {"digest": value, "algorithm": "sha-256"}
        }
      ]
    };
    send(msg);
    _authentication = await _toFuture<RocketChatSocketToken>(id, (result) {
      return RocketChatSocketToken.fromMap(result);
    });
    _socketEvent.add(SocketEvent(
        SocketStatus.authenticated, SocketStatus.authenticated.toString()));
    return _authentication;
  }

  Future loginWithEmail({
    required String email,
    required String password,
  }) async {
    var key = utf8.encode(password);
    var value = sha256.convert(key).toString();
    final id = getID();
    Map msg = {
      "msg": "method",
      "method": "login",
      "id": id,
      "params": [
        {
          "user": {"email": email},
          "password": {"digest": value, "algorithm": "sha-256"}
        }
      ]
    };
    send(msg);
    _authentication = await _toFuture<RocketChatSocketToken>(id, (result) {
      return RocketChatSocketToken.fromMap(result);
    });
    _socketEvent.add(SocketEvent(
        SocketStatus.authenticated, SocketStatus.authenticated.toString()));
    return _authentication;
  }

  Future deleteRoom(String roomId) async {
    final id = '92';
    Map msg = {
      "msg": "method",
      "method": "eraseRoom",
      "id": id,
      "params": [roomId]
    };
    send(msg);
    final response = await _toFuture(id, (result) {
      return result;
    });
    return response;
  }

  void streamNotifyUserSubscribe(User user) {
    Map msg = {
      "msg": "sub",
      "id": "stream-notify-user-${user.id}",
      "name": "stream-notify-user",
      "params": ["${user.id}/notification", false]
    };

    send(msg);
  }

  void streamRoomMessagesSubscribe(String room) {
    Map msg = {
      "msg": "sub",
      "id": room,
      "name": "stream-room-messages",
      "params": [
        room,
        false,
        {"useCollection": false, "args": []}
      ]
    };
    send(msg);
  }

  void streamRoomMessagesUnsubscribe(String room) {
    Map msg = {
      "msg": "unsub",
      "id": 'stream-room-messages-' + room,
    };
    send(msg);
  }
//["{\"msg\":\"sub\",\"id\":\"6Lt72bmXfAmj3H7p8\",
// \"name\":\"stream-notify-user\",
// \"params\":[\"DrieZNyea2RLYZpui/rooms-changed\",
// {\"useCollection\":false,\"args\":[]}]}"]

  void streamRoomsChangeSubscribe(String userId) {
    Map msg = {
      "msg": "sub",
      "id": "rooms-changed-$userId",
      "name": "stream-notify-user",
      "params": [userId + "/rooms-changed", false]
    };
    send(msg);
  }

  void streamSubscriptionChange(String userId) {
    Map msg = {
      "msg": "sub",
      "id": "subscriptions-changed-$userId",
      "name": "stream-notify-user",
      "params": [
        "$userId/subscriptions-changed",
        {"useCollection": false, "args": []}
      ]
    };
    send(msg);
  }

  void sendMessageOnChannel(String message, Channel channel) {
    Map msg = {
      "msg": "method",
      "method": "sendMessage",
      "id": "42",
      "params": [
        {"rid": channel.id, "msg": message}
      ]
    };

    send(msg);
  }

  void sendMessageOnRoom(String message, Room room) {
    Map msg = {
      "msg": "method",
      "method": "sendMessage",
      "id": "42",
      "params": [
        {"rid": room.sId, "msg": message}
      ]
    };

    send(msg);
  }

  void sendUserPresence() {
    Map msg = {
      "msg": "method",
      "method": "UserPresence:setDefaultStatus",
      "id": "42",
      "params": ["online"]
    };
    send(msg);
  }

  //{"msg":"method","method":"livechat:setUpConnection","params":[{"token":"6h9t9l7lsw8w1unng7jp2"}],"id":"ddp-1"}
  //#region LIVE CHAT
  liveChatSetupConnection(String? vToken) {
    Map msg = {
      "msg": "method",
      "method": "livechat:setUpConnection",
      "id": "ddp-1",
      "params": [
        {"token": vToken}
      ]
    };
    send(msg);
  }

  liveChatListenRoom(String? roomId, String? vToken, bool sub) {
    Map msg = {
      "msg": sub == true ? "sub" : "unsub",
      "name": "stream-livechat-room",
      "params": [
        roomId,
        {
          "useCollection": false,
          "args": [
            {
              "token": vToken,
              "visitorToken": vToken,
            }
          ]
        }
      ],
      "id": "$roomId-ddp-315"
    };
    send(msg);
  }

  liveChatSendTyping(
    String? roomId,
    String? vUserName,
    String? vToken,
    bool? isTyping,
  ) {
    Map msg = {
      "msg": "method",
      "method": "stream-notify-room",
      "params": [
        "$roomId/typing",
        vUserName,
        isTyping,
        {"token": vToken}
      ],
      "id": "ddp-318"
    };
    send(msg);
  }

  liveChatListenMessages(String? roomId, String? vToken, bool sub) {
    Map msg = {
      "msg": sub == true ? "sub" : "unsub",
      "name": "stream-room-messages",
      "params": [
        roomId,
        {
          "useCollection": false,
          "args": [
            {
              "token": vToken,
              "visitorToken": vToken,
            }
          ]
        }
      ],
      "id": "$roomId-ddp-326"
    };
    send(msg);
  }

  liveChatListenDeleteMessage(String? roomId, String? vToken, bool sub) {
    Map msg = {
      "msg": sub == true ? "sub" : "unsub",
      "name": "stream-notify-room",
      "params": [
        "$roomId/deleteMessage",
        {
          "useCollection": false,
          "args": [
            {"token": vToken, "visitorToken": vToken}
          ]
        }
      ],
      "id": "$roomId-ddp-328"
    };
    send(msg);
  }

  //#endregion

  //"msg":"sub","name":"stream-notify-room","params":["6e5yDskrpWtF5XKEs/typing",{"useCollection":false,"args":[{"token":"6h9t9l7lsw8w1unng7jp2","visitorToken":"6h9t9l7lsw8w1unng7jp2"}]}],"id":"ddp-3"}
  //["{\"msg\":\"sub\",\"id\":\"rqQuqqgs86BwqvP8t\",\"name\":\"stream-notify-room\",\"params\":[\"6e5yDskrpWtF5XKEs/typing\",{\"useCollection\":false,\"args\":[]}]}"]
  liveChatListenTyping(String? roomId, String? vToken, bool sub) {
    final msg = {
      "msg": sub == true ? "sub" : "unsub",
      "name": "stream-notify-room",
      "params": [
        "$roomId/typing",
        {
          "useCollection": false,
          "args": [
            {"token": vToken, "visitorToken": vToken}
          ]
        }
      ],
      "id": "$roomId-ddp-3"
    };
    send(msg);
  }

  listenTyping(String? roomId, bool sub) {
    final msg = {
      "msg": sub == true ? "sub" : "unsub",
      "id": "$roomId-typing",
      "name": "stream-notify-room",
      "params": [
        "$roomId/typing",
        {"useCollection": false, "args": []}
      ]
    };
    send(msg);
  }

  //{"msg":"method","method":"stream-notify-room","params":["yTjNyoarWu2Y98YRFz8TwLi8sL433em6bD/typing","Tài Nguyễn",true],"id":"20"}
  sendTyping(
    String? roomId,
    String? userName,
    bool? isTyping,
  ) {
    Map msg = {
      "msg": "method",
      "method": "stream-notify-room",
      "params": [
        "$roomId/typing",
        userName,
        isTyping,
      ],
      "id": "40"
    };
    send(msg);
  }
}
