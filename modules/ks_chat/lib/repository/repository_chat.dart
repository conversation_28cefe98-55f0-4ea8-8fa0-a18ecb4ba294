import 'package:common/repository/strapi/index.dart';
import 'package:ks_chat/repository/rocket_chat_socket.dart';
import 'package:rocket_chat_flutter_connector/services/channel_service.dart';
import 'package:rocket_chat_flutter_connector/services/subscription_service.dart';

import 'chat/rocket_chat_authentication.dart';
import 'chat/rocket_chat_message_api.dart';
import 'chat/rocket_chat_room_api.dart';
import 'chat/rocket_chat_user_api.dart';

abstract class RepositoryChat {
  RocketChatAuthentication get authApi;

  ChannelService get channelApi;

  RocketChatMessageApi get messageApi;

  SubscriptionService get subscriptionApi;

  RocketChatRoomApi get roomApi;

  RocketChatUserApi get userApi;

  RocketChatSocket get socket;

  StrApi get strApi;

  void setApiUri(Uri apiUri);

  void setSocketUrl(String socketUrl);

  Uri get apiUri;
}
