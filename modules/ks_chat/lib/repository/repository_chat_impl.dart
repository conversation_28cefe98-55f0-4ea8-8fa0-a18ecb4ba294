import 'package:common/repository/strapi/index.dart';
import 'package:ks_chat/repository/chat/rocket_chat_authentication.dart';
import 'package:ks_chat/repository/chat/rocket_chat_http_service.dart';
import 'package:ks_chat/repository/chat/rocket_chat_message_api.dart';
import 'package:ks_chat/repository/chat/rocket_chat_room_api.dart';
import 'package:ks_chat/repository/repository_chat.dart';
import 'package:ks_chat/repository/rocket_chat_socket.dart';
import 'package:rocket_chat_flutter_connector/services/channel_service.dart';
import 'package:rocket_chat_flutter_connector/services/subscription_service.dart';

import 'chat/rocket_chat_user_api.dart';

class RepositoryChatImpl extends RepositoryChat {
  final RocketChatAuthentication authApi;

  final ChannelService channelApi;

  final RocketChatMessageApi messageApi;

  final RocketChatSocket socket;

  final SubscriptionService subscriptionApi;

  final RocketChatRoomApi roomApi;

  final RocketChatUserApi userApi;

  final RocketChatHttpService httpService;

  final StrApi strApi;

  void setApiUri(Uri apiUri) {
    authApi.logout();
    this.httpService.setApiUri(apiUri);
  }

  void setSocketUrl(String socketUrl) {
    socket.setEndpoint(socketUrl);
  }

  Uri get apiUri => httpService.apiUri;

  RepositoryChatImpl({
    required this.authApi,
    required this.channelApi,
    required this.messageApi,
    required this.subscriptionApi,
    required this.roomApi,
    required this.userApi,
    required this.socket,
    required this.httpService,
    required this.strApi,
  });
}
