import 'dart:async';

import 'package:common/utils/log.dart';
import 'package:meta/meta.dart';
import 'package:uuid/uuid.dart';
import 'package:web_socket_channel/io.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

abstract class SocketIO {
  String _endPoint;
  SocketIO(this._endPoint);
  WebSocketChannel? _channel;
  StreamSubscription? _subscription;
  bool isConnected = false;

  String getID() {
    return Uuid().v1();
  }

  /// ----------------------------------------------------------
  /// Initialization the WebSockets connection with the server
  /// ----------------------------------------------------------
  initCommunication() {
    ///
    /// Just in case, close any previous communication
    ///
    reset();
    logger.t(_endPoint);
    try {
      _subscription?.cancel();
      _channel = IOWebSocketChannel.connect(
        _endPoint,
        pingInterval: Duration(seconds: 30),
      );
      _subscription = _channel?.stream.listen(
        onReceptionOfMessageFromServer,
        onError: (error, StackTrace stackTrace) {
          logger.e("Socket onError", error: error, stackTrace: stackTrace);
          onSocketError(error, stackTrace);
          isConnected = false;
        },
        onDone: () {
          logger.t("Socket closed!");
          onSocketClose();
          isConnected = false;
        },
      );
      isConnected = true;
      onConnectSocket();
    } catch (e) {
      logger.e(e);
      onSocketError("Socket exception", e);
      isConnected = false;
    }
  }

  /// ----------------------------------------------------------
  /// Closes the WebSocket communication
  /// ----------------------------------------------------------
  void reset() {
    logger.t("Socket reset");
    _subscription?.cancel();
    _channel?.sink.close();
    isConnected = false;
    onSocketClose();
  }

  void setEndpoint(String endPoint) {
    reset();
    _endPoint = endPoint;
  }

  String get endPoint => _endPoint;

//  isConnect() {
//
//    if (_channel?.closeCode == null && _channel.sink != null) {
//      print("Socket isConnect == true ${_channel?.closeCode}");
//      return true;
//    }
//    print(
//        "Socket isConnect == false: ${_channel?.closeCode} -> ${_channel?.closeReason}");
//
//    return false;
//  }

  /// ---------------------------------------------------------
  /// Sends a message to the server
  /// ---------------------------------------------------------
  @protected
  send(message, [bool showLog = true]) {
    if (!isConnected) {
      initCommunication();
    }
    if (showLog != false) {
      logger.i(message);
    }

    _channel?.sink.add(message);
  }

  /// ----------------------------------------------------------
  /// Callback which is invoked each time that we are receiving
  /// a message from the server
  /// ----------------------------------------------------------
  void onReceptionOfMessageFromServer(message);
  void onConnectSocket();
  void onSocketClose();
  void onSocketError(error, stackTrace);
}
