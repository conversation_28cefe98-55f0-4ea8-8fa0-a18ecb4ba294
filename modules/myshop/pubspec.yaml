name: umee_shop
description: Umee shop new project
version: 1.0.0+1
publish_to: none

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  ksb_common:
    path: ./../ksb_common
  common:
    path: ./../common
  ksbank_api_smartbank:
    path: ./../../open_api/ksbank/smartbank
  ksbank_api_media:
    path: ./../../open_api/ksbank/media
  myshop_api:
    path: ./../../open_api/ksbank/myshop
  ksbank_api_loyalty:
    path: ./../../open_api/ksbank/loyalty
  open_api_umee:
    path: ./../open-api-umee
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  flutter_bloc: ^8.1.3
  intl: ^0.19.0
  flutter_svg: ^2.0.7
  equatable: ^2.0.3
  get_it: ^7.2.0
  dio: ^5.7.0
  image_picker: ^1.1.2
  flutter_speed_dial: ^6.0.0
  connectivity: ^3.0.3
  flutter_switch: ^0.3.1
  timelines_plus: ^1.0.6
  uuid:  ^4.4.0
  device_info: ^2.0.3
  share: ^2.0.4
  url_launcher: ^6.3.0
  flutter_slidable: ^4.0.0
  infinite_scroll_pagination: ^4.0.0
  nested: ^1.0.0
  provider: ^6.0.5
  bloc_notification: ^1.1.2+1
  shimmer: ^3.0.0
  quiver: ^3.2.1
  rxdart: ^0.27.7
  shared_preferences: ^2.0.15

dev_dependencies:
  bloc_test: ^9.0.3
  flutter_test:
    sdk: flutter
  mocktail: ^0.3.0
  very_good_analysis: ^2.4.0

flutter:
  uses-material-design: true
  generate: true

flutter_intl:
  enabled: true
