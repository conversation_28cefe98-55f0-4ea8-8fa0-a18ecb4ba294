import 'package:common/ks_common.dart';
import 'package:common/utils/dialog_util.dart';
import 'package:common/widgets/bottom_sheet_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ksb_common/shared/assets.dart';
import 'package:ksb_common/shared/constant.dart';
import 'package:ksb_common/shared/theme/custom_theme.dart';
import 'package:timelines_plus/timelines_plus.dart';
import 'package:umee_shop/generated/l10n.dart';

class GuideAddMemberPage extends StatelessWidget {
  const GuideAddMemberPage({Key? key, this.shopCode}) : super(key: key);
  final String? shopCode;

  @override
  Widget build(BuildContext context) {
    return BottomSheetWidget(
      title: 'Hướng dẫn thêm thành viên',
      height: getMaxHeightBottomSheet(context) - 150.h,
      child: Column(
        children: [
          Expanded(
            child: Padding(
              padding: kPaddingStandard.copyWith(bottom: kPadding24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                      width: MediaQuery.of(context).size.width,
                      child: ImageAssets.svgAssets(
                          ImageAssets.img_guide_add_member)),
                  SizedBox(height: kOuterPadding),
                  Flexible(child: _guide(context)),
                ],
              ),
            ),
          ),
          BottomButton(title: 'Đã hiểu', onTap: () => Navigator.pop(context))
        ],
      ),
    );
  }

  Widget _guide(BuildContext context) {
    return Timeline.tileBuilder(
      physics: NeverScrollableScrollPhysics(),
      theme: TimelineThemeData(
        nodePosition: 0,
        // color: Color(0xffc2c5c9),
        connectorTheme: ConnectorThemeData(
          thickness: 1.0,
          indent: 4,
          color: DynamicTheme.of(context)?.customColor.blue40,
        ),
        indicatorTheme: IndicatorThemeData(
          color: Colors.blue,
          position: 0,
        ),
      ),
      padding: EdgeInsets.only(top: 20.0),
      builder: TimelineTileBuilder.connected(
        indicatorBuilder: (context, index) {
          return CustomIndicator(step: index + 1);
        },
        connectorBuilder: (_, index, connectorType) {
          return SolidLineConnector();
        },
        contentsBuilder: (_, index) {
          switch (index) {
            case 0:
              return _step1(context);
            case 1:
              return _step2(context);
            case 2:
              return _step3(context);
          }
        },
        itemCount: 3,
      ),
    );
  }

  Widget _step1(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: kOuterPadding, bottom: kOuterPadding),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Gửi ID shop cho thành viên của quý khách',
            style: StyleApp.bodyText2(context)?.copyWith(
              color: DynamicTheme.of(context)?.customColor.titleColor2,
            ),
          ),
          SizedBox(height: kInnerPadding),
          Container(
            decoration: BoxDecoration(
              color: DynamicTheme.of(context)?.customColor.blue0,
              borderRadius: BorderRadius.circular(kRadiusButton),
            ),
            padding: EdgeInsets.symmetric(
                vertical: kSmallPadding, horizontal: kOuterPadding),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'ID Shop',
                  style: StyleApp.bodyText2(context)?.copyWith(
                    color: DynamicTheme.of(context)?.customColor.ink80,
                  ),
                ),
                SizedBox(height: kTinyPadding),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Expanded(
                      child: Text(
                        shopCode ?? '',
                        style: StyleApp.subtitle1(context)?.copyWith(
                          color:
                              DynamicTheme.of(context)?.customColor.titleColor2,
                        ),
                      ),
                    ),
                    SizedBox(width: kSmallPadding),
                    GestureDetector(
                      onTap: () {
                        Clipboard.setData(ClipboardData(text: shopCode ?? ''));
                        DialogUtil.showFlushBar(
                            context, S.of(context).copy_successfully);
                      },
                      child: ImageAssets.svgAssets(ImageAssets.ic_copy_bold),
                    ),
                  ],
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _step2(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: kOuterPadding, bottom: kOuterPadding),
      child: RichText(
        text: TextSpan(
          text: 'Thành viên truy cập',
          style: StyleApp.bodyText2(context)?.copyWith(
            color: DynamicTheme.of(context)?.customColor.titleColor2,
            height: 20 / 14,
          ),
          children: [
            TextSpan(
              text: ' MyShop ',
              style: StyleApp.bodyText2(context)?.copyWith(
                color: DynamicTheme.of(context)?.customColor.titleColor2,
                fontWeight: FontWeight.w700,
              ),
            ),
            TextSpan(
              text: 'rồi ấn nút',
            ),
            TextSpan(
              text: ' Nhập mã shop ',
              style: StyleApp.bodyText2(context)?.copyWith(
                color: DynamicTheme.of(context)?.customColor.titleColor2,
                fontWeight: FontWeight.w700,
              ),
            ),
            TextSpan(
              text: 'ở màn đầu tiên để nhập ID Shop và gửi yêu cầu tham gia',
            ),
          ],
        ),
      ),
    );
  }

  Widget _step3(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: kOuterPadding),
      child: RichText(
        text: TextSpan(
          text: 'Vào mục',
          style: StyleApp.bodyText2(context)?.copyWith(
            color: DynamicTheme.of(context)?.customColor.titleColor2,
            height: 20 / 14,
          ),
          children: [
            TextSpan(
              text: ' Thành viên ',
              style: StyleApp.bodyText2(context)?.copyWith(
                color: DynamicTheme.of(context)?.customColor.titleColor2,
                fontWeight: FontWeight.w700,
              ),
            ),
            TextSpan(
              text:
                  'và xác nhận yêu cầu tham gia shop cho thành viên của quý khách',
            ),
          ],
        ),
      ),
    );
  }
}

class CustomIndicator extends Indicator {
  const CustomIndicator({Key? key, required this.step}) : super(key: key);
  final int step;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 20,
      width: 20,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: Color(0xFF228BCC)),
        color: Color(0xFF228BCC),
      ),
      alignment: Alignment.center,
      child: Text(
        '$step',
        style: StyleApp.caption(context)?.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.w700,
        ),
      ),
    );
  }
}
