/*
* Created by DuanVH
* on 5/22/2021.
*/
import 'package:cached_network_image/cached_network_image.dart';
import 'package:common/ks_common.dart';
import 'package:common/widgets/images/typical_project_model.dart';
import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';

import 'design_model.dart';

class MediasViewFullScreen extends StatefulWidget {
  final List<ImageDesign> images;
  final int index;

  MediasViewFullScreen({
    required this.images,
    required this.index,
  });

  @override
  _MediasViewFullScreenState createState() => _MediasViewFullScreenState();
}

class _MediasViewFullScreenState extends State<MediasViewFullScreen> {
  late PageController _pageController;
  late String tag;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: widget.index);
    tag = '${widget.index + 1}/${widget.images.length}';
  }

  @override
  void dispose() {
    super.dispose();
  }

  Widget _playIcon() {
    if (_pageController.hasClients) {
      final currentItem = widget.images[toInt(_pageController.page)];
      if (isNotImage(currentItem)) {
        return Icon(
          Icons.play_circle_filled_rounded,
          color: Theme.of(context).cardColor,
        );
      }
    }
    return Container();
  }

  bool isNotImage(ImageDesign item) {
    final others = [MediaTypeEnum.photo360, MediaTypeEnum.video];
    return others.indexOf(item.mediaType ?? MediaTypeEnum.img) >= 0;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            Container(
              color: Colors.black,
              alignment: Alignment.center,
              child: PhotoViewGallery.builder(
                itemCount: widget.images.length,
                builder: (context, index) {
                  final item = widget.images[index];
                  return PhotoViewGalleryPageOptions(
                    imageProvider:
                        CachedNetworkImageProvider(item.thumbs!.large ?? ''),
                    initialScale: PhotoViewComputedScale.contained,
                    onTapDown: (_, __, ___) {
                      if (isNotImage(item) && item.url?.isNotEmpty == true) {
                        openUrl(url: item.url);
                      }
                    },
                  );
                },
                loadingBuilder: (context, event) => Container(
                  alignment: Alignment.center,
                  child: ring,
                ),
                onPageChanged: (index) {
                  setState(() {
                    tag = '${index + 1}/${widget.images.length}';
                  });
                },
                pageController: _pageController,
              ),
            ),
            Positioned.fill(
              child: Center(child: _playIcon()),
            ),
            Positioned(
              bottom: 35,
              right: 15,
              child: Text(
                tag,
                style: Theme.of(context)
                    .textTheme
                    .bodyMedium!
                    .copyWith(color: Theme.of(context).cardColor),
              ),
            ),
            Positioned(
              top: 0,
              right: 0,
              child: InkWell(
                onTap: () {
                  Navigator.of(context).pop();
                },
                child: Container(
                  padding: EdgeInsets.all(15),
                  child: Icon(
                    Icons.close_rounded,
                    color: Theme.of(context).cardColor,
                    size: 24,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
