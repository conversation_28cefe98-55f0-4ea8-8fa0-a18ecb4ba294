import 'package:common/widgets/load_image_url.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:rxdart/rxdart.dart';

class ImageTile extends StatefulWidget {
  ImageTile({
    @required this.sKey,
    this.id,
    this.gridImage,
    this.more,
    this.note,
    this.border = false,
    this.round = false,
    this.singleVeo = false,
    this.streamDuration,
    this.textColor,
    this.url,
    this.icon,
    this.videoUrl,
    this.type,
    this.gridImages,
    this.crossAxisCount,
    this.tile,
    this.mainAxisCount,
  });

  final int? crossAxisCount;
  final int? mainAxisCount;
  final StaggeredGridTile? tile;
  final List<String?>? gridImages;
  final String? gridImage;
  final String? url;
  final String? videoUrl;
  final String? type;
  final String? note;
  final String? sKey;
  final int? id;
  final int? more;
  final bool? border;
  final bool? round;
  final bool? singleVeo;
  final Color? textColor;
  final Widget? icon;
  final BehaviorSubject Function(String x)? streamDuration;

  @override
  _ImageTileState createState() => _ImageTileState();
}

class _ImageTileState extends State<ImageTile> {
  var _color;

  final _streamVisible = BehaviorSubject<bool>.seeded(false);

  dispose() {
    _streamVisible.close();
    super.dispose();
  }

  String get _thumb {
    if (widget.tile == null) return widget.gridImage ?? '';
    final ss = widget.crossAxisCount! * widget.mainAxisCount!;
    final s = widget.tile!.crossAxisCellCount * widget.tile!.mainAxisCellCount!;
    final rate = s / ss;
    int index = 1;
    if (rate >= 0.5) index = 2;
    final thumb = widget.gridImages![index];
    return (thumb?.isNotEmpty ?? false) == true ? thumb! : widget.gridImage!;
  }

  BehaviorSubject? _duration(String url) {
    final stream = widget.streamDuration;
    return stream != null ? stream(url) : null;
  }

  // Widget get _videoItem {
  //   final img = widget.img;
  //   return StackVideo(
  //     sKey: widget.sKey,
  //     itemId: widget.id?.toString(),
  //     streamVisible: _streamVisible,
  //     duration: _duration,
  //     url: img.link,
  //     width: img.width,
  //     height: img.height,
  //     thumb: _thumb,
  //   );
  // }

  get _image {
    final _isMore = (widget.more != null);
    if (_isMore && _color == null) {
      _color = BehaviorSubject<Color>.seeded(
        widget.textColor ?? Theme.of(context).cardColor,
      );
//      imageConfig(gridImage, _color.add);
    }
    return Stack(
      alignment: Alignment.center,
      children: <Widget>[
        LoadImageUrl(
          url: _thumb,
          height: 250,
          fitHeight: true,
        ),
        if (widget.icon != null) widget.icon!,
        if (_isMore)
          StreamBuilder<Color>(
              stream: _color.stream,
              builder: (context, snapshot) {
                return Text(
                  "+${widget.more}",
                  style: TextStyle(
                      fontSize: 25,
                      fontWeight: FontWeight.bold,
                      color: snapshot.data),
                );
              })
      ],
    );
  }

  Widget build(BuildContext context) {
    return _image;
  }
}
