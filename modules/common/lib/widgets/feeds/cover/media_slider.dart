import 'package:common/model/feeds/index.dart';
import 'package:common/widgets/load_image_url.dart';
import 'package:common/widgets/video/ui_video.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

class MediaSlider extends StatefulWidget {
  final List<ProjectMediaModel> photos;
  final double imageBorderRadius;
  final Function(int) onPageChanged;
  final double radius;

  MediaSlider({
    Key? key,
    required this.photos,
    this.imageBorderRadius = 0.0,
    this.radius = 0.0,
    required this.onPageChanged,
  }) : super(key: key);

  @override
  _MediaSliderState createState() {
    return _MediaSliderState();
  }
}

class _MediaSliderState extends State<MediaSlider> {
  late PageController _pageController;

  @override
  void didUpdateWidget(MediaSlider oldWidget) {
    super.didUpdateWidget(oldWidget);
    // if (oldWidget.photos != widget.photos) {
    //   _initPage();
    // }
  }

  @override
  void initState() {
    super.initState();
    _initPage();
  }

  @override
  void dispose() {
    super.dispose();
    _pageController.dispose();
  }

  _initPage() {
    _pageController = PageController();
  }

  Widget _buildPagerViewSlider() {
    return Positioned.fill(
      child: widget.photos.isNotEmpty
          ? PageView.builder(
              controller: _pageController,
              itemCount: widget.photos.length,
              itemBuilder: (BuildContext context, int index) {
                return _buildImagePageItem(widget.photos[index]);
              },
            )
          : Container(),
    );
  }

  get _dot => SmoothPageIndicator(
        controller: _pageController,
        count: widget.photos.length,
        effect: ScrollingDotsEffect(
            activeDotColor: Theme.of(context).colorScheme.secondary,
            activeDotScale: 1.3,
            dotColor: Colors.grey,
            dotWidth: 8,
            dotHeight: 8,
            radius: 11,
            spacing: 10.0),
      );

  _move(page) {
    //movePage(_pageController, _page, page);
  }

  Widget _photoItem(ProjectMediaModel img) {
    return InkWell(
      onTap: () {},
      child: _thumb(img.thumbLarge),
    );
  }

  _thumb(String? thumb) {
    final radius = widget.imageBorderRadius;
    final child = LoadImageUrl(
      url: thumb,
      fitHeight: true,
    );
    return radius > 0
        ? ClipRRect(borderRadius: BorderRadius.circular(radius), child: child)
        : child;
  }

  Widget _videoItem(ProjectMediaModel img) {
    //L().v("--_videoItem--${img.name}---");
    if (img.link == null || img.link!.isEmpty) {
      return Container(
        child: ClipRRect(
          borderRadius: BorderRadius.circular(widget.radius),
          child: LoadImageUrl(
            url: img.thumbLarge ?? '',
            height: MediaQuery.of(context).size.width * 9 / 16,
          ),
        ),
      );
    }
    return UIVideo(
      url: img.link!,
      width: img.width,
      height: img.height,
      thumbUrl: img.thumbLarge!,
      radius: widget.radius,
    );
  }

  Widget _img360Item(ProjectMediaModel img) {
    return Container();
    // return InkWell(
    //     onTap: () => openWeb(context, img.name, img.link),
    //     child: Stack(
    //       alignment: Alignment.center,
    //       children: <Widget>[
    //         _thumb(img.thumbLarge),
    //         img360Icon,
    //       ],
    //     ));
  }

  Widget _imgMoreSlider(ProjectMediaModel img) {
    return Container();
    // return InkWell(
    //     onTap: () => go(
    //         context,
    //         MediaSliderMore(
    //           title: widget.title,
    //           photos: widget.photos,
    //         )),
    //     child: Center(
    //       child: Row(
    //         children: <Widget>[
    //           Spacer(),
    //           Container(
    //             height: 40.0,
    //             padding: EdgeInsets.only(left: 15.0, right: 15.0),
    //             margin: const EdgeInsets.only(bottom: 5.0),
    //             decoration: BoxDecoration(
    //               borderRadius: BorderRadius.all(Radius.circular(4.0)),
    //               border: Border.all(
    //                   color: Theme.of(context).textTheme.caption!.color!,
    //                   width: 1.0),
    //             ),
    //             child: Center(
    //               child: Text(
    //                 FlutterI18n.translate(context, "view_more_slider",
    //                     {"count": "${widget.photos.length - _limitSlider}"}),
    //                 style: StyleApp.titleStyle(context,
    //                     color: Theme.of(context).textTheme.subtitle2?.color),
    //               ),
    //             ),
    //           ),
    //           Spacer(),
    //         ],
    //       ),
    //     ));
  }

  Widget _buildImagePageItem(ProjectMediaModel img) {
    if (img.type == MediaType.img360) {
      return _img360Item(img);
    } else if (img.type == MediaType.more_slider) {
      return _imgMoreSlider(img);
    } else if (img.type == MediaType.video) {
      return _videoItem(img);
    } else {
      return _photoItem(img);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: 16 / 9,
      child: Stack(
        children: [
          _buildPagerViewSlider(),
          if (widget.photos.length > 1)
            Positioned(
              bottom: 16.0,
              left: 0.0,
              right: 0.0,
              child: Center(
                child: _dot,
              ),
            ),
          //if (widget.showTitle) _title(currentItem),
          // if (widget.showShare)
          //   widgetMediaShare(context, () => currentItem.shareLink)
        ],
      ),
    );
  }
}
