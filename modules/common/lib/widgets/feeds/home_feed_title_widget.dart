import 'package:common/assets.dart';
import 'package:common/ks_common.dart';
import 'package:common/model/feeds/index.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class HomeFeedTitleWidget extends StatelessWidget {
  final VoidCallback? onTapTitle;
  final VoidCallback? onTapMoreOption;
  final HomeFeedModel model;
  final Widget? logoWidget;

  const HomeFeedTitleWidget({
    Key? key,
    required this.model,
    this.onTapTitle,
    this.onTapMoreOption,
    this.logoWidget,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          InkWell(
            child: _getLogo(context),
            onTap: onTapTitle,
          ),
          SizedBox(width: 10.0),
          Expanded(
            child: InkWell(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    _getName(),
                    style: StyleApp.bodyText2(context, true),
                  ),
                  SizedBox(height: 5.0),
                  Text(
                    model.date != null
                        ? DateFormat("dd/MM/yyyy").format(model.date!)
                        : "",
                    style: TextStyle(
                        color: Theme.of(context).textTheme.bodySmall!.color),
                  ),
                ],
              ),
              onTap: onTapTitle,
            ),
          ),
          //_getMoreOptionButton(context)
        ],
      ),
    );
  }

  _getName() {
    if (model.title?.isNotEmpty == true) return model.title;
    return model.getName();
  }

  _getLogo(BuildContext context) {
    // if (model.logo.isNullOrEmpty && model.medias?.first.image == null) {
    //   return ClipOval(
    //     child: Container(
    //       width: 40.0,
    //       height: 40.0,
    //       child: Container(
    //         child:
    //         Image(image: AssetImage(ImageAssets.logo, package: 'common')),
    //       ),
    //     ),
    //   );
    // }
    return ClipOval(
      child: Container(
        width: 52.0,
        height: 52.0,
        child: Container(
          height: double.infinity,
          width: double.infinity,
          decoration: BoxDecoration(shape: BoxShape.circle),
          child: logoWidget ??
              ImageAssets.svgAssets(ImageAssets.ic_logo, fit: BoxFit.cover),
        ),
      ),
    );
  }

  _getMoreOptionButton(BuildContext context) {
    return InkWell(
      child: Container(
        padding: const EdgeInsets.only(left: 16),
        width: 40.0,
        child: Icon(
          Icons.more_horiz,
          color: Theme.of(context).colorScheme.secondary,
          size: 35,
        ),
      ),
      onTap: onTapMoreOption,
    );
  }
}
