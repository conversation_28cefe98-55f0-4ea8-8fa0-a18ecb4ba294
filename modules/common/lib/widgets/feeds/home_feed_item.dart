import 'dart:math';
import 'dart:ui';

import 'package:common/assets.dart';
import 'package:common/ks_common.dart';
import 'package:common/model/feeds/index.dart';
import 'package:common/widgets/feeds/home_feed_title_widget.dart';
import 'package:common/widgets/feeds/image_tile.dart';
import 'package:common/widgets/feeds/staggeredx.dart';
import 'package:common/widgets/load_image_url.dart';
import 'package:common/widgets/markdown_custom.dart';
import 'package:common/widgets/style_app.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:rxdart/rxdart.dart';
import 'package:tuple/tuple.dart';

class HomeFeedItem extends StatelessWidget {
  final Function(int)? onTapTag;
  final VoidCallback? onTapButtonViewAll;
  final VoidCallback? onTapButtonViewMore;
  final VoidCallback? onTapTitle;
  final VoidCallback? onTapDescription;
  final VoidCallback? onTapShare;
  final bool isHomeFeed;
  final bool isProjectFeed;
  final showMedia;
  final bool isTapDescription;
  final bool isHideCategory;
  final isFloatingAction;
  final bool showDescription;
  final BehaviorSubject Function(String x)? streamDuration;
  final HomeFeedModel model;
  final Widget? logoWidget;

  HomeFeedItem({
    Key? key,
    required this.model,
    this.isHomeFeed = false,
    this.streamDuration,
    this.onTapButtonViewMore,
    this.onTapButtonViewAll,
    this.onTapTag,
    this.onTapShare,
    this.onTapTitle,
    this.onTapDescription,
    this.showMedia = true,
    this.isProjectFeed = false,
    this.isHideCategory = false,
    this.isTapDescription = false,
    this.isFloatingAction = false,
    this.showDescription = false,
    this.logoWidget,
  }) : super(key: key);

  double get defaultPadding => sp12;

  _onTapTitle() {
    final projectId = model.project?.id;
    if (projectId?.isNotEmpty == true && onTapTitle != null) {
      onTapTitle!();
    }
  }

  @override
  Widget build(BuildContext context) {
    final widthButton = (MediaQuery.of(context).size.width - 16 * 2 - 17) / 2;
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.all(Radius.circular(0.0)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          _getCategoryView(context),
          HomeFeedTitleWidget(
            model: model,
            onTapTitle: _onTapTitle,
            onTapMoreOption: () {},
            logoWidget: logoWidget,
          ),
          _getDescriptionView(context),
          if (showMedia) _getMediaView(context),
          _getTags(context),
          Padding(
            padding: const EdgeInsets.only(left: 16, right: 16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (model.featureProject ?? false)
                  InkWell(
                    onTap: () {
                      if (onTapButtonViewMore != null) {
                        onTapButtonViewMore!();
                      }
                    },
                    child: Container(
                      width: widthButton,
                      margin: EdgeInsets.only(right: 17),
                      padding: EdgeInsets.only(
                          right: 16, left: 16, top: 12, bottom: 12),
                      decoration: BoxDecoration(
                          color: context.isDarkTheme
                              ? Colors.white10
                              : Color(0xFFF3F9FC),
                          borderRadius: BorderRadius.circular(4)),
                      child: Center(
                        child: Text(
                          "Tìm hiểu dự án",
                          style: StyleApp.bodyText2(context)?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: context.isDarkTheme
                                ? Theme.of(context).primaryColor
                                : Color(0xFF228BCC),
                          ),
                        ),
                      ),
                    ),
                  ),
                InkWell(
                  onTap: onTapShare,
                  child: Container(
                    width: widthButton,
                    padding: EdgeInsets.only(
                        right: 16, left: 16, top: 12, bottom: 12),
                    decoration: BoxDecoration(
                        color: context.isDarkTheme
                            ? Colors.white10
                            : Color(0xFFF3F9FC),
                        borderRadius: BorderRadius.circular(4)),
                    child: Center(
                      child: Text(
                        "Chia sẻ",
                        style: StyleApp.bodyText2(context)?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: context.isDarkTheme
                                ? Theme.of(context).primaryColor
                                : Color(0xFF228BCC)),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 18,
          )
        ],
      ),
    );
  }

  _getTags(BuildContext context) {
    if (model.tags == null || model.tags!.isEmpty)
      return Container(
        padding: EdgeInsets.only(bottom: 10),
      );
    final spacing = 8.0;
    double numberItem = 4.0;
    final width =
        MediaQuery.of(context).size.width - ((numberItem - 1) * spacing) - 30;
    final gridWidth = width / numberItem;
    List<Widget> childWrap = [];
    for (int index = 0; index < model.tags!.length; index++) {
      childWrap.add(GestureDetector(
        onTap: () {
          if (onTapTag != null) {
            onTapTag!(index);
          }
        },
        child: Container(
          width: gridWidth,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Padding(
                padding: const EdgeInsets.only(right: 10),
                child: ImageAssets.svgAssets(ImageAssets.ic_tag,
                    color: context.isDarkTheme ? white80 : null),
              ),
              Expanded(
                child: Text(
                  model.tags![index],
                  style: StyleApp.bodyText2(context)?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: context.isDarkTheme ? white80 : Color(0xFF353282)),
                ),
              ),
            ],
          ),
        ),
      ));
    }
    return Padding(
      padding: const EdgeInsets.only(top: 15, bottom: 15, left: 16, right: 16),
      child: Wrap(
        spacing: 10,
        runSpacing: 10,
        direction: Axis.horizontal,
        alignment: WrapAlignment.start,
        crossAxisAlignment: WrapCrossAlignment.start,
        children: childWrap,
      ),
    );
  }

  _getCategoryView(BuildContext context) {
    if (model.category?.getName?.isNotEmpty == true && !isHideCategory) {
      return Padding(
        padding: EdgeInsets.only(
          left: defaultPadding,
          right: defaultPadding,
          top: defaultPadding,
          bottom: 5.0,
        ),
        child: GestureDetector(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Text(
                  model.category!.getName!,
                  style: StyleApp.titleStyle(context),
                ),
              ),
              SizedBox(width: 10.0),
              Text(
                "Xem tất cả",
                style: StyleApp.bodyText2(context)?.copyWith(
                    color: context.isDarkTheme
                        ? Theme.of(context).primaryColor
                        : Theme.of(context).colorScheme.secondary),
              ),
            ],
          ),
          onTap: () {
            if (onTapButtonViewAll != null) {
              onTapButtonViewAll!();
            }
            // goToFeedCategories(
            //   FeedQueryModel(
            //     categoryName: model.category?.name,
            //     categoryId: model.category?.id,
            //     projectId: isHomeFeed != true ? model.project?.id : null,
            //   ),
            //   model.category!.name,
            // );
          },
        ),
      );
    }
    return Container();
  }

  _getDescriptionView(BuildContext context) {
    String description = model.getDes();
    // String content = model.getContent();
    // content = content?.isNotEmpty == true ? content : description ?? "";
    if (description.length > 0) {
      return GestureDetector(
        onTap: () {
          if (onTapDescription != null) onTapDescription!();
        },
        child: Padding(
          padding: EdgeInsets.only(
            left: defaultPadding,
            right: defaultPadding,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              markdownBodyCustom(context, data: description),
              Text(
                "Xem thêm",
                style: TextStyle(
                    color: context.isDarkTheme
                        ? Theme.of(context).primaryColor
                        : Theme.of(context).colorScheme.secondary),
              )
            ],
          ),
        ),
      );
    }
    return Container();
  }

  _getStaggerView(context) {
    final images = model.medias!;
    final _med = images.length;
    final fix = sizeTilePost;
    final sizeMin = min(fix, _med);
    final first = _med > 0 ? images[0] : null;

    var _crossTile = model.crossTitle;
    if (_crossTile == null) {
      _crossTile = _obtainCrossTile(first, sizeMin);
      model.crossTitle = _crossTile;
    }

    final List? tiles = _crossTile?.item3;
    final crossAxisCount = _crossTile?.item1 ?? 0;
    final mainAxisCount = _crossTile?.item2 ?? 0;
    if (tiles.isNullOrEmpty) return Container();
    final l = tiles?.length ?? 0;
    final single = sizeMin == 1;
    bool singleVeo = false;
    Widget wTile;
    final list = List<InkWell>.generate(sizeMin, (i) {
      return InkWell(
        onTap: () {
          if (onTapDescription != null) onTapDescription!();
          // Get.to(HomeFeedDetail(model: model),
          //     binding: BindingsBuilder.put(() => HomeFeedDetailController()));
        },
        child: ImageTile(
          sKey: '$i',
          id: i,
          streamDuration: streamDuration,
          mainAxisCount: mainAxisCount,
          crossAxisCount: crossAxisCount,
          tile: tiles![min(i, l - 1)],
          singleVeo: false,
          gridImages: images[i].getThumbs,
          gridImage: images[i].getImage(),
          url: images[i].getImage(ImageSize.large),
          videoUrl: '',
          more: null,
          icon: null,
          note: 'note',
        ),
      );
    });

    return Container(
      margin: const EdgeInsets.only(top: 10.0),
      child: list.length > 1
          ? postStaggered(list, _crossTile!)
          : list.length == 1
              ? _singleImage(
                  context, model.medias!.first.getImage(ImageSize.large))
              : Container(),
    );
  }

  _getMediaView(context) {
    // final viewType = model.viewType;
    // if (model.type == PostType.lottery) {
    //   return _getLotteryView();
    // } else if (model.medias.length > 0) {
    //   if (viewType == 'slider') {
    //     return Container(
    //       height: 250,
    //       margin: const EdgeInsets.only(top: 10.0),
    //       child: _getSliderView(),
    //     );
    //   } else if (viewType == 'topic') {
    //     return _getTopicView();
    //   } else {
    //     return _getStaggerView();
    //   }
    // }
    // return Container();

    return _getStaggerView(context);
  }

  Tuple3? _obtainCrossTile(FeedMediaModel? model, int l) {
    if (l is int && l > 0) {
      return crossTile(l, orientation(model?.width, model?.height));
    }
  }

  _singleImage(BuildContext context, String? url) {
    final wPhoto = MediaQuery.of(context).size.width;
    return Container(
      width: wPhoto,
      child: LoadImageUrl(
        url: url ?? "",
        fit: BoxFit.fitWidth,
      ),
    );
  }
}

class FeedViewMoreButton extends StatelessWidget {
  final String? text;
  final VoidCallback? onTap;

  const FeedViewMoreButton({Key? key, this.text, this.onTap}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Row(
        children: <Widget>[
          Spacer(),
          MaterialButton(
            color: Theme.of(context).primaryColor,
            shape: StadiumBorder(),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 25),
              height: 34.0,
              child: Center(
                child: Text(
                  text ?? '',
                  style: TextStyle(color: Theme.of(context).cardColor),
                ),
              ),
            ),
            onPressed: onTap,
          ),
          Spacer()
        ],
      ),
    );
  }
}

Color white80 = Colors.white.withOpacity(0.8);
