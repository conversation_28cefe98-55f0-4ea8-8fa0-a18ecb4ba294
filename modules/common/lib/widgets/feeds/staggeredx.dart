import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:tuple/tuple.dart';

enum ImageOrientation { pano, landscape, square, portrait }

orientation(width, height) {
  final rate = width * height == 0 ? 4 / 3 : width / height;
  //5 items up, recommend rate in [0.8-1.6]
  if (rate > 1.6) return ImageOrientation.pano;
  if (rate > 1.2) return ImageOrientation.landscape;
  if (rate < 0.8) return ImageOrientation.portrait;
  return ImageOrientation.square;
}

_tile(x, y) => StaggeredGridTile.count(
      crossAxisCellCount: x,
      mainAxisCellCount: y,
      child: const SizedBox(),
    );
//progress tile with 4 items
final _tileProgress = [
  _tile(2, 2),
  _tile(2, 1),
  _tile(2, 2),
  _tile(2, 1),
];

final _tileProgress2 = [
  _tile(2, 2),
  _tile(2, 3),
  _tile(2, 2),
  _tile(2, 1),
];

final _tileProgress3 = [
  _tile(2, 3),
  _tile(2, 2),
  _tile(2, 2),
  _tile(2, 1),
];

///"+more" pivot position, tile
List<Tuple2> tileProgress = [
  Tuple2(2, _tileProgress),
  Tuple2(1, _tileProgress2),
  Tuple2(2, _tileProgress3)
];

///post with 1,...,5 items
//1
final _tile1l = [_tile(4, 3)];
final _tile1pn = [_tile(4, 2)];
final _tile1sq = [_tile(4, 4)];
final _tile1p = [_tile(4, 6)];

//2
//pano
final _tile2pn = [
  _tile(4, 2),
  _tile(4, 2),
];

//2nd taller
final _tile2pnt = [
  _tile(4, 2),
  _tile(4, 3),
];
//----pano end----

//landscape
final _tile2l = [
  _tile(4, 3),
  _tile(4, 3),
];

//2nd shorter
final _tile2ls = [
  _tile(4, 3),
  _tile(4, 2),
];
//----landscape end----

//*square
//--horizontal
final _tile2hs = [
  _tile(2, 2),
  _tile(2, 2),
];

//--vertical
final _tile2vs = [
  _tile(4, 4),
  _tile(4, 4),
];
//---*square end---

//portrait
final _tile2p = [
  _tile(2, 3),
  _tile(2, 3),
];
//--portrait end---

//3
//panorama
final _tile3pn = [
  _tile(4, 2),
  _tile(2, 2),
  _tile(2, 2),
];
//panorama end---

//landscape
final _tile3l = [
  _tile(4, 3),
  _tile(2, 2),
  _tile(2, 2),
];
//landscape end---

//square
//3rd bottom
final _tile3sb = [
  _tile(2, 2),
  _tile(2, 2),
  _tile(4, 2),
];

//3rd right
final _tile3sr = [
  _tile(5, 4),
  _tile(5, 8),
  _tile(5, 4),
];
//square end---

//portrait---
final _tile3p = [
  _tile(5, 8),
  _tile(5, 4),
  _tile(5, 4),
];
//portrait end---

//4
//panorama
final _tile4pn = [
  _tile(6, 3),
  _tile(2, 2),
  _tile(2, 2),
  _tile(2, 2),
];
//panorama end---

//landscape
final _tile4l = [
  _tile(12, 9),
  _tile(4, 4),
  _tile(4, 4),
  _tile(4, 4),
];
//lanscape end

//square
final _tile4s = [
  _tile(2, 2),
  _tile(2, 2),
  _tile(2, 2),
  _tile(2, 2),
];
//square end---

//portrait
final _tile4p = [
  _tile(2, 3),
  _tile(1, 1),
  _tile(1, 1),
  _tile(1, 1),
];
//portrait end---

//5
//landscape
final _tile5l = [
  _tile(4, 3),
  _tile(3, 2),
  _tile(3, 2),
  _tile(4, 3),
  _tile(3, 2),
];
//landscape end---

//*square
final _tile5s = [
  _tile(3, 3),
  _tile(3, 2),
  _tile(3, 2),
  _tile(3, 3),
  _tile(3, 2),
];
//horizontal
final _tile5sht = [
  _tile(3, 3),
  _tile(3, 3),
  _tile(2, 2),
  _tile(2, 2),
  _tile(2, 2),
];
//horizontal 1.2
final _tile5sh = [
  _tile(6, 5),
  _tile(6, 5),
  _tile(4, 4),
  _tile(4, 4),
  _tile(4, 4),
];
//*square end---

///crossAxisCount, mainAxisCount, tile
///1
List<Tuple3> tilePost1pn = [Tuple3(4, 2, _tile1pn)];
List<Tuple3> tilePost1l = [Tuple3(4, 3, _tile1l)];
List<Tuple3> tilePost1sq = [Tuple3(4, 4, _tile1sq)];
List<Tuple3> tilePost1p = [Tuple3(4, 6, _tile1p)];

///2
List<Tuple3> tilePost2pn = [
  Tuple3(4, 4, _tile2pn),
  Tuple3(4, 5, _tile2pnt),
];

List<Tuple3> tilePost2l = [
  Tuple3(4, 6, _tile2l),
  Tuple3(4, 5, _tile2ls),
];

//square
List<Tuple3> tilePost2s = [
  Tuple3(4, 2, _tile2hs),
  Tuple3(4, 8, _tile2vs),
];

//portrait
List<Tuple3> tilePost2p = [
  Tuple3(4, 3, _tile2p),
];

///3
//pano
List<Tuple3> tilePost3pn = [
  Tuple3(4, 4, _tile3pn),
];
//landscape
List<Tuple3> tilePost3l = [
  Tuple3(4, 5, _tile3l),
];

//square
List<Tuple3> tilePost3s = [
  Tuple3(4, 4, _tile3sb),
  Tuple3(10, 8, _tile3sr),
];

//portrait
List<Tuple3> tilePost3p = [
  Tuple3(10, 8, _tile3p),
];

///4
//pano
List<Tuple3> tilePost4pn = [
  Tuple3(6, 5, _tile4pn),
];
//landscape
List<Tuple3> tilePost4l = [
  Tuple3(12, 13, _tile4l),
];
//square
List<Tuple3> tilePost4s = [
  Tuple3(4, 4, _tile4s),
];
//portrait
List<Tuple3> tilePost4p = [
  Tuple3(3, 3, _tile4p),
];

///5
//pano
List<Tuple3> tilePost5pn = [
  Tuple3(7, 6, _tile5l),
];

//landscape
List<Tuple3> tilePost5l = [
  Tuple3(7, 6, _tile5l),
  Tuple3(12, 9, _tile5sh),
];

//square
List<Tuple3> tilePost5s = [
  Tuple3(6, 6, _tile5s),
  Tuple3(12, 9, _tile5sh),
  Tuple3(6, 5, _tile5sht),
];

//images count, first image orientation: set of tiles
final Map<Tuple2<int, ImageOrientation>, List<Tuple3>> _tileMapPost = {
  ///1
  Tuple2(1, ImageOrientation.pano): tilePost1pn,
  Tuple2(1, ImageOrientation.landscape): tilePost1l,
  Tuple2(1, ImageOrientation.square): tilePost1sq,
  Tuple2(1, ImageOrientation.portrait): tilePost1p,

  ///2
  Tuple2(2, ImageOrientation.pano): tilePost2pn,
  Tuple2(2, ImageOrientation.landscape): tilePost2l,
  Tuple2(2, ImageOrientation.square): tilePost2s,
  Tuple2(2, ImageOrientation.portrait): tilePost2p,

  ///3
  Tuple2(3, ImageOrientation.pano): tilePost3pn,
  Tuple2(3, ImageOrientation.landscape): tilePost3l,
  Tuple2(3, ImageOrientation.square): tilePost3s,
  Tuple2(3, ImageOrientation.portrait): tilePost3p,

  ///4
  Tuple2(4, ImageOrientation.pano): tilePost4pn,
  Tuple2(4, ImageOrientation.landscape): tilePost4l,
  Tuple2(4, ImageOrientation.square): tilePost4s,
  Tuple2(4, ImageOrientation.portrait): tilePost4p,

  ///5
  //ugly
  Tuple2(5, ImageOrientation.pano): tilePost5pn,
  //ok
  Tuple2(5, ImageOrientation.landscape): tilePost5l,
  Tuple2(5, ImageOrientation.square): tilePost5s,
  //---ok---
  //ugly
  Tuple2(5, ImageOrientation.portrait): tilePost5s,
};

sqStaggered(
  List<Widget> list, {
  List<StaggeredGridTile>? stagger,
  double mainAxisSpacing = 10.0,
  double crossAxisSpacing = 8.0,
}) {
  final staggeredTiles = stagger ?? _grid(list.length);
  final children = List.generate(
    list.length,
    (index) => StaggeredGridTile.count(
      crossAxisCellCount: staggeredTiles[index].crossAxisCellCount,
      mainAxisCellCount: staggeredTiles[index].mainAxisCellCount ?? 0,
      child: list[index],
    ),
  ).toList();
  return list.isNotEmpty
      ? StaggeredGrid.count(
          crossAxisCount: sizeStaggered,
          mainAxisSpacing: mainAxisSpacing,
          crossAxisSpacing: crossAxisSpacing,
          // shrinkWrap: true,
          // physics: const ClampingScrollPhysics(),
          // staggeredTiles: stagger ?? _grid(list.length),
          children: stagger ?? children,
        )
      : Container();
}

//=>crossAxisCount, mainAxisCount, tile
Tuple3 crossTile(int l, ImageOrientation orientation) {
  final key = Tuple2(l, orientation);
  final size = _tileMapPost[key]!.length;
  final x = min(Random().nextInt(size), size - 1);
  return _tileMapPost[key]![x];
}

postStaggered(
  List list,
  Tuple3 crossTile, {
  double mainAxisSpacing = 3.5,
  double crossAxisSpacing = 3.5,
}) {
  final List tiles = crossTile.item3;
  final crossAxisCount = crossTile.item1;
  final l = tiles.length;
  final children = List.generate(
      l,
      (index) => StaggeredGridTile.count(
            crossAxisCellCount: tiles[index].crossAxisCellCount,
            mainAxisCellCount: tiles[index].mainAxisCellCount ?? 0,
            child: list[index],
          )).toList();
  return list.isNotEmpty
      ? StaggeredGrid.count(
          crossAxisCount: crossAxisCount,
          // shrinkWrap: true,
          // padding: const EdgeInsets.all(0),
          // physics: const ClampingScrollPhysics(),
          // staggeredTileBuilder: (int index) => tiles[min(index, l - 1)],
          // itemBuilder: (BuildContext context, int index) => list[index],
          // itemCount: list.length,
          mainAxisSpacing: mainAxisSpacing,
          crossAxisSpacing: crossAxisSpacing,
          children: children,
        )
      : Container();
}

List<StaggeredGridTile> _grid(int count) {
  final res = <StaggeredGridTile>[];
  final l = count % 2 == 0 ? count : count - 1;
  res.addAll(List.generate(l, (i) => _tile(2, 2)));
  if (count > l) res.add(_tile(4, 2));
  return res;
}

const sizeStaggered = 4;
const sizeTilePost = 5;

int _percent(String text, int crossCount, double scale, double width) {
  final l = text.length;
  final size = 10 * l * scale;
  final per = size * crossCount / width;
  var p = per.ceil();
  p = max(1, p);
  return p;
}

Widget staggered<T>(BuildContext context, List<T> data, String toText(T),
    bool isSelected(T), Widget viewItem(T, bool)) {
  final width = MediaQuery.of(context).size.width;
  const scale = 1.0;
  final x = max(scale, 1);
  const _maxCrossExtent = 100.0;
  final _crossCount = (width / _maxCrossExtent).round();

  final tiles = data
      .map((model) => _percent(toText(model), _crossCount, scale, width))
      .toList();

  final children = List.generate(data.length, (index) {
    final tile = _tile(tiles[index], 0.5 * x);
    final model = data[index];
    final selected = isSelected(model);
    return StaggeredGridTile.count(
      crossAxisCellCount: tile.crossAxisCellCount,
      mainAxisCellCount: tile.mainAxisCellCount ?? 0,
      child: Container(
          color: Colors.transparent,
          padding: const EdgeInsets.symmetric(vertical: 4),
          margin: const EdgeInsets.symmetric(vertical: 1),
          child: viewItem(model, selected)),
    );
  }).toList();

  return StaggeredGrid.count(
    // primary: false,
    // shrinkWrap: true,
    // physics: const NeverScrollableScrollPhysics(),
    crossAxisCount: _crossCount,
    crossAxisSpacing: 4.0,
    children: children,
    // itemCount: data.length,
    // itemBuilder: (context, index) {
    //   final model = data[index];
    //   final selected = isSelected(model);
    //   return Container(
    //       color: Colors.transparent,
    //       padding: const EdgeInsets.symmetric(vertical: 4),
    //       margin: const EdgeInsets.symmetric(vertical: 1),
    //       child: viewItem(model, selected));
    // },
    // staggeredTileBuilder: (index) {
    //   return _tile(tiles[index], 0.5 * x);
    // },
  );
}
