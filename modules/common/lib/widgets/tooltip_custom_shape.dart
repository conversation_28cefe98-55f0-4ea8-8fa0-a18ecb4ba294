/*
 * Created by DuanVH 
 * on 02/08/2022
 */
import 'package:flutter/material.dart';
import 'dart:math';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TooltipCustom extends StatefulWidget {
  final String? message;
  final EdgeInsetsGeometry? margin;
  final Color? color;
  final double? verticalOffset;
  final bool? preferBelow;
  static Color toolTipDefaultColor = const Color(0xFFF04F24);

  const TooltipCustom({
    Key? key,
    this.message,
    this.margin,
    this.color,
    this.verticalOffset,
    this.preferBelow,
  }) : super(key: key);

  @override
  State<TooltipCustom> createState() => _TooltipWidgetState();
}

class _TooltipWidgetState extends State<TooltipCustom> {
  @override
  initState() {
    super.initState();
    WidgetsBinding.instance?.addPostFrameCallback((timeStamp) {
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    var dx = 0.0;
    var widthScreen = MediaQuery.of(context).size.width;
    var halfScreen = widthScreen / 2;
    final widthIcon = 14.h;
    final box = context.findRenderObject();
    if (box is RenderBox) {
      Offset position =
          box.localToGlobal(Offset.zero); //this is global position
      dx = position.dx; //this is y - I think it's what you want
    }
    return Tooltip(
      message: widget.message ?? '',
      child: Icon(
        Icons.info_outline,
        color: widget.color ?? TooltipCustom.toolTipDefaultColor,
        size: widthIcon,
      ),
      triggerMode: TooltipTriggerMode.tap,
      showDuration: const Duration(seconds: 1),
      decoration: ShapeDecoration(
        color: Theme.of(context).primaryColor,
        shape: ToolTipCustomShape(
          preferBelow: widget.preferBelow ?? false,
          widthIcon: widthIcon,
        ),
      ),
      verticalOffset: widget.verticalOffset ?? 16,
      margin: widget.margin ??
          EdgeInsets.only(
            left: dx >= halfScreen ? 10 : max(dx - widthIcon, 0),
            right: dx >= halfScreen ? max((widthScreen - dx), 0) : 10,
          ),
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      textStyle:
          Theme.of(context).textTheme.bodySmall?.copyWith(color: Colors.white),
    );
  }
}

class ToolTipCustomShape extends ShapeBorder {
  final bool preferBelow;
  final double? widthIcon;

  ToolTipCustomShape({this.preferBelow = true, this.widthIcon = 16});

  @override
  EdgeInsetsGeometry get dimensions => EdgeInsets.zero;

  @override
  Path getInnerPath(Rect rect, {TextDirection? textDirection}) => Path();

  @override
  Path getOuterPath(Rect rect, {TextDirection? textDirection}) {
    rect = Rect.fromPoints(rect.topLeft, rect.bottomRight);
    final _widthIcon = widthIcon ?? 10;
    if (preferBelow == true) {
      return Path()
        ..addRRect(RRect.fromRectAndRadius(rect, Radius.circular(8)))
        ..moveTo(rect.bottomLeft.dx + _widthIcon, rect.bottomCenter.dy)
        ..relativeLineTo(5, 5)
        ..relativeLineTo(5, -5)
        ..close();
    }
    return Path()
      ..addRRect(RRect.fromRectAndRadius(rect, Radius.circular(8)))
      ..moveTo(rect.bottomLeft.dx + _widthIcon, rect.topCenter.dy)
      ..relativeLineTo(5, -5)
      ..relativeLineTo(5, 5)
      ..close();
  }

  @override
  void paint(Canvas canvas, Rect rect, {TextDirection? textDirection}) {}

  @override
  ShapeBorder scale(double t) => this;
}
