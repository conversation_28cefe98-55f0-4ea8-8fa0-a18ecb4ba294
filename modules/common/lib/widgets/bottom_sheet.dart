import 'dart:io';

import 'package:common/ks_common.dart';
import 'package:common/utils/permission_util.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:share/share.dart';

class BottomSheetPdf extends StatefulWidget {
  final String? title;
  final String path;
  final String? fileName;

  const BottomSheetPdf({
    Key? key,
    this.title,
    required this.path,
    this.fileName,
  }) : super(key: key);

  @override
  _BottomSheetPdfState createState() => _BottomSheetPdfState();
}

class _BottomSheetPdfState extends State<BottomSheetPdf> {
  GlobalKey<PdfWidgetState> pdfViewKey = GlobalKey<PdfWidgetState>();

  _onPressShare() async {
    File? pdfFile = pdfViewKey.currentState?.getPdfFile();
    if (pdfFile != null) {
      if (Platform.isAndroid) {
        final status = await PermissionUtil.checkPermission(
            context, Permission.storage, 'Bộ nhớ');
        if (status) {
          pdfFile = await FileUtils.copyFileToDownload(pdfFile);
          Share.shareFiles(
            [pdfFile.path],
            subject: widget.title ?? '',
          );
        }
      } else {
        Share.shareFiles(
          [pdfFile.path],
          subject: widget.title ?? '',
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(color: Theme.of(context).cardColor),
      child: Stack(
        children: [
          Column(
            children: [
              BottomSheetHeader(
                title: widget.title ?? '',
                hasAnchor: false,
                action: IconButton(
                  icon: Icon(
                    Icons.ios_share,
                    color: Theme.of(context).textTheme.titleMedium?.color,
                  ),
                  onPressed: _onPressShare,
                ),
              ),
              Expanded(
                child: PdfWidget(
                  key: pdfViewKey,
                  path: widget.path,
                  swipeHorizontal: false,
                  fileName: widget.fileName,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class BottomSheetHeader extends StatelessWidget {
  final String? title;
  final bool hasAnchor;
  final bool hasDivider;
  final bool hasCloseButton;
  final Widget? action;

  const BottomSheetHeader({
    Key? key,
    this.title,
    this.hasAnchor = true,
    this.hasDivider = true,
    this.hasCloseButton = true,
    this.action,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (hasAnchor)
          Container(
            width: 50,
            height: 6,
            margin: EdgeInsets.only(top: 10),
            decoration: BoxDecoration(
              color:
                  context.isDarkTheme ? Color(0xFF333333) : Color(0xFFE2E2E2),
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        Padding(
          padding: const EdgeInsets.only(top: 5),
          child: Row(
            children: [
              if (hasCloseButton == true)
                IconButton(
                    icon: Icon(
                      Icons.close,
                      color: StyleApp.titleColor(context),
                    ),
                    onPressed: () => Navigator.pop(context)),
              if (title != null && title!.length > 0)
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(
                      right: hasCloseButton ? 50 : 0,
                      left: action != null ? 50 : 0,
                    ),
                    child: Text(
                      title ?? '',
                      style: StyleApp.titleStyle(context),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              action ?? Container(),
            ],
          ),
        ),
        if (hasDivider) SizedBox(height: 5),
        if (hasDivider)
          Divider(
            height: 1,
            color: !context.isDarkTheme
                ? Theme.of(context).dividerColor
                : Colors.white.withOpacity(0.5),
          ),
      ],
    );
  }
}
