/*
* Created by DuanVH 
* on 4/26/2021.
*/
import 'package:flutter/material.dart';

import 'ink_well_button.dart';

class BottomButton extends StatelessWidget {
  final String? title;
  final VoidCallback? onTap;
  final double? width;
  final bool isDisable;
  final bool? isDivider;
  final Color? textColor;
  final Color? buttonColor;
  final Color? buttonDarkColor;
  final Color? backgroundColor;
  final Widget? hint;
  final Widget? titleWidget;
  final double? radius;
  final Color? borderColor;

  BottomButton({
    @required this.title,
    @required this.onTap,
    this.width,
    this.isDisable = false,
    this.isDivider = true,
    this.textColor,
    this.buttonColor,
    this.backgroundColor,
    this.hint,
    this.titleWidget,
    this.radius,
    this.borderColor,
    this.buttonDarkColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: backgroundColor ?? Theme.of(context).cardColor,
      child: SafeArea(
        top: false,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            isDivider == true ? Divider(height: 1) : SizedBox(),
            SizedBox(height: 15),
            if (hint != null) hint!,
            Container(
              padding: EdgeInsets.only(left: 15, right: 15, bottom: 15),
              child: InkWellButton(
                title: title ?? '',
                width: width,
                labelWidget: titleWidget,
                textColor: textColor ?? Theme.of(context).cardColor,
                buttonColor: buttonColor,
                fontSize: Theme.of(context).textTheme.bodyMedium!.fontSize,
                isDisable: isDisable,
                onTap: onTap,
                radius: radius,
                borderColor: borderColor,
                buttonDarkColor: buttonDarkColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
