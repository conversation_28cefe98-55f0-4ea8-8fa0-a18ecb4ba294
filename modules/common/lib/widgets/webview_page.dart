import 'dart:async';
import 'dart:io';

import 'package:common/ks_common.dart';
import 'package:common/model/option_sheet_model.dart';
import 'package:common/utils/dialog_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_android/webview_flutter_android.dart';
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';

import 'bar/appbar_button_widget.dart';

const FILE_EXTENSIONS = [
  '.pdf',
  '.doc',
  '.docx',
  '.ppt',
  '.pptx',
  '.xls',
  '.xlsx'
];

const customScheme = [
  'ksbank://',
  'kssecurities://',
  'sunshine://',
  'ksfinance://',
];

const testMiniApp = false;

class WebViewPage extends StatefulWidget {
  final String? url;
  final String? title;
  final String? icon;
  final SystemUiOverlayStyle? overlayStyle;
  final Color? backgroundColor;
  final bool? showAppBar;
  final bool? safeArea;
  final bool? zoomEnabled;
  final bool showBottomBtn;

  const WebViewPage({
    super.key,
    this.url,
    this.title,
    this.icon,
    this.overlayStyle,
    this.backgroundColor,
    this.showAppBar,
    this.safeArea = true,
    this.zoomEnabled = true,
    this.showBottomBtn = false,
  });

  @override
  State<WebViewPage> createState() => _WebViewPageState();
}

class _WebViewPageState extends State<WebViewPage> with RouteAware {
  final String trickJs = '123;';
  late WebViewController _webController;
  final _loadingStream = StreamController<bool>()..add(false);
  bool _showAppBar = true;
  Color? _backgroundColor;
  SystemUiOverlayStyle? _overlayStyle;
  var _firstLoad = true;
  var clearCache = false;
  late PlatformWebViewControllerCreationParams params;

  @override
  void initState() {
    super.initState();
    // Exception on clear cached android
    // if (Platform.isAndroid) WebView.platform = SurfaceAndroidWebView();
    _overlayStyle = widget.overlayStyle;
    _backgroundColor = widget.backgroundColor;
    _showAppBar = widget.showAppBar ?? true;

    if (WebViewPlatform.instance is WebKitWebViewPlatform) {
      ///IOS
      params = WebKitWebViewControllerCreationParams(
        allowsInlineMediaPlayback: true,
        mediaTypesRequiringUserAction: const <PlaybackMediaTypes>{},
      );
    } else {
      ///Android
      params = const PlatformWebViewControllerCreationParams();
    }

    _webController = WebViewController.fromPlatformCreationParams(params)
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..enableZoom(widget.zoomEnabled ?? false)
      ..setNavigationDelegate(NavigationDelegate(
        onPageStarted: (String url) {
          _loadingStream.add(true);
        },
        onPageFinished: (String url) {
          _loadingStream.add(false);
          _updateFirstLoad();
        },
        onNavigationRequest: (action) async {
          final url = action.url;
          final isOpenFileAndroid =
              Platform.isAndroid && _checkFileExtension(url);
          final isInstallApp = _isInstallApp(url);
          if (isOpenFileAndroid || isInstallApp) {
            openUrl(url: url);
            return NavigationDecision.prevent;
          }

          return NavigationDecision.navigate;
        },
      ));
    if (!widget.url.isNullOrEmpty) {
      _webController.loadRequest(Uri.parse(widget.url ?? ""));
    }

    if (_webController.platform is AndroidWebViewController) {
      AndroidWebViewController.enableDebugging(testMiniApp);
      (_webController.platform as AndroidWebViewController)
          .setMediaPlaybackRequiresUserGesture(false);
    } else {}
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    super.dispose();
    _loadingStream.close();
    if (clearCache == true) {
      _webController.clearCache();
    }
  }

  @override
  void didPopNext() {
    super.didPopNext();
    _webController.runJavaScript(
        '''SunshineWebSdk.onResume();$trickJs''').catchError((e) {
      logger.t(e);
    });
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvoked: (didPop) => _back(),
      child: AnnotatedRegion<SystemUiOverlayStyle>(
        value: _overlayStyle ?? SystemUiOverlayStyle.dark,
        child: Scaffold(
          appBar: _getAppBar(),
          body: Column(
            children: [
              if (!widget.url.isNullOrEmpty)
                Expanded(
                  child: _getBody(
                    safeArea:
                        widget.showBottomBtn == true ? false : widget.safeArea,
                  ),
                ),
              // if (_firstLoad) _loadingPage(),
              if (widget.showBottomBtn) _bottomNavigate(),
            ],
          ),
        ),
      ),
    );
  }

  _getAppBar() {
    return _showAppBar == true
        ? AppBarCustom(
            leading: _getPreviousButton(),
            title: widget.title ?? 'KienlongBank',
            loadingStream: _loadingStream.stream,
            //titleSpacing: 0,
            actions: [
              AppbarButtonWidget(
                onLeftPressed: () async {
                  final item = await DialogUtil.options<OptionSheetModel>(
                    context,
                    adaptive: false,
                    creatorItem: (item) {
                      return Container(
                        child: Row(
                          children: [
                            item.icon ?? Container(),
                            const SizedBox(width: 10),
                            Text(item.name),
                          ],
                        ),
                      );
                    },
                    options: [
                      OptionSheetModel(
                        id: 0,
                        name: 'Mở bằng trình duyệt',
                        icon: Icon(
                          Icons.open_in_browser_rounded,
                          size: 20,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                      OptionSheetModel(
                        id: 1,
                        name: 'Tải lại',
                        icon: Icon(
                          Icons.refresh_rounded,
                          size: 20,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                      OptionSheetModel(
                        id: 2,
                        name: 'Xoá bộ nhớ tạm',
                        icon: Icon(
                          Icons.layers_clear_rounded,
                          size: 20,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    ],
                  );
                  final index = item?.id;
                  if (index == 0) {
                    openUrl(url: widget.url);
                  } else if (index == 1) {
                    _webController.reload();
                  } else if (index == 2) {
                    _webController.clearCache();
                  }
                },
              )
            ],
          )
        : null;
  }

  _getPreviousButton() {
    return TextButton(
      child: Container(
        alignment: Alignment.center,
        child: Icon(
          Icons.arrow_back_ios,
          color: Theme.of(context).appBarTheme.actionsIconTheme?.color,
        ),
      ),
      onPressed: () async {
        final onWillPop = await _back();
        if (onWillPop == true && mounted) {
          Navigator.of(context).pop();
        }
      },
    );
  }

  Future<bool> _back() async {
    final canBack = await _webController.canGoBack();
    if (canBack == true && mounted) {
      _webController.goBack();
      return Future.value(false);
    }
    return Future.value(true);
  }

  _loadingPage() {
    return Container(
      color: _backgroundColor,
      alignment: Alignment.center,
      child: const Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.max,
        children: [
          CupertinoActivityIndicator(),
        ],
      ),
    );
  }

  _updateFirstLoad() {
    Future.delayed(const Duration(seconds: 2), () {
      if (_firstLoad && mounted) {
        setState(() {
          _firstLoad = false;
        });
      }
    });
  }

  bool _isInstallApp(String url) {
    if (url.isEmpty) return false;
    final regex = customScheme.join("|");
    return url.startsWith(RegExp(regex)) ||
        url.contains(RegExp(r'itms-appss://apps.apple.com|play.google.com'));
  }

  Widget _getBody({bool? safeArea}) {
    return Container(
      color: _backgroundColor ?? Theme.of(context).colorScheme.surface,
      child: SafeArea(
        bottom: safeArea ?? true,
        child: WebViewWidget(
          controller: _webController,
          //  WebView.gestureNavigationEnabled -> WebKitWebViewController.setAllowsBackForwardNavigationGestures
          //prevent white screen when using swipe to back on IOS
          gestureRecognizers: Set()
            ..add(Factory<VerticalDragGestureRecognizer>(
              () => VerticalDragGestureRecognizer(),
            ))
            ..add(Factory<HorizontalDragGestureRecognizer>(
              () => HorizontalDragGestureRecognizer(),
            )),
          //  WebView.debuggingEnabled -> static AndroidWebViewController.enableDebugging
          //
        ),
      ),
    );
  }

  bool _checkFileExtension(String url) {
    for (var extension in FILE_EXTENSIONS) {
      if (url.contains(extension)) {
        return true;
      }
    }
    return false;
  }

  Widget _bottomNavigate() {
    return SafeArea(
      top: false,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10)
            .copyWith(right: toSp(16)),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
            top: BorderSide(color: Theme.of(context).dividerColor, width: 1),
          ),
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                IconButton(
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    _webController.goBack();
                  },
                  icon: Icon(
                    Icons.arrow_back_ios_outlined,
                    size: toSp(20),
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                IconButton(
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    _webController.goForward();
                  },
                  icon: Icon(
                    Icons.arrow_forward_ios_outlined,
                    size: toSp(20),
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                IconButton(
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  icon: Icon(
                    Icons.home,
                    size: toSp(24),
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                IconButton(
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    _webController.reload();
                  },
                  icon: Icon(
                    Icons.restart_alt_outlined,
                    size: toSp(24),
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
