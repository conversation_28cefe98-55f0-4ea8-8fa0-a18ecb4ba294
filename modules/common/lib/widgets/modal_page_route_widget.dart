import 'package:common/ks_common.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';

class ModalPageRouteWidget extends StatelessWidget {
  final WidgetBuilder builder;

  ModalPageRouteWidget({required this.builder});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Navigator(
        onGenerateRoute: (_) {
          logger.v(_);
          return MaterialWithModalsPageRoute(
            builder: (context) => CupertinoPageScaffold(
              child: Builder(builder: builder),
            ),
          );
        },
        onUnknownRoute: (route) {
          logger.v(route);
        },
        observers: [
          _NavigatorObserver(onEndRoute: () {
            Navigator.of(context).pop();
          }),
        ],
      ),
    );
  }
}

class _NavigatorObserver extends RouteObserver<PageRoute<dynamic>> {
  final VoidCallback? onEndRoute;

  _NavigatorObserver({this.onEndRoute});

  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPush(route, previousRoute);
    logger.t(previousRoute?.settings, error: route.settings);
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
    logger.t(oldRoute?.settings, error: newRoute?.settings);
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPop(route, previousRoute);
    logger.t(route.settings, error: previousRoute?.settings);
    if (previousRoute == null) {
      onEndRoute?.call();
    }
  }
}
