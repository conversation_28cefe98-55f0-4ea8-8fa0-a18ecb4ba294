import 'dart:math';

import 'package:common/assets.dart';
import 'package:common/ks_common.dart';
import 'package:common/model/base_item.dart';
import 'package:common/widgets/money_widget.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

enum ChartDisplay { MODE1, MODE2 }

class RoomInvestPriceChangeChart extends StatefulWidget {
  final String? title;
  final String? date;
  final Color? barColor;
  final double divideValue;
  final String? verticalAxisName;
  final List<BaseItem> chartItems;
  final double? height;
  final Widget? subWidget;
  final double? heightSubWidget;

  const RoomInvestPriceChangeChart({
    Key? key,
    this.title,
    this.date,
    this.barColor,
    required this.chartItems,
    this.divideValue = 1000000,
    this.verticalAxisName = '',
    this.height,
    this.subWidget,
    this.heightSubWidget,
  }) : super(key: key);

  @override
  State<RoomInvestPriceChangeChart> createState() =>
      _RoomInvestPriceChangeChartState();
}

class _RoomInvestPriceChangeChartState extends State<RoomInvestPriceChangeChart>
    with PriceChartChangeFunction {
  double? maxY;
  double? minY;
  double? maxX;
  int touchedGroupIndex = -1;
  ChartDisplay displayType = ChartDisplay.MODE1;

  double getHeightView() {
    if (displayType == ChartDisplay.MODE1) {
      return (widget.height ?? 300.h);
    }
    final tableHeight = widget.chartItems.length * 56.h;
    final headerHeight = 100.h;
    var heightSubWidget = 0.0;
    if (widget.subWidget != null) {
      heightSubWidget = (widget.heightSubWidget ?? 30.h) + 16;
    }
    return tableHeight + headerHeight + heightSubWidget;
  }

  @override
  Widget build(BuildContext context) {
    final updatedDate = widget.date ?? '';
    final defaultPadding = 16.0;
    return SizedBox(
      height: getHeightView(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: defaultPadding),
                    child: Text(
                      widget.title ?? 'Biến động giá',
                      style: StyleApp.subtitle1(context, true),
                    ),
                  ),
                  if (updatedDate.isNotEmpty)
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: defaultPadding),
                      child: Text(
                        'Cập nhật lúc $updatedDate',
                        style:
                            StyleApp.bodyText1(context)?.copyWith(height: 1.5),
                      ),
                    ),
                ],
              ),
              Spacer(),
              if (widget.chartItems.isNotEmpty) switchButton(),
              SizedBox(width: defaultPadding)
            ],
          ),
          const SizedBox(height: 16),
          if (widget.subWidget != null) ...[
            widget.subWidget!,
            const SizedBox(height: 16),
          ],
          if (displayType == ChartDisplay.MODE1)
            Row(
              children: [
                const Spacer(),
                Text(
                  widget.verticalAxisName ?? '',
                  style: TextStyle(
                      fontSize: Theme.of(context).textTheme.bodySmall?.fontSize),
                ),
                SizedBox(width: defaultPadding)
              ],
            ),
          if (displayType == ChartDisplay.MODE1) const SizedBox(height: 16),
          Expanded(
            child: displayType == ChartDisplay.MODE1
                ? Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: _buildBarChart(),
                  )
                : _buildTable(),
          ),
        ],
      ),
    );
  }

  Widget _buildBarChart() {
    return Stack(
      children: [
        BarChart(
          BarChartData(
            barTouchData: barTouchData,
            titlesData: titlesData,
            borderData: borderData,
            barGroups: handleData(),
            alignment: BarChartAlignment.spaceAround,
            gridData: FlGridData(
              drawVerticalLine: false,
              getDrawingHorizontalLine: (value) => FlLine(
                color: Theme.of(context).dividerColor,
                strokeWidth: 0.4,
              ),
            ),
            maxY: maxY,
            minY: minY,
          ),
        ),
        if (widget.chartItems.isEmpty)
          Center(
            child: Opacity(
              opacity: 0.8,
              child: Text(
                'Không có dữ liệu',
                style: StyleApp.bodyText1(context),
              ),
            ),
          )
      ],
    );
  }

  Widget _buildTable() {
    return Column(
      children: [
        Container(
          color: Theme.of(context).colorScheme.background,
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          child: Row(
            children: [Text('Thời gian'), Spacer(), Text('Triệu/m2')],
          ),
        ),
        Expanded(
          child: ListView.separated(
            physics: ClampingScrollPhysics(),
            padding: EdgeInsets.all(0),
            itemBuilder: (context, index) {
              final item = widget.chartItems[index];
              return ListTile(
                title: getDateWidget(item.title, isFutureSt: item.isSelected),
                trailing: MoneyWidget(amount: toDouble(item.subTitle)),
              );
            },
            separatorBuilder: (context, index) => Divider(height: 1),
            itemCount: widget.chartItems.length,
          ),
        )
      ],
    );
  }

  BarTouchData get barTouchData => BarTouchData(
        enabled: true,
        handleBuiltInTouches: false,
        touchCallback: (event, response) {
          if (event is FlTapUpEvent &&
              response != null &&
              response.spot != null) {
            setState(() {
              if (response.spot!.touchedBarGroupIndex == touchedGroupIndex) {
                touchedGroupIndex = -1;
              } else {
                touchedGroupIndex = response.spot!.touchedBarGroupIndex;
              }
            });
          }
        },
        touchTooltipData: BarTouchTooltipData(
          getTooltipColor: (_) => Colors.transparent,
          tooltipPadding: const EdgeInsets.all(0),
          tooltipMargin: 0,
          getTooltipItem: (
            group,
            groupIndex,
            rod,
            rodIndex,
          ) {
            return rod.toY.round() > 0
                ? BarTooltipItem(
                    // '${rod.toY.round().toString()}${widget.unitValueShort}',
                    toDouble(widget.chartItems[groupIndex].subTitle)
                            .currencyFormat +
                        MoneyWidget.unitDefault,
                    TextStyle(
                      // color: const Color(0xff292663),
                      fontWeight: FontWeight.w400,
                      fontSize: 12.sp,
                    ),
                    textAlign: TextAlign.left,
                  )
                : BarTooltipItem('', const TextStyle());
          },
        ),
      );

  FlTitlesData get titlesData => FlTitlesData(
        show: true,
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            getTitlesWidget: (value, meta) {
              if (value >= widget.chartItems.length) {
                return SizedBox();
              }
              return Padding(
                padding: const EdgeInsets.only(top: 8),
                child: getDateWidget(
                  widget.chartItems[value.toInt()].title,
                  fontSize: 10.sp,
                  isFutureSt: widget.chartItems[value.toInt()].isSelected,
                ),
              );
            },
          ),
        ),
        rightTitles: AxisTitles(
          sideTitles: rightTitles(
            getTitles: (value, meta) {
              return Padding(
                padding: EdgeInsets.only(top: 8),
                child: Text(
                  '${value.toInt()}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              );
            },
          ),
        ),
        // rightTitles: AxisTitles(
        //   sideTitles: SideTitles(
        //     reservedSize: 40,
        //     showTitles: true,
        //   ),
        // ),
        leftTitles: AxisTitles(),
        topTitles: AxisTitles(),
      );

  leftTitles({required GetTitleWidgetFunction getTitles}) => AxisTitles(
          sideTitles: SideTitles(
        getTitlesWidget: getTitles,
        showTitles: true,
        interval: 1,
        reservedSize: 40,
      ));

  SideTitles rightTitles({required GetTitleWidgetFunction getTitles}) {
    final _maxY = maxY ?? 0.0;
    final _minY = minY ?? 1.0;
    return SideTitles(
      getTitlesWidget: getTitles,
      showTitles: true,
      interval: minY == null || (_maxY / _minY) > 5 ? null : 50,
      reservedSize: 40,
    );
  }

  FlBorderData get borderData => FlBorderData(
        show: true,
        border: Border(
          bottom: BorderSide(
              color: widget.chartItems.isEmpty
                  ? Theme.of(context).dividerColor
                  : Theme.of(context).primaryColor,
              width: 1),
          left: const BorderSide(color: Colors.transparent),
          right: const BorderSide(color: Colors.transparent),
          top: const BorderSide(color: Colors.transparent),
        ),
      );

  List<BarChartGroupData> handleData() {
    if (widget.chartItems.isNotEmpty) {
      final values =
          widget.chartItems.map((e) => toDouble(e.subTitle)).toList();
      final maxValue = values.reduce(((value, element) => max(value, element)));
      maxY = maxValue / widget.divideValue;
      // final maxContract = widget.dataByMonth!
      //     .map((e) => e.revenueQty)
      //     .toList()
      //     .reduce(((value, element) => max(value!, element!)))!;
      // maxY = (maxSale > maxContract ? maxSale : maxContract) as double;

      if (values.length == 1) {
        var distance = 1;
        if (maxY != null && (maxY! > 10)) {
          distance = maxY! ~/ 10;
        }
        maxY = maxY! + distance;
      }
      /////////
      maxX = widget.chartItems.length.toDouble() - 1;
      ////////////
      if (values.length > 1) {
        final minValue =
            values.reduce(((value, element) => min(value, element)));
        minY = minValue / widget.divideValue;
      }
    }
    final barGroups = <BarChartGroupData>[];
    if (widget.chartItems.isEmpty) {
      for (var i = 0; i < 3; i++) {
        barGroups.add(
          BarChartGroupData(
            x: i,
            barsSpace: 0,
            barRods: [
              BarChartRodData(
                toY: (i + 1) * 10,
                width: 12.h,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
                color: Theme.of(context).dividerColor,
              ),
            ],
            showingTooltipIndicators: [],
          ),
        );
      }
    }
    for (var i = 0; i < widget.chartItems.length; i++) {
      final item = widget.chartItems[i];
      Color? barColor = (widget.barColor ?? Theme.of(context).primaryColor);
      final barColorFuture = barColor.withOpacity(0.5);
      barGroups.add(
        BarChartGroupData(
          x: i,
          barsSpace: 0,
          barRods: [
            BarChartRodData(
              toY: toDouble(item.subTitle) / widget.divideValue,
              width: 12.h,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
              color: _isFuture(item.title, isPassedSt: item.isSelected)
                  ? barColorFuture
                  : barColor,
            ),
          ],
          showingTooltipIndicators: touchedGroupIndex == i ? [0] : [],
        ),
      );
    }
    return barGroups;
  }

  Widget switchButton() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: Theme.of(context).dividerColor,
        ),
      ),
      child: Row(
        children: [
          switchViewButton(
            icon: ImageAssets.ic_list_horizontal,
            active: displayType == ChartDisplay.MODE1,
            onTap: () {
              setState(() {
                displayType = ChartDisplay.MODE1;
              });
            },
          ),
          Container(
            height: 20.h,
            width: 1,
            color: Theme.of(context).dividerColor,
            margin: const EdgeInsets.symmetric(horizontal: 5),
          ),
          switchViewButton(
            icon: ImageAssets.ic_list_vertical,
            active: displayType == ChartDisplay.MODE2,
            onTap: () {
              setState(() {
                displayType = ChartDisplay.MODE2;
              });
            },
          )
        ],
      ),
    );
  }

  Widget switchViewButton({
    required String icon,
    required bool active,
    required VoidCallback onTap,
  }) {
    return InkWell(
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 4,
          vertical: 4,
        ),
        child: ImageAssets.svgAssets(
          icon,
          color: active
              ? Theme.of(context).primaryColor
              : Theme.of(context).dividerColor,
          height: 22.h,
        ),
      ),
      onTap: onTap,
    );
  }
}

mixin PriceChartChangeFunction {
  Widget getDateWidget(String? title, {double? fontSize, dynamic isFutureSt}) {
    return Text.rich(
      TextSpan(
        text: title ?? '',
        style: TextStyle(fontSize: fontSize ?? 14.sp),
        children: [
          if (_isFuture(title, isPassedSt: isFutureSt))
            TextSpan(
              text: "*",
              style: TextStyle(
                fontSize: fontSize ?? 14.sp,
                color: Colors.red,
              ),
            )
        ],
      ),
    );
  }

  /// isPassedSt = 1 | true: Đã thực hiện
  /// isPassedSt = 0 | false: Trong tương lai -> blur
  bool _isFuture(String? title, {dynamic isPassedSt}) {
    if (isPassedSt != null) return !(toBool(isPassedSt));
    final now = DateTime.now();
    var isFuture = false;
    try {
      final date = title.strToDate;
      isFuture = date != null && now.isBefore(date);
    } catch (e) {
      logger.e(e);
    }
    return isFuture;
  }
}
