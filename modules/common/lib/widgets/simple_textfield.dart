/*
* Created by DuanVH 
* on 8/3/2021.
*/
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class SimpleTextField extends StatefulWidget {
  final TextEditingController? controller;
  final FocusNode? node;
  final Function(String?)? onChanged;
  final String? labelText;
  final String? hintText;
  final Widget? trailing;
  final TextInputType? textInputType;
  final List<TextInputFormatter>? inputFormatters;
  final EdgeInsetsGeometry? contentPadding;
  final bool? readOnly;
  final Stream<String>? errorStream;
  final Color? backgroundColor;
  final bool? isDivider;
  final bool? enabled;

  const SimpleTextField({
    Key? key,
    this.controller,
    this.node,
    this.onChanged,
    this.labelText,
    this.hintText,
    this.trailing,
    this.textInputType,
    this.inputFormatters,
    this.contentPadding,
    this.readOnly,
    this.errorStream,
    this.backgroundColor,
    this.isDivider = true,
    this.enabled,
  }) : super(key: key);

  @override
  _SimpleTextFieldState createState() => _SimpleTextFieldState();
}

class _SimpleTextFieldState extends State<SimpleTextField> {
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.node ?? FocusNode();
    _focusNode.addListener(() {
      if (_focusNode.hasFocus == true) {}
      setState(() {});
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<String>(
      stream: widget.errorStream,
      builder: (context, snapshot) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: EdgeInsets.only(left: 15, right: 5),
              decoration: BoxDecoration(
                color: widget.backgroundColor,
                borderRadius: BorderRadius.circular(4),
                border: snapshot.hasData && snapshot.data?.isNotEmpty == true
                    ? Border.all(color: Theme.of(context).colorScheme.error)
                    : widget.isDivider == true
                        ? Border.all(
                            color: _focusNode.hasFocus
                                ? Theme.of(context).primaryColor
                                : Theme.of(context).dividerColor,
                          )
                        : null,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: widget.controller,
                      focusNode: _focusNode,
                      keyboardType: widget.textInputType,
                      readOnly: widget.readOnly ?? false,
                      style: Theme.of(context).textTheme.titleMedium,
                      decoration: InputDecoration(
                        labelText: widget.labelText?.isNotEmpty == true
                            ? widget.labelText
                            : widget.hintText,
                        labelStyle:
                            TextStyle(color: Theme.of(context).disabledColor),
                        hintText: widget.hintText,
                        border: InputBorder.none,
                        enabledBorder: InputBorder.none,
                        isDense: true,
                        contentPadding: widget.contentPadding,
                      ),
                      inputFormatters: widget.inputFormatters,
                      onChanged: widget.onChanged,
                      enabled: widget.enabled ?? true,
                    ),
                  ),
                  widget.trailing ?? Container(),
                ],
              ),
            ),
            snapshot.hasData && snapshot.data?.isNotEmpty == true
                ? Padding(
                    padding: const EdgeInsets.only(top: 2, left: 8),
                    child: Text(
                      snapshot.data!,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.error,
                      ),
                    ),
                  )
                : SizedBox(),
          ],
        );
      },
    );
  }
}
