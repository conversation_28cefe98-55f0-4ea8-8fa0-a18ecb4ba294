import 'package:flutter/material.dart';

import 'ink_well_button.dart';

class Bottom2Button extends StatelessWidget {
  final String? title1;
  final String? title2;
  final VoidCallback? onTapTitle1;
  final VoidCallback? onTapTitle2;
  final bool? isDivider;
  final Color? backgroundColor;
  final Color? colorButton1;
  final Color? colorDarkButton1;
  final Color? colorButton2;
  final Color? colorDarkButton2;
  final Color? colorTextButton1;
  final Color? colorTextButton2;
  final Color? colorBorderButton1;
  final Color? colorBorderButton2;
  final bool isDisable1;
  final bool isDisable2;
  final Widget? hint;
  final double? radius;
  final Widget? labelWidget1;
  final Widget? labelWidget2;

  Bottom2Button({
    this.title1,
    this.title2,
    this.onTapTitle1,
    this.onTapTitle2,
    this.isDivider = true,
    this.backgroundColor,
    this.colorButton1,
    this.colorDarkButton1,
    this.colorButton2,
    this.colorDarkButton2,
    this.colorTextButton1,
    this.colorTextButton2,
    this.colorBorderButton1,
    this.colorBorderButton2,
    this.isDisable1 = false,
    this.isDisable2 = false,
    this.hint,
    this.radius,
    this.labelWidget1,
    this.labelWidget2,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: backgroundColor ?? Theme.of(context).cardColor,
      child: SafeArea(
        top: false,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            isDivider == true ? Divider(height: 1) : SizedBox(),
            SizedBox(height: 15),
            if (hint != null) hint!,
            Container(
              padding: EdgeInsets.only(left: 15, right: 15, bottom: 15),
              child: Row(
                children: [
                  Expanded(
                    child: InkWellButton(
                      title: title1 ?? '',
                      textColor:
                          colorTextButton1 ?? Theme.of(context).cardColor,
                      buttonColor: colorButton1 ?? Colors.grey,
                      buttonDarkColor: colorDarkButton1,
                      borderColor: colorBorderButton1,
                      fontSize: Theme.of(context).textTheme.bodyMedium!.fontSize,
                      onTap: onTapTitle1,
                      isDisable: isDisable1,
                      radius: radius,
                      labelWidget: labelWidget1,
                    ),
                  ),
                  SizedBox(width: 10),
                  Expanded(
                    child: InkWellButton(
                      title: title2 ?? '',
                      textColor:
                          colorTextButton2 ?? Theme.of(context).cardColor,
                      buttonColor: colorButton2,
                      buttonDarkColor: colorDarkButton2,
                      borderColor: colorBorderButton2,
                      fontSize: Theme.of(context).textTheme.bodyMedium!.fontSize,
                      onTap: onTapTitle2,
                      isDisable: isDisable2,
                      radius: radius,
                      labelWidget: labelWidget2,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
