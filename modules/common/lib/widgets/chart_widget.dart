/*
* Created by DuanVH 
* on 6/7/2021.
*/
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';

typedef GetLineTooltipItems = List<LineTooltipItem?> Function(
    List<LineBarSpot> touchedSpots);

class ChartWidget extends StatefulWidget {
  final List<List<double>> dataList;
  final List<num> xDataList;
  final List<Color> colors;
  final Color? fillColor;
  final String? xUnit;
  final String? yUnit;
  final bool? isCurved;
  final AxisTitles? bottomTitles;
  final AxisTitles? leftTitles;
  final GetLineTooltipItems? getLineTooltipItems;

  const ChartWidget(
      {Key? key,
      required this.dataList,
      required this.xDataList,
      required this.colors,
      this.fillColor = Colors.transparent,
      this.xUnit = 'T',
      this.yUnit = '',
      this.isCurved,
      this.bottomTitles,
      this.leftTitles,
      this.getLineTooltipItems})
      : super(key: key);

  @override
  _ChartWidgetState createState() => _ChartWidgetState();
}

class _ChartWidgetState extends State<ChartWidget> {
  static const double _maxX = 11;
  static const double _maxY = 20;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: 1.70,
      child: Container(
        margin: EdgeInsets.only(top: 10),
        child: Padding(
          padding: EdgeInsets.only(right: 15),
          child: LineChart(mainData()),
        ),
      ),
    );
  }

  LineChartData mainData() {
    return LineChartData(
      gridData: FlGridData(
        show: true,
        drawVerticalLine: true,
        getDrawingHorizontalLine: (value) {
          return FlLine(color: Colors.transparent, strokeWidth: 0.5);
        },
        getDrawingVerticalLine: (value) {
          return FlLine(
            color: Theme.of(context).textTheme.bodySmall!.color?.withOpacity(0.1),
            strokeWidth: 0.5,
          );
        },
      ),
      titlesData: FlTitlesData(
        show: true,
        bottomTitles: widget.bottomTitles ??
            AxisTitles(
              sideTitles: SideTitles(
                  showTitles: true,
                  reservedSize: 22,
                  getTitlesWidget: (value, meta) => Text(
                        _getXTitle(value),
                        style: Theme.of(context).textTheme.bodySmall,
                      )),
            ),
        leftTitles: widget.leftTitles ??
            AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 28,
                getTitlesWidget: (value, meta) => Text(
                  _getYTitle(value),
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ),
            ),
        rightTitles: AxisTitles(
          sideTitles: SideTitles(),
        ),
        topTitles: AxisTitles(
          sideTitles: SideTitles(),
        ),
      ),
      borderData: FlBorderData(
        show: true,
        border: Border(
          bottom: BorderSide(color: Theme.of(context).primaryColor),
          left: BorderSide(
            color: Theme.of(context).textTheme.bodySmall!.color!.withOpacity(0.1),
          ),
        ),
      ),
      lineTouchData: LineTouchData(
        enabled: true,
        touchTooltipData: LineTouchTooltipData(
          getTooltipColor: (_) => Theme.of(context).scaffoldBackgroundColor,
          tooltipRoundedRadius: 8.0,
          getTooltipItems: widget.getLineTooltipItems ??
              (items) {
                return items
                    .map(
                      (x) => LineTooltipItem(
                        '${x.y} ${widget.yUnit ?? ''}',
                        Theme.of(context).textTheme.bodySmall!.copyWith(
                              color:
                                  Theme.of(context).textTheme.bodyMedium!.color,
                            ),
                      ),
                    )
                    .toList();
              },
        ),
      ),
      minX: 0,
      maxX: _getMaxX(),
      minY: _getMinY(),
      maxY: _getMaxY(),
      lineBarsData: widget.dataList.isNotEmpty ? _getData() : [],
    );
  }

  List<LineChartBarData> _getData() {
    List<LineChartBarData> chartData = <LineChartBarData>[];
    for (int i = 0; i < widget.dataList.length; i++) {
      List<FlSpot> spots = <FlSpot>[];
      final data = widget.dataList[i];
      for (int j = 0; j < data.length; j++) {
        spots.add(FlSpot(j.toDouble(), data[j]));
      }
      chartData.add(LineChartBarData(
        spots: spots,
        color: widget.colors[i],
        barWidth: 2,
        isStrokeCapRound: true,
        isCurved: widget.isCurved ?? false,
        belowBarData: BarAreaData(
          show: true,
          gradient: LinearGradient(
            begin: Alignment(0, -1),
            end: Alignment(0, 1),
            stops: [0.7, 1],
            colors: [
              i == 0
                  ? widget.fillColor ?? Colors.transparent
                  : Colors.transparent,
              Colors.transparent,
            ],
          ),
        ),
      ));
    }
    return chartData;
  }

  /*
  * NOTE
  * MaxX default = 11 because started index = 0
  * MaxY = max value of list data & default MaxY = 20
  * */
  _getMaxX() {
    double maxX = _maxX;
    if (widget.xDataList.isNotEmpty == true) {
      maxX = widget.xDataList.length.toDouble() - 1;
    }
    return maxX;
  }

  _getMaxY() {
    double maxY = 3;
    if (widget.dataList.isNotEmpty == true) {
      widget.dataList.forEach((element) {
        maxY = element.fold(
            maxY, (value, element) => value > element ? value : element);
      });
    }
    return maxY;
  }

  _getMinY() {
    double minY = 0;
    if (widget.dataList.isNotEmpty == true) {
      widget.dataList.forEach((element) {
        minY = element.fold(
            minY, (value, element) => value < element ? value : element);
      });
    }
    return minY;
  }

  String _getXTitle(double x) {
    double maxX = _getMaxX();
    int value = x.toInt();
    if (widget.xDataList.length < 10) {
      return value > widget.xDataList.length - 1
          ? '$value${widget.xUnit}'
          : '${widget.xDataList[value]}${widget.xUnit}';
    } else {
      String title = '';
      if (value == 0) {
        title = '${widget.xDataList[value].toInt()}';
      } else if (value % 3 == 0 && value != maxX.toInt()) {
        title = '${widget.xDataList[value].toInt()}';
      }
      if (title.isNotEmpty) {
        return '$title${widget.xUnit}';
      }
      return title;
    }
  }

  String _getYTitle(double y) {
    double maxY = _getMaxY();
    int value = y.toInt();
    String title = '';
    if (value == maxY ~/ 4) {
      title = '${maxY ~/ 4}';
    } else if (value == 2 * (maxY ~/ 4)) {
      title = '${(maxY ~/ 4) * 2}';
    } else if (value == 3 * (maxY ~/ 4)) {
      title = '${(maxY ~/ 4) * 3}';
    } else if (value == 4 * (maxY ~/ 4)) {
      title = '${(maxY ~/ 4) * 4}';
    } else if (value == maxY.toInt()) {
      title = '${maxY.toInt()}';
    }
    if (title.isNotEmpty) {
      return '$title${widget.yUnit}';
    }
    return title;
  }
}
