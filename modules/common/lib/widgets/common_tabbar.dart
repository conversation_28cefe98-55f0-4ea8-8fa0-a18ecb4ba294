/*
* Created by Duan<PERSON><PERSON>
* on 7/4/2021.
*/
import 'package:flutter/material.dart';

class CommonTabBar extends StatelessWidget {
  final TabController? tabController;
  final List<String>? titles;
  final List<Widget>? widgets;
  final Color? selectedColor;
  final Color? unselectedLabelColor;
  final TextStyle? selectedLabelStyle;
  final TextStyle? unSelectedLabelStyle;
  final bool isTextLabel;
  final bool isShowDivider;
  final bool isScrollable;
  final EdgeInsetsGeometry? labelPadding;
  final Function(int)? onTap;
  final TabAlignment? tabAlignment;

  const CommonTabBar({
    Key? key,
    this.tabController,
    this.titles,
    this.widgets,
    this.selectedColor,
    this.unselectedLabelColor,
    this.selectedLabelStyle,
    this.unSelectedLabelStyle,
    this.isTextLabel = true,
    this.isShowDivider = true,
    this.isScrollable = false,
    this.labelPadding,
    this.onTap,
    this.tabAlignment,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Align(
          alignment: Alignment.centerLeft,
          child: TabBar(
            tabAlignment:
                isScrollable ? (tabAlignment ?? TabAlignment.start) : null,
            controller: tabController,
            indicatorColor: selectedColor ?? Colors.white,
            indicatorSize: TabBarIndicatorSize.tab,
            labelColor: selectedColor ?? Colors.white,
            automaticIndicatorColorAdjustment: true,
            unselectedLabelColor: unselectedLabelColor,
            isScrollable: isScrollable,
            indicatorPadding: labelPadding ?? EdgeInsets.zero,
            labelPadding: labelPadding,
            labelStyle: selectedLabelStyle,
            unselectedLabelStyle: unSelectedLabelStyle,
            tabs: isTextLabel && titles?.isNotEmpty == true
                ? List<Tab>.generate(
                    titles!.length,
                    (index) {
                      return Tab(text: titles![index]);
                    },
                  )
                : widgets ?? [Container()],
            onTap: onTap,
            dividerColor: Colors.transparent,
          ),
        ),
        if (isShowDivider) Divider(height: 0.5),
      ],
    );
  }
}

class SliverTabs extends StatelessWidget {
  final TabController tabController;
  final bool? isScrollable;
  final List<Tab> tabs;
  final EdgeInsets? margin;
  final Function(int)? onChange;
  final Color? selectedColor;
  final Color? lineColor;
  final VoidCallback? onTap;

  const SliverTabs({
    Key? key,
    required this.tabController,
    required this.tabs,
    this.isScrollable,
    this.margin,
    this.onChange,
    this.selectedColor,
    this.lineColor,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SliverPersistentHeader(
      pinned: true,
      floating: false,
      delegate: _Delegate(
        tabController,
        tabs,
        isScrollable,
        margin,
        onChange,
        selectedColor,
        lineColor,
        onTap,
      ),
    );
  }
}

class _Delegate extends SliverPersistentHeaderDelegate {
  final TabController tabController;
  final bool? isScrollable;
  final List<Tab> tabs;
  final EdgeInsets? margin;
  final Function(int)? onChange;
  final Color? selectedColor;
  final Color? lineColor;
  final VoidCallback? onTap;

  _Delegate(
    this.tabController,
    this.tabs,
    this.isScrollable,
    this.margin,
    this.onChange,
    this.selectedColor,
    this.lineColor,
    this.onTap,
  );

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(
      child: Container(
          margin: margin,
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            border: Border(
              bottom: BorderSide(
                color: lineColor ?? Theme.of(context).primaryColor,
                width: 1,
              ),
            ),
          ),
          child: TabBar(
            labelColor:
                selectedColor ?? Theme.of(context).textTheme.bodyMedium!.color,
            unselectedLabelColor: Theme.of(context).textTheme.bodySmall!.color,
            indicatorColor: Theme.of(context).primaryColor,
            isScrollable: isScrollable ?? false,
            controller: tabController,
            tabs: this.tabs,
            onTap: (index) {
              if (onChange != null) {
                onChange!(index);
              }
              onTap?.call();
            },
          )),
    );
  }

  @override
  double get maxExtent => 50;

  @override
  double get minExtent => 50;

  @override
  bool shouldRebuild(SliverPersistentHeaderDelegate oldDelegate) {
    return true;
  }
}
