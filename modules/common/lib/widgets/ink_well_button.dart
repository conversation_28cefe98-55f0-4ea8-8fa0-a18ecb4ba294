import 'package:common/assets.dart';
import 'package:flutter/material.dart';

import '../utils/extension/context_extension.dart';
import 'common_size.dart';

class InkWellButton extends StatelessWidget {
  final String? title;
  final bool isDisable;
  final Color? buttonColor;
  final Color? buttonDarkColor;
  final Color? borderColor;
  final Color? textColor;
  final double? fontSize;
  final double? radius;
  final EdgeInsetsGeometry? padding;
  final double? width;
  final double? height;
  final VoidCallback? onTap;
  final Widget? labelWidget;
  final bool isWrapContentChild;
  final int? maxLines;
  final TextOverflow? overflow;

  const InkWellButton({
    Key? key,
    @required this.title,
    this.isDisable = false,
    this.buttonColor,
    this.borderColor,
    this.textColor,
    this.fontSize,
    this.radius,
    this.padding,
    this.width,
    this.height,
    this.onTap,
    this.labelWidget,
    this.maxLines,
    this.overflow,
    this.isWrapContentChild = false,
    this.buttonDarkColor,
  });

  @override
  Widget build(BuildContext context) {
    final currentStyle = Theme.of(context).elevatedButtonTheme.style;
    var colorBackground = WidgetStateProperty.all(buttonColor);
    var colorLabelText = textColor;
    if (isDarkTheme(context)) {
      if (buttonDarkColor != null) {
        colorBackground = WidgetStateProperty.all(buttonDarkColor);
      } else if (ImageAssets.gradientColor != null) {
        colorBackground = WidgetStateProperty.all(Colors.transparent);
      }
      if (isDisable) {
        colorLabelText = null;
        colorBackground = WidgetStateProperty.all(const Color(0xFF333333));
      }
    } else if (isDisable) {
      colorLabelText = const Color(0xFF333333).withOpacity(0.6);
      colorBackground =
          WidgetStateProperty.all(const Color(0xFF333333).withOpacity(0.2));
    }
    final radiusBtn = radius ?? borderButton;
    final result = SizedBox(
      width: width ?? (isWrapContentChild ? null : double.infinity),
      height: height ?? minSubmitButtonSize.height,
      child: ElevatedButton(
        onPressed: isDisable ? null : onTap,
        style: currentStyle?.copyWith(
          backgroundColor: colorBackground,
          minimumSize: WidgetStateProperty.all(minSubmitButtonSize),
          shape: WidgetStateProperty.all(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(radiusBtn),
              side: borderColor != null
                  ? BorderSide(color: borderColor!, width: 1)
                  : BorderSide.none,
            ),
          ),
        ),
        child: labelWidget ??
            Text(
              title ?? '',
              style: TextStyle(
                fontSize: fontSize,
                color: colorLabelText,
                fontWeight: FontWeight.w700,
              ),
              maxLines: maxLines,
              overflow: overflow,
            ),
      ),
    );
    if (buttonDarkColor == null) {
      return result.dynamicBackground(context, radius: radiusBtn);
    }
    return result;
  }
}

extension DynamicButton on Widget {
  Widget dynamicBackground(BuildContext context,
      {double? radius, Gradient? gradient, bool isGradientVertical = false}) {
    final background = gradient ??
        (isGradientVertical
            ? ImageAssets.gradientVertical
            : ImageAssets.gradientColor);
    if (isDarkTheme(context) && background != null) {
      return DecoratedBox(
        decoration: BoxDecoration(
          gradient: background,
          borderRadius: radius != null ? BorderRadius.circular(radius) : null,
        ),
        child: this,
      );
    }
    return this;
  }
}
