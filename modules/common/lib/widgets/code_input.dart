library code_input;

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// A widget for inputting content with a fixed length, visually treating each
/// character as a separate segment.
///
/// ## Sample code
///
/// ```dart
/// CodeInput(
///   length: 4,
///   keyboardType: TextInputType.number,
///   builder: CodeInputBuilders.lightCircle(),
///   onFilled: (value) => print('Your input is $value.'),
/// )
/// ```
///
/// See also:
///
/// * [TextField], an input where the characters aren't separated from each
///   other.
typedef CodeInputBuilder = Widget Function(
    bool hasFocus, String char, bool obscureText);

class CodeInput extends StatefulWidget {
  const CodeInput._(
      {Key? key,
      this.length = 6,
      this.keyboardType,
      this.inputFormatters,
      this.builder,
      this.controller,
      this.onChanged,
      this.onFilled,
      required this.backgroundColor,
      this.showBorder = true,
      this.obscureText = false})
      : super(key: key);

  factory CodeInput(
      {Key? key,
      int length = 6,
      TextEditingController? controller,
      TextInputType keyboardType = TextInputType.text,
      List<TextInputFormatter>? inputFormatters,
      CodeInputBuilder? builder,
      void Function(String value)? onChanged,
      void Function(String value)? onFilled,
      required Color backgroundColor,
      bool showBorder = true,
      bool obscureText = false}) {
    assert(length > 0, 'The length needs to be larger than zero.');
    assert(length.isFinite, 'The length needs to be finite.');
    assert(builder != null,
        'The builder is required for rendering the character segments.');

    inputFormatters ??= _createInputFormatters(length, keyboardType);

    return CodeInput._(
      key: key,
      length: length,
      controller: controller,
      keyboardType: keyboardType,
      inputFormatters: inputFormatters,
      builder: builder,
      onChanged: onChanged,
      onFilled: onFilled,
      backgroundColor: backgroundColor,
      showBorder: showBorder,
      obscureText: obscureText,
    );
  }

  /// The length of character entities to always display.
  ///
  /// ## Sample code
  ///
  /// A code input with 4 characters:
  ///
  /// ```dart
  /// CodeInput(length: 4)
  /// ```
  final int length;

  final TextEditingController? controller;

  /// The type of thconstard which shows up.
  ///
  /// ## Sample codeconst
  ///
  /// ```dart
  /// CodeInput(keyboardType: TextInputType.number)
  /// ```
  final TextInputType? keyboardType;

  /// A list of input formatters which can validate the text as it is being
  /// typed.
  ///
  /// If you specify this parameter, the default input formatters aren't used,
  /// so make sure you really check for everything (like length of the input).
  ///
  /// ## Sample code
  ///
  /// An code input that displays a normal keyboard but only allows for
  /// hexadecimal input:
  ///
  /// ```dart
  /// CodeInput(
  ///   inputFormatters: [
  ///     WhitelistingTextInputFormatter(RegExp('^[0-9a-fA-F]*\$'))
  ///   ]
  /// )
  /// ```
  final List<TextInputFormatter>? inputFormatters;

  /// A builder for the character entities.
  ///
  /// See [CodeInputBuilders] for examples.
  final CodeInputBuilder? builder;

  /// A callback for changes to the input.
  final void Function(String value)? onChanged;

  /// A callback for when the input is filled.
  final void Function(String value)? onFilled;

  final Color? backgroundColor;
  final bool showBorder;
  final bool obscureText;

  /// A helping function that creates input formatters for a given length and
  /// keyboardType.
  static List<TextInputFormatter> _createInputFormatters(
      int length, TextInputType keyboardType) {
    final formatters = <TextInputFormatter>[];

    // Add keyboard specific formatters.
    // For example, a code input with a number keyboard type probably doesn't
    // want to allow decimal separators or signs.
    if (keyboardType == TextInputType.number) {
      formatters.add(FilteringTextInputFormatter.allow(RegExp('^[0-9]*\$')));
    }

    return formatters;
  }

  @override
  _CodeInputState createState() => _CodeInputState();
}

class _CodeInputState extends State<CodeInput> {
  final node = FocusNode();
  late TextEditingController controller;

  String get text => widget.controller?.text ?? "";

  @override
  void initState() {
    super.initState();
    controller = widget.controller??TextEditingController();
  }

  @override
  Widget build(BuildContext context) {
    // We'll display the visual widget and a not shown EditableText for doing
    // the actual work on top of each other.
    return Stack(children: <Widget>[
      // This is the actual EditableText wrapped in a Container with zero
      // dimensions.
      Container(
          color: widget.backgroundColor ?? Theme.of(context).colorScheme.background,
          width: 0.0,
          height: 0.0,
          child: EditableText(
              backgroundCursorColor: Theme.of(context).cardColor,
              controller: controller,
              focusNode: node,
              autofocus: true,
              obscureText: true,
              inputFormatters: widget.inputFormatters,
              keyboardType: widget.keyboardType,
              style: TextStyle(),
              // Doesn't really matter.
              cursorColor: Colors.black,
              // Doesn't really matter.
              onChanged: (value) => setState(() {
                    if (value.length == widget.length) {
                      widget.onChanged?.call(value);
                      widget.onFilled?.call(value);
                    }
                  }))),
      // These are the actual character widgets. A transparent container lies
      // right below the gesture detector, so all taps get collected, even
      // the ones between the character entities.
      GestureDetector(
          onTap: () {
            final focusScope = FocusScope.of(context);
            focusScope.requestFocus(FocusNode());
            Future.delayed(Duration.zero, () => focusScope.requestFocus(node));
          },
          child: Container(
            decoration: BoxDecoration(
                color:
                    widget.backgroundColor ?? Theme.of(context).colorScheme.background,
                borderRadius: BorderRadius.circular(8),
                border: widget.showBorder
                    ? Border.all(color: Color(0xFFC2C2C2), width: 0.5)
                    : null),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(widget.length, (i) {
                final hasFocus = widget.controller?.selection.start == i;
                final char = i < text.length ? text[i] : '';
                final characterEntity =
                    widget.builder!(hasFocus, char, widget.obscureText);

                return Container(
                  width: MediaQuery.of(context).size.width * 0.12,
                  height: MediaQuery.of(context).size.width * 0.12,
                  margin: EdgeInsets.only(left: i >= 1 ? 10.0 : 0.0),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.all(Radius.circular(4.0)),
                      color: Theme.of(context).cardColor,
                      border: Border.all(width: 1, color: Color(0x3333331A))),
                  child: characterEntity,
                );
              }),
            ),
          )),
    ]);
  }
}

/// An abstract class that provides some commonly-used builders for the
/// character entities.
///
/// * [containerized]: A builder putting chars in an animated container.
/// * [circle]: A builder putting chars in circles.
/// * [rectangle]: A builder putting chars in rectangles.
/// * [lightCircle]: A builder putting chars in light circles.
/// * [darkCircle]: A builder putting chars in dark circles.
/// * [lightRectangle]: A builer putting chars in light rectangles.
/// * [darkRectangle]: A builder putting chars in dark rectanlges.
abstract class CodeInputBuilders {
  /// Builds the input inside an animated container.
  static CodeInputBuilder containerized({
    Duration animationDuration = const Duration(milliseconds: 50),
    required Size totalSize,
    required Size emptySize,
    required Size filledSize,
    required BoxDecoration emptyDecoration,
    required BoxDecoration filledDecoration,
    required TextStyle emptyTextStyle,
    required TextStyle filledTextStyle,
  }) {
    return (bool hasFocus, String char, bool obscureText) => Container(
        width: totalSize.width,
        height: totalSize.height,
        alignment: Alignment.center,
        child: AnimatedContainer(
          duration: Duration(milliseconds: 100),
          color: char.isEmpty ? Color(0x33333366) : Color(0xFFFFFFFF),
          // decoration: char.isEmpty ? emptyDecoration : filledDecoration,
          width: char.isEmpty ? 8.0 : filledSize.width,
          height: char.isEmpty ? 2.0 : filledSize.height,
          alignment: Alignment.center,
          child: Text(obscureText ? "\u25CF" : char,
              style: char.isEmpty ? emptyTextStyle : filledTextStyle),
        ));
  }

  /// Builds the input inside a circle.
  static CodeInputBuilder circle(
      {double totalRadius = 30.0,
      double emptyRadius = 10.0,
      double filledRadius = 25.0,
      required Border? border,
      required Color? color,
      required TextStyle textStyle}) {
    final decoration = BoxDecoration(
      shape: BoxShape.circle,
      border: border,
      color: color,
    );

    return containerized(
        totalSize: Size.fromRadius(totalRadius),
        emptySize: Size.fromRadius(emptyRadius),
        filledSize: Size.fromRadius(filledRadius),
        emptyDecoration: decoration,
        filledDecoration: decoration,
        emptyTextStyle: textStyle.copyWith(fontSize: 0.0),
        filledTextStyle: textStyle);
  }

  /// Builds the input inside a rectangle.
  static CodeInputBuilder rectangle({
    Size totalSize = const Size(50.0, 60.0),
    Size emptySize = const Size(8.0, 2.0),
    Size filledSize = const Size(40.0, 40.0),
    BorderRadius borderRadius = BorderRadius.zero,
    Border? border,
    Color? emptyColor,
    Color? filledColor,
    TextStyle? textStyle,
  }) {
    final emptyDecoration = BoxDecoration(
      border: border,
      borderRadius: borderRadius,
      color: emptyColor,
    );

    final filledDecoration = BoxDecoration(
      border: border,
      borderRadius: borderRadius,
      color: filledColor,
    );

    return containerized(
        totalSize: totalSize,
        emptySize: emptySize,
        filledSize: filledSize,
        emptyDecoration: emptyDecoration,
        filledDecoration: filledDecoration,
        emptyTextStyle: textStyle!.copyWith(fontSize: 0.0),
        filledTextStyle: textStyle);
  }

  /// Builds the input inside a light circle.
  static CodeInputBuilder lightCircle({
    double totalRadius = 30.0,
    double emptyRadius = 10.0,
    double filledRadius = 25.0,
  }) {
    return circle(
        totalRadius: totalRadius,
        emptyRadius: emptyRadius,
        filledRadius: filledRadius,
        border: Border.all(color: Colors.white, width: 2.0),
        color: Colors.white10,
        textStyle: TextStyle(
            color: Colors.white, fontSize: 20.0, fontWeight: FontWeight.bold));
  }

  /// Builds the input inside a light circle.
  static CodeInputBuilder darkCircle({
    double totalRadius = 30.0,
    double emptyRadius = 10.0,
    double filledRadius = 25.0,
  }) {
    return circle(
        totalRadius: totalRadius,
        emptyRadius: emptyRadius,
        filledRadius: filledRadius,
        border: Border.all(color: Colors.black, width: 2.0),
        color: Colors.black12,
        textStyle: TextStyle(
            color: Colors.black, fontSize: 20.0, fontWeight: FontWeight.bold));
  }

  static CodeInputBuilder lineDashed({
    BuildContext? context,
    required Color borderColor,
    Size totalSize = const Size(50.0, 50.0),
    Size emptySize = const Size(20.0, 1),
    Size filledSize = const Size(40.0, 40.0),
    BorderRadius borderRadius = BorderRadius.zero,
  }) {
    return rectangle(
        totalSize: totalSize,
        emptySize: emptySize,
        filledSize: filledSize,
        borderRadius: borderRadius,
        border: Border.all(color: borderColor, width: 2.0),
        filledColor: Colors.white10,
        emptyColor: Color(0x33333366),
        textStyle: Theme.of(context!).textTheme.titleLarge);
  }

  /// Builds the input inside a light rectangle.
  static CodeInputBuilder lightRectangle({
    Size totalSize = const Size(50.0, 60.0),
    Size emptySize = const Size(20.0, 20.0),
    Size filledSize = const Size(40.0, 60.0),
    BorderRadius borderRadius = BorderRadius.zero,
  }) {
    return rectangle(
        totalSize: totalSize,
        emptySize: emptySize,
        filledSize: filledSize,
        borderRadius: borderRadius,
        border: Border.all(color: Colors.white, width: 2.0),
        emptyColor: Colors.white10,
        filledColor: Colors.white10,
        textStyle: TextStyle(
            color: Colors.white, fontSize: 20.0, fontWeight: FontWeight.bold));
  }

  /// Builds the input inside a dark rectangle.
  static CodeInputBuilder darkRectangle({
    Size totalSize = const Size(50.0, 60.0),
    Size emptySize = const Size(20.0, 20.0),
    Size filledSize = const Size(40.0, 60.0),
    BorderRadius borderRadius = BorderRadius.zero,
  }) {
    return rectangle(
        totalSize: totalSize,
        emptySize: emptySize,
        filledSize: filledSize,
        borderRadius: borderRadius,
        border: Border.all(color: Colors.black, width: 2.0),
        emptyColor: Colors.black12,
        filledColor: Colors.black12,
        textStyle: TextStyle(
            color: Colors.black, fontSize: 20.0, fontWeight: FontWeight.bold));
  }
}

class TextEditingControllerCustom extends TextEditingController {
  TextEditingControllerCustom({String? text}) : super(text: text);

  void setTextAndPosition(String newText, {int? caretPosition}) {
    int offset = caretPosition != null ? caretPosition : newText.length;
    value = value.copyWith(
        text: newText,
        selection: TextSelection.collapsed(offset: offset),
        composing: TextRange.empty);
  }
}
