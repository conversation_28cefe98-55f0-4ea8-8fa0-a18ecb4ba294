import 'package:common/ks_common.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class EmptyChartWidget extends StatefulWidget {
  final String title;
  final Color? columnColor;

  const EmptyChartWidget({
    Key? key,
    required this.title,
    this.columnColor,
  }) : super(key: key);

  @override
  State<EmptyChartWidget> createState() => _EmptyChartWidgetState();
}

class _EmptyChartWidgetState extends State<EmptyChartWidget> {
  int touchedGroupIndex = -1;

  late double maxY;

  late double maxX;

  int distance = 1;

  var barGroups = <BarChartGroupData>[];

  @override
  void initState() {
    super.initState();
    handleData();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 240,
      width: ScreenUtil().screenWidth - 30.w,
      child: Stack(
        children: [
          BarChart(
            BarChartData(
              barTouchData: barTouchData,
              titlesData: titlesData,
              borderData: borderData,
              barGroups: barGroups,
              alignment: BarChartAlignment.spaceAround,
              maxY: maxY,
            ),
          ),
          Center(
              child: Text(
            widget.title,
            style: StyleApp.bodyText1(context),
          ))
        ],
      ),
    );
  }

  BarTouchData get barTouchData => BarTouchData(
        enabled: false,
        touchTooltipData: BarTouchTooltipData(
          getTooltipColor: (_) => Colors.transparent,
          tooltipPadding: EdgeInsets.all(0),
          tooltipMargin: 0,
          getTooltipItem: (
            BarChartGroupData group,
            int groupIndex,
            BarChartRodData rod,
            int rodIndex,
          ) {
            return BarTooltipItem('', TextStyle());
          },
        ),
      );

  FlTitlesData get titlesData => FlTitlesData(
        show: true,
        bottomTitles: AxisTitles(
            sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) {
                  return Padding(
                    padding: EdgeInsets.only(top: 8.0),
                    child: Text(
                      '${value.toInt()}',
                      style: TextStyle(
                        color: Color(0xff333333).withOpacity(0.6),
                        fontWeight: FontWeight.w400,
                        fontSize: 10.sp,
                      ),
                    ),
                  );
                })),
        leftTitles: leftTitles(
          getTitlesWidget: (value, meta) {
            int check = value.toInt() % distance;
            return Padding(
              padding: EdgeInsets.only(top: 8.0),
              child: Text(
                check == 0 ? '${value.toInt()}M' : '',
                style: TextStyle(
                  color: Color(0xff333333).withOpacity(0.6),
                  fontWeight: FontWeight.bold,
                  fontSize: 10.sp,
                ),
              ),
            );
          },
        ),
        topTitles: AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        rightTitles: rightTitles(
          getTitlesWidget: (value, meta) {
            int check = value.toInt() % distance;
            return Padding(
              padding: EdgeInsets.only(top: 8.0),
              child: Text(
                '${check == 0 ? value.toInt() : ''}',
                style: TextStyle(
                  color: Color(0xff333333).withOpacity(0.6),
                  fontWeight: FontWeight.bold,
                  fontSize: 10.sp,
                ),
              ),
            );
          },
        ),
      );

  AxisTitles leftTitles({required GetTitleWidgetFunction getTitlesWidget}) =>
      AxisTitles(
        sideTitles: SideTitles(
          getTitlesWidget: getTitlesWidget,
          showTitles: true,
          interval: 1,
          reservedSize: 40,
        ),
      );

  AxisTitles rightTitles({required GetTitleWidgetFunction getTitlesWidget}) =>
      AxisTitles(
        sideTitles: SideTitles(
          getTitlesWidget: getTitlesWidget,
          showTitles: true,
          interval: 1,
          reservedSize: 40,
        ),
      );

  FlBorderData get borderData => FlBorderData(
        show: true,
        border: const Border(
          bottom: BorderSide(color: Color(0xffeaeaea), width: 1),
          left: BorderSide(color: Colors.transparent),
          right: BorderSide(color: Colors.transparent),
          top: BorderSide(color: Colors.transparent),
        ),
      );

  handleData() {
    maxX = 5;
    maxY = 5;
    barGroups = <BarChartGroupData>[];
    for (var i = 0; i < 5; i++) {
      barGroups.add(
        BarChartGroupData(
          x: i,
          barsSpace: 0,
          barRods: [
            BarChartRodData(
              toY: (i + 1).toDouble(),
              width: 20.w,
              borderRadius: BorderRadius.circular(0),
              color: widget.columnColor ?? Color(0xFFEAEAEA),
            ),
          ],
          showingTooltipIndicators: [0],
        ),
      );
    }
  }
}
