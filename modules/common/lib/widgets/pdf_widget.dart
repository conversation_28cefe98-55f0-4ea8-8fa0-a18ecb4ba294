/*
* Created by DuanVH 
* on 6/14/2021.
*/
import 'dart:async';
import 'dart:io';

import 'package:common/ks_common.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:path_provider/path_provider.dart';
import 'package:rxdart/rxdart.dart';

class PdfWidget extends StatefulWidget {
  final String path;
  final bool isShowPageNumber;
  final bool swipeHorizontal;
  final String? fileName;

  const PdfWidget({
    Key? key,
    required this.path,
    this.isShowPageNumber = true,
    this.swipeHorizontal = true,
    this.fileName,
  }) : super(key: key);

  @override
  PdfWidgetState createState() => PdfWidgetState();
}

class PdfWidgetState extends State<PdfWidget> with WidgetsBindingObserver {
  final controller = BehaviorSubject<File>();
  int pages = 0;
  int currentPage = 0;
  bool isReady = false;
  String errorMessage = '';

  @override
  void initState() {
    super.initState();
    createFileOfPdfUrl();
  }

  File? getPdfFile() {
    return controller.valueOrNull;
  }

  @override
  void dispose() {
    super.dispose();
    controller.close();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        StreamBuilder<File>(
          stream: controller.stream,
          builder: (context, snapshot) {
            if (!snapshot.hasData) return Container();
            return PDFView(
              filePath: snapshot.data!.path,
              enableSwipe: true,
              swipeHorizontal: widget.swipeHorizontal,
              autoSpacing: false,
              pageFling: true,
              pageSnap: true,
              defaultPage: currentPage,
              fitPolicy: FitPolicy.BOTH,
              preventLinkNavigation: false,
              // if set to true the link is handled in flutter
              onRender: (_pages) {
                setState(() {
                  pages = _pages!;
                  isReady = true;
                });
              },
              onError: (error) {
                setState(() {
                  errorMessage = error.toString();
                });
                logger.e(error.toString());
              },
              onPageError: (page, error) {
                setState(() {
                  errorMessage = '$page: ${error.toString()}';
                });
                logger.e('$page: ${error.toString()}');
              },
              onViewCreated: (PDFViewController pdfViewController) {},
              onLinkHandler: (String? uri) {
                logger.v('PDF - goto uri: $uri');
              },
              onPageChanged: (int? page, int? total) {
                logger.v('PDF - page change: $page/$total');
                setState(() {
                  currentPage = page ?? 0;
                });
              },
            );
          },
        ),
        errorMessage.isEmpty
            ? !isReady
                ? Center(child: CupertinoActivityIndicator(radius: 12))
                : Positioned(
                    bottom: 20,
                    left: 0,
                    right: 0,
                    child: widget.isShowPageNumber
                        ? Container(
                            alignment: Alignment.center,
                            child: Text(
                              '${currentPage + 1}/$pages',
                              style: TextStyle(
                                color: Colors.black54,
                              ),
                            ),
                          )
                        : Container(),
                  )
            : Container(
                color: Theme.of(context).cardColor,
                padding: EdgeInsets.all(16),
                child: Center(child: Text('Error: $errorMessage')),
              ),
      ],
    );
  }

  Future<File> createFileOfPdfUrl() async {
    final pathOrUrl = widget.path;
    logger.v(pathOrUrl);
    if (pathOrUrl.startsWith('http') || pathOrUrl.startsWith('https')) {
      try {
        await Future.delayed(Duration(seconds: 1), () {});
        final file = await FileUtils.downloadFileToCached(pathOrUrl,
            name: widget.fileName);
        controller.add(file);
        return file;
      } catch (e) {
        logger.e(e);
        throw Exception(e);
      }
    } else {
      var file = File(pathOrUrl);
      controller.add(file);
      return Future.value(file);
    }
  }

  Future<File> fromAsset(String asset, String filename) async {
    try {
      var dir = await getApplicationDocumentsDirectory();
      File file = File("${dir.path}/$filename");
      var data = await rootBundle.load(asset);
      var bytes = data.buffer.asUint8List();
      await file.writeAsBytes(bytes, flush: true);
      return file;
    } catch (e) {
      throw Exception('Error parsing asset file!');
    }
  }
}
