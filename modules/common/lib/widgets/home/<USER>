import 'package:common/ks_common.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../utils/global.dart';

class HomeCardUserItem extends StatefulWidget {
  final String title;
  final String? titleEmpty;
  final String? titleButtonEmpty;
  final num? totalBalance;
  final num? profit;
  final VoidCallback? onTap;
  final VoidCallback? onTapEmpty;
  final VoidCallback? onRetry;
  final bool? isShowLoading;

  HomeCardUserItem({
    required this.title,
    this.titleEmpty,
    this.titleButtonEmpty,
    this.totalBalance,
    this.profit,
    this.onTapEmpty,
    this.onTap,
    this.onRetry,
    this.isShowLoading,
  });

  @override
  _ScreenState createState() => _ScreenState();
}

class _ScreenState extends State<HomeCardUserItem> {
  bool showHint = true;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return _getRealView();
  }

  _getRealView() {
    return Container(
      decoration: new BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.all(Radius.circular(toSp(6.0))),
        boxShadow: Global.getBoxShadow(context),
      ),
      child: Container(
        padding: EdgeInsets.all(12),
        color: Colors.transparent,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            InkWell(
              onTap: () {
                setState(() {
                  showHint = !showHint;
                });
              },
              child: Row(
                children: [
                  Text(
                    widget.title,
                    style: StyleApp.subtitle1(context),
                  ),
                  if (widget.totalBalance != null && widget.totalBalance! > 0)
                    IconButton(
                      icon: showHint == false
                          ? const Icon(Icons.visibility)
                          : const Icon(Icons.visibility_off),
                      iconSize: toSp(17),
                      color: Theme.of(context).textTheme.bodySmall!.color,
                      constraints: const BoxConstraints(
                        minWidth: 24,
                        minHeight: 24,
                      ),
                      onPressed: () {
                        setState(() {
                          showHint = !showHint;
                        });
                      },
                    )
                ],
              ),
            ),
            Expanded(
              child: widget.isShowLoading == true
                  ? _loading()
                  : (widget.totalBalance == null || widget.totalBalance == -1)
                      ? _error()
                      : (widget.totalBalance! > 0)
                          ? _getAssets()
                          : _getEmpty(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _getAssets() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 5),
            AnimatedCrossFade(
              firstChild: AutoSizeText(
                "******* VND",
                textAlign: TextAlign.center,
                style: Theme.of(context)
                    .textTheme
                    .titleLarge!
                    .copyWith(fontWeight: FontWeight.w600),
                maxLines: 1,
              ),
              secondChild: AutoSizeText(
                "${(widget.totalBalance ?? 0).currencyFormat} VND",
                style: Theme.of(context)
                    .textTheme
                    .titleLarge!
                    .copyWith(fontWeight: FontWeight.w600),
                maxLines: 1,
              ),
              crossFadeState: showHint == true
                  ? CrossFadeState.showFirst
                  : CrossFadeState.showSecond,
              duration: const Duration(milliseconds: 500),
            ),
            const SizedBox(height: 5),
            _profit(),
          ],
        ),
        Container(
          decoration: BoxDecoration(
            color: const Color(0xFFEAEAF0),
            borderRadius: BorderRadius.circular(100),
          ),
          child: IconButton(
            onPressed: widget.onTap,
            icon: const Icon(Icons.arrow_forward_ios),
            color: Theme.of(context).primaryColor,
            iconSize: toSp(15),
            constraints: const BoxConstraints(
              minWidth: 24,
              minHeight: 24,
            ),
          ),
        ),
      ],
    );
  }

  Widget _profit() {
    final profit = widget.profit ?? 0;
    return AnimatedCrossFade(
      firstChild: AutoSizeText(
        "******* VND",
        textAlign: TextAlign.center,
        style: Theme.of(context)
            .textTheme
            .titleMedium!
            .copyWith(color: const Color(0xFF00BC3C)),
        maxLines: 1,
      ),
      secondChild: Row(
        children: [
          profit != 0
              ? Icon(
                  profit > 0 ? Icons.arrow_drop_up : Icons.arrow_drop_down,
                  color: const Color(0xFF00BC3C),
                )
              : const SizedBox(),
          Text(
            profit != 0
                ? profit > 0
                    ? '+'
                    : '-'
                : '',
            style: const TextStyle(color: Color(0xFF00BC3C)),
          ),
          AutoSizeText(
            "${profit.currencyFormat} VND",
            style: Theme.of(context)
                .textTheme
                .titleMedium!
                .copyWith(color: const Color(0xFF00BC3C)),
            maxLines: 1,
          ),
        ],
      ),
      crossFadeState: showHint == true
          ? CrossFadeState.showFirst
          : CrossFadeState.showSecond,
      duration: const Duration(milliseconds: 500),
    );
  }

  Widget _getEmpty() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          flex: 2,
          child: Text(
            widget.titleEmpty ?? "",
            style: StyleApp.bodyText2(context),
          ),
        ),
        widget.titleButtonEmpty.isNullOrEmpty
            ? SizedBox()
            : Expanded(
                flex: 1,
                child: Center(
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: context.isDarkTheme
                          ? Colors.white.withOpacity(0.15)
                          : Theme.of(context).scaffoldBackgroundColor,
                      padding: EdgeInsets.symmetric(
                        horizontal: 5,
                        vertical: toSp(12),
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                      child: Text(
                        widget.titleButtonEmpty ?? "",
                        style: StyleApp.subtitle2(context, true)
                            ?.copyWith(color: Theme.of(context).primaryColor),
                      ),
                    ),
                    onPressed: () {
                      if (widget.onTapEmpty != null) widget.onTapEmpty!();
                    },
                  ),
                ),
              ),
      ],
    );
  }

  Widget _error() {
    return InkWell(
      onTap: widget.onRetry,
      child: Row(
        children: [
          Text(
            'Không tải được dữ liệu',
            style: TextStyle(
              color: Theme.of(context).textTheme.bodySmall?.color,
            ),
          ),
          const SizedBox(width: 12),
          Icon(Icons.refresh),
        ],
      ),
    );
  }

  Widget _loading() {
    return Row(
      children: [
        Text(
          'Đang tải dữ liệu',
          style: TextStyle(
            color: Theme.of(context).textTheme.bodySmall?.color,
          ),
        ),
        const SizedBox(width: 12),
        CupertinoActivityIndicator(),
      ],
    );
  }
}
