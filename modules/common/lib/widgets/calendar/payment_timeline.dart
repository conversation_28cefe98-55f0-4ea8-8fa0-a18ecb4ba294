import 'package:common/widgets/money_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:timelines_plus/timelines_plus.dart';

typedef OnTapTimelineItem = Function(PaymentTimelineItemModel item, int index);

class PaymentTimeline extends StatelessWidget {
  final List<PaymentTimelineItemModel>? items;
  final OnTapTimelineItem? onTapItem;
  final EdgeInsets? margin;
  final double? indicatorSize;

  const PaymentTimeline({
    Key? key,
    this.items,
    this.onTapItem,
    this.margin,
    this.indicatorSize,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final connectorColor = Theme.of(context).dividerColor;
    return Container(
      margin: margin,
      child: Timeline.tileBuilder(
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        padding: EdgeInsets.zero,
        theme: TimelineThemeData(
          nodePosition: 0,
          indicatorPosition: 0,
          direction: Axis.vertical,
          connectorTheme: ConnectorThemeData(thickness: 4.h),
        ),
        builder: TimelineTileBuilder.connected(
          itemCount: items?.length ?? 0,
          indicatorBuilder: (_, index) =>
              _buildIndicator(context, items![index], index),
          connectorBuilder: (_, index, type) =>
              SolidLineConnector(color: connectorColor, thickness: 1),
          // oppositeContentsBuilder: (_, index) =>
          //     _buildTime(widget.progresses[index], index),
          contentsBuilder: (_, index) =>
              _buildContent(context, items![index], index),
          lastConnectorBuilder: (_) =>
              SolidLineConnector(color: connectorColor, thickness: 1),
          firstConnectorBuilder: (_) =>
              SolidLineConnector(color: connectorColor, thickness: 1),
        ),
      ),
    );
  }

  Widget _buildIndicator(
    BuildContext context,
    PaymentTimelineItemModel model,
    int index,
  ) {
    final defaultIndicatorColor = Theme.of(context).primaryColor;
    final connectorColor = Theme.of(context).dividerColor;
    double size = indicatorSize ?? 20.h;

    final defaultIndicator = Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: defaultIndicatorColor,
      ),
    );

    if (onTapItem == null) {
      return defaultIndicator;
    }

    Widget indicator = defaultIndicator;

    if (model.state == TimelineState.PASS) {
      indicator = Icon(
        Icons.check_circle_rounded,
        color: Colors.green,
        size: size,
      );
    } else if (model.isSelected == true) {
      indicator = Icon(
        Icons.check_circle_rounded,
        color: Colors.blue,
        size: size,
      );
    } else if (model.state == TimelineState.CURRENT ||
        model.state == TimelineState.NEXT) {
      indicator = Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(color: connectorColor),
        ),
      );
    }
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () => _onTap(model, index),
      child: indicator,
    );
  }

  _onTap(
    PaymentTimelineItemModel model,
    int index,
  ) {
    onTapItem?.call(model, index);
  }

  _buildContent(
    BuildContext context,
    PaymentTimelineItemModel model,
    int index,
  ) {
    final defaultTextColor = Theme.of(context).textTheme.bodyLarge?.color;
    TextStyle styleTitle = TextStyle(fontWeight: FontWeight.w700);
    TextStyle styleAmount = TextStyle(
      fontWeight: FontWeight.w700,
      color: defaultTextColor,
    );

    if (model.state == TimelineState.CURRENT) {
      styleTitle = styleTitle.copyWith(color: Theme.of(context).primaryColor);
      // styleAmount = styleAmount.copyWith(color: Colors.red);
    } else if (model.state == TimelineState.NEXT) {
      styleTitle = styleTitle.copyWith(color: defaultTextColor);
    }

    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () => _onTap(model, index),
      child: Container(
        padding: EdgeInsets.only(left: 16.h),
        child: Column(
          children: [
            Container(
              height: 25.h,
              alignment: Alignment.topLeft,
              child: Row(
                children: [
                  Text(model.title ?? '', style: styleTitle),
                  Spacer(),
                  MoneyWidget(
                    amount: model.totalAmount,
                    textStyle: styleAmount,
                    unitStyle: styleAmount.copyWith(
                      color: styleAmount.color?.withOpacity(0.6),
                    ),
                  ),
                ],
              ),
            ),
            Container(
                height: 25.h,
                alignment: Alignment.topLeft,
                child: Row(
                  children: [
                    if ((model.description ?? '').isNotEmpty == true) ...[
                      Text(
                        model.description ?? '',
                        style: TextStyle(color: defaultTextColor),
                      ),
                      SizedBox(width: 6),
                    ],
                    Text(
                      model.date ?? '',
                      style: TextStyle(color: defaultTextColor),
                    ),
                    Spacer(),
                    Text(
                      (model.percent ?? '').toString() + '%',
                      style: TextStyle(color: defaultTextColor),
                    )
                  ],
                )),
            if ((model.paidAmount ?? 0) > 0)
              Container(
                height: 25.h,
                alignment: Alignment.topLeft,
                child: Row(
                  children: [
                    Text(
                      'Đã thanh toán',
                      style: TextStyle(color: defaultTextColor),
                    ),
                    Spacer(),
                    MoneyWidget(
                      amount: model.paidAmount,
                      textStyle: TextStyle(fontWeight: FontWeight.w700),
                      unitStyle: TextStyle(
                        fontWeight: FontWeight.w700,
                        color: defaultTextColor?.withOpacity(0.6),
                      ),
                    )
                  ],
                ),
              ),
            if ((model.status ?? '').isNotEmpty)
              Container(
                  height: 25.h,
                  alignment: Alignment.topLeft,
                  child: Row(
                    children: [
                      Text(
                        model.status!,
                        style: TextStyle(color: model.statusColor),
                      ),
                      Spacer(),
                      Text(
                        model.note ?? '',
                        style: TextStyle(color: defaultTextColor),
                      ),
                    ],
                  )),
            if (index < (items?.length ?? 0) - 1) Divider(),
            if (index < (items?.length ?? 0) - 1) SizedBox(height: 10)
          ],
        ),
      ),
    );
  }
}

class PaymentTimelineItemModel {
  String? id;
  String? title;
  String? description;
  double? totalAmount;
  double? percent;
  String? date;
  double? paidAmount;
  String? status;
  Color? statusColor;
  String? note;
  TimelineState? state;
  bool? isSelected;

  PaymentTimelineItemModel({
    this.id,
    this.title,
    this.description,
    this.totalAmount,
    this.percent,
    this.date,
    this.paidAmount,
    this.status,
    this.statusColor,
    this.note,
    this.state,
    this.isSelected,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentTimelineItemModel &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

enum TimelineState { PASS, CURRENT, NEXT }
