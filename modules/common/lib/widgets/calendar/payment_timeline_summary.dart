import 'package:common/ks_common.dart';
import 'package:common/widgets/money_widget.dart';
import 'package:common/widgets/tooltip_custom_shape.dart';
import 'package:flutter/material.dart';

class PaymentTimelineSummary extends StatelessWidget {
  final double? orderAmount;
  final double? paidAmount;
  final double? remainAmount;
  final double? paymentAmount;
  final EdgeInsets? margin;
  final double? feeAmount;
  final double? profitAmount;
  final double? profitVat;

  const PaymentTimelineSummary({
    Key? key,
    this.orderAmount,
    this.paidAmount,
    this.remainAmount,
    this.paymentAmount,
    this.margin,
    this.feeAmount,
    this.profitAmount,
    this.profitVat,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final defaultTextStyle = StyleApp.subtitle1(context, true);
    final defaultUniStyle = StyleApp.subtitle1(context, true)?.copyWith(
      color: Color(0xFF8E9ABB),
    );
    final gapHeight = 12.0;
    final vat = toDouble(profitVat);
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 16),
      decoration: BoxDecoration(
        color: Color(0xFFF3F6FF),
        borderRadius: BorderRadius.circular(8.0),
      ),
      margin: margin,
      child: Column(
        children: [
          Row(
            children: [
              Text('Số tiền đặt mua'),
              Spacer(),
              MoneyWidget(
                amount: orderAmount,
                textStyle: defaultTextStyle,
                unitStyle: defaultUniStyle,
              )
            ],
          ),
          SizedBox(height: gapHeight),
          Row(
            children: [
              Text('Đã thanh toán'),
              Spacer(),
              MoneyWidget(
                amount: paidAmount,
                textStyle: defaultTextStyle,
                unitStyle: defaultUniStyle,
              )
            ],
          ),
          SizedBox(height: gapHeight),
          Row(
            children: [
              Text('Thanh toán còn lại'),
              Spacer(),
              MoneyWidget(
                amount: remainAmount,
                textStyle: defaultTextStyle,
                unitStyle: defaultUniStyle,
              )
            ],
          ),
          if ((profitAmount ?? 0) > 0) ...[
            SizedBox(height: gapHeight),
            Row(
              children: [
                Text('Tổng lợi ích'),
                SizedBox(width: 10),
                if (vat > 0)
                  Tooltip(
                    message: 'Đã trừ VAT ${vat.interestRateFormat}',
                    preferBelow: false,
                    child: Icon(
                      Icons.info_outline,
                      color: Theme.of(context).primaryColor,
                      size: 16,
                    ),
                    triggerMode: TooltipTriggerMode.tap,
                    showDuration: const Duration(seconds: 3),
                    decoration: ShapeDecoration(
                      color: Theme.of(context).primaryColor,
                      shape: ToolTipCustomShape(preferBelow: true),
                    ),
                    verticalOffset: 8,
                    margin:
                        const EdgeInsets.only(left: 110, right: 30, bottom: 10),
                    padding:
                        const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                    textStyle: Theme.of(context)
                        .textTheme
                        .bodyMedium
                        ?.copyWith(color: Theme.of(context).cardColor),
                  ),
                Spacer(),
                MoneyWidget(
                  amount: profitAmount,
                  textStyle:
                      defaultTextStyle?.copyWith(color: Color(0xFF00CB51)),
                  unitStyle:
                      defaultUniStyle?.copyWith(color: Color(0xFF40DE85)),
                )
              ],
            ),
          ],
          if ((feeAmount ?? 0) > 0) ...[
            SizedBox(height: gapHeight),
            Row(
              children: [
                Text('Phí phạt'),
                Spacer(),
                MoneyWidget(
                  amount: feeAmount,
                  textStyle: defaultTextStyle?.copyWith(color: Colors.red),
                  unitStyle:
                      defaultUniStyle?.copyWith(color: Color(0xFFFF6585)),
                )
              ],
            ),
          ],
          SizedBox(height: gapHeight),
          Row(
            children: [
              Text(
                'Thanh toán',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
              Spacer(),
              MoneyWidget(
                amount: paymentAmount,
                textStyle: StyleApp.headline6(context)?.copyWith(
                  color: Color(0xFF3366FF),
                  fontWeight: FontWeight.w600,
                ),
                unitStyle: StyleApp.headline6(context)?.copyWith(
                  color: Color(0xFF7597FF),
                  fontWeight: FontWeight.w600,
                ),
              )
            ],
          )
        ],
      ),
    );
  }
}
