/*
 * Created by DuanVH 
 * on 21/10/2022
 */
import 'package:common/model/base_item.dart';
import 'package:common/model/view_data_model.dart';
import 'package:common/navigator.dart';
import 'package:common/utils/extension/string.dart';
import 'package:common/utils/model_util.dart';
import 'package:common/widgets/common_dropdown.dart';
import 'package:common/widgets/loading/loading.dart';
import 'package:common/widgets/projects_sheet.dart';
import 'package:common/widgets/room_invest_price_change_chart.dart';
import 'package:flutter/material.dart';

typedef VDMMultiDataChartModel = ViewDataModel<MultiDataChartModel?>;

class MultiDataChartModel {
  BaseItem? currentProject;
  List<BaseItem>? projectList;
  List<BaseItem>? chartDataList;

  MultiDataChartModel({
    this.currentProject,
    this.projectList,
    this.chartDataList,
  });
}

class ProjectPriceChangeChart extends StatefulWidget {
  final VDMMultiDataChartModel? vdmMultiDataChart;
  final Function(BaseItem) onChangeProject;
  final String? titleChart;
  final String? titleTable;
  final Color? chartColor;

  const ProjectPriceChangeChart({
    Key? key,
    this.vdmMultiDataChart,
    required this.onChangeProject,
    this.titleChart,
    this.titleTable,
    this.chartColor,
  }) : super(key: key);

  @override
  State<ProjectPriceChangeChart> createState() =>
      _ProjectPriceChangeChartState();
}

class _ProjectPriceChangeChartState extends State<ProjectPriceChangeChart>
    with PriceChartChangeFunction {
  @override
  Widget build(BuildContext context) {
    if (widget.vdmMultiDataChart?.data != null) {
      return Stack(
        alignment: Alignment.center,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                color: Theme.of(context).cardColor,
                padding: const EdgeInsets.only(top: 15),
                child: RoomInvestPriceChangeChart(
                  title: widget.titleChart ?? 'Biến động giá',
                  date: DateTime.now().formatDMY,
                  verticalAxisName: '(Triệu/m²)',
                  subWidget: _projectDropdownWidget(),
                  chartItems:
                      widget.vdmMultiDataChart?.data?.chartDataList ?? [],
                  barColor: widget.chartColor,
                ),
              ),
              _pricesWidget(widget.vdmMultiDataChart?.data?.chartDataList),
            ],
          ),
          if (widget.vdmMultiDataChart?.isLoading() == true)
            Positioned.fill(
              child: Container(
                padding: const EdgeInsets.all(15),
                color: Colors.black12,
                child: Center(child: ring),
              ),
            ),
        ],
      );
    }
    return const SizedBox();
  }

  Widget _projectDropdownWidget() {
    final _selectedProject = widget.vdmMultiDataChart?.data?.currentProject;
    if (widget.vdmMultiDataChart?.data?.projectList?.isNotEmpty == true) {
      return CommonDropdown(
        title: 'Dự án',
        value: _selectedProject?.subTitle ?? '',
        margin: const EdgeInsets.symmetric(horizontal: 15),
        onTap: () async {
          final projects = widget.vdmMultiDataChart?.data?.projectList;
          final result = await goPresent(
            context,
            ProjectsSheet(
              projects: projects!,
              currentProject: _selectedProject,
            ),
          );
          if (result is BaseItem && result.id != _selectedProject?.id) {
            widget.onChangeProject(result);
          }
        },
      );
    }
    return const SizedBox();
  }

  Widget _pricesWidget(List<BaseItem<dynamic>>? priceChangeList) {
    if (priceChangeList?.isNotEmpty == true) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 15),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 15),
            child: Text(
              widget.titleTable ?? 'Lợi ích BĐS tăng giá',
              style: Theme.of(context)
                  .textTheme
                  .titleMedium
                  ?.copyWith(fontWeight: FontWeight.w500),
            ),
          ),
          const SizedBox(height: 15),
          _header(),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              return _priceWidget(priceChangeList![index]);
            },
            separatorBuilder: (_, __) => const Divider(height: 1),
            itemCount: priceChangeList!.length,
          ),
        ],
      );
    }
    return const SizedBox();
  }

  Widget _header() {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      padding: const EdgeInsets.all(15),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            flex: 1,
            child: Align(
              alignment: Alignment.centerLeft,
              child: _headerItem('Ngày'),
            ),
          ),
          Expanded(
            flex: 2,
            child: Align(
              alignment: Alignment.centerRight,
              child: _headerItem('Giá cũ (m²)'),
            ),
          ),
          Expanded(
            flex: 2,
            child: Align(
              alignment: Alignment.center,
              child: _headerItem('Giá mới (m²)'),
            ),
          ),
          Expanded(
            flex: 1,
            child: Align(
              alignment: Alignment.centerRight,
              child: _headerItem('Lợi ích'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _headerItem(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.w500,
            color: Theme.of(context).textTheme.bodyLarge?.color,
          ),
    );
  }

  /// title: Ngày
  /// subTitle2: Giá cũ
  /// subTitle: Giá mới
  /// trailing: Lợi ích
  Widget _priceWidget(BaseItem model) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: getDateWidget(model.title, isFutureSt: model.isSelected),
          ),
          Expanded(
            child: Align(
              alignment: Alignment.centerRight,
              child: Text(_toMoneyText(model.subTitle2)),
            ),
          ),
          Expanded(
            child: Align(
              alignment: Alignment.centerRight,
              child: Text(
                _toMoneyText(model.subTitle),
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
            ),
          ),
          Expanded(
            child: Align(
              alignment: Alignment.centerRight,
              child: Text(
                _toBenefitText(model.trailing),
                style: TextStyle(color: Color(0xFF00BC3C)),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _toMoneyText(String? amt) {
    return '${(toDouble(amt) / 1000000).currencyFormat} tr';
  }

  String _toBenefitText(String? rate) {
    String result = '${toDouble(rate).percentFormat}';
    if (toDouble(rate) > 0) {
      return '+$result';
    }
    return result;
  }
}
