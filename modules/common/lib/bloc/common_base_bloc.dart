import 'dart:async';

import 'package:common/model/loading_event.dart';
import 'package:common/utils/extension/rx.dart';
import 'package:common/utils/log.dart';
import 'package:connectivity/connectivity.dart';
import 'package:flutter/material.dart';
import 'package:rxdart/rxdart.dart';

//same with use case
abstract class CommonBaseBloc {
  final _progressVisible = BehaviorSubject<bool>.seeded(false);
  final _error = BehaviorSubject<String>.seeded("");
  final _refreshVisible = BehaviorSubject<bool>();
  final _loadingScreenVisible = BehaviorSubject<LoadingWidgetModel>();
  BehaviorSubject<ConnectivityResult> networkStatus =
  BehaviorSubject<ConnectivityResult>();

  final List<StreamSubscription> streamSubs = [];

  Sink<bool> get showRefresh => _refreshVisible.sink;

  Stream<bool> get refreshVisible => _refreshVisible.stream;

  Sink<String> get error => _error.sink;

  Stream<String> get errorStream => _error.stream;

  Sink<bool> get showProgress => _progressVisible.sink;

  Stream<bool> get progressVisible => _progressVisible.stream;

  Stream<LoadingWidgetModel> get loadingScreenStream =>
      _loadingScreenVisible.stream;

  var refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();
  var refreshCompleter = Completer();

  int _loadingScreenCount = 0;
  String _lastScreenError = "";

  @protected
  void init() {
    logger.t('$runtimeType init()');
    streamSubs.add(refreshVisible.listen((value) {
      logger.t("refreshVisible:$value");
      if (value) {
        refreshIndicatorKey.currentState?.show();
      } else {
        if (!refreshCompleter.isCompleted) {
          refreshCompleter.complete();
        }
      }
    }));
    _handlerNetWorkChange();
  }

  @protected
  void reload() {
    logger.t('$runtimeType reload()');
    _error.safeAdd("");
  }

  @protected
  void successResponse(response, {data}) {
    _progressVisible.safeAdd(false);
    _refreshVisible.safeAdd(false);
  }

  @protected
  void dispose() {
    logger.t('$runtimeType dispose()');
    _progressVisible.close();
    _error.close();
    streamSubs.forEach((sub) => sub.cancel());
    streamSubs.clear();
    _refreshVisible.close();
    networkStatus.close();
    _loadingScreenVisible.close();
  }

  @protected
  Future<void> onRefresh() async {
    logger.t('onRefresh');
    if (refreshCompleter.isCompleted) {
      refreshCompleter = Completer();
      reload();
    }
    return refreshCompleter.future;
  }

  showLoading() {
    _progressVisible.safeAdd(true);
  }

  completeLoading() {
    if (!_progressVisible.isClosed) {
      _progressVisible.safeAdd(false);
    }
    if (!_refreshVisible.isClosed) {
      _refreshVisible.safeAdd(false);
    }
    completeScreenLoading();
  }

  void handlerApiError(dynamic error, {BehaviorSubject<dynamic>? behavior}) {
    logger.e(error);
  }

  Future<ConnectivityResult> getNetworkStatus() async {
    var connectivityResult = await (Connectivity().checkConnectivity());
    if (!networkStatus.isClosed) {
      networkStatus.safeAdd(connectivityResult);
    }
    return connectivityResult;
  }

  _handlerNetWorkChange() {
    streamSubs.add(Connectivity()
        .onConnectivityChanged
        .listen((ConnectivityResult result) {
      logger.t('onConnectivityChanged $result');
      networkStatus.safeAdd(result);
      if (result != ConnectivityResult.none) {
        reload();
      }
    }));
  }

  // Loading khi gọi toàn màn hình
  showScreenLoading({int maxApi = 1, bool isSubmit = false}) {
    _lastScreenError = "";
    _loadingScreenCount = maxApi;
    LoadingResult.loading(behavior: _loadingScreenVisible, isSubmit: isSubmit);
  }

  completeScreenLoading({bool isSubmit = false}) {
    _loadingScreenCount = _loadingScreenCount - 1;
    if (_loadingScreenCount == 0) {
      if (_lastScreenError.length > 0 && isSubmit == false) {
        LoadingResult.error(
            error: _lastScreenError, behavior: _loadingScreenVisible);
      } else {
        LoadingResult.success(behavior: _loadingScreenVisible);
      }
    }
  }

// Loading khi gọi area
  showAreaLoading({BehaviorSubject<dynamic>? behavior}) {
    safeAddData(behavior, null);
  }

  safeAddData<T>(Subject<T>? subject, T data) {
    if (subject == null || subject.isClosed) return;
    subject.add(data);
  }
}
