import 'dart:io';

import 'package:common/utils/log.dart';
import 'package:intl/intl.dart';

const localeVN = 'vi_VN';
const localeEnUS = 'en_US';
String localeFormat = localeVN;
final _numFormat = NumberFormat("#,###", localeFormat);
final numFormatDecimal = NumberFormat("#,##0.00", localeFormat);
final _numFormatCurrency = NumberFormat("###,###,###.##", localeFormat);
final _numFormatCurrencyNotDecimal = NumberFormat("###,###,###", localeFormat);
final _dateFormatFull = DateFormat("dd/MM/yyyy HH:mm:ss");
final _dateFormatDMYHHmm = DateFormat("dd/MM/yyyy HH:mm");
final _dateFormatDMY = DateFormat("dd/MM/yyyy");
final _dateFormat_YMD = DateFormat("yyyyMMdd");
final _formatYMDHMS = DateFormat("yyyyMMddHHmmss");
final _dateFormatDM = DateFormat("dd/MM");
final _dateFormatY = DateFormat("yyyy");
final _dateFormatM = DateFormat("MM");
final _dateFormatD = DateFormat("dd");
final _dateFormatDMYcf = DateFormat("yyyy-MM-ddTHH:mm:ss");
final _dateFormat24h = DateFormat("hh:mm dd/MM/yyyy");
final _dateFormat24H = DateFormat("HH:mm - dd/MM/yyyy");
final numFormatter = NumberFormat("#,##0", localeFormat);
final formatDate = DateFormat("dd-MM-yyyy");
final formatHours = DateFormat("hh:mm a");
final formatDateTime = DateFormat("hh:mm a dd/MM/yyyy");
final _formatMMMYYYY = DateFormat("MMMM yyyy");
final _formatMMYYYY = DateFormat("MM/yyyy");
final _formatEEE = DateFormat("EEE");
final _formatWithOutYY = DateFormat("HH:mm dd MMM");
final formatTime24h = DateFormat("HH:mm");
final formatDMYHHmm = DateFormat("dd/MM/yyyy HH:mm");
final _dateFormatYMD = DateFormat("yyyy-MM-dd");
final formatime24h = DateFormat("HH:mm");
final _dateFormatDMYHHmma = DateFormat("dd/MM/yyyy HH:mm a");

///this file's purpose is converting from dynamic to String
///and any files in the parent's folder (ext) work in similar rule

const _kb = 1024;
const _M = 1000000;
//exp: 1%
const _minRate = 1;

extension Nilly on num {
  num get safeNum => this;

  num roundAsFixed(int frag) {
    return toStringAsFixed(frag).strSafeNum;
  }

  num get safePeriodMonth {
    final n = this;
    final x = (n == 0) ? 12 : n;
    return x;
  }

  //109.139.000 -> '109,14'
  //90.000.000 -> '90'
  //90.123.000 -> '90,12'
  //### 900.123 -> '900.123' //hâm => 900 tỉ 123 triệu
  //### min: _M
  //------------
  //9,012 -> '9,01'
  //90,812 -> '90,81'
  //90,689 -> '90,69'
  //90 -> '90'
  //999 -> '999'
  //999,123 -> '999,12'
  //999,999 -> '999,99'
  //max: 999,999 (because: 1000 triệu, hâm)
  String get millionFormat {
    final n = safeNum;
    final d = (n >= _M) ? (n / _M) : n;
    return d._decimalFormat();
  }

  String pad({int pad = 2}) {
    return safeNum.toString().padLeft(2, '0');
  }

  String get mbFormat {
    final n = safeNum / _kb;
    return "${n._decimalFormat()} Mb";
  }

  String get sign => safeNum >= 0 ? "+" : "";

  String get currencyFormat => _numFormatCurrency.format(safeNum);

  String get currencyFormatNotDecimal =>
      _numFormatCurrencyNotDecimal.format(safeNum);

  //for int, x >= 1000
  String get numFormat => _numFormat.format(safeNum);

  //đã chia cho total
  //=> nhân với 100
  String get dividedPercentFormat {
    //return "${(safeNum * 100)}%";
    return "${(safeNum * 100)._decimalFormat()}%";
  }

  //for double, nullable
  String get percentFormat {
    return "${safeNum._decimalFormat()}%";
  }

  String get interestRateFormat {
    return "${_real._decimalFormat()}%";
  }

  //có thể đã đc api chia 100
  //0.9 => 90
  //0.2 => 20
  //1.1 => 1.1
  //12 => 12
  num get _real {
    final n = safeNum;
    return (n >= _minRate) ? n : n * 100;
  }

  //for double not null
  String _decimalFormat({int fix = 2}) => (this is int)
      ? numFormat
      : _numFormatCurrency.format(double.parse(toStringAsFixed(fix)));

  String get signNumFormat => "$sign$numFormat";
}

extension Dilly on DateTime? {
  String get formatDM => (this != null) ? _dateFormatDM.format(this!) : "";

  String get formatYY => (this != null) ? _dateFormatY.format(this!) : "";

  String get formatMM => (this != null) ? _dateFormatM.format(this!) : "";

  String get formatDD => (this != null) ? _dateFormatD.format(this!) : "";

  String get formatDMY => (this != null) ? _dateFormatDMY.format(this!) : "";

  String get formatYMDHMS => (this != null) ? _formatYMDHMS.format(this!) : "";

  String get formatHHmm => (this != null) ? formatTime24h.format(this!) : "";

  String get formatDMYHHmm =>
      (this != null) ? _dateFormatDMYHHmm.format(this!) : "";

  String get formatFull => (this != null) ? _dateFormatFull.format(this!) : "";

  String get format24h => (this != null) ? _dateFormat24h.format(this!) : "";

  String get format24H => (this != null) ? _dateFormat24H.format(this!) : "";

  String get formatMMMYYYY =>
      (this != null) ? _formatMMMYYYY.format(this!) : "";

  String get formatEEE => (this != null) ? _formatEEE.format(this!) : "";

  String get formatMMYYYY => (this != null) ? _formatMMYYYY.format(this!) : "";

  String get formatWithOutYY =>
      (this != null) ? _formatWithOutYY.format(this!) : "";

  String get formatYMD => (this != null) ? _dateFormatYMD.format(this!) : "";

  String get format_YMD => (this != null) ? _dateFormat_YMD.format(this!) : "";

  String get formatDMYHHmma =>
      (this != null) ? _dateFormatDMYHHmma.format(this!) : "";

  String get chatTime {
    if (this == null) return '';
    final now = DateTime.now();
    if (now.formatDMY == this.formatDMY) {
      return formatHours.format(this!);
    }
    return DateFormat("hh:mm a dd/MM").format(this!);
  }
}

extension Dlly on dynamic {
  DateTime? get toDate {
    try {
      final value = this;
      if (value == null) return null;
      if (value is DateTime) return value;
      //if (value is Timestamp) return value.toDate();
      if (value is String) return value.strToDate;
    } catch (e) {
      logger.e(e);
    }
    return null;
  }
}

const pdf = '.pdf';

extension Silly on String? {
  String? get unBreak => this != null ? this!.replaceAll('\n', '') : null;

  bool isPdf({bool get = false}) {
    final url = this != null ? this!.toLowerCase() : null;
    return get || !Platform.isIOS
        ? url?.contains(pdf) ?? false
        : url?.endsWith(pdf) ?? false;
  }

  bool get isUrl {
    if (this == null) return false;
    return Uri.parse(this!).isAbsolute;
  }

  String get toPdfPath {
    var path = this != null
        ? this!.substring(this!.lastIndexOf("://") + 1).replaceAll("/", "_")
        : "";
    if (!path.isPdf()) path += pdf;
    return path;
  }

  //f: formatted
  int? get fToInt => int.tryParse(this!.replaceAll(",", ""))?.safeNum as int;

  int get trimLength => this?.replaceAll(' ', '').length ?? 0;

  int get safeLength => this?.length ?? 0;

  String get spreadFormat => "${this} m2";

  DateTime get dateOrNow {
    final value = this;
    return !value.isNullOrEmpty ? _dateFormatDMY.parse(value!) : DateTime.now();
  }

  //2020-03-15T09:21:26.000Z
  DateTime? get strToDateCf {
    return isNullOrEmpty ? null : _dateFormatDMYcf.parse(this!);
  }

  DateTime? get strToDate {
    return isNullOrEmpty ? null : _dateFormatDMY.parse(this!);
  }

  String get parenthesesFormat => "($this)";

  int? get toInt => this != null ? int.tryParse(this!) : null;

  bool get isNullOrEmpty => this == null || this!.trim().isEmpty;

  String? get formatPhoneNumber {
    var phone = this;
    if (phone != null) {
      logger.v("---phone = $phone------");
      if (phone.startsWith("+84")) return phone;
      phone = this!.replaceAll("[^\\d.]", "");
      if (phone.startsWith('0')) {
        phone = this!.replaceFirst('0', '84');
      }
      final builder = StringBuffer();
      builder.write('+');
      if (phone.startsWith('84')) {
        builder.write(phone);
      } else {
        builder
          ..write('84')
          ..write(phone);
      }
      return builder.toString();
    }
    return null;
  }

  String? get correctUrl {
    final url = this;
    if (url != null) {
      var start = url.indexOf("http://");
      if (start < 0) start = url.indexOf("https://");
      return start <= 0 ? url : url.substring(start);
    }
    return null;
  }

  String get strDecimal {
    return strSafeNum._decimalFormat();
  }

  num get strSafeNum {
    if (isNullOrEmpty) return 0;
    return double.tryParse(this!)!.safeNum;
  }

  String get normalSearchText {
    if (this == null) return '';
    return this!.trim().toLowerCase();
  }

  String get capitalize {
    if (isNullOrEmpty) return "";
    return "${this![0].toUpperCase()}${this!.substring(1)}";
  }
}

String simplePhoneNumber(String? phoneNumber) {
  if (phoneNumber == null) return "";

  var normal = phoneNumber.replaceAll(RegExp(r'[^\d.-]'), "");
  if (normal.startsWith("84")) {
    normal = normal.replaceFirst("84", "0");
  }
  logger.t(phoneNumber, error: normal);
  return normal;
}

bool isEmail(String? email) {
  if (email == null || email.trim().isEmpty) return false;

  return RegExp(r"(^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$)")
      .hasMatch(email);
}
