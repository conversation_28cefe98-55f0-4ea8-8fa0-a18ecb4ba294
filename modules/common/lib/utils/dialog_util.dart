import 'dart:io';

import 'package:another_flushbar/flushbar.dart';
import 'package:common/model/option_sheet_model.dart';
import 'package:common/widgets/ink_well_button.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class DialogUtil {
  static bool isDialogShowing = false;
  static BuildContext? alertContext;
  static String defaultTitle = "KSFinance";
  static TextStyle? submitStyle;

  static Future<T?> confirm<T>(
    BuildContext? context,
    Widget content, {
    String? title,
    String? cancelText,
    String? submitText,
    Function? onCancel,
    Function? onSubmit,
    TextStyle? styleCancel,
    TextStyle? styleConfirm,
    bool? barrierDismissible,
  }) {
    if (context == null || !context.mounted) return Future.value();
    if (Platform.isIOS) {
      return showCupertinoDialog(
          context: context,
          useRootNavigator: false,
          barrierDismissible: barrierDismissible ?? false,
          builder: (context) {
            return _alertConfirm(
              context,
              content,
              title: title,
              cancelText: cancelText,
              submitText: submitText,
              styleCancel: styleCancel,
              styleConfirm: styleConfirm,
              onCancel: onCancel,
              onSubmit: onSubmit,
            );
          });
    }
    return showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible ?? false, // user must tap button!
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title?.isNotEmpty == true ? title! : defaultTitle),
          elevation: 8,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
          content: content,
          actions: <Widget>[
            TextButton(
                child: Text(
                  cancelText?.isNotEmpty == true ? cancelText! : 'Huỷ',
                  style: styleCancel,
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                  if (onCancel != null) {
                    onCancel();
                  }
                }),
            TextButton(
              child: Text(
                submitText?.isNotEmpty == true ? submitText! : 'Đồng ý',
                style: styleConfirm,
              ),
              onPressed: () {
                Navigator.of(context).pop();
                if (onSubmit != null) {
                  onSubmit();
                }
              },
            )
          ],
        );
      },
    );
  }

  static Future<T?> options<T>(
    BuildContext context, {
    String? title,
    List<T>? options,
    Widget? creatorItem(T item)?,
    bool? adaptive,
  }) {
    if (Platform.isIOS && adaptive != false) {
      return showCupertinoDialog(
        context: context,
        useRootNavigator: false,
        builder: (BuildContext context) {
          return _simpleOptions<T>(
            context,
            title: title,
            options: options,
            creatorItem: creatorItem,
          );
        },
      );
    }
    return showDialog<T>(
      context: context,
      builder: (BuildContext context) {
        return SimpleDialog(
          title: title != null ? Text(title) : null,
          elevation: 8,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
          children: options
              ?.map(
                (item) => SimpleDialogOption(
                  onPressed: () => Navigator.pop(context, item),
                  child: creatorItem?.call(item),
                ),
              )
              .toList(),
        );
      },
    );
  }

  static Future<T?> alert<T>(
    BuildContext? context,
    dynamic content, {
    Function? onSubmit,
    String? title,
    String? submit,
    bool? barrierDismissible,
    bool? canPop,
  }) {
    if (context == null ||
        content == null ||
        (content is String && content.trim().isEmpty) ||
        !context.mounted) {
      return Future.value();
    }

    isDialogShowing = true;
    if (Platform.isIOS) {
      return showCupertinoDialog(
        context: context, useRootNavigator: false,
        barrierDismissible:
            barrierDismissible ?? false, // user must tap button!
        builder: (BuildContext context) {
          alertContext = context;
          return _alert(
            context,
            content,
            onSubmit: onSubmit,
            title: title,
            submit: submit,
            canPop: canPop,
          );
        },
      );
    }
    return showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible ?? false, // user must tap button!
      builder: (BuildContext context) {
        return PopScope(
          canPop: canPop ?? true,
          child: AlertDialog(
            elevation: 8,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
            title:
                title?.isNotEmpty == true ? Text(title!) : Text(defaultTitle),
            content: content is String && content.isNotEmpty == true
                ? Text(content)
                : content,
            actions: <Widget>[
              TextButton(
                child: Text(
                  submit?.isNotEmpty == true ? submit! : 'Đồng ý',
                  style: submitStyle,
                ),
                onPressed: () {
                  isDialogShowing = false;
                  Navigator.of(context).pop();
                  if (onSubmit != null) {
                    onSubmit();
                  }
                },
              )
            ],
          ),
        );
      },
    );
  }

  static _alertConfirm(
    BuildContext context,
    Widget content, {
    String? title,
    String? cancelText,
    String? submitText,
    Function? onCancel,
    Function? onSubmit,
    TextStyle? styleCancel,
    TextStyle? styleConfirm,
  }) {
    return CupertinoAlertDialog(
      title: Text(title?.isNotEmpty == true ? title! : defaultTitle),
      content: content,
      actions: <Widget>[
        CupertinoDialogAction(
            child: Text(
              cancelText?.isNotEmpty == true ? cancelText! : 'Huỷ',
              style: styleCancel,
            ),
            onPressed: () {
              Navigator.of(context).pop();
              if (onCancel != null) {
                onCancel();
              }
            }),
        CupertinoDialogAction(
          child: Text(
            submitText?.isNotEmpty == true ? submitText! : 'Đồng ý',
            style: styleConfirm,
          ),
          onPressed: () {
            Navigator.of(context).pop();
            if (onSubmit != null) {
              onSubmit();
            }
          },
        )
      ],
    );
  }

  static _simpleOptions<T>(
    BuildContext context, {
    String? title,
    List<T>? options,
    Widget? creatorItem(T item)?,
  }) {
    return CupertinoAlertDialog(
      title: title != null ? Text(title) : null,
      actions: options
              ?.map(
                (item) => CupertinoDialogAction(
                  onPressed: () => Navigator.pop(context, item),
                  child: creatorItem?.call(item) ?? Container(),
                ),
              )
              .toList() ??
          [],
    );
  }

  static _alert(
    BuildContext context,
    dynamic content, {
    Function? onSubmit,
    String? title,
    String? submit,
    bool? canPop,
  }) {
    return PopScope(
      canPop: canPop ?? true,
      child: CupertinoAlertDialog(
        title:
            title?.isNotEmpty == true ? Text(title ?? '') : Text(defaultTitle),
        content: content is String && content.isNotEmpty == true
            ? Text(content)
            : content,
        actions: <Widget>[
          CupertinoDialogAction(
            child: Text(
              submit?.isNotEmpty == true ? submit! : 'Đồng ý',
              style: submitStyle,
            ),
            onPressed: () {
              isDialogShowing = false;
              Navigator.of(context).pop();
              if (onSubmit != null) {
                onSubmit();
              }
            },
          )
        ],
      ),
    );
  }

  static comingSoonMessage(BuildContext context) {
    DialogUtil.alert(
        context, "Chức năng đang phát triển vui lòng thử lại sau.");
  }

  static dismissAlert() {
    if (isDialogShowing == true && alertContext != null) {
      Navigator.pop(alertContext!);
    }
  }

  static showFlushBar(BuildContext context, String message,
      {Color? backgroundColor,
      Widget? iconFlushBar,
      Duration? duration = const Duration(seconds: 2)}) {
    Flushbar(
      backgroundColor: backgroundColor ?? Colors.green,
      flushbarStyle: FlushbarStyle.GROUNDED,
      messageColor: Colors.white,
      duration: duration,
      flushbarPosition: FlushbarPosition.TOP,
      icon: iconFlushBar ??
          const Icon(
            Icons.check_circle_rounded,
            color: Colors.white,
          ),
      message: message,
    ).show(context);
  }

  static Future<T?> confirmSheet<T>(
    BuildContext context, {
    String? title,
    dynamic content,
    String? cancelText,
    String? submitText,
    Color? submitColor,
    Color? cancelColor,
  }) {
    return showCupertinoModalPopup(
        context: context,
        useRootNavigator: false,
        builder: (popupContext) {
          return CupertinoActionSheet(
            title: Text(
              title ?? defaultTitle,
              style: Theme.of(context)
                  .textTheme
                  .titleSmall!
                  .copyWith(fontWeight: FontWeight.w500),
            ),
            message: (content is String && content.length > 0)
                ? Container(
                    alignment: Alignment.center,
                    child: Text(content,
                        style: Theme.of(context).textTheme.bodySmall),
                  )
                : content is Widget
                    ? content
                    : null,
            actions: [
              CupertinoActionSheetAction(
                onPressed: () {
                  Navigator.of(popupContext).pop(true);
                },
                child: Text(
                  submitText?.isNotEmpty == true ? submitText! : "Xác nhận",
                  style: TextStyle(
                    color: submitColor ?? Theme.of(context).primaryColor,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              )
            ],
            cancelButton: CupertinoActionSheetAction(
              onPressed: () {
                Navigator.of(popupContext).pop();
              },
              child: Text(
                cancelText?.isNotEmpty == true ? cancelText! : "Hủy",
                style: TextStyle(
                  color: cancelColor ?? Theme.of(context).primaryColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          );
        });
  }

  static Future<T?> optionSheet<T>(
    BuildContext context, {
    String? title,
    dynamic content,
    String? cancelText,
    Color? cancelColor,
    @required List<OptionSheetModel>? options,
  }) {
    return showCupertinoModalPopup(
      context: context,
      useRootNavigator: false,
      builder: (popupContext) {
        return CupertinoActionSheet(
          title: Text(
            title?.isNotEmpty == true ? title! : defaultTitle,
            style: Theme.of(context)
                .textTheme
                .titleSmall!
                .copyWith(fontWeight: FontWeight.w500),
          ),
          message: (content is String && content.length > 0)
              ? Container(
                  alignment: Alignment.center,
                  child: Text(
                    content,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                )
              : content is Widget
                  ? content
                  : null,
          actions: options!
              .map<CupertinoActionSheetAction>((e) =>
                  CupertinoActionSheetAction(
                    onPressed: () {
                      Navigator.of(popupContext).pop(e.id);
                    },
                    child: Text(
                      e.name,
                      style: TextStyle(
                        color: e.textColor ?? Theme.of(context).primaryColor,
                        fontWeight: (e.isBold == true
                            ? FontWeight.w500
                            : FontWeight.w400),
                      ),
                    ),
                  ))
              .toList(),
          cancelButton: CupertinoActionSheetAction(
            onPressed: () {
              Navigator.of(popupContext).pop();
            },
            child: Text(
              cancelText?.isNotEmpty == true ? cancelText! : "Hủy",
              style: TextStyle(
                color: cancelColor ?? Theme.of(context).primaryColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        );
      },
    );
  }

  ///for new dialog
  static Future<T?> alertV2<T>(
    BuildContext? context,
    dynamic content, {
    Function? onSubmit,
    TextStyle? submitStyle,
    String? title,
    String? submit,
    Color? backgroundColorButton,
    Color? textColorButton,
    bool? barrierDismissible,
    bool? canPop,
    TextAlign? contentAlignForIOS,
  }) {
    if (context == null ||
        content == null ||
        (content is String && content.trim().isEmpty) ||
        !context.mounted) {
      return Future.value();
    }

    isDialogShowing = true;
    if (Platform.isIOS) {
      return showCupertinoDialog(
        context: context, useRootNavigator: false,
        barrierDismissible:
            barrierDismissible ?? false, // user must tap button!
        builder: (BuildContext context) {
          alertContext = context;
          return _newAlertIOS(
            context,
            content,
            onSubmit: onSubmit,
            submitStyle: submitStyle,
            backgroundColorButton: backgroundColorButton,
            title: title,
            submit: submit,
            textColorButton: textColorButton,
            canPop: canPop,
            contentAlign: contentAlignForIOS,
          );
        },
      );
    }
    return showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible ?? false, // user must tap button!
      builder: (BuildContext context) {
        return _newAlertAndroid(
          context,
          content,
          onSubmit: onSubmit,
          backgroundColorButton: backgroundColorButton,
          title: title,
          submit: submit,
          textColorButton: textColorButton,
          canPop: canPop,
        );
      },
    );
  }

  static _newAlertAndroid(
    BuildContext context,
    dynamic content, {
    Function? onSubmit,
    String? title,
    Color? backgroundColorButton,
    String? submit,
    Color? textColorButton,
    bool? canPop,
  }) {
    return PopScope(
      canPop: canPop ?? true,
      child: AlertDialog(
        elevation: 8,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
        title: title?.isNotEmpty == true ? Text(title!) : Text(defaultTitle),
        content: content is String && content.isNotEmpty == true
            ? Text(content)
            : content,
        actions: <Widget>[
          Container(
            margin: const EdgeInsets.all(8),
            child: InkWellButton(
              title: submit?.isNotEmpty == true ? submit! : 'Đồng ý',
              buttonDarkColor: backgroundColorButton,
              textColor: textColorButton,
              onTap: () {
                isDialogShowing = false;
                Navigator.of(context).pop();
                if (onSubmit != null) {
                  onSubmit();
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  static _newAlertIOS(
    BuildContext context,
    dynamic content, {
    Function? onSubmit,
    String? title,
    TextStyle? submitStyle,
    Color? backgroundColorButton,
    String? submit,
    Color? textColorButton,
    bool? canPop,
    TextAlign? contentAlign,
  }) {
    return PopScope(
      canPop: canPop ?? true,
      child: CupertinoAlertDialog(
        title:
            title?.isNotEmpty == true ? Text(title ?? '') : Text(defaultTitle),
        content: content is String && content.isNotEmpty == true
            ? Text(
                content,
                textAlign: contentAlign,
              )
            : content,
        actions: <Widget>[
          Container(
            margin: const EdgeInsets.all(16),
            child: InkWellButton(
              title: submit?.isNotEmpty == true ? submit! : 'Đồng ý',
              buttonColor: backgroundColorButton,
              textColor: textColorButton,
              onTap: () {
                isDialogShowing = false;
                Navigator.of(context).pop();
                if (onSubmit != null) {
                  onSubmit();
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  static Future<T?> confirmV2<T>(
    BuildContext? context,
    Widget content, {
    String? title,
    String? cancelText,
    String? submitText,
    Function? onCancel,
    Function? onSubmit,
    Color? cancelBgColor,
    Color? confirmBgColor,
    Color? cancelTextColor,
    Color? confirmTextColor,
    bool? barrierDismissible,
  }) {
    if (context == null || !context.mounted) return Future.value();
    if (Platform.isIOS) {
      return showCupertinoDialog(
          context: context,
          useRootNavigator: false,
          barrierDismissible: barrierDismissible ?? false,
          builder: (context) {
            return _alertConfirmIOS(
              context,
              content,
              title: title,
              cancelText: cancelText,
              submitText: submitText,
              cancelBgColor: cancelBgColor,
              confirmBgColor: confirmBgColor,
              onCancel: onCancel,
              onSubmit: onSubmit,
            );
          });
    }
    return showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible ?? false, // user must tap button!
      builder: (BuildContext context) {
        return _newAlertConfirmAndroid(
          context,
          content,
          title: title,
          cancelText: cancelText,
          submitText: submitText,
          cancelBgColor: cancelBgColor,
          confirmBgColor: confirmBgColor,
          onCancel: onCancel,
          onSubmit: onSubmit,
        );
      },
    );
  }

  static _newAlertConfirmAndroid(
    BuildContext context,
    Widget content, {
    String? title,
    String? cancelText,
    String? submitText,
    Function? onCancel,
    Function? onSubmit,
    Color? cancelBgColor,
    Color? confirmBgColor,
    Color? cancelTextColor,
    Color? confirmTextColor,
  }) {
    return AlertDialog(
      title: Text(title?.isNotEmpty == true ? title! : defaultTitle),
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
      content: content,
      actions: <Widget>[
        Container(
          margin: const EdgeInsets.all(8),
          child: InkWellButton(
            title: cancelText?.isNotEmpty == true ? cancelText! : 'Huỷ',
            buttonColor: cancelBgColor,
            textColor: cancelTextColor,
            onTap: () {
              Navigator.of(context).pop();
              if (onCancel != null) {
                onCancel();
              }
            },
          ),
        ),
        Container(
          margin: const EdgeInsets.all(8),
          child: InkWellButton(
            title: submitText?.isNotEmpty == true ? submitText! : 'Đồng ý',
            buttonColor: confirmBgColor,
            textColor: confirmTextColor,
            onTap: () {
              Navigator.of(context).pop();
              if (onSubmit != null) {
                onSubmit();
              }
            },
          ),
        ),
      ],
    );
  }

  static _alertConfirmIOS(
    BuildContext context,
    Widget content, {
    String? title,
    String? cancelText,
    String? submitText,
    Function? onCancel,
    Function? onSubmit,
    Color? cancelBgColor,
    Color? confirmBgColor,
    Color? cancelTextColor,
    Color? confirmTextColor,
  }) {
    return CupertinoAlertDialog(
      title: Text(title?.isNotEmpty == true ? title! : defaultTitle),
      content: content,
      actions: <Widget>[
        Container(
          margin: const EdgeInsets.all(8),
          child: InkWellButton(
            title: cancelText?.isNotEmpty == true ? cancelText! : 'Huỷ',
            buttonColor: cancelBgColor,
            textColor: cancelTextColor,
            onTap: () {
              Navigator.of(context).pop();
              if (onCancel != null) {
                onCancel();
              }
            },
          ),
        ),
        Container(
          margin: const EdgeInsets.all(8),
          child: InkWellButton(
            title: submitText?.isNotEmpty == true ? submitText! : 'Đồng ý',
            buttonColor: confirmBgColor,
            textColor: confirmTextColor,
            onTap: () {
              Navigator.of(context).pop();
              if (onSubmit != null) {
                onSubmit();
              }
            },
          ),
        ),
      ],
    );
  }
}
