name: common
description: A new Flutter project.

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
#
# This version is used _only_ for the Runner app, which is used if you just do
# a `flutter run` or a `flutter make-host-app-editable`. It has no impact
# on any other native host app that you embed your Flutter project into.
version: 1.0.0+1

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.2
  url_launcher: ^6.3.0
  flutter_screenutil: ^5.5.3+2
  rxdart: ^0.27.7
  path_provider: ^2.0.11
  flutter_pdfview: ^1.3.4
  flutter_svg: ^2.0.7
  flutter_html: ^3.0.0-beta.2
  logger: ^2.3.0
  http: ^0.13.1
  dio: ^5.7.0
  intl: ^0.19.0
  cached_network_image: ^3.2.0
#  flare_flutter: 3.0.2
  connectivity: ^3.0.3
  jwt_decoder: ^2.0.1
  modal_bottom_sheet: ^3.0.0-pre
  permission_handler: ^11.3.1
  share: ^2.0.4
  flutter_markdown: ^0.7.3
  shimmer: ^3.0.0
  animations: ^2.0.0
  tuple: ^2.0.0
  smooth_page_indicator: ^1.0.0+2
  video_player: ^2.1.6
  flick_video_player: ^0.9.0
  visibility_detector: ^0.4.0+2
  flutter_staggered_grid_view: ^0.7.0
  fl_chart: ^0.68.0
  another_flushbar: ^1.10.23
  webview_flutter: ^4.10.0
  webview_flutter_android: ^4.2.0
  webview_flutter_wkwebview: ^3.14.0
  qr_code_scanner: ^1.0.1
#  cloud_firestore: ^4.6.0
  firebase_analytics: ^10.10.7
  auto_size_text: ^3.0.0
  equatable: ^2.0.3
  photo_view: ^0.15.0
  google_mlkit_barcode_scanning: ^0.12.1
  timelines_plus: ^1.0.6
  flutter_device_type: ^0.4.0
  device_info_plus: ^10.1.0
  firebase_crashlytics: ^3.5.7
  flare_flutter:
    git:
      url: https://github.com/mbfakourii/Flare-Flutter.git
      path: flare_flutter
      ref: remove_hashValues

dev_dependencies:
  flutter_test:
    sdk: flutter

flutter:
  uses-material-design: true
  assets:
    - assets/icons/

  module:
    androidX: true
    androidPackage: com.sunshine.ks_global.common.common
    iosBundleIdentifier: com.sunshine.ksglobal.common.common
