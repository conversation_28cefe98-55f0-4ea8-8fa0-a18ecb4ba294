name: ksb_common
description: Umee common module
version: 0.0.1
homepage:

environment:
  sdk: ">=3.0.0 <4.0.0"
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter

  common:
    path: ../common
  ksbank_api_smartbank:
    path: ../../open_api/ksbank/smartbank
  ksbank_api_stm:
    path: ../../open_api/ksbank/stm
  ksbank_api_profile:
    path: ../../open_api/ksbank/profile
  myshop_api:
    path: ../../open_api/ksbank/myshop
  ksbank_api_nickname:
    path: ../../open_api/ksbank/nickname
  geolocator: ^9.0.2
  google_maps_flutter: ^2.2.0
  google_directions_api: ^0.9.1
  geocoding: ^2.0.1
  pull_to_refresh: ^2.0.0
  flutter_speed_dial: ^6.0.0
  image_gallery_saver: ^2.0.3
  lottie: ^2.3.2
  flutter_slidable: ^4.0.0
  qr_flutter: ^4.1.0
  flutter_svg_provider: ^1.0.4
  equatable: ^2.0.3
  permission_handler: ^11.3.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  assets:
    - assets/icons/
    - assets/icons/home_chart/
    - assets/icons/loan/
    - assets/icons/invest_tab/
    - assets/icons/paybox/
    - assets/images/
    - assets/images/finance/
    - assets/map/
    - assets/animation/
    - assets/icons/signin_screen/
    - assets/v2/images/
    - assets/v2/icon/
    - assets/v2/images/theme/
    - assets/v2/images/theme/diamond/
    - assets/v2/images/theme/ruby/
    - assets/v2/images/theme/sapphire/
  # To add assets to your package, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
