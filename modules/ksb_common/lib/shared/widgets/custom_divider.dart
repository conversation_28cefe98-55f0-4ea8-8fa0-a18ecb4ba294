import 'package:flutter/material.dart';

class CustomDivider extends StatelessWidget {
  const CustomDivider(
      {Key? key,
      this.color,
      this.width = double.infinity,
      this.height = 1,
      this.margin = EdgeInsets.zero})
      : super(key: key);
  final Color? color;
  final double width;
  final double height;
  final EdgeInsets margin;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: color ?? Theme.of(context).dividerColor,
      height: height,
      width: width,
      margin: margin,
    );
  }
}
