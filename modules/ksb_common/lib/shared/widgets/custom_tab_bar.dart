import 'package:flutter/material.dart';
import 'package:common/ks_common.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ksb_common/shared/constant.dart';
import 'package:ksb_common/shared/theme/custom_theme.dart';
import 'package:ksb_common/shared/widgets/index.dart';

class CustomTabBar extends StatelessWidget {
  const CustomTabBar({
    Key? key,
    required this.tabs,
    this.controller,
  }) : super(key: key);
  final List<Widget> tabs;
  final TabController? controller;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 40.0.h,
      margin: kHorizontalPaddingStandard,
      decoration: BoxDecoration(
          color: const Color(0xFFEAEAF0), borderRadius: BorderRadius.circular(8.0)),
      child: TabBar(
        indicator: BubbleTabIndicator(
          tabBarIndicatorSize: TabBarIndicatorSize.tab,
          indicatorHeight: 36.0.h,
          indicatorColor: Colors.white,
          indicatorRadius: 6,
          insets: const EdgeInsets.symmetric(horizontal: 2.0),
        ),
        labelStyle:
            StyleApp.bodyText2(context)?.copyWith(fontWeight: FontWeight.bold),
        unselectedLabelStyle: StyleApp.bodyText2(context),
        unselectedLabelColor: Colors.black87,
        labelColor: Theme.of(context).primaryColor,
        labelPadding: const EdgeInsets.all(0.0),
        tabs: tabs,
        controller: controller,
        dividerColor: Colors.transparent,
      ),
    );
  }
}
