import 'package:flutter/material.dart';

class CustomColor {
  final Color? buttonBottomColor;
  final Color? backgroundAppBarColor;
  final Color? backgroundAlertColor;
  final Color? backgroundSegmentColor;
  final Color? activeSegmentColor;
  final Color? bgMessageChat;
  final Color? bgMessageChatOther;
  final Color? textMessageChat;
  final Color? textMessageChatOther;
  final Color? backgroundWarning;
  final Color? ksBackgroundAction;
  final Color? backgroundUnreadCountColor;
  final Color? errorColor;
  final Color? backgroundColor;
  final Color? disabledColor;
  final Color? highlightColor;
  final Color? shimmerChild;
  final Color? shimmerHighlight;
  final Color? toggleableActiveColor;
  final Color? yellow2;
  final Color? electricGreen;
  final Color? electricGreenV2;
  final Color? black;
  final Color? red;
  final Color? darkBlue;
  final Color? backgroundButton;
  final Color? backgroundGray;
  final Color? headerColor;
  final Color? emptyColor;
  final Color? newRed10;
  final Color? newRed20;
  final Color? newRed90;
  final Color? newRed;
  final Color? newBlue;
  final Color? newBlue10;
  final Color? buttonTransparentColor;
  final Color? bgTextFieldGrey;
  final Color? unSelectedColorTabBar;

  final Color? border;
  final Color? dividerColor;
  final Color? titleColor2;
  final Color? captionColor;
  final Color? unitMoneyColor;
  final Color? blue;
  final Color? hintTextColor;
  final Color? green;
  final Color? blue0;
  final Color? backgroundSplash;
  final Color? yellow100;
  final Color? yellow0;
  final Color? yellow5;
  final Color? labelTextField;
  final Color? black20;
  final Color? red100;
  final Color? black100;
  final Color? snackMessageWarning;
  final Color? green100;
  final Color? shadowCard;
  final Color? orange100;
  final Color? blue10;
  final Color? orange10;
  final Color? orange80;
  final Color? blue05;
  final Color? blue20;
  final Color? blue60;
  final Color? blue40;
  final Color? blue80;
  final Color? blue100;
  final Color? blue150;
  final Color? unitMoneyGreen;
  final Color? blue5;
  final Color? red5;
  final Color? red60;
  final Color? red150;
  final Color? red200;
  final Color? green0;
  final Color? green05;
  final Color? green10;
  final Color? green20;
  final Color? green60;
  final Color? green70;
  final Color? green80;
  final Color? green90;
  final Color? red10;
  final Color? ink80;
  final Color? ink0;
  final Color? unFocusEToken;
  final Color? focusEToken;
  final Color? orange0;
  final Color? umeeMint5;
  final Color? umeeMint40;
  final Color? umeeMint80;
  final Color? umeeMint100;
  final Color? purple80;
  final Color? ink20;
  final Color? ink60;
  final Color? ink40;
  final Color? purple;
  final Color? purple0;
  final Color? ink10;
  final Color? pink100;
  final Color? violet100;
  final Color? ink5;
  final Color? umeeBlue60;
  final Color? submitDialog;
  final Color? warning;
  final Color? orange1;
  final Color? yellow90;
  final Color? yellow60;
  final Color? yellow10;
  final Color? lightBlue05;
  final Color? lightBlue20;
  final Color? lightBlue30;
  final Color? lightBlue40;
  final Color? lightBlue;
  final Color? neutralGrey10;
  final Color? neutralGrey20;
  final Color? neutralGrey;
  final Color? chili;
  final Color? chili10;
  final Color? textGreen;
  final Color? nobleColor;

  CustomColor({
    this.buttonBottomColor,
    this.backgroundAppBarColor,
    this.backgroundAlertColor,
    this.backgroundSegmentColor,
    this.activeSegmentColor,
    this.bgMessageChat,
    this.bgMessageChatOther,
    this.textMessageChat,
    this.textMessageChatOther,
    this.backgroundWarning,
    this.ksBackgroundAction,
    this.backgroundUnreadCountColor,
    this.errorColor,
    this.backgroundColor,
    this.disabledColor,
    this.highlightColor,
    this.shimmerChild,
    this.shimmerHighlight,
    this.toggleableActiveColor,
    this.yellow2,
    this.electricGreen,
    this.electricGreenV2,
    this.black,
    this.red,
    this.darkBlue,
    this.backgroundButton,
    this.backgroundGray,
    this.headerColor,
    this.emptyColor,
    this.newRed10,
    this.newRed20,
    this.newRed90,
    this.newRed,
    this.newBlue,
    this.newBlue10,
    this.buttonTransparentColor,
    this.dividerColor,
    this.border,
    this.titleColor2,
    this.captionColor,
    this.unitMoneyColor,
    this.blue,
    this.hintTextColor,
    this.green,
    this.blue0,
    this.backgroundSplash,
    this.yellow100,
    this.yellow0,
    this.yellow5,
    this.labelTextField,
    this.black20,
    this.red100,
    this.black100,
    this.snackMessageWarning,
    this.green100,
    this.shadowCard,
    this.orange100,
    this.blue10,
    this.orange10,
    this.orange80,
    this.blue05,
    this.blue20,
    this.blue60,
    this.blue40,
    this.blue80,
    this.blue100,
    this.blue150,
    this.unitMoneyGreen,
    this.blue5,
    this.red5,
    this.red60,
    this.red150,
    this.red200,
    this.green0,
    this.green05,
    this.green10,
    this.green20,
    this.green60,
    this.green70,
    this.green80,
    this.green90,
    this.red10,
    this.ink80,
    this.ink0,
    this.unFocusEToken,
    this.focusEToken,
    this.orange0,
    this.umeeMint5,
    this.umeeMint40,
    this.umeeMint80,
    this.umeeMint100,
    this.purple80,
    this.ink20,
    this.ink60,
    this.ink40,
    this.purple,
    this.purple0,
    this.ink10,
    this.pink100,
    this.violet100,
    this.ink5,
    this.umeeBlue60,
    this.submitDialog,
    this.warning,
    this.orange1,
    this.yellow90,
    this.yellow60,
    this.yellow10,
    this.lightBlue05,
    this.lightBlue20,
    this.lightBlue30,
    this.lightBlue40,
    this.lightBlue,
    this.neutralGrey10,
    this.neutralGrey20,
    this.neutralGrey,
    this.chili,
    this.chili10,
    this.bgTextFieldGrey,
    this.unSelectedColorTabBar,
    this.textGreen,
    this.nobleColor,
  });
}

CustomColor getCustomColor(bool isLight) {
  if (isLight) {
    return CustomColor(
      buttonBottomColor: const Color(0xFF0095DE),
      backgroundAppBarColor: Colors.white,
      backgroundAlertColor: const Color(0xFF0095DE),
      backgroundSegmentColor: const Color(0xFFE1E1E1),
      activeSegmentColor: Colors.white,
      bgMessageChat: const Color(0xff0095DE),
      bgMessageChatOther: const Color(0xffF4F4F4),
      textMessageChat: Colors.white,
      textMessageChatOther: Colors.black,
      backgroundWarning: const Color(0xFFF2FBD7),
      ksBackgroundAction: const Color(0xFFFFFFFF),
      backgroundUnreadCountColor: const Color(0xFFF04F24),
      errorColor: Colors.redAccent,
      backgroundColor: const Color(0xFFF4F4F4),
      disabledColor: Colors.grey[400],
      highlightColor: const Color(0xFF83AB2F),
      shimmerChild: Colors.black54,
      shimmerHighlight: const Color(0xFFF4F4F4),
      toggleableActiveColor: const Color(0xFF2F80ED),
      yellow2: const Color(0xFFFF4A12),
      electricGreen: const Color(0xFF00B929),
      electricGreenV2: const Color(0xFF00BC3C),
      black: const Color(0xFF333333),
      red: const Color(0xFFFF2D2E),
      darkBlue: const Color(0xFF353282),
      backgroundButton: const Color(0xFFEDEDED),
      backgroundGray: const Color(0xFFF3F3F6),
      headerColor: const Color(0xFF818181),
      emptyColor: const Color(0xFFD2D2D2),
      newRed10: const Color(0XFFFEEEEA),
      newRed20: const Color(0XFFFDEDE9),
      newRed90: const Color(0XFFF37250),
      newRed: const Color(0xFFF04F24),
      newBlue: const Color(0xFF228BCC),
      newBlue10: const Color(0XFFE9F4FA),
      buttonTransparentColor: const Color(0xFFF4F4F7),
      dividerColor: const Color(0xFFECEFF8),
      border: const Color(0xFFE6EBF8),
      titleColor2: const Color(0xFF212633),
      captionColor: const Color(0xFF525B73),
      unitMoneyColor: const Color(0xFF8E9ABB),
      blue: const Color(0xFF0283FD),
      hintTextColor: const Color(0xFFCAD2E6),
      green: const Color(0xff29CE6B),
      blue0: const Color(0xFFF2F6FF),
      backgroundSplash: const Color(0xFFFFBB33),
      yellow100: const Color(0xFFFEBD00),
      yellow0: const Color(0xFFFFFAEA),
      yellow5: const Color(0xFFFFF4D5),
      labelTextField: const Color(0xFFA9B3CC),
      black20: const Color(0xFF2B2B33),
      green100: const Color(0xFF00CB51),
      red100: const Color(0xFFFD3549),
      black100: const Color(0xFF2B2B33),
      snackMessageWarning: const Color(0xFFFDCE35),
      shadowCard: const Color(0xFF7090B0),
      orange100: const Color(0xFFFF7C59),
      blue10: const Color(0xFFBFE0FE),
      orange10: const Color(0xFFFFDBD0),
      orange80: const Color(0xFFFF8F6D),
      blue05: const Color(0XFFEBF5FF),
      blue20: const Color(0xFFD1DCFF),
      blue60: const Color(0xFF44A9FE),
      blue40: const Color(0xFFA3BBFF),
      blue80: const Color(0xFF5C85FF),
      blue100: const Color(0xFF3366FF),
      blue150: const Color(0XFF1A56DB),
      unitMoneyGreen: const Color(0xFF44E283),
      blue5: const Color(0xFFEBF0FF),
      red5: const Color(0xFFFFF2F4),
      red60: const Color(0xFFFF6585),
      red150: const Color(0XFFE72447),
      red200: const Color(0XFFED0000),
      green0: const Color(0xFFF0FFF5),
      green05: const Color(0XFFF2FCF5),
      green10: const Color(0XFFE6F9EC),
      green20: const Color(0XFFDEF7EC),
      green60: const Color(0xFF40DE85),
      green70: const Color(0xFF40DE85),
      green80: const Color(0XFF009931),
      green90: const Color(0XFF046C4E),
      red10: const Color(0xFFFFE0E7),
      ink80: const Color(0xFF59637E),
      ink0: const Color(0xFFF3F6FF),
      unFocusEToken: const Color(0xFFE2E9FC),
      focusEToken: const Color(0xFF505A71),
      orange0: const Color(0xFFFFEFEB),
      umeeMint5: const Color(0xFFE8FCFC),
      umeeMint40: const Color(0xFFADEBEB),
      umeeMint80: const Color(0xFF60D6D6),
      umeeMint100: const Color(0xFF33CCCC),
      purple80: const Color(0xFF9482FF),
      ink20: const Color(0xFFD6DFF6),
      ink60: const Color(0xFF8E9ABB),
      ink40: const Color(0xFFB8C3E1),
      purple: const Color(0xFF7255E2),
      purple0: const Color(0xFFF4F3FF),
      ink10: const Color(0xFFE2E9FC),
      pink100: const Color(0xFFF746A3),
      violet100: const Color(0xFF210A9B),
      ink5: const Color(0xFFE2E9FC),
      umeeBlue60: const Color(0xFF7597FF),
      submitDialog: const Color(0xFF007AFF),
      warning: const Color(0xFFFAAD14),
      orange1: const Color(0xFFFFF8F3),
      yellow90: const Color(0XFFEB9100),
      yellow60: const Color(0xFFFCC559),
      yellow10: const Color(0XFFFFF5E5),
      lightBlue05: const Color(0XFFF7F7F9),
      lightBlue20: const Color(0XFFEAEAF0),
      lightBlue30: const Color(0XFFD4D4E0),
      lightBlue40: const Color(0xFFA9A8C1),
      lightBlue: const Color(0xFF292663),
      neutralGrey10: const Color(0XFFF0F0F1),
      neutralGrey20: const Color(0XFFE2E2E3),
      neutralGrey: const Color(0XFF6D6E71),
      chili: const Color(0XFFC81E1E),
      chili10: const Color(0XFFFDF2F2),
      bgTextFieldGrey: const Color(0XFF333333).withOpacity(0.05),
      unSelectedColorTabBar: const Color(0XFF333333).withOpacity(0.8),
      textGreen: const Color(0XFF00BC3C),
      nobleColor: const Color(0xFFD6A35C),
    );
  } else {
    //dark
    return CustomColor(
      buttonBottomColor: const Color(0xFF0095DE),
      backgroundAppBarColor: const Color(0xFF171717),
      backgroundAlertColor: const Color(0xFF0095DE),
      backgroundSegmentColor: const Color(0xFF000000),
      activeSegmentColor: const Color(0xFF171717),
      bgMessageChat: const Color(0xFF767676),
      bgMessageChatOther: const Color(0xFF2C2C2C),
      textMessageChat: Colors.white,
      textMessageChatOther: Colors.white,
      backgroundWarning: const Color(0xFF3D3D3D),
      ksBackgroundAction: const Color(0xFF484848),
      backgroundUnreadCountColor: const Color(0xFFF04F24),
      errorColor: Colors.redAccent,
      backgroundColor: const Color(0xFFF4F4F4),
      disabledColor: Colors.grey[400],
      highlightColor: const Color(0xFF83AB2F),
      shimmerChild: const Color(0xFFE8E8E8),
      shimmerHighlight: const Color(0xFFF4F4F4),
      toggleableActiveColor: const Color(0xFF925FFF),
      yellow2: const Color(0xFFFF4A12),
      electricGreen: const Color(0xFFFF4A12),
      electricGreenV2: const Color(0xFF00BC3C),
      black: const Color(0xFF333333),
      red: const Color(0xFFFF2D2E),
      darkBlue: const Color(0xFF353282),
      backgroundButton: const Color(0x66E8E8E8),
      backgroundGray: Colors.black,
      headerColor: const Color(0xFFAFAFAF),
      emptyColor: Colors.white,
      newRed10: const Color(0XFFFEEEEA),
      newRed20: const Color(0XFFFDEDE9),
      newRed90: const Color(0XFFF37250),
      newRed: const Color(0xFFF04F24),
      newBlue: const Color(0xFF228BCC),
      newBlue10: const Color(0XFFE9F4FA),
      buttonTransparentColor: const Color(0xFFF4F4F7),
      dividerColor: const Color(0xFFECEFF8),
      border: const Color(0xFFE6EBF8),
      titleColor2: const Color(0xFF212633),
      captionColor: const Color(0xFF525B73),
      unitMoneyColor: const Color(0xFF8E9ABB),
      blue: const Color(0xFF0283FD),
      hintTextColor: const Color(0xFFCAD2E6),
      green: const Color(0xff29CE6B),
      blue0: const Color(0xFFF2F6FF),
      backgroundSplash: const Color(0xFFFFBB33),
      yellow100: const Color(0xFFFEBD00),
      yellow5: const Color(0xFFFFF4D5),
      labelTextField: const Color(0xFFA9B3CC),
      black20: const Color(0xFF2B2B33),
      red100: const Color(0xFFFD3549),
      black100: const Color(0xFF2B2B33),
      snackMessageWarning: const Color(0xFFFDCE35),
      green100: const Color(0xFF00CB51),
      shadowCard: const Color(0xFF7090B0),
      orange100: const Color(0xFFFF7C59),
      blue10: const Color(0xFFBFE0FE),
      orange10: const Color(0xFFFFDBD0),
      orange80: const Color(0xFFFF8F6D),
      blue05: const Color(0XFFEBF5FF),
      blue20: const Color(0xFFD1DCFF),
      blue60: const Color(0xFF44A9FE),
      blue40: const Color(0xFFA3BBFF),
      blue80: const Color(0xFF5C85FF),
      blue100: const Color(0xFF3366FF),
      blue150: const Color(0XFF1A56DB),
      unitMoneyGreen: const Color(0xFF44E283),
      blue5: const Color(0xFFEBF0FF),
      red5: const Color(0xFFFFF2F4),
      red60: const Color(0xFFFF6585),
      red150: const Color(0XFFE72447),
      red200: const Color(0XFFED0000),
      green0: const Color(0xFFF0FFF5),
      green05: const Color(0XFFF2FCF5),
      green10: const Color(0XFFE6F9EC),
      green20: const Color(0XFFDEF7EC),
      green60: const Color(0xFF40DE85),
      green70: const Color(0xFF40DE85),
      green80: const Color(0XFF009931),
      green90: const Color(0XFF046C4E),
      red10: const Color(0xFFFFE0E7),
      ink80: const Color(0xFF59637E),
      ink0: const Color(0xFFF3F6FF),
      unFocusEToken: const Color(0xFFE2E9FC),
      focusEToken: const Color(0xFF505A71),
      orange0: const Color(0xFFFFEFEB),
      umeeMint5: const Color(0xFFE8FCFC),
      umeeMint40: const Color(0xFFADEBEB),
      umeeMint80: const Color(0xFF60D6D6),
      umeeMint100: const Color(0xFF33CCCC),
      purple80: const Color(0xFF9482FF),
      ink20: const Color(0xFFD6DFF6),
      ink60: const Color(0xFF8E9ABB),
      ink40: const Color(0xFFB8C3E1),
      purple: const Color(0xFF7255E2),
      purple0: const Color(0xFFF4F3FF),
      ink10: const Color(0xFFE2E9FC),
      pink100: const Color(0xFFF746A3),
      violet100: const Color(0xFF210A9B),
      ink5: const Color(0xFFE2E9FC),
      umeeBlue60: const Color(0xFF7597FF),
      submitDialog: const Color(0xFF007AFF),
      warning: const Color(0xFFFAAD14),
      orange1: const Color(0xFFFFF8F3),
      yellow90: const Color(0XFFEB9100),
      yellow60: const Color(0xFFFCC559),
      yellow10: const Color(0XFFFFF5E5),
      lightBlue05: const Color(0XFFF7F7F9),
      lightBlue20: const Color(0XFFEAEAF0),
      lightBlue30: const Color(0XFFD4D4E0),
      lightBlue40: const Color(0xFFA9A8C1),
      lightBlue: const Color(0xFF292663),
      neutralGrey10: const Color(0XFFF0F0F1),
      neutralGrey20: const Color(0XFFE2E2E3),
      neutralGrey: const Color(0XFF6D6E71),
      chili: const Color(0XFFC81E1E),
      chili10: const Color(0XFFFDF2F2),
      bgTextFieldGrey: const Color(0XFF333333).withOpacity(0.05),
      unSelectedColorTabBar: const Color(0XFF333333).withOpacity(0.8),
      textGreen: const Color(0XFF00BC3C),
      nobleColor: const Color(0xFFD6A35C),
    );
  }
}
