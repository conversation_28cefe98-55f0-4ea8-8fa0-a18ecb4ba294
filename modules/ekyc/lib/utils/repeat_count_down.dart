import 'dart:async';

import 'package:common/utils/log.dart';
import 'package:rxdart/rxdart.dart';

mixin RepeatCountdown {
  final BehaviorSubject<int> _countDown = BehaviorSubject<int>();
  final PublishSubject<bool> _isEnded = PublishSubject<bool>();

  Stream<int> get countDown => _countDown.stream;

  Stream<int> get countDownStream => _countDown.stream;
  StreamSubscription? _subEnd;

  Timer? _timer;
  int _countNumber = 60;
  DateTime? _startAt;
  static bool isLoginPage = false;

  void startCountDown([int delaySeconds = 0]) {
    if (delaySeconds > 0) {
      _countNumber = delaySeconds;
    }
    if (_countDown.isClosed) return;
    _countDown.add(_countNumber);

    if (_isEnded.isClosed) return;
    _isEnded.add(false);

    _subEnd?.cancel();
    _subEnd = _isEnded.distinct().listen((value) async {
      logger.v(value);
      if (value) {
        await onCountdownEnd();
      }
    });

    _startTimer();
  }

  void _startTimer() {
    _timer?.cancel();
    _startAt = DateTime.now();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      var newValue = (_countDown.valueOrNull ?? 1) - 1;
      if (newValue <= 0) {
        newValue = 0;
        _isEnded.add(true);
      }
      _countDown.add(newValue);
    });
  }

  void pauseCountdown() {
    logger.v('pauseCountdown');
    _timer?.cancel();
  }

  void onAppResume() {
    final now = DateTime.now();
    if (_startAt != null) {
      final diff = now.difference(_startAt!).inSeconds;
      final oldRemain = _countDown.valueOrNull ?? 1;
      final newRemain = oldRemain - diff;
      if (newRemain > 0) {
        startCountDown(newRemain);
      } else {
        startCountDown(1);
      }
    }
  }

  Future onCountdownEnd();

  void disposeCountdown() {
    logger.v('disposeCountdown');
    _countDown.close();
    _timer?.cancel();
    _isEnded.close();
    _subEnd?.cancel();
  }
}
