import 'package:common/assets.dart';
import 'package:common/ks_common.dart';
import 'package:common/widgets/bottom_sheet_widget.dart';
import 'package:ekyc/generated/assets.dart';
import 'package:ekyc/main.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class ManualPageV2 extends StatelessWidget {
  final bool isBottomSheet;
  final Widget? chatIcon;
  final Widget? backIcon;

  const ManualPageV2(
      {Key? key, this.isBottomSheet = false, this.chatIcon, this.backIcon})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (isBottomSheet) {
      return BottomSheetWidget(
        title: '<PERSON><PERSON><PERSON> thực khuôn mặt',
        height: MediaQuery.of(context).size.height,
        child: _body(context),
      );
    }
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
        leading: backIcon,
      ),
      body: Column(
        children: [
          Expanded(child: _body(context)),
          SafeArea(
            top: false,
            child: BottomButton(
              title: 'Tiến hành xác thực khuôn mặt',
              onTap: () {
                Navigator.pop(context);
              },
            ),
          ),
        ],
      ),
      floatingActionButton: Container(
        margin: const EdgeInsets.only(bottom: 84),
        child: FloatingActionButton(
          onPressed: () {
            GlobalCallback.instance.onChatOpen?.call(
              ChatModel(openLiveChat: true),
            );
          },
          child: chatIcon ??
              ImageAssets.svgAssets(
                Assets.assetsIcMailbox,
                packageName: ekyPackage,
                width: 28,
                height: 28,
              ),
        ),
      ),
    );
  }

  Widget _body(BuildContext context) {
    final text16Bold = Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
        );
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (!isBottomSheet) ...[
              Text(
                'Xác thực khuôn mặt',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 16),
              Container(
                width: MediaQuery.of(context).size.width,
                alignment: Alignment.center,
                child: ImageAssets.svgAssets(
                  Assets.icEkyc,
                  packageName: ekyPackage,
                ),
              ),
              const SizedBox(height: 24),
            ],
            _stepManualView(
              context,
              img: Assets.icManual1,
              guideText: 'Đặt khuôn mặt vào chính giữa khu vực nhận diện.',
            ),
            const SizedBox(height: 24),
            _stepManualView(
              context,
              img: Assets.icManual2,
              guideText: 'Đảm bảo không bị lóa sáng hoặc tối.',
            ),
            const SizedBox(height: 24),
            _stepManualView(
              context,
              img: Assets.icManual3,
              guideText: 'Không đội mũ, đeo kính râm hoặc khẩu trang.',
            ),
            SizedBox(height: isBottomSheet ? 24 : 112),
          ],
        ),
      ),
    );
  }

  Widget _stepManualView(
    BuildContext context, {
    required String img,
    required dynamic guideText,
  }) {
    return Row(
      children: [
        ImageAssets.svgAssets(
          img,
          packageName: ekyPackage,
        ),
        const SizedBox(width: 16),
        if (guideText is Widget)
          Expanded(child: guideText)
        else
          Expanded(
            child: Text(
              guideText,
              style: Theme.of(context)
                  .textTheme
                  .bodyMedium
                  ?.copyWith(fontWeight: FontWeight.w500),
            ),
          )
      ],
    );
  }
}
