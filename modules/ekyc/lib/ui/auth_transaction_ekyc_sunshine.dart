import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'dart:ui';

import 'package:camera/camera.dart';
import 'package:common/assets.dart';
import 'package:common/ks_common.dart';
import 'package:common/utils/dialog_util.dart';
import 'package:common/utils/permission_util.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:ekyc/di/injection.dart';
import 'package:ekyc/generated/assets.dart';
import 'package:ekyc/main.dart';
import 'package:ekyc/ui/detector_face_direction.dart';
import 'package:ekyc/ui/detector_painters.dart';
import 'package:ekyc/ui/manual_page_v2.dart';
import 'package:ekyc/utils/constant.dart';
import 'package:ekyc/utils/repeat_count_down.dart';
import 'package:ekyc/utils/scanner_utils.dart';
import 'package:ekyc/widgets/simple_button.dart';
import 'package:ekyc_bloc/bloc/live_ness_bloc_v2.dart';
import 'package:ekyc_bloc/model/profile_liveness_model.dart';
import 'package:ekyc_bloc/model/frame_model.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:path/path.dart' as $path;
import 'package:percent_indicator/percent_indicator.dart';
import 'package:connectivity/connectivity.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

import 'ekyc_sunshine.dart';
import 'inverted_circle_clipper.dart';
import 'profile_ekyc_sunshine.dart';

class AuthTransactionEKycSunshine extends StatefulWidget {
  final String? personId;
  final String? referralCode;
  final LiveNessBlocIntV2? bloc;
  final String? titleBtnDone;
  final Color? progressColor;
  final Color? progressBackgroundColor;
  final Widget? chatIcon;
  final Widget? backIcon;
  final bool? showManual;
  final String? exercise;
  final bool? static;
  final Widget? manualPage;
  final Function()? onAuthSuccess;
  final Function(String? message)? onAuthFailedThreeTimes;

  const AuthTransactionEKycSunshine({
    Key? key,
    this.personId,
    this.referralCode,
    this.bloc,
    this.titleBtnDone,
    this.progressBackgroundColor,
    this.progressColor,
    this.chatIcon,
    this.showManual,
    this.backIcon,
    this.exercise,
    this.static,
    this.manualPage,
    this.onAuthSuccess,
    this.onAuthFailedThreeTimes,
  }) : super(key: key);

  @override
  State<AuthTransactionEKycSunshine> createState() =>
      AuthTransactionEKycSunshineState();
}

class AuthTransactionEKycSunshineState
    extends State<AuthTransactionEKycSunshine>
    with WidgetsBindingObserver, RepeatCountdown {
  //#region Variables
  late LiveNessBlocIntV2 _bloc;
  CameraController? _camera;
  bool mirror = false;
  StreamSubscription? _sub;

  //list task cần verify
  final List<TaskDirection?> _tasks = [];

  //list guide keys - thực hiện animateTo giữa có guide
  final List<GlobalKey<State>> _keys = [];
  String _guide = ''; //text hướng dẫn quay mặt khi thực hiện liveness
  int currentTaskIndex = 0; //current index của task liveness
  FaceDirection? currentTask;
  bool? checkDirection = true; //cờ enable/disable detect face direction

  final CameraLensDirection _cameraDirection = CameraLensDirection.front;
  final FaceDetector _faceDetector = FaceDetector(
    options: FaceDetectorOptions(
      performanceMode: FaceDetectorMode.fast,
      enableLandmarks: true,
      enableContours: true,
      enableClassification: true,
    ),
  );

  dynamic _scanResults;
  bool _isDetectingFace = false; //Đang nhận diện face từ ảnh
  bool isShowingManual = false; //cờ show hướng dẫn

  bool sendingFrame = false; // đang gửi ảnh lên
  //cờ cho nút submit là retry hay close
  VerifyState status = VerifyState.unStart;
  final int totalStep = 130; //vòng xoay tiến độ

  int currentStep = 0;
  int countFrame = 0; //số frame của từng task cần verify
  int countFrameSent = 0; //tổng số frames cần verify

  //List FrameModel - frame ảnh và task tương ứng khi thực hiện liveness
  final List<FrameModel> _frames = <FrameModel>[];
  final sizeImageGuide = 70.0; // kích thước ảnh guide của các task liveness
  final marginGuide = 8.0; // khoảng cách giữa các guide
  final padding = 15.0;
  PageController? pageController;
  int countNoFace = 0;

  String _framesFolder = ''; //thư mục lưu frames khi liveness

  String putFaceCenter = 'Vui lòng để khuôn mặt vào chính giữa';
  String putFaceLeft = 'Vui lòng quay sang trái';
  String putFaceRight = 'Vui lòng quay sang phải';
  String putFaceUp = 'Vui lòng ngửa mặt hướng lên trên';
  String putFaceDown = 'Vui lòng nhìn xuống dưới';
  String smiling = 'Vui lòng cười';
  String eyesOpen = 'Vui lòng nháy mắt';
  String checkFaceSuccess = 'Thành công!';

  String rotateFace =
      'Xoay từ từ khuôn mặt để đưa mũi của quý khách vào ô vuông màu cam';

  String verifyFailed = "Xác thực thất bại!. Xin vui lòng thử lại";

  String rotateTooFast =
      "Xác thực thất bại\nBạn quay mặt quá nhanh, xin vui lòng thử lại";

  String keepFace = "Vui lòng giữ nguyên tư thế để hệ thống xác nhận";

  String done = "Xin chúc mừng, nhận diện thành công!";

  String verifying = "Đang thực hiện xác thực, xin vui lòng đợi!";

  String actionTimeOut =
      "Xác thực thất bại!\nQuá thời gian thao tác, xin vui lòng thực hiện lại!";

  String multiFaces =
      "Xác thực thất bại!\nCó quá nhiều khuôn mặt!. Xin vui lòng thử lại";

  String noFace =
      "Xác thực thất bại!\nKhông phát hiện khuôn mặt!. Xin vui lòng thử lại";

  String messageOutSide =
      "Xác thực thất bại!\nVui lòng để khuôn mặt vào chính giữa vùng nhận diện";

  String sendDataError = "Tải dữ liệu lỗi, xin vui lòng thử lại";

  String errorNetwork = "Kết nối mạng không ổn định, vui lòng kiểm tra lại!";

  String? errorMessage = '';

  final resolution =
      Platform.isIOS ? ResolutionPreset.medium : ResolutionPreset.high;

  String get resultText {
    if (errorMessage?.isNotEmpty == true) return errorMessage!;
    if (status == VerifyState.unStart) {
      return '';
    }
    if ([
      VerifyState.faceInCentered,
      VerifyState.started,
      VerifyState.inProgress
    ].contains(status)) {
      return _getFaceDirectionText();
    }
    if (status == VerifyState.error) {
      return verifyFailed;
    }
    if (status == VerifyState.done) {
      return done;
    }
    if (status == VerifyState.verifying || status == VerifyState.tasksDone) {
      return verifying;
    }
    if (status == VerifyState.checkDirectionSuccess) {
      return checkFaceSuccess;
    }
    return '';
  }

  Color getStatusColor() {
    if (status == VerifyState.error) return Colors.red;
    if (status == VerifyState.done ||
        status == VerifyState.checkDirectionSuccess) {
      return Colors.green;
    }

    return Colors.white;
  }

  Color getProgressColor(bool isMain) {
    if (status == VerifyState.done) return Colors.green;
    if (isMain) {
      if (status == VerifyState.error) return Colors.red;
      return widget.progressColor ?? const Color(0xFF33CCCC);
    } else {
      return widget.progressBackgroundColor ?? const Color(0xFFE8FCFC);
    }
  }

  final errorCodeResponse = {
    ProfileLiveNessVerifyResponse.NO_FACE_DETECTED.toLowerCase():
        "Xác thực thất bại!\nKhông tìm thấy khuôn mặt trong hình",
    ProfileLiveNessVerifyResponse.FOUND_MORE_THAN_ONE_FACE.toLowerCase():
        "Xác thực thất bại!\nQuá nhiều khuôn mặt trong khung hình",
    ProfileLiveNessVerifyResponse.TASK_ORDER_NOT_FOUND.toLowerCase():
        "Xác thực thất bại!\nKhông tìm thấy task order",
    ProfileLiveNessVerifyResponse.REQUEST_CHALLENGE_ID_NOT_FOUND.toLowerCase():
        "Xác thực thất bại!\nChallenge ID không tồn tại",
    ProfileLiveNessVerifyResponse.MOVING_PHOTO_IS_MISSING.toLowerCase():
        "Xác thực thất bại!\nChưa có ảnh để xác thực",
    ProfileLiveNessVerifyResponse.RIGHT_FACE_ANGEL_NOT_CORRECT.toLowerCase():
        "Xác thực thất bại!\nKhuôn mặt góc phải không chính xác",
    ProfileLiveNessVerifyResponse.LEFT_FACE_ANGEL_NOT_CORRECT.toLowerCase():
        "Xác thực thất bại!\nKhuôn mặt góc trái không chính xác",
    ProfileLiveNessVerifyResponse.UP_FACE_ANGEL_NOT_CORRECT.toLowerCase():
        "Xác thực thất bại!\nKhuôn mặt ngước trên không chính xác",
    ProfileLiveNessVerifyResponse.DOWN_FACE_ANGEL_NOT_CORRECT.toLowerCase():
        "Xác thực thất bại!\nKhuôn mặt cúi dưới không chính xác",
    ProfileLiveNessVerifyResponse.AHEAD_FACE_ANGEL_NOT_CORRECT.toLowerCase():
        "Xác thực thất bại!\nKhuôn mặt góc thẳng không chính xác",
    ProfileLiveNessVerifyResponse.TRAJECTORY_TOO_SLOW.toLowerCase():
        "Xác thực thất bại!\nHết thời gian thao tác",
    ProfileLiveNessVerifyResponse.unknownException.toLowerCase():
        "Có lỗi không xác định xảy ra, vui lòng thử lại",
  };

  final String faceIDFailed = '4007006';

  //#endregion

  @override
  void initState() {
    super.initState();
    _initializeCamera();
    WidgetsBinding.instance.addObserver(this);
    _bloc = widget.bloc ?? Injection.injector.get<LiveNessBlocV2>();
    _sub = _bloc.liveNessErrorStream.listen(_handleErrorMessage);
    WakelockPlus.enable();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _requestPermission();
      if (widget.showManual == true) {
        _openManual();
      } else {
        pageController = PageController(
          viewportFraction: (sizeImageGuide + marginGuide) /
              (MediaQuery.of(context).size.width - 2 * padding),
          keepPage: true,
        );
      }
    });
  }

  _requestPermission() async {
    PermissionUtil.checkPermissionHandler(
      permissionCamera,
      onAllowed: () => _initializeCamera(),
      onNeverAllowed: () {
        DialogUtil.confirm(
          context,
          const Text.rich(
            TextSpan(
              text: 'Bạn cần cấp quyền ',
              children: [
                TextSpan(
                  text: 'truy cập camera',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                TextSpan(text: " cho ứng dụng để sử dụng tính năng này")
              ],
            ),
          ),
          submitText: 'Mở cài đặt',
          onSubmit: () {
            openAppSettings().whenComplete(() => Navigator.pop(context));
          },
          onCancel: () => Navigator.pop(context),
        );
      },
    );
  }

  void _jumpToIndex() {
    if (!_checkVisibility(_keys[currentTaskIndex]) &&
        pageController?.hasClients == true) {
      pageController?.animateToPage(
        currentTaskIndex,
        curve: Curves.linear,
        duration: const Duration(milliseconds: 500),
      );
    }
  }

  bool _checkVisibility(GlobalKey<State> key) {
    final RenderBox? renderBox =
        key.currentContext?.findRenderObject() as RenderBox?;
    final size = renderBox?.size;
    final topLeftOffset = renderBox?.localToGlobal(Offset.zero);
    final bottomRightOffset = renderBox?.localToGlobal(
        size != null ? Offset(size.width, size.height) : Offset.zero);
    if (!mounted) return false;
    final screenSize = MediaQuery.of(context).size;
    return topLeftOffset != null &&
        bottomRightOffset != null &&
        (topLeftOffset.dy >= 0) &&
        (topLeftOffset.dx >= 0) &&
        (bottomRightOffset.dx <= screenSize.width) &&
        (bottomRightOffset.dy <= screenSize.height) &&
        size != null &&
        size.width > 0 &&
        size.height > 0;
  }

  _handleErrorMessage(String? event) async {
    if ((await getNetworkStatus()) == ConnectivityResult.none) {
      onError(errorNetwork);
      return;
    }
    if (!event.isNullOrEmpty) {
      String? key;
      errorCodeResponse.forEach((k, v) {
        if (event!.toLowerCase().contains(k) == true) {
          key = k;
        }
      });
      if (!key.isNullOrEmpty) {
        final errorCode = event!.toLowerCase().split(key!.toLowerCase()).last;
        onError(errorCodeResponse[key]! + errorCode);
      } else {
        if (event!.contains(faceIDFailed)) {
          onError(verifyFailed);
          widget.onAuthFailedThreeTimes?.call(event);
        } else {
          onError(event);
        }
      }
    }
  }

  Future<ConnectivityResult> getNetworkStatus() async {
    var connectivityResult = await (Connectivity().checkConnectivity());
    return connectivityResult;
  }

  _openManual([bool? isHint]) async {
    isShowingManual = true;
    if (isHint == true) {
      await goPresent(context,
          widget.manualPage ?? const ManualPageV2(isBottomSheet: true));
    } else {
      await go(
        context,
        widget.manualPage ??
            ManualPageV2(
              chatIcon: widget.chatIcon,
              backIcon: widget.backIcon,
            ),
      );
    }
    isShowingManual = false;
  }

  Future<dynamic> Function(InputImage image) _getDetectionMethod() {
    return _faceDetector.processImage;
  }

  void _initializeCamera() async {
    try {
      final CameraDescription description =
          await ScannerUtils.getCamera(_cameraDirection);

      _camera = CameraController(
        description,
        resolution,
        enableAudio: false,
        imageFormatGroup: Platform.isAndroid
            ? ImageFormatGroup.nv21
            : ImageFormatGroup.bgra8888,
      );

      await _camera?.initialize();
      // await _startStreamImage();
      await _startVideo();
      mirror = FaceDetectorDirection.isMirror(
          _camera!.description.sensorOrientation);
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(
        'INITIAL_LIVENESS_ERROR + $e',
        StackTrace.current,
      );
    }
  }

  initData() async {
    _tasks.clear();
    _keys.clear();
    _frames.clear();
    clearFrameImage();
    _framesFolder = '';
    errorMessage = '';
    _scanResults = null;
    _isDetectingFace = false;
    sendingFrame = false;
    countFrame = 0;
    countFrameSent = 0;
    currentStep = 0;
    checkDirection = true;
    currentTaskIndex = 0;
    countNoFace = 0;
    status = VerifyState.unStart;
    await _startVideo();
  }

  clearFrameImage() async {
    if (!_framesFolder.isNullOrEmpty) {
      await ScannerUtils.deleteDirectory(path: _framesFolder);
    }
  }

  Future<String?> getPath(String path) async {
    try {
      final tempDir = await getTemporaryDirectory();
      final filePath = $path.join(
        tempDir.path,
        path,
      );
      return filePath;
    } catch (e) {
      logger.e(e);
      return null;
    }
  }

  _getTasks() {
    if (_bloc.liveNessValue?.taskOrder?.tasks?.isNotEmpty == true) {
      for (var element in _bloc.liveNessValue!.taskOrder!.tasks!) {
        final direction = _mapTaskToDirection(element.task);
        _tasks.add(
          TaskDirection(
            checked: false,
            faceDirection: direction,
            assetGuide: _guide,
          ),
        );
        _keys.add(GlobalKey());
      }
    }
  }

  FaceDirection? _mapTaskToDirection(String? task) {
    final sensorOrientation = _camera?.description.sensorOrientation;
    switch (task) {
      case turnFaceRight:
        if (sensorOrientation == 270) {
          _guide = Assets.imgTurnFaceLeft;
          return FaceDirection.left;
        }
        _guide = Assets.imgTurnFaceRight;
        return FaceDirection.right;
      case turnFaceLeft:
        if (sensorOrientation == 270) {
          _guide = Assets.imgTurnFaceRight;
          return FaceDirection.right;
        }
        _guide = Assets.imgTurnFaceLeft;
        return FaceDirection.left;
      case keepFaceAhead:
        _guide = Assets.imgKeepFaceAhead;
        return FaceDirection.straight;
      case tileFaceUp:
        _guide = Assets.imgTiltFaceUp;
        return FaceDirection.up;
      case tileFaceDown:
        _guide = Assets.imgTiltFaceDown;
        return FaceDirection.down;
      case keepSmile:
        _guide = Assets.imgSmiling;
        return FaceDirection.smiling;
      case makeEyesWink:
        _guide = Assets.imgOpenEyes;
        return FaceDirection.eyeOpen;
      default:
        return FaceDirection.notFound;
    }
  }

  String _getFaceDirectionText() {
    if (_tasks.isNullOrEmpty) {
      return '';
    }
    final task = _tasks[currentTaskIndex]?.faceDirection;
    switch (task) {
      case FaceDirection.right:
        return putFaceRight;
      case FaceDirection.left:
        return putFaceLeft;
      case FaceDirection.straight:
        return putFaceCenter;
      case FaceDirection.up:
        return putFaceUp;
      case FaceDirection.down:
        return putFaceDown;
      case FaceDirection.smiling:
        return smiling;
      case FaceDirection.eyeOpen:
        return eyesOpen;
      default:
        return '';
    }
  }

  _startVideo() {
    if (_camera?.value.isInitialized != true ||
        _camera?.value.isRecordingVideo == true) {
      return;
    }
    try {
      _camera?.startImageStream(
        (image) async {
          if (isShowingManual) return;
          if (status == VerifyState.unStart) {
            status = VerifyState.started;
            final isVertical = image.width < image.height;
            await _bloc.start(
              width: isVertical ? image.width : image.height,
              height: isVertical ? image.height : image.width,
              personId: widget.personId,
              referralCode: widget.referralCode,
              exercise: widget.exercise ?? 'pitch-yaw',
              static: widget.static ?? true,
            );
            if (_bloc.liveNessValue == null) {
              onError();
              return;
            }
            _framesFolder = _bloc.liveNessValue?.challengeId ?? '';
            ScannerUtils.createFolder(_framesFolder);
            _getTasks();
            _updateTask();
            startCountDown(_tasks.length * 10);
          }

          if (checkDirection == true) {
            _detectionFace(image);
            if (status == VerifyState.faceInCentered) {
              status = VerifyState.inProgress;
            }

            if (_isInProgress()) {
              _getFrameImage(image);
            }
          } else if (checkDirection == false) {
            checkDirection = null;
            if (status != VerifyState.error &&
                currentTaskIndex < _tasks.length - 1) {
              _tasks[currentTaskIndex]?.checked = true;
              checkDirection = true;
              status = VerifyState.started;
              countFrame = 0;
              if (mounted) {
                setState(() {});
              }
              _updateTask();
            }
          }
          if (status == VerifyState.tasksDone) {
            _startVerify();
          }
        },
      );
    } catch (e) {
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
    }
  }

  _updateTask() {
    final index = _tasks.indexWhere((element) => element?.checked == false);
    if (index != -1) {
      currentTaskIndex = index;
      currentTask = _tasks[index]?.faceDirection;
      if (index != 0) {
        _jumpToIndex();
      }
    }
  }

  _isInProgress() {
    return status == VerifyState.inProgress;
  }

  _getFrameImage(CameraImage image) async {
    if (!sendingFrame && ((countFrame < _getFrameRate()))) {
      sendingFrame = true;
      final timeStamp = (DateTime.now().millisecondsSinceEpoch) ~/ 1000;
      final path = '$_framesFolder/${timeStamp.toString()}.jpg';
      try {
        final isIos = defaultTargetPlatform == TargetPlatform.iOS;
        final filePath = await getPath(path);
        final params = CameraImageParams(
          image: image,
          orient: _camera!.description.sensorOrientation,
          isIos: isIos,
          quality: resolution == ResolutionPreset.high ? 50 : 80,
          file: !filePath.isNullOrEmpty ? File(filePath!) : null,
        );
        final isSuccess = await compute(ScannerUtils.saveToFile2, params);
        if (isSuccess) {
          _frames.add(FrameModel(
            personId: widget.personId,
            timeStamp: timeStamp,
            task: _bloc.liveNessValue?.taskOrder?.tasks?[currentTaskIndex].task,
            filePath: path,
          ));

          ++countFrame;
          ++countFrameSent;
          currentStep = toInt(lerpDouble(
            0,
            totalStep.toDouble(),
            countFrameSent.toDouble() / totalFrame,
          ));
          currentStep = min(currentStep, totalStep);
          if (countFrame == _getFrameRate()) {
            if (currentTaskIndex < _tasks.length - 1) {
              checkDirection = false;
            } else {
              if (status != VerifyState.error) {
                _tasks[currentTaskIndex]?.checked = true;
                checkDirection = null;
                status = VerifyState.tasksDone;
              }
            }
          }
        }
      } catch (e) {
        logger.e(e);
        sendingFrame = false;
        FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      } finally {
        sendingFrame = false;
      }
    }
  }

  _detectionFace(CameraImage image) async {
    if (status == VerifyState.done ||
        status == VerifyState.error ||
        _bloc.liveNessValue == null) {
      return;
    }
    if (_isDetectingFace || _camera == null) return;
    _isDetectingFace = true;
    try {
      final results = await ScannerUtils.detect(
        image: image,
        detectInImage: _getDetectionMethod(),
        sensorOrientation: _camera!.description.sensorOrientation,
        deviceOrientation: _camera!.value.deviceOrientation,
        lensDirection: _cameraDirection,
      );
      if (mounted) {
        setState(() {
          _scanResults = results;
          _checkCurrentFace();
        });
      }
    } catch (e) {
      logger.e(e);
      _isDetectingFace = false;
    } finally {
      _isDetectingFace = false;
    }
  }

  _checkCurrentFace() async {
    if (_scanResults is List<Face> && _scanResults.length > 0) {
      logger.i(status);
      countNoFace = 0;

      if (_isInProgress() && (_scanResults as List<Face>).length > 1) {
        onError(multiFaces);
        return;
      }

      Face face = _scanResults[0];
      final minAreaPercent = _bloc.liveNessValue?.minFaceAreaPercent != null
          ? _bloc.liveNessValue!.minFaceAreaPercent! / 100
          : 0.5;
      // isStartedStatus || isCenterStatus ? 0.07 : 0.5,
      final faceRect = face.boundingBox;
      final isStartedStatus = status == VerifyState.started;
      final isCenterStatus = status == VerifyState.faceInCentered;
      final isFaceInRect = isFaceBoxInsideFaceArea(
        faceRect,
        1 - minAreaPercent,
      );
      //quay trái, quay phải, hay nhìn thẳng?
      final faceDirection = FaceDetectorDirection.getDirection(
        _scanResults,
        _camera!.description.sensorOrientation,
      );
      final isFaceStraight = faceDirection == currentTask;
      //remove delay
      if (isStartedStatus && isFaceInRect && isFaceStraight) {
        status = VerifyState.faceInCentered;
      }
      if (isCenterStatus && (!isFaceInRect || !isFaceStraight)) {
        status = VerifyState.started;
      }
      if (_isInProgress() && !isFaceInRect) {
        onError(messageOutSide);
      }
    } else {
      ++countNoFace;
      Future.delayed(
        const Duration(seconds: 2),
        () {
          if (countNoFace > 0 &&
              status != VerifyState.error &&
              status != VerifyState.verifying) {
            onError(noFace);
            countNoFace = 0;
          }
        },
      );
    }
  }

  _getFrameRate() {
    return _bloc.liveNessValue?.taskOrder?.tasks?[currentTaskIndex].times ?? 1;
  }

  int get totalFrame {
    int countFrame = 0;
    if (_bloc.liveNessValue?.taskOrder?.tasks?.isNotEmpty == true) {
      for (var task in _bloc.liveNessValue!.taskOrder!.tasks!) {
        countFrame = countFrame + (task.times ?? 1);
      }
    } else {
      countFrameSent = 1;
    }
    return countFrame;
  }

  _startVerify() async {
    if (status == VerifyState.verifying ||
        sendingFrame ||
        countFrameSent < totalFrame) return;
    if (mounted) {
      setState(() {
        status = VerifyState.verifying;
        pauseCountdown();
      });
    }

    bool isSuccess = false;
    for (final frame in _frames) {
      try {
        isSuccess = await _sendingFrameImage(frame);
        if (!isSuccess) {
          if ((await getNetworkStatus()) == ConnectivityResult.none) {
            onError(errorNetwork);
          }
          break;
        }
      } catch (e) {
        logger.e(e);
        isSuccess = false;
        FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
        onError(sendDataError);
        break;
      }
    }

    if (isSuccess) {
      try {
        logger.v('start verify');
        final result = await _bloc.verify(personId: widget.personId);
        if (result?.success == true && status == VerifyState.verifying) {
          setState(() {
            status = VerifyState.done;
            currentStep = totalStep;
            pauseCountdown();
            widget.onAuthSuccess?.call() ?? Navigator.pop(context, true);
          });
        } /*else {
          onError(_handleErrorMessage(result?.message));
        }*/
      } catch (e) {
        logger.e(e);
        onError();
      }
    }
  }

  Future<bool> _sendingFrameImage(FrameModel frame) async {
    String? content;
    final imageBytes = await ScannerUtils.readFile(frame.filePath ?? '');
    if (_bloc.isToBase64Frame()) {
      if (imageBytes != null) {
        content = base64Encode(imageBytes);
      }
    }
    return _bloc.frames(
      content: content,
      imageBytes: imageBytes,
      personId: frame.personId,
      timeStamp: frame.timeStamp,
      task: frame.task,
    );
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _camera?.dispose().then((_) {
      _faceDetector.close();
    });
    _camera = null;
    if (widget.bloc == null) {
      _bloc.dispose();
    }
    _sub?.cancel();
    disposeCountdown();
    WakelockPlus.disable();
    clearFrameImage();
    pageController?.dispose();
    super.dispose();
  }

  Widget detectionView({required Widget child}) {
    return Center(
      child: AspectRatio(
        aspectRatio: 1.0 / _camera!.value.aspectRatio,
        child: child,
      ),
    );
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (_camera == null || _camera?.value.isInitialized != true) {
      return;
    }
    if (state == AppLifecycleState.paused) {
      _camera?.dispose();
    } else if (state == AppLifecycleState.resumed) {
      _initializeCamera();
    }
    super.didChangeAppLifecycleState(state);
  }

  @override
  Widget build(BuildContext context) {
    final hintStyle = Theme.of(context).textTheme.titleMedium?.copyWith(
          color: getStatusColor(),
          fontSize: toSp(18),
        );
    return WillPopScope(
      onWillPop: () async {
        return status != VerifyState.verifying;
      },
      child: Scaffold(
        extendBodyBehindAppBar: true,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          iconTheme: Theme.of(context).iconTheme.copyWith(color: Colors.white),
          systemOverlayStyle: SystemUiOverlayStyle.light,
          title: Text(
            'Xác thực khuôn mặt',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
          ),
          centerTitle: true,
          actions: [
            IconButton(
              onPressed: () {
                _openManual(true);
              },
              icon: ImageAssets.svgAssets(
                Assets.assetsIcQuestionMark,
                packageName: ekyPackage,
              ),
            ),
          ],
        ),
        body: _camera == null || _camera?.value.isInitialized == false
            ? Container(color: const Color(0xFF2E2D36))
            : Stack(
                fit: StackFit.expand,
                children: [
                  Center(child: CameraPreview(_camera!)),
                  Transform(
                    alignment: Alignment.center,
                    transform: Matrix4.rotationY(mirror ? pi : 0),
                    child: _faceScope(),
                  ),
                  if (resultText.isNotEmpty)
                    Positioned(
                      top: kToolbarHeight + MediaQuery.of(context).padding.top,
                      left: padding,
                      right: padding,
                      child: Container(
                        alignment: Alignment.center,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (currentTaskIndex >= 0 &&
                                currentTaskIndex < _tasks.length &&
                                ![VerifyState.done, VerifyState.error]
                                    .contains(status)) ...[
                              AnimatedSwitcher(
                                duration: const Duration(milliseconds: 500),
                                transitionBuilder: (Widget child,
                                    Animation<double> animation) {
                                  return FadeTransition(
                                    opacity: animation,
                                    child: child,
                                  );
                                },
                                child: SizedBox(
                                  key: ValueKey<int>(currentTaskIndex),
                                  child: ImageAssets.svgAssets(
                                    _tasks[currentTaskIndex]?.assetGuide ?? '',
                                    packageName: ekyPackage,
                                    height: toHeight(sizeImageGuide),
                                  ),
                                ),
                              ),
                              const SizedBox(height: 6),
                            ],
                            Text(
                              resultText,
                              style: hintStyle,
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  Positioned(
                    bottom: 24,
                    left: 0,
                    right: 0,
                    child: Column(
                      children: [
                        // if (status != VerifyState.error) _buildGuide(),
                        // if (status == VerifyState.error ||
                        //     status == VerifyState.done)
                        //   const SizedBox(height: 16),
                        if (status == VerifyState.error)
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: padding),
                            child: SimpleButton(
                              title: "Thử lại",
                              color: Theme.of(context).primaryColor,
                              onPressed: () {
                                setState(() {
                                  initData();
                                });
                              },
                            ),
                          )
                        else if (status == VerifyState.done)
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: padding),
                            child: SimpleButton(
                              title: widget.titleBtnDone ?? "Tiếp tục",
                              color: Theme.of(context).primaryColor,
                              onPressed: () {
                                Navigator.pop(context, _bloc.liveNessValue);
                              },
                            ),
                          )
                      ],
                    ),
                  ),
                  if (status == VerifyState.verifying)
                    Positioned.fill(
                      child: Container(
                        color: Colors.black.withOpacity(0.8),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            ring,
                            Text(
                              verifying,
                              style: const TextStyle(color: Colors.white),
                            ),
                          ],
                        ),
                      ),
                    )
                ],
              ),
      ),
    );
  }

  // Widget _buildGuide() {
  //   return Container(
  //     padding: EdgeInsets.symmetric(horizontal: padding),
  //     height: sizeImageGuide,
  //     child: _checkScrollable()
  //         ? PageView.builder(
  //             itemCount: _tasks.length,
  //             scrollDirection: Axis.horizontal,
  //             padEnds: false,
  //             controller: pageController,
  //             physics: const ClampingScrollPhysics(),
  //             itemBuilder: (context, index) => ImageGuideWidget(
  //               key: _keys[index],
  //               asset: _tasks[index]?.assetGuide ?? '',
  //               hasFilter: currentTaskIndex != index &&
  //                   _tasks[index]?.checked == false,
  //               checkSuccess: _tasks[index]?.checked,
  //               // hasPadding: index != _tasks.length - 1,
  //               size: sizeImageGuide,
  //               paddingSizeItem: marginGuide,
  //             ),
  //           )
  //         : Row(
  //             mainAxisAlignment: MainAxisAlignment.center,
  //             children: List.generate(
  //               _tasks.length,
  //               (index) => ImageGuideWidget(
  //                 key: _keys[index],
  //                 asset: _tasks[index]?.assetGuide ?? '',
  //                 hasFilter: currentTaskIndex != index &&
  //                     _tasks[index]?.checked == false,
  //                 checkSuccess: _tasks[index]?.checked,
  //                 hasPadding: index != _tasks.length - 1,
  //                 size: sizeImageGuide,
  //                 paddingSizeItem: marginGuide,
  //               ),
  //             ).toList(),
  //           ),
  //   );
  // }

  Widget _faceScope() {
    if (_camera == null || _camera?.value.isInitialized == false) {
      return Container();
    }
    final widthScreen = MediaQuery.of(context).size.width;
    final heightScreen = MediaQuery.of(context).size.height;
    final Size imageSize = Size(
      _camera!.value.previewSize?.height ?? 0,
      _camera!.value.previewSize?.width ?? 0,
    );
    const paddingOuterHorizontal = 32;
    const paddingInnerHorizontal = 40;
    final widthProgress = widthScreen - paddingOuterHorizontal;
    final widthCircle = widthProgress - paddingInnerHorizontal;
    final double scaleY = heightScreen / imageSize.height;

    return StreamBuilder<ViewDataModel<ProfileLivenessModel>>(
      stream: _bloc.liveNessStream,
      builder: (context, snapshot) {
        if ((!snapshot.hasData || snapshot.data?.isLoading() == true) &&
            status != VerifyState.error) {
          return Stack(
            children: [
              Positioned.fill(
                child: Container(
                  color: const Color(0xFF2E2D36),
                  child: ring,
                ),
              ),
            ],
          );
        }

        final radius = widthCircle / 2;
        final data = snapshot.data?.data;
        final top = data?.areaTop ?? (heightScreen / 2 - radius);
        final heightArea = data?.areaHeight ?? 0.0;
        double paddingTop = ((top + heightArea / 2) * scaleY) - radius;
        if (paddingTop < 0) paddingTop = radius;

        return Stack(
          children: [
            if (status == VerifyState.started ||
                status == VerifyState.faceInCentered)
              Positioned.fill(
                child: CustomPaint(
                  painter: EkycPainter(
                    left: toInt(data?.areaLeft),
                    top: toInt(data?.areaTop),
                    width: toInt(data?.areaWidth),
                    height: toInt(data?.areaHeight),
                    absoluteImageSize: imageSize,
                    found: status == VerifyState.faceInCentered,
                  ),
                ),
              ),
            Positioned.fill(
              child: ClipPath(
                clipper: InvertedCircleClipper(
                  radius: widthCircle / 2,
                  dy: paddingTop + (paddingInnerHorizontal / 2) + radius,
                ),
                child: Container(color: const Color(0xFF2E2D36)),
              ),
            ),
            // Container(
            //   alignment: Alignment.topCenter,
            //   margin: EdgeInsets.only(top: paddingTop),
            //   child: CircularPercentIndicator(
            //     radius: widthProgress / 2,
            //     lineWidth: 8,
            //     backgroundColor: getProgressColor(false),
            //     percent: currentStep / totalStep,
            //     progressColor: getProgressColor(true),
            //     animation: true,
            //     animateFromLastPercent: true,
            //     reverse: true,
            //     animationDuration: 1500,
            //   ),
            // ),
          ],
        );
      },
    );
  }

  Widget get ring => const SizedBox(
        width: 30.0,
        height: 30.0,
        child: Center(
          child: CupertinoActivityIndicator(
            color: Colors.white,
          ),
        ),
      );

  // bool _checkScrollable() {
  //   final length = _tasks.length;
  //   final widthItem = (MediaQuery.of(context).size.width -
  //           2 * padding -
  //           length * toHeight(marginGuide)) /
  //       length;
  //   return widthItem < toHeight(sizeImageGuide);
  // }

  double toPercent(num? value) {
    if (value == null) return 0.0;
    if (value >= 0 || value <= 1) return toDouble(value);
    if (value > 1) return toDouble(value) / 100.0;
    return 0.0;
  }

  bool isFaceBoxInsideFaceArea(Rect faceBox, [double? tolerance = 0.3]) {
    final liveNessResponse = _bloc.liveNessValue;
    if (liveNessResponse == null) {
      return false;
    }
    tolerance = tolerance ?? toPercent(liveNessResponse.minFaceAreaPercent);
    final leftPosition = faceBox.left >=
        toInt(liveNessResponse.areaLeft) -
            toInt(liveNessResponse.areaWidth) * tolerance;
    final topPosition = faceBox.top >=
        toInt(liveNessResponse.areaTop) -
            toInt(liveNessResponse.areaHeight) * tolerance;

    final widthFace = faceBox.left + faceBox.width <=
        toInt(liveNessResponse.areaLeft) +
            toInt(liveNessResponse.areaWidth) * (1 + tolerance);
    final heightFace = faceBox.top + faceBox.height <=
        toInt(liveNessResponse.areaTop) +
            toInt(liveNessResponse.areaHeight) * (1 + tolerance);

    return leftPosition && topPosition && widthFace && heightFace;
  }

  onError([String? message]) {
    pauseCountdown();
    if (_camera?.value.isRecordingVideo == true) {
      stopVideoRecording().then((value) {
        if (value != null) {
          ScannerUtils.deleteFile(value.path);
        }
      });
    }
    if (mounted) {
      setState(() {
        if (message?.isNotEmpty == true) errorMessage = message;
        status = VerifyState.error;
      });
    }
  }

  Future<XFile?> stopVideoRecording() async {
    final CameraController? cameraController = _camera;
    if (cameraController == null || !cameraController.value.isRecordingVideo) {
      return null;
    }
    try {
      return await cameraController.stopVideoRecording();
    } on CameraException catch (e) {
      logger.e(e);
      return null;
    }
  }

  @override
  Future onCountdownEnd() {
    logger.e('onCountdownEnd');
    // if (_isInProgress()) {
    onError(actionTimeOut);
    // }
    return Future.value();
  }
}
