import 'package:common/assets.dart';
import 'package:common/ks_common.dart';
import 'package:common/widgets/bottom_sheet_widget.dart';
import 'package:ekyc/generated/assets.dart';
import 'package:ekyc/main.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class ManualPage extends StatelessWidget {
  final bool isBottomSheet;
  final Widget? chatIcon;
  final Widget? backIcon;
  const ManualPage(
      {Key? key, this.isBottomSheet = false, this.chatIcon, this.backIcon})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (isBottomSheet) {
      return BottomSheetWidget(
        title: 'Hướng dẫn xác thực khuôn mặt',
        height: MediaQuery.of(context).size.height,
        child: _body(context),
      );
    }
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
        leading: backIcon,
      ),
      body: Column(
        children: [
          Expanded(child: _body(context)),
          SafeArea(
            top: false,
            child: BottomButton(
              title: 'Tiến hành xác thực khuôn mặt',
              onTap: () {
                Navigator.pop(context);
              },
            ),
          ),
        ],
      ),
      floatingActionButton: Container(
        margin: const EdgeInsets.only(bottom: 84),
        child: FloatingActionButton(
          onPressed: () {
            GlobalCallback.instance.onChatOpen?.call(
              ChatModel(openLiveChat: true),
            );
          },
          child: chatIcon ??
              ImageAssets.svgAssets(
                Assets.assetsIcMailbox,
                packageName: ekyPackage,
                width: 28,
                height: 28,
              ),
        ),
      ),
    );
  }

  Widget _body(BuildContext context) {
    final text16Bold = Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
        );
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            if (!isBottomSheet) ...[
              Text(
                'Hướng dẫn xác thực khuôn mặt eKYC',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 16),
              Text(
                'Bạn vui lòng đọc kĩ hướng dẫn dưới đây để có thể xác thực khuôn mặt đúng nhất',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 24),
            ],
            _stepManualView(
              context,
              step: 1,
              img: Assets.assetsIcGuideStep12,
              guideText:
                  'Đặt khuôn mặt vào chính giữa khu vực nhận diện. Đảm bảo khuôn mặt nằm trong khung chỉ định, đợi đến khi khung chuyển xanh.',
            ),
            const Divider(
              height: 32,
              color: Color(0xFFEAF0FD),
            ),
            _stepManualView(
              context,
              step: 2,
              img: Assets.assetsIcGuideStep12,
              guideText:
                  'Giữ nguyên khuôn mặt cho tới khi xuất hiện 1 ô mục tiêu và thanh tiến trình chạy được khoảng 15%, sau đó di chuyển mũi vào trong ô mục tiêu.',
            ),
            const Divider(
              height: 32,
              color: Color(0xFFEAF0FD),
            ),
            _stepManualView(
              context,
              step: 3,
              img: Assets.assetsIcGuideStep3,
              guideText: Text.rich(
                TextSpan(
                  style: Theme.of(context).textTheme.titleMedium,
                  children: [
                    const TextSpan(text: 'Giữ nguyên điện thoại, xoay mặt'),
                    TextSpan(
                      text: ' thật chậm',
                      style: text16Bold,
                    ),
                    const TextSpan(text: ' và'),
                    TextSpan(
                      text: ' đều đặn',
                      style: text16Bold,
                    ),
                    const TextSpan(
                        text: ' để di chuyển mũi vào ô mục tiêu theo một'),
                    TextSpan(
                      text: ' đường cong.',
                      style: text16Bold,
                    ),
                    TextSpan(
                      text: '\n\nChú ý:',
                      style: text16Bold,
                    ),
                    const TextSpan(
                        text:
                            ' di chuyển chậm, khi mũi chạm ô mục tiêu thì thanh tiến trình đạt >60%.'),
                  ],
                ),
              ),
            ),
            const Divider(
              height: 32,
              color: Color(0xFFEAF0FD),
            ),
            _stepManualView(
              context,
              step: 4,
              img: Assets.assetsIcGuideStep4,
              guideText:
                  'Khi mũi chạm vào chính giữa ô mục tiêu, giữ nguyên đợi hệ thống kết thúc xác thực.',
            ),
            SizedBox(height: isBottomSheet ? 24 : 112),
          ],
        ),
      ),
    );
  }

  Widget _stepManualView(
    BuildContext context, {
    required int step,
    required String img,
    required dynamic guideText,
  }) {
    return Column(
      children: [
        Align(
          alignment: Alignment.centerLeft,
          child: Text(
            'Bước $step',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
        ),
        const SizedBox(height: 24),
        ImageAssets.svgAssets(
          img,
          packageName: ekyPackage,
        ),
        const SizedBox(height: 24),
        if (guideText is Widget)
          guideText
        else
          Text(
            guideText,
            style: Theme.of(context).textTheme.titleMedium,
          )
      ],
    );
  }
}
