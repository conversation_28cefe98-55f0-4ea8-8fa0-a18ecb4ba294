import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'dart:ui';

import 'package:camera/camera.dart';
import 'package:common/assets.dart';
import 'package:common/ks_common.dart';
import 'package:ekyc/di/injection.dart';
import 'package:ekyc/generated/assets.dart';
import 'package:ekyc/main.dart';
import 'package:ekyc/ui/detector_face_direction.dart';
import 'package:ekyc/ui/detector_painters.dart';
import 'package:ekyc/ui/manual_page.dart';
import 'package:ekyc/utils/repeat_count_down.dart';
import 'package:ekyc/utils/scanner_utils.dart';
import 'package:ekyc/widgets/simple_button.dart';
import 'package:ekyc_bloc/bloc/live_ness_bloc.dart';
import 'package:ekyc_bloc/model/liveness_model.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:path/path.dart' as $path;
import 'package:percent_indicator/percent_indicator.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

import 'inverted_circle_clipper.dart';

enum VerifyState {
  unStart,
  started,
  faceInCentered,
  inProgress,
  checkDirectionSuccess,
  tasksDone,
  noseInTarget,
  verifying,
  done,
  error
}

class EKycSunshine extends StatefulWidget {
  final String? personId;
  final String? referralCode;
  final LiveNessBlocInt? bloc;
  final String? titleBtnDone;
  final Color? progressColor;
  final Color? progressBackgroundColor;
  final Widget? chatIcon;
  final Widget? backIcon;
  final bool? showManual;
  final double noseTargetDisplayPercent;
  final Widget? manualPage;

  const EKycSunshine({
    Key? key,
    this.personId,
    this.referralCode,
    this.bloc,
    this.titleBtnDone,
    this.progressBackgroundColor,
    this.progressColor,
    this.chatIcon,
    this.showManual,
    this.backIcon,
    this.noseTargetDisplayPercent = 0.5,
    this.manualPage,
  })  : assert(
          noseTargetDisplayPercent >= 0.0 && noseTargetDisplayPercent <= 1,
        ),
        super(key: key);

  @override
  _EKycSunshineState createState() => _EKycSunshineState();
}

class _EKycSunshineState extends State<EKycSunshine>
    with WidgetsBindingObserver, RepeatCountdown {
  //#region Variables
  late LiveNessBlocInt _bloc;
  CameraController? _camera;
  bool mirror = false;
  StreamSubscription? _sub;

  final CameraLensDirection _cameraDirection = CameraLensDirection.front;
  final FaceDetector _faceDetector = FaceDetector(
    options: FaceDetectorOptions(
      performanceMode: FaceDetectorMode.fast,
      enableLandmarks: true,
      enableContours: true,
    ),
  );

  dynamic _scanResults;
  bool _isDetectingFace = false; //Đang nhận diện face từ ảnh
  bool takingFrame = false; //cờ lưu file ảnh
  bool isShowingManual = false; //cờ show hướng dẫn

  bool sendingFrame = false; // đang gửi ảnh lên
  //cờ cho nút submit là retry hay close
  VerifyState status = VerifyState.unStart;
  final int totalStep = 130; //vòng xoay tiến độ

  int currentStep = 0;
  int countFrame = 0;
  bool? needCheckNose;

  String? _imagePath;
  String putFaceCenter =
      'Để khuôn mặt vào chính giữa khung hình vài giây để hệ thống ghi nhận';

  String rotateFace =
      'Xoay từ từ khuôn mặt để đưa mũi của quý khách vào ô vuông màu cam';

  String verifyFailed = "Xác thực thất bại!. Xin vui lòng thử lại";

  String rotateTooFast =
      "Xác thực thất bại\nBạn quay mặt quá nhanh, xin vui lòng thử lại";

  String keepFace = "Vui lòng giữ nguyên tư thế để hệ thống xác nhận";

  String done = "Xin chúc mừng, nhận diện thành công!";

  String verifying = "Đang thực hiện xác thực, xin vui lòng đợi!";

  String actionTimeOut =
      "Xác thực thất bại!\nQuá thời gian thao tác, xin vui lòng thực hiện lại!";

  String multiFaces =
      "Xác thực thất bại!\nCó quá nhiều khuôn mặt!. Xin vui lòng thử lại";

  String? errorMessage = '';

  final resolution =
      Platform.isIOS ? ResolutionPreset.medium : ResolutionPreset.high;

  bool get needShowNoseTarget =>
      (currentStep >= (totalStep * widget.noseTargetDisplayPercent));

  String get resultText {
    if (errorMessage?.isNotEmpty == true) return errorMessage!;
    if (status == VerifyState.unStart ||
        status == VerifyState.started ||
        status == VerifyState.faceInCentered) {
      return putFaceCenter;
    }
    if (status == VerifyState.inProgress) {
      if (needShowNoseTarget) {
        return rotateFace;
      }
      return putFaceCenter;
    }
    if (status == VerifyState.error) {
      return verifyFailed;
    }
    if (status == VerifyState.done) {
      return done;
    }
    if (status == VerifyState.noseInTarget) {
      return keepFace;
    }
    if (status == VerifyState.verifying) {
      return verifying;
    }
    return '';
  }

  Color getStatusColor() {
    if (status == VerifyState.error) return Colors.red;
    if (status == VerifyState.done) return Colors.green;

    return Colors.white;
  }

  Color getProgressColor(bool isMain) {
    if (status == VerifyState.done) return Colors.green;
    if (isMain) {
      if (status == VerifyState.error) return Colors.red;
      return widget.progressColor ?? const Color(0xFF33CCCC);
    } else {
      return widget.progressBackgroundColor ?? const Color(0xFFE8FCFC);
    }
  }

  final errorCodeResponse = {
    LivenessVerifyResponse.PHOTO_MISSING:
        "Xác thực thất bại!\nKhông đủ ảnh khuôn mặt cho quá trình xác thực, xin vui lòng thao tác một cách từ từ",
    LivenessVerifyResponse.CHALLENGE_TIMEOUT:
        "Xác thực thất bại!\nHết thời gian thao tác",
    LivenessVerifyResponse.FACE_NOT_SEEN:
        "Xác thực thất bại!\nKhông nhận diện được khuôn mặt có thể do ảnh quá mờ",
    LivenessVerifyResponse.FACE_NOT_IN_POSITION:
        "Xác thực thất bại!\nKhuôn mặt của bạn bị xoay ra ngoài",
    LivenessVerifyResponse.FACE_TOO_FAR:
        "Xác thực thất bại!\nKhuôn mặt để quá xa so với camera, hãy để máy ảnh lại gần hơn",
    LivenessVerifyResponse.NOSE_NOT_AT_POSITION:
        "Xác thực thất bại!\nMũi của bạn không ở trong khu vực được chỉ định",
    LivenessVerifyResponse.NOSE_MOTION_NOT_SMOOTH:
        "Xác thực thất bại!\nMũi của bạn bị gián đoạn khi di chuyển",
    LivenessVerifyResponse.FACE_ROTATION_NOT_NATURAL:
        "Xác thực thất bại!\nKhuôn mặt xoay không tự nhiên",
    LivenessVerifyResponse.FACE_NOT_MOVING:
        "Xác thực thất bại!\nKhuôn mặt không xoay trong quá trình di chuyển",
    LivenessVerifyResponse.FACE_NOT_GENUINE:
        "Xác thực thất bại!\nKhuôn mặt không hợp lệ",
  };

  //#endregion

  @override
  void initState() {
    super.initState();
    _initializeCamera();
    WidgetsBinding.instance.addObserver(this);
    _bloc = widget.bloc ?? Injection.injector.get<LiveNessBloc>();
    _sub = _bloc.liveNessErrorStream.listen((event) {
      if (!event.isNullOrEmpty) {
        if (errorCodeResponse.containsKey(event!.toLowerCase())) {
          onError(errorCodeResponse[event.toLowerCase()]);
        } else {
          onError(event);
        }
      }
    });
    WakelockPlus.enable();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      if (widget.showManual == true) {
        _openManual();
      }
    });
  }

  _openManual([bool? isHint]) async {
    isShowingManual = true;
    if (isHint == true) {
      await goPresent(
          context, widget.manualPage ?? const ManualPage(isBottomSheet: true));
    } else {
      await go(
        context,
        widget.manualPage ??
            ManualPage(
              chatIcon: widget.chatIcon,
              backIcon: widget.backIcon,
            ),
      );
    }
    isShowingManual = false;
  }

  Future<dynamic> Function(InputImage image) _getDetectionMethod() {
    return _faceDetector.processImage;
  }

  void _initializeCamera() async {
    final CameraDescription description =
        await ScannerUtils.getCamera(_cameraDirection);

    _camera = CameraController(
      description,
      resolution,
      enableAudio: false,
      imageFormatGroup: Platform.isAndroid
          ? ImageFormatGroup.nv21
          : ImageFormatGroup.bgra8888,
    );

    await _camera?.initialize();
    await _startStreamImage();
    mirror =
        FaceDetectorDirection.isMirror(_camera!.description.sensorOrientation);
    setState(() {});
  }

  initData() {
    errorMessage = '';
    _scanResults = null;
    _isDetectingFace = false;
    takingFrame = false;
    sendingFrame = false;
    status = VerifyState.unStart;
    countFrame = 0;
    currentStep = 0;
    needCheckNose = null;
  }

  _startStreamImage() {
    return _camera?.startImageStream((image) async {
      if (isShowingManual) return;

      if (status == VerifyState.unStart) {
        status = VerifyState.started;
        final isVertical = image.width < image.height;
        await _bloc.start(
          width: isVertical ? image.width : image.height,
          height: isVertical ? image.height : image.width,
          personId: widget.personId,
          referralCode: widget.referralCode,
        );
        if (_bloc.liveNessValue == null) {
          onError();
          return;
        }
        startCountDown(120);
      }
      _detectionFace(image);
      if (status == VerifyState.faceInCentered) {
        Future.delayed(const Duration(seconds: 2), () async {
          if (status == VerifyState.faceInCentered && !takingFrame) {
            takingFrame = true;
            await _saveFile(image);
            status = VerifyState.inProgress;
            takingFrame = false;
          }
        });
      }

      if (_isInProgress()) {
        _sendFrameImage(image);
      }
    });
  }

  _isInProgress() {
    return status == VerifyState.inProgress ||
        status == VerifyState.noseInTarget;
  }

  Future _saveFile(CameraImage image) async {
    try {
      final tempDir = await getTemporaryDirectory();
      final filePath = $path.join(
        tempDir.path,
        '${DateTime.now().millisecondsSinceEpoch}.jpg',
      );
      logger.v(filePath);
      _imagePath = filePath;
      final isIos = defaultTargetPlatform == TargetPlatform.iOS;
      await ScannerUtils.saveToFile(
        image,
        File(filePath),
        _camera!.description.sensorOrientation,
        isIos: isIos,
        quality: resolution == ResolutionPreset.high ? 80 : 100,
      );
    } catch (e) {
      logger.e(e);
    }
  }

  _sendFrameImage(CameraImage image) async {
    if (!sendingFrame &&
        ((countFrame < _getFrameRate()) || (needCheckNose != true))) {
      sendingFrame = true;
      try {
        final isIos = defaultTargetPlatform == TargetPlatform.iOS;
        String? imageBase64;
        List<int>? imageBytes;
        final params = CameraImageParams(
          image: image,
          orient: _camera!.description.sensorOrientation,
          isIos: isIos,
          quality: resolution == ResolutionPreset.high ? 50 : 80,
        );
        if (_bloc.isToBase64Frame()) {
          imageBase64 =
              await compute(ScannerUtils.convertImageToBase64, params);
        } else {
          imageBytes = await compute(ScannerUtils.convertImageToBytes, params);
        }

        final isSuccess = await _bloc.frames(
          content: imageBase64 ?? '',
          imageBytes: imageBytes,
          personId: widget.personId,
        );

        if (isSuccess == true) {
          ++countFrame;
          final lastStep = currentStep;
          currentStep = toInt(lerpDouble(
            0,
            totalStep.toDouble(),
            countFrame.toDouble() / _getFrameRate(),
          ));

          if (status != VerifyState.verifying &&
              currentStep > toInt(totalStep * 0.9)) {
            currentStep = lastStep;
          }
          setState(() {});
        }
      } catch (e) {
        logger.e(e);
        sendingFrame = false;
      } finally {
        sendingFrame = false;
      }
    }
  }

  _detectionFace(CameraImage image) async {
    if (status == VerifyState.done || status == VerifyState.error) {
      return;
    }
    if (_isDetectingFace) return;
    _isDetectingFace = true;
    try {
      final results = await ScannerUtils.detect(
        image: image,
        detectInImage: _getDetectionMethod(),
        sensorOrientation: _camera!.description.sensorOrientation,
        deviceOrientation: _camera!.value.deviceOrientation,
        lensDirection: _cameraDirection,
      );
      if (mounted) {
        setState(() {
          _scanResults = results;
          if (_isInProgress()) {
            _checkCurrentNose(image);
          }
          _checkCurrentFace();
        });
      }
    } catch (e) {
      logger.e(e);
      _isDetectingFace = false;
    } finally {
      _isDetectingFace = false;
    }
  }

  _checkCurrentFace() {
    if (_scanResults is List<Face> && _scanResults.length > 0) {
      logger.i(status);

      if (_isInProgress() && (_scanResults as List<Face>).length > 1) {
        onError(multiFaces);
        return;
      }

      Face face = _scanResults[0];
      final faceRect = face.boundingBox;
      final isStartedStatus = status == VerifyState.started;
      final isCenterStatus = status == VerifyState.faceInCentered;
      final isFaceInRect = isFaceBoxInsideFaceArea(
        faceRect,
        isStartedStatus || isCenterStatus ? 0.07 : 0.5,
      );
      //quay trái, quay phải, hay nhìn thẳng?
      final faceDirection = FaceDetectorDirection.getDirection(
        _scanResults,
        _camera!.description.sensorOrientation,
      );
      final isFaceStraight = faceDirection == FaceDirection.straight;
      if (isStartedStatus && isFaceInRect && isFaceStraight) {
        status = VerifyState.faceInCentered;
      }
      if (isCenterStatus && (!isFaceInRect || !isFaceStraight)) {
        status = VerifyState.started;
      }
      if (_isInProgress() && !isFaceInRect) {
        onError();
      }
    } else if (_isInProgress()) {
      onError();
    }
  }

  _checkCurrentNose(CameraImage image) {
    if (_scanResults is List<Face> && _scanResults.length > 0) {
      final face = _scanResults[0] as Face;
      final noseBridge = face.contours[FaceContourType.noseBridge]?.points;
      final point =
          noseBridge != null && noseBridge.length > 1 ? noseBridge[1] : null;
      if (isNoseInsideNoseArea(point)) {
        if (countFrame < (_getFrameRate() * 0.5)) {
          onError(rotateTooFast);
        } else {
          status = VerifyState.noseInTarget;
          if (needCheckNose == null) {
            needCheckNose = false;
            Future.delayed(const Duration(milliseconds: 2000)).then((value) {
              needCheckNose = true;
            });
          }
          _startVerify(image);
        }
      } else if ((needCheckNose == true) &&
          (status == VerifyState.noseInTarget)) {
        onError();
      }
    }
  }

  _getFrameRate() {
    final rate = _bloc.liveNessValue?.frameRate ?? 8;
    return rate < 8 ? 8 : rate;
  }

  _startVerify(CameraImage image) async {
    if (status == VerifyState.verifying ||
        countFrame < _getFrameRate() ||
        needCheckNose != true ||
        sendingFrame) return;
    status = VerifyState.verifying;
    try {
      logger.v('start verify');
      final result = await _bloc.verify(personId: widget.personId);
      if (result?.success == true) {
        setState(() {
          status = VerifyState.done;
          currentStep = totalStep;
        });
      } else {
        // TODO show message
        onError(errorCodeResponse[result?.message]);
      }
    } catch (e) {
      logger.e(e);
      onError();
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _camera?.dispose().then((_) {
      _faceDetector.close();
    });
    if (widget.bloc == null) {
      _bloc.dispose();
    }
    _sub?.cancel();
    disposeCountdown();
    WakelockPlus.disable();
    super.dispose();
  }

  Widget detectionView({required Widget child}) {
    return Center(
      child: AspectRatio(
        aspectRatio: 1.0 / _camera!.value.aspectRatio,
        child: child,
      ),
    );
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.paused) {
      _camera?.dispose();
    } else if (state == AppLifecycleState.resumed) {
      if (_camera != null) _initializeCamera();
    }
    super.didChangeAppLifecycleState(state);
  }

  @override
  Widget build(BuildContext context) {
    final widthScreen = MediaQuery.of(context).size.width;
    final heightScreen = MediaQuery.of(context).size.height;
    final hintStyle = Theme.of(context).textTheme.titleMedium?.copyWith(
          color: getStatusColor(),
          height: 1.5,
        );
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: Theme.of(context).iconTheme.copyWith(color: Colors.white),
        systemOverlayStyle: SystemUiOverlayStyle.light,
        title: Text(
          'Xác thực khuôn mặt',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: () {
              _openManual(true);
            },
            icon: ImageAssets.svgAssets(
              Assets.assetsIcQuestionMark,
              packageName: ekyPackage,
            ),
          ),
        ],
      ),
      body: _camera == null || _camera?.value.isInitialized == false
          ? Container()
          : Stack(
              fit: StackFit.expand,
              children: [
                Center(child: CameraPreview(_camera!)),
                if (needShowNoseTarget)
                  detectionView(
                    child: Transform(
                      alignment: Alignment.center,
                      transform: Matrix4.rotationY(mirror ? pi : 0),
                      child: _noseTarget(),
                    ),
                  ),
                Transform(
                  alignment: Alignment.center,
                  transform: Matrix4.rotationY(mirror ? pi : 0),
                  child: _faceScope(),
                ),
                detectionView(
                  child: Transform(
                    alignment: Alignment.center,
                    transform: Matrix4.rotationY(mirror ? pi : 0),
                    child: _currentNose(),
                  ),
                ),
                Positioned(
                  top: ((heightScreen + widthScreen) / 2) - 15,
                  left: 15,
                  right: 15,
                  child: Container(
                    alignment: Alignment.center,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: ((status != VerifyState.inProgress) ||
                            !needShowNoseTarget)
                        ? Text(
                            resultText,
                            style: hintStyle,
                            textAlign: TextAlign.center,
                          )
                        : Text.rich(
                            TextSpan(
                              text: 'Xoay từ từ khuôn mặt để đưa mũi',
                              children: [
                                TextSpan(
                                  text: ' ● ',
                                  style:
                                      hintStyle?.copyWith(color: Colors.blue),
                                ),
                                const TextSpan(
                                  text: 'của quý khách vào ô vuông màu cam',
                                ),
                                TextSpan(
                                  text: ' □',
                                  style: hintStyle?.copyWith(
                                    color: Colors.orange,
                                    fontSize: toSp(30),
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                              ],
                            ),
                            textAlign: TextAlign.center,
                            style: hintStyle,
                          ),
                  ),
                ),
                if (status == VerifyState.error)
                  Positioned(
                    bottom: 35,
                    left: 15,
                    right: 15,
                    child: Row(
                      children: [
                        Expanded(
                          child: SimpleButton(
                            icon: ImageAssets.svgAssets(
                              Assets.assetsIcQuestionMark2,
                              packageName: ekyPackage,
                            ),
                            title: "Hướng dẫn",
                            color: const Color(0xFFF2F6FF),
                            colorTitle: Theme.of(context).primaryColor,
                            onPressed: () {
                              _openManual();
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: SimpleButton(
                            title: "Thử lại",
                            color: Theme.of(context).primaryColor,
                            onPressed: () {
                              setState(() {
                                initData();
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                  )
                else if (status == VerifyState.done)
                  Positioned(
                    bottom: 35,
                    left: 15,
                    right: 15,
                    child: SimpleButton(
                      title: widget.titleBtnDone ?? "Tiếp tục",
                      color: Theme.of(context).primaryColor,
                      onPressed: () {
                        _bloc.liveNessValue?.imagePath = _imagePath;
                        Navigator.pop(context, _bloc.liveNessValue);
                      },
                    ),
                  )
              ],
            ),
    );
  }

  Widget _noseTarget() {
    if (_camera == null || _camera?.value.isInitialized == false) {
      return Container();
    }

    final Size imageSize = Size(
      _camera!.value.previewSize?.height ?? 0,
      _camera!.value.previewSize?.width ?? 0,
    );

    return StreamBuilder<ViewDataModel<LivenessModel>>(
      stream: _bloc.liveNessStream,
      builder: (context, snapshot) {
        var data = snapshot.data?.data;
        if (!snapshot.hasData || data == null) {
          return Container();
        }

        if (status == VerifyState.verifying || status == VerifyState.done) {
          return Container();
        }

        return CustomPaint(
          painter: EkycPainter(
            left: data.noseLeft!.toInt(),
            top: data.noseTop!.toInt(),
            width: data.noseWidth!.toInt(),
            height: data.noseHeight!.toInt(),
            absoluteImageSize: imageSize,
            found: status == VerifyState.noseInTarget,
          ),
        );
      },
    );
  }

  Widget _faceScope() {
    if (_camera == null || _camera?.value.isInitialized == false) {
      return Container();
    }
    final Size imageSize = Size(
      _camera!.value.previewSize?.height ?? 0,
      _camera!.value.previewSize?.width ?? 0,
    );
    const paddingOuterHorizontal = 32;
    const paddingInnerHorizontal = 40;
    final widthProgress =
        MediaQuery.of(context).size.width - paddingOuterHorizontal;
    final widthCircle = widthProgress - paddingInnerHorizontal;
    final double scaleY = MediaQuery.of(context).size.height / imageSize.height;

    return StreamBuilder<ViewDataModel<LivenessModel>>(
      stream: _bloc.liveNessStream,
      builder: (context, snapshot) {
        final radius = widthCircle / 2;
        final data = snapshot.data?.data;
        final top = data?.areaTop ?? 0.0;
        final heightArea = data?.areaHeight ?? 0.0;
        double paddingTop = ((top + heightArea / 2) * scaleY) - radius;
        if (paddingTop < 0) paddingTop = radius;

        return Stack(
          children: [
            if (status == VerifyState.started ||
                status == VerifyState.faceInCentered)
              Positioned.fill(
                child: CustomPaint(
                  painter: EkycPainter(
                    left: toInt(data?.areaLeft),
                    top: toInt(data?.areaTop),
                    width: toInt(data?.areaWidth),
                    height: toInt(data?.areaHeight),
                    absoluteImageSize: imageSize,
                    found: status == VerifyState.faceInCentered,
                  ),
                ),
              ),
            Positioned.fill(
              child: ClipPath(
                clipper: InvertedCircleClipper(
                  radius: widthCircle / 2,
                  dy: paddingTop + (paddingInnerHorizontal / 2) + radius,
                ),
                child: Container(color: const Color(0xFF2E2D36)),
              ),
            ),
            Container(
              alignment: Alignment.topCenter,
              margin: EdgeInsets.only(top: paddingTop),
              child: CircularPercentIndicator(
                radius: widthProgress / 2,
                lineWidth: 8,
                backgroundColor: getProgressColor(false),
                percent: currentStep / totalStep,
                progressColor: getProgressColor(true),
                animation: true,
                animateFromLastPercent: true,
                reverse: true,
                animationDuration: 1500,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _currentNose() {
    if (_scanResults == null ||
        _camera == null ||
        _camera?.value.isInitialized == false) {
      return Container();
    }

    if (!_isInProgress()) return Container();

    CustomPainter painter;

    final Size imageSize = Size(
      _camera!.value.previewSize?.height ?? 0,
      _camera!.value.previewSize?.width ?? 0,
    );
    if (_scanResults is! List<Face>) return Container();
    painter = FaceDetectorPainter(
      imageSize,
      _scanResults,
      _camera!.description.sensorOrientation,
    );
    return CustomPaint(painter: painter);
  }

  double toPercent(num? value) {
    if (value == null) return 0.0;
    if (value >= 0 || value <= 1) return toDouble(value);
    if (value > 1) return toDouble(value) / 100.0;
    return 0.0;
  }

  bool isFaceBoxInsideFaceArea(Rect faceBox, [double? tolerance = 0.3]) {
    final liveNessResponse = _bloc.liveNessValue;
    if (liveNessResponse == null) {
      return false;
    }
    tolerance = tolerance ?? toPercent(liveNessResponse.minFaceAreaPercent);
    final leftPosition = faceBox.left >=
        toInt(liveNessResponse.areaLeft) -
            toInt(liveNessResponse.areaWidth) * tolerance;
    final topPosition = faceBox.top >=
        toInt(liveNessResponse.areaTop) -
            toInt(liveNessResponse.areaHeight) * tolerance;

    final widthFace = faceBox.left + faceBox.width <=
        toInt(liveNessResponse.areaLeft) +
            toInt(liveNessResponse.areaWidth) * (1 + tolerance);
    final heightFace = faceBox.top + faceBox.height <=
        toInt(liveNessResponse.areaTop) +
            toInt(liveNessResponse.areaHeight) * (1 + tolerance);

    return leftPosition && topPosition && widthFace && heightFace;
  }

  bool isNoseInsideNoseArea(Point? nose) {
    final liveNessResponse = _bloc.liveNessValue;
    if (liveNessResponse == null) {
      return false;
    }
    if (nose == null) return false;
    final result = (nose.x >= toInt(liveNessResponse.noseLeft) &&
        nose.y >= toInt(liveNessResponse.noseTop) &&
        nose.x <=
            toInt(liveNessResponse.noseLeft) +
                toInt(liveNessResponse.noseWidth) &&
        nose.y <=
            toInt(liveNessResponse.noseTop) +
                toInt(liveNessResponse.noseHeight));

    return result;
  }

  onError([String? message]) {
    pauseCountdown();
    if (mounted) {
      setState(() {
        if (message?.isNotEmpty == true) errorMessage = message;
        status = VerifyState.error;
      });
    }
  }

  @override
  Future onCountdownEnd() {
    logger.e('onCountdownEnd');
    if (_isInProgress()) {
      onError(actionTimeOut);
    }
    return Future.value();
  }
}
