import 'dart:io';
import 'dart:typed_data';

import 'package:common/model/token.dart';
import 'package:common/utils/extension/string.dart';
import 'package:common/utils/log.dart';
import 'package:dio/dio.dart';
import 'package:ekyc/di/injection.dart';
import 'package:ekyc_bloc/repository/session/session.dart';

class AppInterceptors extends Interceptor {
  final bool isHasToken;
  final bool isStrapiToken;
  final String? liveNessToken;

  AppInterceptors({
    this.isHasToken = true,
    this.isStrapiToken = false,
    this.liveNessToken,
  });

  @override
  void onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    final path = options.path;
    final data = options.data;
    final query = options.queryParameters;
    final shouldLogData = data != null && (data is Map || data is String);

    logger.t(options.baseUrl + path, error: query);
    if (shouldLogData) {
      logger.t(data);
    }
    final prefs = Injection.preferences;
    String language = prefs.languageCode;
    String serverType = prefs.serverType;

    if (isHasToken) {
      final session = Injection.injector.get<Session>();
      try {
        Token? token = await session.sunshine.getToken();
        options.headers[HttpHeaders.authorizationHeader] =
            token?.accessTokenRequest;
      } catch (e) {
        logger.e(e);
      }
    } else if (isStrapiToken) {
      String? strApiToken = await prefs.getTokenStrApi();
      if (strApiToken?.isNullOrEmpty == false &&
          !options.path.contains('/auth/is4')) {
        options.headers[HttpHeaders.authorizationHeader] =
            'Bearer $strApiToken';
        logger.i('strApi token - $strApiToken');
      }
    } else {
      options.headers[HttpHeaders.authorizationHeader] =
          'Basic ${liveNessToken.isNullOrEmpty ? 'c3NnLWhuLWRldjpob2Fub2tob25nbWF1' : liveNessToken!}';
    }
    options.headers[HttpHeaders.acceptLanguageHeader] = language;
    options.headers['X-ENV'] = serverType;
    options.headers[HttpHeaders.contentTypeHeader] = 'application/json';
    options.headers[HttpHeaders.acceptHeader] = 'application/json';
    super.onRequest(options, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    logger.t("DioError - ${err.response?.data ?? ''}",
        error: err.requestOptions.path);
    super.onError(err, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (response.data != null && response.data is Uint8List) {
      logger.t(response.requestOptions.path);
      super.onResponse(response, handler);
      return;
    }
    logger.t(response.data, error: response.requestOptions.path);
    final successCode = [200, 201, 204, 2000000, 2000001, 2000004, 1];

    if (successCode.contains(response.statusCode ?? 200)) {
      final data = response.data;
      var isBankOk = data != null && data is Map;
      if (isBankOk) {
        final code = data['statusCode'] ?? data['code'];
        if (code != null) {
          isBankOk = successCode.contains(code);
        }
      }
      if (data == null || isBankOk || isStrapiToken == true) {
        super.onResponse(response, handler);
        return;
      } else {
        throw data;
      }
    }
    throw response;
  }
}
