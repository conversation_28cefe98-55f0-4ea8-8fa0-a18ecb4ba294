import 'dart:io';

import 'package:common/model/view_data_model.dart';
import 'package:ekyc_bloc/bloc/base_bloc.dart';
import 'package:ekyc_bloc/model/liveness_model.dart';
import 'package:ekyc_bloc/model/profile_liveness_model.dart';
import 'package:ekyc_bloc/repository/preferences.dart';
import 'package:ekyc_bloc/repository/repository.dart';
import 'package:ekyc_bloc/repository/session/session.dart';
import 'package:rxdart/rxdart.dart';

import 'live_ness_bloc.dart';

mixin LiveNessBlocIntV2 {
  int counterFailed = 0;

  start({
    int? width,
    int? height,
    String? personId,
    String? referralCode,
    String? exercise,
    bool? static,
  });

  Future<bool> frames({
    String? content,
    List<int>? imageBytes,
    String? personId,
    String? task,
    int? timeStamp,
  });

  Future<bool> sendVideoEKyc({
    required List<int> videoBytes,
    required String personId,
  });

  Future<LivenessVerifyResponse?> verify({String? personId});

  Stream<String?> get liveNessErrorStream;

  void dispose();

  ProfileLivenessModel? get liveNessValue;

  Stream<ViewDataModel<ProfileLivenessModel>> get liveNessStream;

  bool isToBase64Frame();
}

class LiveNessBlocV2 extends BaseBloc with LiveNessBlocIntV2 {
  LiveNessBlocV2(
      Repository repository, EKycPreferences preferences, Session session)
      : super(repository, preferences, session);

  final _liveNess = BehaviorSubject<ViewDataModel<ProfileLivenessModel>>();

  @override
  Stream<ViewDataModel<ProfileLivenessModel>> get liveNessStream =>
      _liveNess.stream;

  @override
  ProfileLivenessModel? get liveNessValue => _liveNess.valueOrNull?.data;

  @override
  void dispose() {
    _liveNess.close();
    super.dispose();
  }

  @override
  Stream<String?> get liveNessErrorStream => errorStream;

  @override
  bool isToBase64Frame() {
    return true;
  }

  @override
  start({
    int? width,
    int? height,
    String? personId,
    String? referralCode,
    String? exercise,
    bool? static,
  }) async {
    showLoading();
    safeAddData(_liveNess, ViewDataModel<ProfileLivenessModel>.loading());
    final String userId = (personId != null && personId.isNotEmpty)
        ? personId
        : await preferences.userId ?? '';
    return repository.liveNessApiV2!
        .start(
      width: width,
      height: height,
      externalPersonId: userId,
      exercise: exercise,
      static: static,
    )
        .then((value) {
      safeAddData(
        _liveNess,
        ViewDataModel<ProfileLivenessModel>.success(value),
      );
      return value;
    }).catchError((error) {
      handlerApiError(error);
      safeAddData(
        _liveNess,
        ViewDataModel<ProfileLivenessModel>.error(errorValue ?? ''),
      );
    }).whenComplete(completeLoading);
  }

  @override
  Future<bool> frames({
    String? content,
    List<int>? imageBytes,
    String? personId,
    String? task,
    int? timeStamp,
  }) {
    return repository.liveNessApiV2!
        .frame(
          faceId: liveNessValue!.id!,
          content: content ?? '',
          task: task ?? '',
          timestamp: timeStamp,
        )
        .then((value) => value)
        .catchError((error) {
      handlerApiError(error);
      return false;
    });
  }

  @override
  Future<LivenessVerifyResponse?> verify({String? personId}) {
    return repository.liveNessApiV2!
        .verify(faceId: liveNessValue!.id!)
        .then((value) {
      return LivenessVerifyResponse(success: value, message: '');
    }).catchError((error) {
      handlerApiError(error);
    });
  }

  void changeEnv({String? baseUrl, String? newToken}) {
    repository.liveNessApiV2!.dio.options.baseUrl =
        baseUrl ?? 'https://api.unicloud.ai/ekyc';
    repository.liveNessApiV2!.dio.options
            .headers[HttpHeaders.authorizationHeader] =
        'Basic ' + (newToken ?? 'c3NnLWhuLWRldjpob2Fub2tob25nbWF1');
  }

  @override
  Future<bool> sendVideoEKyc(
      {required List<int> videoBytes, required String personId}) {
    return Future.value(true);
  }
}
