/*
* Created by DuanVH 
* on 10/12/2021.
*/
import 'dart:async';
import 'dart:io';

import 'package:common/global_callback.dart';
import 'package:common/ks_common.dart' as common;
import 'package:common/model/loading_event.dart';
import 'package:connectivity/connectivity.dart';
import 'package:dio/dio.dart';
import 'package:ekyc_bloc/model/action_status.dart';
import 'package:ekyc_bloc/repository/preferences.dart';
import 'package:ekyc_bloc/repository/repository.dart';
import 'package:ekyc_bloc/repository/session/session.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:rxdart/rxdart.dart';

class BaseBloc with WidgetsBindingObserver {
  final EKycPreferences preferences;
  final Session session;
  final Repository repository;

  BaseBloc(this.repository, this.preferences, this.session) {
    init();
  }

  final logger = common.L();
  @protected
  final _progressVisible = BehaviorSubject<bool>.seeded(false);
  final _error = BehaviorSubject<String>.seeded("");
  final String threadFolder = 'threads';
  final String usersFolder = 'users';

  final _refreshVisible = BehaviorSubject<bool>();
  final _connectivity = Connectivity();

  @protected
  bool _isLockingApi = false;

  @protected
  bool get isLockingApi => _isLockingApi;

  @protected
  lockApi({bool locked = true}) {
    _isLockingApi = locked;
  }

  final streamSubs = <StreamSubscription>[];

  @protected
  final submitEnable = PublishSubject<bool>();

  Sink<bool> get showRefresh => _refreshVisible.sink;

  Stream<bool> get refreshVisible => _refreshVisible.stream;

  late StreamSubscription<ConnectivityResult> _connectionSubscription;

  Stream<common.NetworkStatus> get networkStatusStream =>
      session.networkStatus.stream;

  Stream<bool> get submitEnableStream => submitEnable.stream;

  Sink<String> get error => _error.sink;

  Stream<String> get errorStream => _error.stream;

  String? get errorValue => _error.valueOrNull;

  Sink<bool> get progressSink => _progressVisible.sink;

  Stream<bool> get progressStream => _progressVisible.stream;

  bool get isLoading => _progressVisible.valueOrNull == true;

  final _loadMoreBehavior = BehaviorSubject<bool>.seeded(false);

  Sink<bool> get loadMoreSink => _loadMoreBehavior.sink;

  Stream<bool> get loadMoreStream => _loadMoreBehavior.stream;

  bool get isLoadMore => _loadMoreBehavior.valueOrNull == true;

  final refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();
  var refreshCompleter = Completer();

  final dataStream = BehaviorSubject<common.LoadingEvent>();
  final scaffoldKey = GlobalKey<ScaffoldState>();

  static late ConnectivityResult _connect;

  ConnectivityResult get connect => _connect;

  final _lostConnect = BehaviorSubject<bool>();

  Stream<bool> get ourLostConnect => _lostConnect.stream;

  get isConnected => _connect != ConnectivityResult.none;

  final _loadingScreenVisible = BehaviorSubject<LoadingWidgetModel>();

  Stream<LoadingWidgetModel> get loadingScreenStream =>
      _loadingScreenVisible.stream;

  @protected
  void init() {
    logger.v('$runtimeType init()');
    _listen();
  }

  void dispose() {
    logger.v('$runtimeType dispose()');
    _progressVisible.close();
    _error.close();
    streamSubs.forEach((element) {
      element.cancel();
    });
    submitEnable.close();
    _refreshVisible.close();
    _lostConnect.close();
    _loadMoreBehavior.close();
    _connectionSubscription.cancel();
    dataStream.close();
    _loadingScreenVisible.close();
  }

  initSearch(Stream query, Future onSearch(String value)) {
    streamSubs.add(query
        .transform(SwitchMapStreamTransformer<String, dynamic>((value) {
          return Future.delayed(const Duration(milliseconds: 300), () => value)
              .asStream();
        }))
        .distinct()
        .listen((value) => onSearch(value)));
  }

  showLoading([bool show = true]) {
    _progressVisible.wellAdd(show);
  }

  showAreaLoading({BehaviorSubject<dynamic>? behavior}) {
    if (behavior != null) {
      behavior.add(null);
    }
  }

  _listen() {
    _initConnect();
    streamSubs.add(refreshVisible.listen((value) {
      if (value) {
        refreshIndicatorKey.currentState?.show();
      } else {
        if (!refreshCompleter.isCompleted) {
          refreshCompleter.complete();
        }
      }
    }));

    streamSubs.add(errorStream.listen((error) {
      dataStream.add(common.LoadingEvent(
          error.isNullOrEmpty
              ? common.LoadingStatus.success
              : common.LoadingStatus.error,
          message: error));
    }));
  }

  void reload() {
    logger.v('reload $runtimeType');
    _addError("");
  }

  _initConnect() async {
    _connect = await _connectivity.checkConnectivity();
    _lostConnect.wellAdd(!isConnected);
    _connectionSubscription =
        _connectivity.onConnectivityChanged.listen((result) {
      logger.v('$runtimeType onConnectivityChanged $result');
      _connect = result;
      final connected = isConnected;
      _lostConnect.wellAdd(!connected);
      if (connected) {
        reload();
      }
    });
  }

  Future<void> onRefresh() async {
    final completed = refreshCompleter.isCompleted;
    if (completed) {
      refreshCompleter = Completer();
      reload();
    }
    return refreshCompleter.future;
  }

  completeLoading() {
    logger.v('completeLoading  $runtimeType');
    _progressVisible.wellAdd(false);
    _refreshVisible.wellAdd(false);
    _isLockingApi = false;
  }

  _addError(error, {BehaviorSubject<dynamic>? behavior}) async {
    var errMessage =
        "Dịch vụ không thực hiện được lúc này. Quý khách vui lòng thử lại sau";
    if (error is DioException) {
      if (error.type == DioExceptionType.connectionTimeout ||
          error.type == DioExceptionType.receiveTimeout ||
          error.type == DioExceptionType.sendTimeout ||
          error.error is SocketException) {
        session.networkStatus.add(common.NetworkStatus.none);
        _error.add('Kết nối internet không khả dụng, vui lòng kiểm tra lại.');
        return;
      }
      int statusCode = error.response?.statusCode ?? 0;
      if ([500, 503, 404].contains(statusCode)) {
        errMessage = errMessage + " ($statusCode)";
      } else {
        final common.BaseResponseModel? model =
            common.BaseResponseModel.fromMapError(error);
        if (model?.statusCode == 401 ||
            model?.statusCode == common.ProfileStatusCode.INVALID_TOKEN) {
          session.actionStatus.add(ActionStatus.restart());
          return;
        }
        errMessage = model?.message ?? '';
      }
      FirebaseCrashlytics.instance.recordError(
        error,
        StackTrace.current,
      );
    } else if (error is String) {
      final regex = RegExp(r'exception|error', caseSensitive: false);
      if (!error.contains(regex)) {
        errMessage = error;
      }
    }
    _error.safeAdd(errMessage);
    if (behavior != null) {
      common.LoadingResult.error(behavior: behavior, error: errMessage);
    }
  }

  Future<void> handlerApiError(dynamic error,
      {BehaviorSubject<dynamic>? behavior}) async {
    logger.v(error.toString());
    try {
      if (error is DioException && error.response != null) {
        if (error.response!.statusCode == 401) {
          final token = await session.sunshine.refreshToken();
          if (token != null) {
            reload();
          }
        } else if (error.response!.statusCode ==
            common.ErrorCode.NOT_AUTHENTIC) {
          GlobalCallback.instance.onProfileOpen?.call();
          return;
        } else {
          _addError(error, behavior: behavior);
        }
      } else if (!_error.isClosed) {
        _addError(error, behavior: behavior);
      }
    } catch (_) {
      logger.e(_);
    }
  }

  Future logout() async {
    logger.v("logout");
    await session.logout();
  }

  bool diffEnable(diff) {
    if (!diff) return false;
    submitEnable.add(diff);
    return true;
  }

  safeAddData<T>(Subject<T> subject, T data) {
    subject.wellAdd(data);
  }

  // Loading khi gọi toàn màn hình
  showSubmitLoading() {
    LoadingResult.loading(behavior: _loadingScreenVisible, isSubmit: true);
  }

  completeSubmitLoading() {
    LoadingResult.success(behavior: _loadingScreenVisible);
  }
}
