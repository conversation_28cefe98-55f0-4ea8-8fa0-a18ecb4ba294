/*
 * Created by DuanVH
 * on 12/10/2021
 */
import 'dart:io';

import 'package:common/model/view_data_model.dart';
import 'package:ekyc_bloc/bloc/base_bloc.dart';
import 'package:ekyc_bloc/model/liveness_model.dart';
import 'package:ekyc_bloc/model/profile_liveness_model.dart';
import 'package:ekyc_bloc/repository/preferences.dart';
import 'package:ekyc_bloc/repository/repository.dart';
import 'package:ekyc_bloc/repository/session/session.dart';
import 'package:rxdart/rxdart.dart';

mixin LiveNessBlocInt {
  start({int? width, int? height, String? personId, String? referralCode});

  Future<bool> frames({
    String? content,
    List<int>? imageBytes,
    String? personId,
  });

  Future<LivenessVerifyResponse?> verify({String? personId});

  Stream<String?> get liveNessErrorStream;
  // Stream<String?> get profileLiveNessErrorStream;

  void dispose();

  LivenessModel? get liveNessValue;
  // ProfileLivenessModel? get profileLiveNessValue;

  Stream<ViewDataModel<LivenessModel>> get liveNessStream;
  // Stream<ViewDataModel<ProfileLivenessModel>> get profileLiveNessStream;

  bool isToBase64Frame();
}

class LiveNessBloc extends BaseBloc with LiveNessBlocInt {
  LiveNessBloc(
      Repository repository, EKycPreferences preferences, Session session)
      : super(repository, preferences, session);

  final _liveNess = BehaviorSubject<ViewDataModel<LivenessModel>>();

  @override
  Stream<ViewDataModel<LivenessModel>> get liveNessStream => _liveNess.stream;

  @override
  LivenessModel? get liveNessValue => _liveNess.valueOrNull?.data;

  @override
  void dispose() {
    _liveNess.close();
    super.dispose();
  }

  @override
  Stream<String?> get liveNessErrorStream => errorStream;

  @override
  bool isToBase64Frame() {
    return true;
  }

  @override
  start(
      {int? width, int? height, String? personId, String? referralCode}) async {
    showLoading();
    safeAddData(_liveNess, ViewDataModel<LivenessModel>.loading());
    final String userId = (personId != null && personId.isNotEmpty)
        ? personId
        : await preferences.userId ?? '';
    return repository.liveNessApi!
        .start(width: width, height: height, externalPersonId: userId)
        .then((value) {
      safeAddData(
        _liveNess,
        ViewDataModel<LivenessModel>.success(value),
      );
      return value;
    }).catchError((error) {
      handlerApiError(error);
      safeAddData(
        _liveNess,
        ViewDataModel<LivenessModel>.error(errorValue ?? ''),
      );
    }).whenComplete(completeLoading);
  }

  @override
  Future<bool> frames({
    String? content,
    List<int>? imageBytes,
    String? personId,
    String? task,
  }) {
    return repository.liveNessApi!
        .frame(
          faceId: liveNessValue!.id!,
          content: content ?? '',
        )
        .then((value) => value)
        .catchError((error) {
      handlerApiError(error);
    });
  }

  @override
  Future<LivenessVerifyResponse?> verify({String? personId}) {
    return repository.liveNessApi!
        .verify(faceId: liveNessValue!.id!)
        .then((value) {
      return LivenessVerifyResponse(success: value, message: '');
    }).catchError((error) {
      handlerApiError(error);
    });
  }

  void changeEnv({String? baseUrl, String? newToken}) {
    repository.liveNessApi!.dio.options.baseUrl =
        baseUrl ?? 'https://api.unicloud.ai/ekyc';
    repository
            .liveNessApi!.dio.options.headers[HttpHeaders.authorizationHeader] =
        'Basic ' + (newToken ?? 'c3NnLWhuLWRldjpob2Fub2tob25nbWF1');
  }

  // @override
  // Stream<String?> get profileLiveNessErrorStream => errorStream;
  //
  // @override
  // Stream<ViewDataModel<ProfileLivenessModel>> get profileLiveNessStream =>
  //     BehaviorSubject<ViewDataModel<ProfileLivenessModel>>().stream;
  //
  // @override
  // ProfileLivenessModel? get profileLiveNessValue => null;
}
