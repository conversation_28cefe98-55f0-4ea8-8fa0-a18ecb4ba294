name: ekyc
description: A new Flutter project.
version: 0.0.1
homepage: unicloud.com.vn
publish_to: none
environment:
  sdk: ">=3.0.0 <4.0.0"
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter

  flutter_localizations:
    sdk: flutter
  ekyc_bloc:
    path: bloc
  common:
    path: ../common

  flutter_secure_storage: ^6.1.0
  camera: ^0.10.5+9
  cupertino_icons: ^1.0.0
  google_mlkit_face_detection: ^0.11.1
  image: ^3.0.2
  flutter_simple_dependency_injection: ^2.0.0
  ffi: ^2.1.0
  wakelock_plus: ^1.2.7
  percent_indicator: ^4.0.1
  firebase_crashlytics: ^3.5.7
  video_compress: ^3.1.3
  dio: ^5.7.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^1.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:

  # To add assets to your package, add an assets section, like this:
  assets:
    - assets/
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
